const fs = require('fs');
const fsp = require("fs/promises");
const db = require('./src/db')
//const ItemModel = require('./src/models/itemModel')
const { DateTime } = require("luxon");
//const {cd} = require("./config");
const wcode = require('iconv-lite');

const pathForLocalPhoto = __dirname + '/../uploads/'
const pathForPhotoJournal = '/z_ftp/uer-data/mobil/foto/fotoin.txt' // 1c remote // /mnt/server.disk/mobil/foto/fotoin.txt
const pathForPhoto = '/z_ftp/uer-data/mobil/foto/zpr/' // /mnt/server.disk/mobil/foto/zpr/

//

function fixID(mainID) {
    let newID = mainID

    if (mainID.indexOf("/") !== -1) { // fix for ID with slash
        let textArr = mainID.split("/")
        newID = textArr[0] + "_" + textArr[1]
    }

    return newID
}

// async function uploadPhotoTo1c(urls,mainID) {
//
//     let newID = fixID(mainID)
//
//     let dirPath = pathForPhoto + newID + "/"
//
//     console.log("uploadPhotoTo1c path: " + dirPath)
//
//     fs.exists(dirPath, function ( exists) {
//
//         console.log("uploadPhotoTo1c: dirPath extist: " + exists)
//
//         if (exists) {
//             loadPhoto(urls,mainID)
//         } else {
//             fs.mkdir(dirPath, function (err) {
//                 if (err) {
//                     console.log(err)
//                     return
//                 }
//                 loadPhoto(urls,mainID)
//                 console.log('Folder created!');
//             });
//         }
//     });
// }

// async function loadPhoto(urls,mainID) {
//
//     for (let url of urls) {
//
//         let pathToPhotoLocalTemp = pathForLocalPhoto + url
//
//         let data;
//
//         try {
//             data = await fsp.readFile(pathToPhotoLocalTemp);
//         } catch (error) {
//             console.log(error)
//             return
//         }
//
//         let name = url.split("/")
//
//         let newID = fixID(mainID)
//
//         let uploadPath = pathForPhoto + newID + "/" + name[1]
//
//         try {
//             await fsp.writeFile(uploadPath, data, 'binary')
//             console.log("Write photo to server OK")
//
//         } catch (error) {
//             console.log(error)
//             return
//         }
//     }
// }

async function getLastSyncPhotoDate() {

    let fileRows = await readPhotoJournal()

    if (!fileRows) {
        return
    }

    let options = {
        zone: "Asia/Yekaterinburg"
    };

    //console.log(fileRows.length)

    if (fileRows.length > 1) {
        let lastRow = fileRows[fileRows.length - 1]
        let rowArr = lastRow.split(";")
        let dateStr = rowArr[1]

        let date = DateTime.fromFormat(dateStr, 'yyyyLLddHHmmss', options)

        let unix = date.toUnixInteger()
        return unix
    }
}

async function syncPhotoJournal() {

    console.log("syncPhotoJournal")

    let lastUnix = await getLastSyncPhotoDate()

    console.log("LastSyncPhotoDate: " + lastUnix)

    let query;

    if (lastUnix) {
        query = {
            "photos": { $exists: true },
            "photos.createdAt": {$gt: lastUnix}
        }
    } else {
        query = {
            "photos": { $exists: true },
            "photos.createdAt": {$gt: 0}
        }
    }

    var sort = {updatedAt: -1}
    const items = await db.getItems(query,sort)

    var objs = [];

    let options = {
        zone: "Asia/Yekaterinburg"
    }

    items.sort(function(a, b) {
        return parseFloat(a.updatedAt) - parseFloat(b.updatedAt);
    });

    if (items) {

        for (const item of items) {
            let photos = item.photos

            //await addToPhotoJournal(item.mainID,)

            for (const photo of photos) {

                if (lastUnix) {
                    if (photo.createdAt <= lastUnix) {
                        continue
                    }
                }

                let urlArr = photo.url.split("/")
                let pathID = urlArr[0]
                let name = urlArr[1]
                let comment = photo.comment

                let dateStr = DateTime.fromSeconds(photo.createdAt, options).toFormat('yyyyLLddHHmmss');

                let obj = {
                    mainID: item.mainID,
                    pathID: pathID,
                    name: name,
                    comment: comment,
                    createdAt: photo.createdAt,
                    dateStr: dateStr
                }

                objs.push(obj)

                //console.log(obj)
            }
        }
    }

    if (objs.length > 0) {
        //console.log(objs)
        addToPhotoJournal(objs,lastUnix)
    }
}

async function readPhotoJournal() {

    let data;

    try {
        data = await fsp.readFile(pathForPhotoJournal);
    } catch (error) {
        console.log(error)
        return
    }

    console.log("Read journal!")
    //console.log(data);

    // Convert from an encoded buffer to a js string.
    str = wcode.decode(data, 'win1251'); //ascii //win1251

    //console.log(str);

    //let fileRows = data.toString().split('\r\n');

    let fileRows = str.split('\r\n');

    return fileRows
}

async function addToPhotoJournal(objs,unixTime) {

    let fileRows = await readPhotoJournal()

    if (!fileRows) {
        return
    }

    //console.log("FileRows:" + fileRows)

    let numberRow = 1

    if (fileRows.length > 0) {
        let lastRow = fileRows[fileRows.length - 1]
        let rowArr = lastRow.split(";")
        let number = rowArr[0]
        numberRow = Number(number) + 1
    }

    let newFile;

    if (!unixTime) {
        fileRows = []
    }

    for (const obj of objs) {

        let name = obj.name
        let pathID = obj.pathID
        let mainID = obj.mainID
        let dateStr = obj.dateStr
        let comment = obj.comment

        let rowNew = numberRow + ";" + dateStr + ";ЗПР;" + mainID + ";foto\\zpr\\" + pathID + "\\" + name + ";" + comment
        //console.log(rowNew)

        if (numberRow === 1) {
            fileRows = [rowNew]
        } else {
            fileRows.push(rowNew)
        }

        numberRow = numberRow + 1
    }

    if (fileRows.length === 1) {
        newFile = fileRows[0]
    } else {
        newFile = fileRows.join('\r\n')
    }

    // // Convert from a js string to an encoded buffer.
    let buf = wcode.encode(newFile, 'win1251');

    //const encodedData = wcode.encode(newFile);

    try {
        await fsp.writeFile(pathForPhotoJournal, buf, "binary") //{ encoding: 'ascii' }
        //console.log(result)
        console.log('It\'s saved journal!');
    } catch (error) {
        console.log(error)
        return
    }

    // fs.writeFile(pathForPhotoJournal, newFile, function (err) {
    //     if (err) throw err;
    //     console.log('It\'s saved journal!');
    // });

}

module.exports = {

    //uploadPhotoTo1c,
    syncPhotoJournal
}
