require('dotenv').config();
require('../src/db.js')

const photo = require('../photoUpload.js')
const order = require('../src/order/order.js')
const ssz = require('../src/ssz/ssz.js')
//const parse = require('./src/parse/parse')
const videos = require('../src/parse/videos_parse.js')
const statsParse = require('../src/parse/outage_stats_parse.js')
const outageCheckPush = require('../src/parse/outage_check_push.js')

process.on('uncaughtException', (error) => {
    console.error('Необработанное исключение:', error);
    // Можно добавить логирование в файл или отправку уведомления
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('Необработанное отклонение Promise:', reason);
    // Можно добавить логирование в файл или отправку уведомления
});

let CronJob = require('cron').CronJob;

let job = new CronJob('*/15 * * * * *', function() {
    console.log('Sync cron ZPR activate');
    try {
        order.readUpdateFile().catch(err => {
            console.error('Ошибка в задании ZPR:', err);
        });
    } catch (error) {
        console.error('Критическая ошибка в cron-задании ZPR:', error);
    }
}, null, true, 'Asia/Yekaterinburg');

let job2 = new CronJob('00 30 19 * * 0-6', function() {
    console.log('Sync cron Photo activate');
    try {
        photo.syncPhotoJournal().catch(err => {
            console.error('Ошибка в задании Photo:', err);
        });
    } catch (error) {
        console.error('Критическая ошибка в cron-задании Photo:', error);
    }
}, null, true, 'Asia/Yekaterinburg');

let job3 = new CronJob('*/15 * * * * *', function() {
    console.log('Sync cron SSZ activate (every 15 seconds)');
    try {
        ssz.getSSZFrom1c().catch(err => {
            console.error('Ошибка в задании SSZ:', err);
        });
    } catch (error) {
        console.error('Критическая ошибка в cron-задании SSZ:', error);
    }
}, null, true, 'Asia/Yekaterinburg');

let job4 = new CronJob('00 00 22 * * 0-6', function() {
    console.log('Sync cron SSZ Task upload activate');
    try {
        ssz.exportSSZForAllDay().catch(err => {
            console.error('Ошибка в задании SSZ Task upload:', err);
        });
    } catch (error) {
        console.error('Критическая ошибка в cron-задании SSZ Task upload:', error);
    }
}, null, true, 'Asia/Yekaterinburg');

let job5 = new CronJob('00 30 21 * * 1-5', function() {
    console.log('Sync cron SSZ Auto close activate');
    try {
        ssz.autoCloseSSZ().catch(err => {
            console.error('Ошибка в задании SSZ Auto close:', err);
        });
    } catch (error) {
        console.error('Критическая ошибка в cron-задании SSZ Auto close:', error);
    }
}, null, true, 'Asia/Yekaterinburg');

let job6 = new CronJob('00 */5 8-19 * * 0-6', function() {
    console.log('Sync cron Video Test Parse Activate');
    try {
        videos.videoParseStart().catch(err => {
            console.error('Ошибка в задании Video Parse:', err);
        });
    } catch (error) {
        console.error('Критическая ошибка в cron-задании Video Parse:', error);
    }
}, null, true, 'Asia/Yekaterinburg');

let job7 = new CronJob('00 10 7-23/5 * * 0-6', function() {
    console.log('Cron: Stats OutAge Parse - Activate');
    try {
        statsParse.outAgeStatsParse().catch(err => {
            console.error('Ошибка в задании Stats OutAge Parse:', err);
        });
    } catch (error) {
        console.error('Критическая ошибка в cron-задании Stats OutAge Parse:', error);
    }
}, null, true, 'Asia/Yekaterinburg');

let job8 = new CronJob('00 00 9 * * 1-5', function() {
    console.log('Cron: OutAge Check and Push - Activate');
    try {
        outageCheckPush.outageCheck().catch(err => {
            console.error('Ошибка в задании OutAge Check and Push:', err);
        });
    } catch (error) {
        console.error('Критическая ошибка в cron-задании OutAge Check and Push:', error);
    }
}, null, true, 'Asia/Yekaterinburg');

// let jobTest = new CronJob('0 */6 * * * 0-6', function() {
//     console.log('Sync cron SSZ TEST');
// }, null, true, 'Asia/Yekaterinburg');

async function start() {
	job.start();
    job2.start();
    job3.start();
    job4.start();
    job5.start();
    job6.start();
    job7.start();
    job8.start();
    //jobTest.start();
}

(async () => {
    try {
        await start();
        console.log('Все cron-задания успешно запущены');
    } catch (error) {
        console.error('Ошибка при запуске cron-заданий:', error);
    }
})();