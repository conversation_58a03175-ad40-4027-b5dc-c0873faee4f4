const {DateTime} = require("luxon");
const db = require('./db')

async function getIdsForMainID(mainID) {

    const item = await db.getItem(mainID)

    let idsArr = [mainID]

    if (item.altIds) {
        for (let id in item.altIds) {
            idsArr.push(id)
        }
    }

    return idsArr
}

function clearEmpties(o) {
    for (var k in o) {

        if (o[k] === undefined) {
            delete o[k]
            continue
        }

        if (!o[k] || typeof o[k] !== "object") {
            continue // If null or not an object, skip to the next iteration
        }

        // The property is an object
        clearEmpties(o[k]); // <-- Make a recursive call on the nested object
        if (Object.keys(o[k]).length === 0) {
            delete o[k]; // The object had no properties, so delete that property
        }
    }
    return o;
}

function normalDateFromUSAFormat(str) {
    let options = {
        zone: "Asia/Yekaterinburg"
    };

    let date = DateTime.fromFormat(str, 'L/d/yyyy', options)
    let newDateStr = date.toFormat('dd.LL.yyyy');

    return newDateStr
}

function dateFromFullStrToUnix(str) {

    let options = {
        zone: "Asia/Yekaterinburg"
    };

    let date = DateTime.fromFormat(str, 'yyyyLLddHHmmss', options)
    let result = date.toUnixInteger()
    return result
}

function dateFromUnixToFullStr(unix) {

    let options = {
        zone: "Asia/Yekaterinburg"
    };

    let dateStr = DateTime.fromSeconds(unix, options).toFormat('yyyyLLddHHmmss');
    return dateStr
}

function dateFromUnixToShortStr(unix) {

    let options = {
        zone: "Asia/Yekaterinburg"
    };

    let dateStr = DateTime.fromSeconds(unix, options).toFormat('dd.LL.yyyy');
    return dateStr
}

function dateFromFullDateFormatToUnix(dateStr) {
    // 2023-06-02 08:53:56

    let options = {
        zone: "Asia/Yekaterinburg"
    };

    let date = DateTime.fromFormat(dateStr, 'yyyy-LL-dd HH:mm:ss', options)
    let result = date.toUnixInteger()
    return result
}

function isArray(a) {
    return (!!a) && (a.constructor === Array);
}

function getNameTypeItem(item) {

    switch (item) {
        case "stator":
            return "Статор";
            break
        case "rotor":
            return "Ротор";
            break
        case "slidingBearing":
            return "Подшипник скольжения";
            break
        case "rollingBearing":
            return "Подшипниковые щиты";
            break
        case "transformer":
            return "Трансформатор"
            break
        case "inductor":
            return "Индуктор"
            break
        case "anchor":
            return "Якорь"
            break
        default:
            return "---"
    }
}


module.exports = {
    normalDateFromUSAFormat,
    clearEmpties,
    dateFromFullStrToUnix,
    dateFromUnixToShortStr,
    dateFromUnixToFullStr,
    getNameTypeItem,
    isArray,
    dateFromFullDateFormatToUnix
}