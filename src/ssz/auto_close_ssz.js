const ssz_requests = require("./ssz_requests");
const moment = require("moment-timezone");

async function autoCloseSSZ(today, date) {
    console.log("autoCloseSSZ Start Script");

    let items = await ssz_requests.getTaskItems(today, date);
    let useDate = today === false && date ? date : new Date();

    // Задаём дату и время в нужной временной зоне
    const ekbTime = moment.tz(useDate, "Asia/Yekaterinburg");

    let week = ekbTime.day();
    let timeEndWork;

    if (week > 0 && week < 5) {
        timeEndWork = ekbTime.hour(17).minute(0).second(0).unix();
    } else if (week === 5) {
        timeEndWork = ekbTime.hour(16).minute(0).second(0).unix();
    } else {
        console.log("autoCloseSSZ Выходные! Отмена!");
        return;
    }

    for (let item of items) {
        let changed = false;
        for (let comp of item.components) {
            for (let history of comp.newHistory) {
                if (history.type === "task") {
                    let lastStatus = history.statuses[history.statuses.length - 1];
                    if (lastStatus.status !== "finish") {
                        let newStatus = {
                            owner: "Автозакрытие",
                            status: "finish",
                            createdAt: lastStatus.status === "inwork" ? timeEndWork : lastStatus.createdAt,
                            updatedAt: lastStatus.status === "inwork" ? timeEndWork : lastStatus.updatedAt
                        };
                        history.statuses.push(newStatus);
                        changed = true;
                    }
                    for (const worker of history.workers) {
                        if (worker.statuses && worker.statuses.length > 0) {
                            let lastStatusWorker = worker.statuses[worker.statuses.length - 1];
                            if (lastStatusWorker.status !== "finish") {
                                let newStatusWorker = {
                                    status: "finish",
                                    createdAt: lastStatusWorker.status === "inwork" ? timeEndWork : lastStatusWorker.createdAt,
                                    updatedAt: lastStatusWorker.status === "inwork" ? timeEndWork : lastStatusWorker.updatedAt
                                };
                                worker.statuses.push(newStatusWorker);
                                changed = true;
                            }
                        }
                    }
                }
            }
        }
        if (changed) {
            await item.save();
            console.log("autoCloseSSZ Update History");
        }
    }
    console.log("autoCloseSSZ End Script");
}

module.exports = { autoCloseSSZ };