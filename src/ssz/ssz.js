const auto_close_ssz = require("./auto_close_ssz");
const ssz_import = require("./ssz_import")
const ssz_export = require("./ssz_export")
const ssz_requests = require("./ssz_requests")
const ssz_worktime_calculate = require("./ssz_worktime_calculate")
const close_missing_ssz = require("./close_missing_ssz")
const utils = require("../utils");

async function getSSZFrom1c() {
    await ssz_import.importSSZWithLastUpdate()
}

// Добавляем новую функцию для повторного импорта ССЗ за определенную дату
async function getSSZFrom1cByDate(dateStr) {
    // Преобразуем строку даты в формат ГГГГММДДЧЧММСС для передачи в importSSZWithLastUpdate
    if (!dateStr) {
        console.log("Дата не указана, используем стандартный импорт");
        await ssz_import.importSSZWithLastUpdate();
        return;
    }
    
    try {
        // Парсим дату из строки формата "YYYY-MM-DD"
        const dateParts = dateStr.split("-");
        if (dateParts.length !== 3) {
            throw new Error("Неверный формат даты. Используйте YYYY-MM-DD");
        }
        
        const year = parseInt(dateParts[0]);
        const month = parseInt(dateParts[1]) - 1; // В JavaScript месяцы от 0 до 11
        const day = parseInt(dateParts[2]);
        
        const date = new Date(year, month, day, 0, 0, 0);
        
        // Преобразуем в формат ГГГГММДДЧЧММСС
        const dateNumber = parseInt(
            date.getFullYear().toString() +
            (date.getMonth() + 1).toString().padStart(2, '0') +
            date.getDate().toString().padStart(2, '0') +
            "000000"
        );
        
        console.log(`Запуск импорта ССЗ с даты: ${dateStr} (${dateNumber})`);
        await ssz_import.importSSZWithLastUpdate(dateNumber);
    } catch (error) {
        console.error(`Ошибка при запуске импорта по дате: ${error.message}`);
    }
}

async function autoCloseSSZ() {

    //let now = new Date();
    //let customDate = new Date(now.getFullYear(), now.getMonth(), 18);

    await auto_close_ssz.autoCloseSSZ(true)
}

async function exportSSZForAllDay() {

    //let now = new Date();
    //let customDate = new Date(now.getFullYear(), now.getMonth(), 18);

    let {result,dateNowStr,dateFullStr} = await ssz_worktime_calculate.getAllTaskForDay(true)

    //console.log(result)

    ssz_export.loadToServer(result,dateNowStr,dateFullStr) // xml, dateNowStr, dateFullStr
}

async function exportSSZForDay(date) {
    //let now = new Date();
    //let customDate = new Date(now.getFullYear(), now.getMonth(), 18);

    let {result,dateNowStr,dateFullStr} = await ssz_worktime_calculate.getAllTaskForDay(false, date)

    //console.log(result)

    let timeSecondsNow = Math.floor(Date.now() / 1000)
    let dateFullStrToday = utils.dateFromUnixToFullStr(timeSecondsNow)

    console.log("Выгрузка в журнал для даты: " + dateFullStrToday)

    ssz_export.loadToServer(result,dateNowStr,dateFullStrToday) // xml, dateNowStr, dateFullStr
}

async function updateExportSZZ() {

    console.log("Начала тестового экспорта")
    // Начальная дата: 19 августа 2024 года
    let currentDate = new Date(2024, 7, 19); // Месяцы в JavaScript начинаются с 0, поэтому 7 — это август

    // Конечная дата: предыдущий день от сегодняшней даты
    let endDate = new Date();
    endDate.setDate(endDate.getDate() - 1);

    // Цикл по датам от 19 августа 2024 года до вчерашнего дня
    while (currentDate <= endDate) {

        console.log("Экспорт по дате: " + currentDate);

        // Вызываем exportSSZForDay для текущей даты
        await exportSSZForDay(new Date(currentDate));

        // Переходим к следующему дню
        currentDate.setDate(currentDate.getDate() + 1);
    }

    console.log("Экспорт завершен для всех дат.");
}

async function closeMissingSSZForDay(dateStr = null) {
    // Если дата не передана, закрываем ССЗ за вчерашний день
    if (!dateStr) {
        console.log("Закрытие незакрытых ССЗ за вчерашний день");
        await close_missing_ssz.closeMissingSSZ();
    } else {
        console.log(`Закрытие незакрытых ССЗ за указанную дату: ${dateStr}`);
        await close_missing_ssz.closeMissingSSZByDate(dateStr);
    }
}

module.exports = {
    getSSZFrom1c,
    autoCloseSSZ,
    exportSSZForAllDay,
    updateExportSZZ,
    closeMissingSSZForDay,
    getSSZFrom1cByDate // Добавляем новую функцию в экспорт
}