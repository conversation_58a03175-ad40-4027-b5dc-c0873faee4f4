const fsp = require("fs/promises");
const wcode = require("iconv-lite");

const pathPrefix = "/z_ftp/uer-data/mobil/ssz/"
const journalExportName = 'sszin.txt'
const folderExportName = "doc_in"
const pathForSSZInJournal = pathPrefix + journalExportName // 1c remote

//const pathPrefixTestMyDisk = '/Users/<USER>/Desktop/uer_other/mobil_old/ssz/'

async function loadToServer(xml, dateNowStr, dateFullStr) {
    try {
        //let uploadPath = pathPrefixTestMyDisk + folderExportName + "/" + dateNowStr + ".xml"
        let uploadPath = pathPrefix + folderExportName + "/" + dateNowStr + ".xml"

        try {
            await fsp.writeFile(uploadPath, xml)
            console.log("Write xml ssz to server OK")

            let pathForJournal = "ssz\\" + folderExportName + "\\" + dateNowStr + ".xml"

            await syncExportJournal(pathForJournal, dateFullStr)
        } catch (error) {
            console.log("Ошибка записи файла XML на сервер:", error)
            return
        }
    } catch (outerError) {
        console.log("Критическая ошибка в loadToServer:", outerError)
    }
}

async function syncExportJournal(pathForJournal, dateFullStr) {
    try {
        let fileRows = await readExportJournal()

        if (!fileRows) {
            console.log("syncExportJournal: FileRows empty or error read")
            return
        }

        //console.log(fileRows)

        let numberRow = 1

        if (fileRows.length > 0) {
            let lastRow = fileRows[fileRows.length - 1]
            let rowArr = lastRow.split(";")
            let number = rowArr[0]
            numberRow = Number(number) + 1
        }

        let newFile;

        let rowNew = numberRow + ";" + dateFullStr + ";ОТЧЕТ;" + pathForJournal

        if (numberRow === 1) {
            fileRows = [rowNew]
        } else {
            fileRows.push(rowNew)
        }

        if (fileRows.length === 1) {
            newFile = fileRows[0]
        } else {
            newFile = fileRows.join('\r\n')
        }

        // // Convert from a js string to an encoded buffer.
        let buf = wcode.encode(newFile, 'win1251');

        try {
            await fsp.writeFile(pathForSSZInJournal, buf, "binary") //{ encoding: 'ascii' }
            //console.log(result)
            console.log('It\'s saved journal SSZ in!');
        } catch (error) {
            console.log("Ошибка записи журнала:", error)
            return
        }
    } catch (outerError) {
        console.log("Критическая ошибка в syncExportJournal:", outerError)
    }
}

async function readExportJournal() {

    let data;

    try {
        data = await fsp.readFile(pathForSSZInJournal);
    } catch (error) {
        console.log(error)
        return
    }

    console.log("Read export SSZ journal!")

    str = wcode.decode(data, 'win1251'); //ascii //win1251

    //console.log(str);

    let fileRows = str.split('\r\n');

    return fileRows
}

module.exports = {
    loadToServer
}