const db = require('./../db')
// Импортируем модель ItemModel
const ItemModel = require('./../models/itemModel');
const utils = require("../utils");

// Функция для закрытия пропущенных ССЗ за указанную дату
async function closeMissingSSZ(specificDate = null) {
    try {
        // Если дата не указана, берем вчерашний день
        const targetDate = specificDate || new Date(Date.now() - 86400000); // 86400000 мс = 1 день
        
        // Определяем начало и конец указанного дня
        const startOfDay = new Date(targetDate);
        startOfDay.setHours(0, 0, 0, 0);
        const startTimestamp = Math.floor(startOfDay.getTime() / 1000);
        
        const endOfDay = new Date(targetDate);
        endOfDay.setHours(23, 59, 59, 999);
        const endTimestamp = Math.floor(endOfDay.getTime() / 1000);

        // Форматируем дату для вывода
        const formattedDate = targetDate.toLocaleDateString('ru-RU');
        
        console.log(`Начинаем поиск незакрытых ССЗ за ${formattedDate}`);

        // Ищем все элементы с компонентами, созданными или обновленными в целевой день
        const query = {
            'components.newHistory.createdAt': { 
                $gte: startTimestamp, 
                $lte: endTimestamp 
            }
        };

        // Находим все элементы по запросу
        const items = await ItemModel.find(query);

        console.log(`Найдено элементов для проверки: ${items.length}`);

        // Время для автозакрытия - 17:00 по Екатеринбургскому времени (UTC+5)
        // Вычисляем timestamp для 17:00 указанного дня
        const closeTime = new Date(targetDate);
        closeTime.setHours(17, 0, 0, 0); // 17:00 в Екатеринбурге будет (17-5):00 UTC
        const closeTimestamp = Math.floor(closeTime.getTime() / 1000);

        let totalClosed = 0;

        for (let item of items) {
            let itemChanged = false;

            console.log(`\nОбрабатываем элемент mainID: ${item.mainID}`);

            for (let comp of item.components) {
                let compChanged = false;

                console.log(`  Обрабатываем компонент: ${comp.identifier}`);

                for (let history of comp.newHistory) {
                    console.log(`    Обрабатываем историю: ${history.name}`);

                    // Проверяем, создана ли история в целевой день
                    if (history.createdAt >= startTimestamp && history.createdAt <= endTimestamp && history.type === 'task') {
                        
                        // Проверяем, есть ли финальный статус "finish"
                        let hasFinishStatus = history.statuses.some(s => s.status === 'finish');
                        
                        if (!hasFinishStatus) {
                            console.log(`      История ${history.name} не закрыта, добавляем статус автозакрытия`);
                            
                            // Создаем статус автозакрытия
                            const autoCloseStatus = {
                                status: 'finish',
                                owner: 'Автозакрытие',
                                //comment: `Автоматическое закрытие за ${formattedDate}`,
                                createdAt: closeTimestamp,
                                updatedAt: closeTimestamp
                            };
                            
                            history.statuses.push(autoCloseStatus);
                            
                            // Закрываем статусы всех работников, которые не имеют статуса "finish"
                            for (let worker of history.workers) {
                                console.log(`        Проверяем работника: ${worker.name}`);
                                
                                let hasWorkerFinish = worker.statuses.some(s => s.status === 'finish');
                                
                                if (!hasWorkerFinish) {
                                    console.log(`          Работник ${worker.name} не завершил работу, добавляем статус закрытия`);
                                    
                                    // Создаем статус закрытия для работника
                                    const workerCloseStatus = {
                                        status: 'finish',
                                        //comment: `Автоматическое закрытие за ${formattedDate}`,
                                        createdAt: closeTimestamp,
                                        updatedAt: closeTimestamp
                                    };
                                    
                                    worker.statuses.push(workerCloseStatus);
                                }
                            }
                            
                            compChanged = true;
                            totalClosed++;
                        }
                    }
                }

                if (compChanged) {
                    itemChanged = true;
                }
            }

            if (itemChanged) {
                await item.save();
                console.log(`Элемент с mainID: ${item.mainID} обновлён и сохранён.`);
            } else {
                console.log(`Элемент с mainID: ${item.mainID} не требует обновления.`);
            }
        }

        console.log(`\nЗакрытие ССЗ завершено. Всего закрыто элементов: ${totalClosed}`);
        if (process.env.NODE_ENV !== 'test') {
            process.exit(0);
        }
    } catch (error) {
        console.error('Ошибка при закрытии ССЗ:', error);
        if (process.env.NODE_ENV !== 'test') {
            process.exit(1);
        }
    }
}

// Функция для закрытия ССЗ за указанную дату
function closeMissingSSZByDate(dateStr) {
    // Преобразуем строку даты в объект Date
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) {
        console.error('Некорректный формат даты. Используйте формат YYYY-MM-DD');
        return;
    }
    
    return closeMissingSSZ(date);
}

module.exports = {
    closeMissingSSZ,
    closeMissingSSZByDate
}
