const db = require("../db");

async function getTaskItems(today, date) {

    // Поиск начатых или уже записаных ССЗ в базе

    let startOfDay;

    if (today === true) {
        let now = new Date();
        startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    } else if (date) {
        startOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate());
    } else {
        return
    }

    let timestampStart = startOfDay / 1000;
    let timestampEnd = timestampStart + 86400 // 1 day

    let query = {
        "components.newHistory.task": {$exists: true},
        "components.newHistory.createdAt": {$gt: timestampStart, $lt: timestampEnd},
    }

    var sort = {updatedAt: -1}
    let items = await db.getItems(query,sort)
    return items
}

module.exports = {
    getTaskItems
}

