const db = require("../db");
const fsp = require("fs/promises");
const convert = require("xml-js");
const utils = require("../utils");
const path = require("path");
const fs = require("fs");

const pathPrefix = "/z_ftp/uer-data/mobil/ssz/"
//const pathPrefixTestMyDisk = '/Users/<USER>/Desktop/uer_other/mobil_old/ssz/'
const journalName = 'sszout.txt'

// Импорт планируемых ССЗ из файла sszout.txt (который генерирует 1с)

async function importSSZWithLastUpdate(updateRewrite) {
    try {
        log("Начало обработки журнала ССЗ");

        // Получаем timestamp начала текущего дня
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const startOfDayTimestamp = Math.floor(today.getTime() / 1000);
        
        // Преобразуем в формат даты, используемый в журнале (ГГГММДДЧЧММСС)
        let lastUpdate = parseInt(today.getFullYear().toString() +
            (today.getMonth() + 1).toString().padStart(2, '0') +
            today.getDate().toString().padStart(2, '0') +
            "000000");
        
        log(`Используем дату начала дня: ${lastUpdate}`);

        if (updateRewrite) {
            lastUpdate = updateRewrite;
            log(`Переопределена дата обновления: ${lastUpdate}`);
        }

        let data;

        // Оборачиваем основную логику в try-catch
        try {
            data = await fsp.readFile(pathPrefix + journalName);
            log("Файл журнала ССЗ прочитан успешно");
        } catch (error) {
            log("Ошибка чтения файла sszout.txt!");
            console.log(error);
            return;
        }

        let fileRows = data.toString('utf8').split('\r\n');
        log(`Всего строк в журнале: ${fileRows.length}`);

        // Создаем структуру для хранения уникальных ССЗ с учетом их статуса
        let uniqueSSZ = new Map(); // Ключ: ID, Значение: {row: строка, time: время, status: статус}

        // Проходим по всем строкам и выбираем уникальные ССЗ с учетом их времени
        for (let i = 0; i < fileRows.length; i++) {
            let row = fileRows[i];
            if (!row.trim()) continue;  // Пропускаем пустые строки

            let objArr = row.split(";");
            if (objArr.length < 5) continue;  // Проверка на правильный формат строки

            let time = objArr[1];
            let id = objArr[2];
            let status = objArr[4];

            if (time <= lastUpdate) {
                continue; // Пропускаем ССЗ, которые старше начала текущего дня
            }

            // Если ССЗ с таким ID уже встречался, обновляем только если новая запись новее
            if (!uniqueSSZ.has(id) || parseInt(time) > parseInt(uniqueSSZ.get(id).time)) {
                uniqueSSZ.set(id, { row: objArr, time: time, status: status });
            }
        }

        log(`Найдено ${uniqueSSZ.size} уникальных ССЗ для обработки`);

        // Обрабатываем каждый уникальный ССЗ
        let processed = 0;
        let errors = 0;
        let skipped = 0;

        for (const [id, data] of uniqueSSZ.entries()) {
            let objArr = data.row;
            let time = data.time;  
            let status = data.status;
            let path = objArr[3]; 
            let comment = objArr[5];

            // Проверка на удаление
            if (status === "D") {
                log(`ССЗ ${id} имеет статус D - удаление из базы`);
                await removeTaskFromDB(id);
                processed++;
                continue;
            }

            let pathArr = String(path).split("doc\\");

            if (pathArr[1] === undefined) {
                log(`Некорректный путь для ССЗ ${id}: ${path}`);
                skipped++;
                continue;
            }

            let pathXml = pathPrefix + "doc/" + pathArr[1];

            // Проверяем существование файла
            if (!fs.existsSync(pathXml)) {
                log(`Файл для ССЗ ${id} не найден: ${pathXml} - будет обработан в следующем цикле`);
                skipped++;
                continue;
            }

            try {
                await readXmlForPath(pathXml, time, status);
                processed++;
            } catch (error) {
                log(`Ошибка при обработке ССЗ ${id}: ${error.message}`);
                errors++;
            }
        }

        log(`Обработка завершена. Обработано: ${processed}, пропущено: ${skipped}, ошибок: ${errors}`);
    } catch (error) {
        // Добавляем обработку всех возможных ошибок на верхнем уровне функции
        log(`Критическая ошибка при импорте ССЗ: ${error.message}`);
        console.error(error);
    }
}

async function readXmlForPath(path, time, status) {
    try {
        let data;

        try {
            data = await fsp.readFile(path);
            //log(`Успешно прочитан XML файл: ${path}`);
        } catch (error) {
            log(`Ошибка чтения XML файла ${path}: ${error.message}`);
            throw error;
        }

        let str = data.toString();

        let splitStr = str.split("</КоммерческаяИнформация>");
        let newStr = splitStr[0] + "</КоммерческаяИнформация>";

        let options = {compact: true, trim: true};
        let json = convert.xml2js(newStr, options);

        let body = json["КоммерческаяИнформация"];

        // Проверка наличия обязательных полей
        if (!body || !body["Дата"] || !body["Дата"]["_text"]) {
            log("Ошибка SSZ xml. Нет даты.");
            throw new Error("Отсутствует дата в XML");
        }

        if (!body["Участок"] || !body["Участок"]["ИдУчастка"] || !body["Участок"]["ИдУчастка"]["_text"]) {
            log("Ошибка SSZ xml. Нет участка.");
            throw new Error("Отсутствует участок в XML");
        }

        let ssz = {
            id: body["Ид"]["_text"],
            number: body["Номер"]["_text"],
            idBranch: body["ИдФилиал"]["_text"],
            areaSsz: {
                id: body["Участок"]["ИдУчастка"]["_text"],
                name: body["Участок"]["НаименованиеУчастка"]["_text"]
            },
            version: body["_attributes"]["ВерсияСхемы"],
            updateDate: body["_attributes"]["ДатаФормирования"],
            updateDateNumbers: Number(time)
        }

        if (ssz.idBranch === "000000023") {
            ssz.idBranch = "000000024";
        }

        // Обработка даты ССЗ
        let dateSSZ = body["Дата"]["_text"];

        if (dateSSZ.indexOf(".") !== -1) {
            ssz.date = dateSSZ;
        } else if (dateSSZ.indexOf("/") !== -1) { // Фикс если дата приходит в формате США (баг в 1с)
            let newDate = utils.normalDateFromUSAFormat(dateSSZ);
            ssz.date = newDate;
        }

        // Остальной код без изменений
        let newWorker = function (worker) {
            let newWorker = {
                id: worker["ИдСотрудника"]["_text"],
                name: worker["НаименованиеСотрудника"]["_text"]
            }

            return newWorker
        };

        if (body["Задания"]) {
            let tasks = body["Задания"]["Задание"];
            let taskObjs = [];

            let newTask = function (task) {
                let newTask = {
                    mainID: task["ЗаказНаПроизводство"]["_text"],
                    plan: task["ПланКоличество"]["_text"],
                    fact: task["ФакиКоличество"]["_text"],
                    job: {
                        id: task["Работа"]["ИдРабота"]["_text"],
                        name: task["Работа"]["НаименованиеРабота"]["_text"],
                    }
                }

                let workers = task["Исполнители"]["Сотрудник"];
                let workersObjs = [];

                if (workers) {
                    if (utils.isArray(workers)) {
                        for (const worker of workers) {
                            workersObjs.push(newWorker(worker));
                        }
                    } else {
                        workersObjs.push(newWorker(workers));
                    }

                    newTask.workers = workersObjs;
                }

                return newTask;
            };

            if (utils.isArray(tasks)) {
                for (const task of tasks) {
                    taskObjs.push(newTask(task));
                }
            } else {
                taskObjs.push(newTask(tasks));
            }

            ssz.tasks = taskObjs;
        }

        if (body["Исполнители"]) {
            let workers = body["Исполнители"]["Сотрудник"];
            let workerObjs = [];

            if (utils.isArray(workers)) {
                for (const worker of workers) {
                    workerObjs.push(newWorker(worker));
                }
            } else {
                workerObjs.push(newWorker(workers));
            }

            ssz.workers = workerObjs;
        }

        ssz = utils.clearEmpties(ssz);

        // Добавление/обновление записи в БД
        if (ssz.workers) {
            await addWorkersToDB(ssz.workers, ssz.areaSsz.id, ssz.idBranch);
        }

        await taskToDB(ssz);
        log(`ССЗ ${ssz.id} успешно обработан`);
    } catch (error) {
        log(`Критическая ошибка при чтении XML ${path}: ${error.message}`);
        throw error; // Перебрасываем ошибку для обработки выше
    }
}

async function taskToDB(task) {
    let query = {
        id: task.id
    }

    let oldTask = await db.getTask(query);

    if (oldTask) {
        // Проверяем, нужно ли обновлять задание
        if (oldTask.updateDateNumbers && oldTask.updateDateNumbers >= task.updateDateNumbers) {
            log(`ССЗ ${task.id} уже существует с более новой или равной датой обновления, пропускаем`);
            return;
        }

        oldTask.number = task.number;
        oldTask.date = task.date;
        oldTask.idBranch = task.idBranch;
        oldTask.areaSsz = task.areaSsz;
        oldTask.tasks = task.tasks;
        oldTask.workers = task.workers;
        oldTask.updateDate = task.updateDate;
        oldTask.updateDateNumbers = task.updateDateNumbers;

        const result = await oldTask.save();
        log(`ССЗ ${task.id} обновлен в базе данных`);
    } else {
        const result = await db.createTask(task);
        log(`ССЗ ${task.id} добавлен в базу данных`);
    }
}

async function addWorkersToDB(workers, idArea, idBranch) {
    for (let worker of workers) {
        const query = {
            id: worker.id
        }

        let result = await db.getWorker(query);

        if (result) {
            //console.log("Worker exist")
            //console.log(result)
        } else {
            log(`Создание нового работника: ${worker.name} (${worker.id})`);
            worker.areas = [idArea];
            worker.branch = idBranch;
            await db.createWorker(worker);
        }
    }
}

async function removeTaskFromDB(id) {
    log(`Удаление ССЗ с ID: ${id}`);
    let query = {
        id: id
    }

    let result = await db.removeTask(query);
    console.log(result);
}

const log = (message) => {
    console.log(`[${new Date().toISOString()}] ${message}`);
};

module.exports = {
    importSSZWithLastUpdate
}