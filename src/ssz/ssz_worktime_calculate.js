const { DateTime } = require("luxon");
const db = require("../db");
const convert = require("xml-js");
const utils = require("../utils");
const ssz_requests = require("./ssz_requests");

// Берем все ССЗ за день и считаем время работы

async function getAllTaskForDay(today, date) {
    let options = {
        zone: "Asia/Yekaterinburg"
    };

    let timeNowUnix;

    if (today === true) {
        timeNowUnix = Math.floor(Date.now() / 1000);
    } else if (date) {
        timeNowUnix = Math.floor(date / 1000);
    }

    let dateNowStr = DateTime.fromSeconds(timeNowUnix, options).toFormat('dd.LL.yyyy');

    let query = {
        date: dateNowStr
    };

    let tasks = await db.getTasks(query);

    let newTasks = [];

    let workTimes = await calculateWorkTime(today, date);

    for (const task of tasks) {
        for (const jobTask of task.tasks) {
            let newTaskForXml = {
                "Ид": task.id,
                "Номер": task.number,
                "Дата": dateNowStr,
                "ИдФилиал": task.idBranch,
                "ИдУчастка": task.areaSsz.id,
                "ЗаказНаПроизводство": jobTask.mainID,
                "ИдРабота": jobTask.job.id,
                "ФакиКоличество": "0"
            };

            if (task.idBranch === "000000024") {
                newTaskForXml["ИдФилиал"] = "000000023";
            }

            let jobAllTimeStr = "0";
            let jobAllTimeNumber = 0; // общее время работы

            let workers = jobTask.workers;
            let newWorkers = [];

            for (const obj of workTimes) {
                if ((obj.mainID === jobTask.mainID) && (obj.task === jobTask.job.id)) {

                    if (obj.taskID) {
                        if (obj.taskID !== task.id) {
                            continue;
                        }
                    }

                    let time = obj.workTime / 3600; // превращаем секунды в часы
                    jobAllTimeNumber += time;

                    for (const w of workers) {
                        if (w.id === obj.workerID) {
                            let timeWorkerNumber = (Math.round(time * 100) / 100);
                            let timeWorkerStr = timeWorkerNumber.toFixed(2);
                            let jobWorkerTime = timeWorkerStr.split(".").join(",");

                            let newWorker = {
                                "ИдСотрудника": w.id,
                                "Количество": jobWorkerTime,
                            };

                            newWorkers.push(newWorker);
                            break;
                        }
                    }
                }
            }

            // перевод из числового формата в тот который принимает 1с

            let timeNumb = (Math.round(jobAllTimeNumber * 100) / 100);
            let timeStr = timeNumb.toFixed(2);
            jobAllTimeStr = timeStr.split(".").join(",");

            if (jobAllTimeStr === "0") {
                continue;
            }

            newTaskForXml["ФакиКоличество"] = jobAllTimeStr;

            for (const w of workers) {
                let founded = false;

                for (const nw of newWorkers) {
                    if (w.id === nw["ИдСотрудника"]) {
                        founded = true;
                    }
                }

                if (founded === false) {
                    let newWorker = {
                        "ИдСотрудника": w.id,
                        "Количество": 0,
                    };

                    newWorkers.push(newWorker);
                }
            }

            newTaskForXml["Исполнители"] = {
                "Сотрудник": newWorkers
            };

            newTasks.push(newTaskForXml);
        }
    }

    let xmlObj = {
        "КоммерческаяИнформация": {
            "Задания": {
                "Задание": newTasks
            }
        }
    };

    // Создаем XML отчет для 1с по фактическим часам работ ССЗ

    let optionsXml = { compact: true, ignoreComment: true, spaces: 4 };
    let result = convert.js2xml(xmlObj, optionsXml); // to convert javascript object to xml text

    let dateFullStr = utils.dateFromUnixToFullStr(timeNowUnix);

    //console.log(result);

    return { result, dateNowStr, dateFullStr };
}

async function calculateWorkTime(today, date) {
    let items = await ssz_requests.getTaskItems(today, date); // fix

    let options = {
        zone: "Asia/Yekaterinburg"
    };

    let startOfDay;
    let endOfDay;

    if (today === true) {
        let now = DateTime.now().setZone(options.zone);
        startOfDay = now.startOf('day');
        endOfDay = now.set({ hour: 22, minute: 0, second: 0 });
    } else if (date) {
        let dt = DateTime.fromJSDate(date, options);
        startOfDay = dt.startOf('day');
        endOfDay = dt.set({ hour: 22, minute: 0, second: 0 });
    }

    let timestampStart = Math.floor(startOfDay.toUnixInteger());
    let timestampEnd = Math.floor(endOfDay.toUnixInteger());

    let taskTimesObjs = [];

    for (const item of items) { // get today history with task
        for (const comp of item.components) {
            for (const hist of comp.newHistory) {
                if (hist.type === "task") {
                    if (hist.createdAt > timestampStart) {
                        if (hist.createdAt >= timestampEnd) {
                            continue;
                        }

                        let arr = calculateTimeForHistory(hist, item.mainID, startOfDay);

                        taskTimesObjs = taskTimesObjs.concat(arr);
                    }
                }
            }
        }
    }

    return taskTimesObjs;
}

function calculateTimeForHistory(history, mainID, date) {
    let itemMainID = mainID;

    let task = history.task;
    let taskID = history.taskID;
    let historyMainID = history.histMainID;

    let resultObjs = [];

    if (historyMainID) {
        itemMainID = historyMainID;
    }

    for (const worker of history.workers) {
        let time = 0;
        let lastTime;
        let lastStatus;

        for (const status of worker.statuses) {
            if (lastTime) {
                if (status.status === "pause") {
                    if (lastStatus === "pause") {
                        lastTime = status.createdAt;
                        continue;
                    } else {
                        let delta = calculateDelta(lastTime, status.createdAt, date);

                        time += delta;
                        lastStatus = status.status;
                        lastTime = status.createdAt;
                        continue;
                    }
                }

                if (status.status !== "finish") {
                    if (lastStatus === "pause") {
                        lastTime = status.createdAt;
                        lastStatus = status.status;
                        continue;
                    } else {
                        continue;
                    }
                } else {
                    if (lastStatus !== "pause") {
                        let delta = calculateDelta(lastTime, status.createdAt, date);
                        time += delta;
                    }

                    let taskTimeObj = {
                        mainID: itemMainID,
                        task: task,
                        workTime: time,
                        workerID: worker.id
                    };

                    if (taskID) {
                        taskTimeObj.taskID = taskID;
                    }

                    resultObjs.push(taskTimeObj);
                    break;
                }

            } else {
                if (status.status === "finish") {
                    continue;
                }

                lastTime = status.createdAt;
                lastStatus = status.status;
                time = 0;
            }
        }
    }

    if (resultObjs.length > 0) {
        return resultObjs;
    }

    let nullObj = [{
        mainID: itemMainID,
        task: task,
        workTime: 0
    }];

    return nullObj;
}

function calculateDelta(from, to, date) {
    // date здесь является объектом DateTime с временной зоной Екатеринбурга

    let lanchStart = date.set({ hour: 12, minute: 0, second: 0 });
    let lanchStop = date.set({ hour: 12, minute: 48, second: 0 });

    let lanchStartUnix = Math.floor(lanchStart.toUnixInteger());
    let lanchStopUnix = Math.floor(lanchStop.toUnixInteger());

    let newFrom = from;
    let newTo = to;

    if ((from < lanchStopUnix) && (from > lanchStartUnix)) {
        newFrom = lanchStopUnix;
    }

    if ((to > lanchStartUnix) && (to < lanchStopUnix)) {
        newTo = lanchStartUnix;
    }

    if (newFrom > newTo) {
        return 0;
    }

    let delta = newTo - newFrom;

    if ((newFrom <= lanchStartUnix) && (newTo >= lanchStopUnix)) {
        delta = delta - (lanchStopUnix - lanchStartUnix); // вычитаем время обеда
    }

    if (delta < 0) {
        return 0;
    }

    return delta;
}

module.exports = {
    getAllTaskForDay
};