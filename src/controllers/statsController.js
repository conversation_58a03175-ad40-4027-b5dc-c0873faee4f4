const statsService = require("../services/stats/stats.service");
const redisClient = require("../utils/redis");
const { credentials } = require("../../config");
const { outAgeStatsService } = require("../services");

/**
 * Контроллер для работы с запросами статистики
 */
const statsController = {
  /**
   * Получает общую статистику по филиалам и участкам
   * @param {Request} req - Express запрос
   * @param {Response} res - Express ответ
   */
  async getStats(req, res) {
    try {
      const branchIds = req.body.branches;
      const areasIds = req.body.areas;

      if (!branchIds || !Array.isArray(branchIds)) {
        return res.status(400).send("Bad request: branches parameter is required");
      }

      const stats = await statsService.getGeneralStats(branchIds, areasIds);
      res.send(stats);
    } catch (error) {
      console.error("Error getting stats:", error);
      res.status(500).send({ error: "Internal server error" });
    }
  },

  /**
   * Получает суммарное количество рабочих часов по месяцам
   * @param {Request} req - Express запрос
   * @param {Response} res - Express ответ
   */
  async getAverageTimeRepair(req, res) {
    try {
      const { year, branch } = req.body;
      
      if (!year || isNaN(year)) {
        return res.status(400).send({ error: "Invalid or missing year parameter" });
      }

      // Попытка использования кэша
      const cacheKey = redisClient.generateCacheKey('average_repair_time', { year, branch });

      const result = await statsService.getAverageTimeRepair(parseInt(year), branch);
      
      // Сохранение в кэш на день
      const ttl = credentials.redis?.caching?.averageTimeRepair || 86400;
      await redisClient.set(cacheKey, JSON.stringify(result), ttl);
      
      res.send(result);
    } catch (error) {
      console.error('Error fetching average repair time statistics:', error);
      res.status(500).send({ error: "Internal server error." });
    }
  },

  /**
   * Получает статистику по простоям
   * @param {Request} req - Express запрос
   * @param {Response} res - Express ответ
   */
  async getOutAgeStats(req, res) {
    try {
      const { mainID, full, branch, archive } = req.body;

      // Попытка использования кэша
      // const cacheKey = redisClient.generateCacheKey('out_age_stats', { mainID, full, branch, archive });
      // const cachedResult = await redisClient.get(cacheKey);
      
      // if (cachedResult) {
      //   console.log('Returning out age stats from cache');
      //   return res.send(cachedResult);
      // }

      const result = await statsService.getOutAgeStats({ 
        mainID, 
        full, 
        branch, 
        archive 
      });
      
      // Сохранение в кэш
      // const ttl = credentials.redis?.caching?.outAgeStats || 1800; // 30 минут
      // await redisClient.set(cacheKey, result, ttl);
      
      res.send(result);
    } catch (error) {
      console.error("Error fetching out age stats:", error);
      res.status(500).send({ error: "Internal server error." });
    }
  },

  /**
   * Получает статистику времени выполнения задач ССЗ
   * @param {Request} req - Express запрос
   * @param {Response} res - Express ответ
   */
  async getSSZTimeStats(req, res) {
    try {
      const areaIds = req.body.areaIds;

      if (!areaIds || !Array.isArray(areaIds)) {
        return res.status(400).send({ error: "Invalid or missing area IDs" });
      }

      // Попытка использования кэша
      // const cacheKey = redisClient.generateCacheKey('ssz_time_stats', { areaIds });
      // const cachedResult = await redisClient.get(cacheKey);
      
      // if (cachedResult) {
      //   console.log('Returning SSZ time stats from cache');
      //   return res.send(cachedResult);
      // }

      const result = await statsService.getSSZTimeStats(areaIds);
      
      // Сохранение в кэш
      // const ttl = credentials.redis?.caching?.sszTimeStats || 3600; // 1 час
      // await redisClient.set(cacheKey, result, ttl);
      
      res.send(result);
    } catch (error) {
      console.error("Error fetching SSZ time stats:", error);
      res.status(500).send({ error: "Internal server error." });
    }
  },

  /**
   * Получает статистику по тегам
   * @param {Request} req - Express запрос
   * @param {Response} res - Express ответ
   */
  async getTagsStats(req, res) {
    try {
      const { branch, inWork, selectedTag } = req.body;
      
      // Попытка использования кэша
      // const cacheKey = redisClient.generateCacheKey('tags_stats', { branch, inWork, selectedTag });
      // const cachedResult = await redisClient.get(cacheKey);
      
      // if (cachedResult) {
      //   console.log('Returning tags stats from cache');
      //   return res.send(cachedResult);
      // }

      const result = await statsService.getTagsStats({ 
        branch, 
        inWork, 
        selectedTag 
      });
      
      // Сохранение в кэш
      // const ttl = credentials.redis?.caching?.tagsStats || 3600; // 1 час
      // await redisClient.set(cacheKey, result, ttl);
      
      res.send(result);
    } catch (error) {
      console.error('Error fetching tags statistics:', error);
      res.status(500).send({ error: "Internal server error." });
    }
  },

  /**
   * Получает статистику среднего времени простоя по месяцам
   * @param {Request} req - Express запрос
   * @param {Response} res - Express ответ
   */
  async getAverageOutageTime(req, res) {
    try {
      const { year, branch } = req.body;
      
      if (!year || isNaN(year)) {
        return res.status(400).send({ error: "Invalid or missing year parameter" });
      }

      // Попытка использования кэша
      const cacheKey = redisClient.generateCacheKey('average_outage_time', { year, branch });

      const result = await outAgeStatsService.getAverageOutageTime(parseInt(year), branch);
      
      // Сохранение в кэш на день
      const ttl = credentials.redis?.caching?.averageOutageTime || 86400;
      await redisClient.set(cacheKey, JSON.stringify(result), ttl);
      
      res.send(result);
    } catch (error) {
      console.error('Error fetching average outage time statistics:', error);
      res.status(500).send({ error: "Internal server error." });
    }
  },

  /**
   * Получает статистику по времени выполнения работ за год
   * @param {Request} req - Express запрос
   * @param {Response} res - Express ответ
   */
  async getTaskStatisticsByYear(req, res) {
    try {
      const { year, taskId, power, turnovers } = req.body;
      
      if (!year || isNaN(year)) {
        return res.status(400).send({ error: "Invalid or missing year parameter" });
      }
      
      if (!taskId) {
        return res.status(400).send({ error: "Task ID is required" });
      }

      // Попытка использования кэша
      const cacheKey = redisClient.generateCacheKey('task_statistics_by_year', { year, taskId, power, turnovers });

      const result = await statsService.getTaskStatisticsByYear({
        year: parseInt(year),
        taskId,
        power,
        turnovers
      });
      
      // Сохранение в кэш
      const ttl = credentials.redis?.caching?.taskStatisticsByYear || 3600; // 1 час
      await redisClient.set(cacheKey, JSON.stringify(result), ttl);
      
      res.send(result);
    } catch (error) {
      console.error('Error fetching task statistics by year:', error);
      res.status(500).send({ error: "Internal server error." });
    }
  }
};

module.exports = statsController;
