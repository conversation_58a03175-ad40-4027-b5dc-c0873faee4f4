const auth = require('../handlers/auth');
const RouteFactory = require('../utils/routeFactory');

module.exports = function(app, prefix) {
  const route = new RouteFactory(app, prefix);
  
  // Маршруты авторизации (без проверки авторизации, только проверка ключа приложения)
  route.post('auth', null, auth.auth);
  route.options('auth', null, auth.auth);
  
  // Маршрут проверки состояния API
  route.get('online', null, (req, res) => {
    res.status(200).send('OK');
  });
};
