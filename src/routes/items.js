const items = require('../handlers/items');
const RouteFactory = require('../utils/routeFactory');

module.exports = function(app, prefix) {
  const route = new RouteFactory(app, prefix);
  
  // Маршруты для авторизованных пользователей
  route.post('items/changeAssembled', 'user', items.changeAssembled);
  route.post('items/edit', 'user', items.edit);
  route.post('items/update', 'user', items.updateItem);
  route.post('items/addNewHistory', 'user', items.addNewHistory);
  route.post('items/changeStatusHistory', 'user', items.changeStatusHistory);
  route.post('items/changeStatusWorkers', 'user', items.changeStatusWorkers);
  route.post('items/transferToArea', 'user', items.transferToArea);
  route.post('items/changeAccessArea', 'user', items.changeAccessArea);
  route.post('items/get', 'user', items.get);
  route.post('items/getReports', 'user', items.getReports);
  route.post('items/mergeItems', 'user', items.mergeItems);
  
  // Маршруты для администраторов
  route.post('items/lastSync', 'admin', items.getLastSyncDate);
};
