const tasks = require('../handlers/tasks');
const userTasks = require('../handlers/userTasks');
const RouteFactory = require('../utils/routeFactory');

module.exports = function(app, prefix) {
  const route = new RouteFactory(app, prefix);
  
  // Маршруты задач для пользователей
  route.post('user_tasks/get', 'user', userTasks.get);
  route.post('user_tasks/getCurrent', 'user', userTasks.getCurrent);
  route.post('user_tasks/create', 'user', userTasks.create);
  route.post('user_tasks/updateStatus', 'user', userTasks.updateStatus);
  
  // Маршруты для ССЗ
  route.post('tasks/get', 'user', tasks.get);
  route.post('tasks/getInfoTasksSSZ', 'user', tasks.getInfoTasksSSZ);
  route.post('tasks/search', 'user', tasks.searchTasksByName);
};
