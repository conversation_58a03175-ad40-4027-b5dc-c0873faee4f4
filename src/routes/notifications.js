const notifications = require('../handlers/notifications');
const pushes = require('../handlers/pushes');
const RouteFactory = require('../utils/routeFactory');

module.exports = function(app, prefix) {
  const route = new RouteFactory(app, prefix);
  
  // Маршруты для уведомлений
  route.post('notifications/get', 'user', notifications.get);
  route.post('notifications/getNew', 'user', notifications.getNew);
  route.post('pushes/send', 'user', pushes.send);
};
