const users = require('../handlers/users');
const RouteFactory = require('../utils/routeFactory');

module.exports = function(app, prefix) {
  const route = new RouteFactory(app, prefix);
  
  // Маршруты доступные для авторизованных пользователей
  route.post('users/getOne', 'user', users.getOne);
  route.post('users/get', 'user', users.get);
  route.post('users/subscribeChange', 'user', users.subscribeChange);
  route.post('users/subscribeItemChange', 'user', users.subscribeItemChange);
  route.post('users/subscribeOutageChange', 'user', users.subscribeOutageChange);
  route.post('users/subscribeSSZChange', 'user', users.subscribeSSZChange);
  route.post('users/subscribeZPRChange', 'user', users.subscribeZPRChange);
  
  // Маршруты доступные только для администраторов
  route.post('users/create', 'admin', users.create);
  route.post('users/edit', 'admin', users.edit);
};
