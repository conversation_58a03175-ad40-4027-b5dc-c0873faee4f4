const fs = require('fs');
const path = require('path');

/**
 * Регистрация всех маршрутов из директории routes
 * @param {Express.Application} app - Express приложение
 * @param {String} prefix - Префикс API
 */
function registerRoutes(app, prefix = '/api/v3/') {
  // Импортируем все JS файлы из директории routes, кроме index.js
  const routeFiles = fs.readdirSync(__dirname)
    .filter(file => file !== 'index.js' && file.endsWith('.js'));
  
  // Регистрируем каждый файл маршрутов
  for (const file of routeFiles) {
    const routeName = file.replace('.js', '');
    try {
      const routeModule = require(path.join(__dirname, file));
      routeModule(app, prefix);
      console.log(`Routes from ${routeName} registered successfully`);
    } catch (error) {
      console.error(`Error registering routes from ${routeName}:`, error);
    }
  }
}

module.exports = registerRoutes;
