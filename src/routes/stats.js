const statsController = require('../controllers/statsController');
const { cacheMiddleware } = require('../middleware/cache');
const RouteFactory = require('../utils/routeFactory');

module.exports = function(app, prefix) {
  const route = new RouteFactory(app, prefix);
  
  // Маршруты для получения статистики
  route.post('items/stats', 'user', statsController.getStats);
  
  // Маршруты статистики с кешированием, доступны для администраторов, менеджеров и директоров
  // Вместо прямого добавления middleware в RouteFactory, добавим обработку массивов middleware
  route.post('stats/getSSZTimeStats', 'adminManagerDirector', [
    cacheMiddleware('ssz_time_stats', 3600),
    statsController.getSSZTimeStats
  ]);
  
  route.post('stats/getOutAgeStats', 'adminManagerDirector', [
    cacheMiddleware('out_age_stats', 1800),
    statsController.getOutAgeStats
  ]);

  route.post('stats/getTagsStats', 'adminManagerDirector', [
    cacheMiddleware('tags_stats', 3600),
    statsController.getTagsStats
  ]);

  route.post('stats/getAverageTimeRepair', 'adminManagerDirector', [
    cacheMiddleware('average_repair_time', 86400),
    statsController.getAverageTimeRepair
  ]);

  route.post('stats/getAverageOutageTime', 'adminManagerDirector', [
    cacheMiddleware('average_outage_time', 86400),
    statsController.getAverageOutageTime
  ]);

  route.post('stats/getTaskStatisticsByYear', 'adminManagerDirector', [
    cacheMiddleware('task_statistics_by_year', 86400),
    statsController.getTaskStatisticsByYear
  ]);
};
