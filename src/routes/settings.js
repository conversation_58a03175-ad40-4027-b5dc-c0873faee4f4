const areas = require('../handlers/areas');
const jobs = require('../handlers/jobs');
const branches = require('../handlers/branches');
const RouteFactory = require('../utils/routeFactory');

module.exports = function(app, prefix) {
  const route = new RouteFactory(app, prefix);
  
  // Маршруты для получения настроек (доступны для авторизованных пользователей)
  route.post('areas/get', 'user', areas.get);
  route.post('jobs/get', 'user', jobs.get);
  route.post('branches/get', 'user', branches.get);
  
  // Маршруты для изменения настроек (только для администраторов)
  route.post('areas/add', 'admin', areas.add);
  route.post('areas/edit', 'admin', areas.edit);
  route.post('jobs/add', 'admin', jobs.add);
  route.post('jobs/edit', 'admin', jobs.edit);
  route.post('branches/add', 'admin', branches.add);
  route.post('branches/edit', 'admin', branches.edit);
};
