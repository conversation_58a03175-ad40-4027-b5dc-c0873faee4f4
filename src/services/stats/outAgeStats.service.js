const db = require("../../db");
const { DateTime } = require("luxon");

/**
 * Сервис для работы со статистикой
 */
class OutAgeStatsService {

/**
   * Получает статистику среднего времени простоя по месяцам, типам простоя и филиалам
   * @param {Number} year - Год для анализа
   * @param {Number} branchID - ID филиала (опционально)
   * @returns {Object} Статистика среднего времени простоя
   */
async getAverageOutageTime(year, branchID) {
    if (!year || isNaN(year)) {
      throw new Error("Invalid or missing year");
    }

    // Определяем временные рамки запрашиваемого года
    const startOfYear = new Date(Date.UTC(year, 0, 1)).getTime() / 1000;
    const endOfYear = new Date(Date.UTC(year + 1, 0, 1)).getTime() / 1000;
    
    // Определяем временные рамки предыдущего года для сравнения
    const startOfPrevYear = new Date(Date.UTC(year - 1, 0, 1)).getTime() / 1000;
    const endOfPrevYear = new Date(Date.UTC(year, 0, 1)).getTime() / 1000;

    // Базовый запрос для получения статистики
    let query = {};

    // Формируем проекцию - получаем ID, филиал и все таймлайны
    // Не используем $elemMatch, чтобы получить все таймлайны
    let projection = {
      mainID: 1, 
      branch: 1, 
      timelines: 1
    };
    
    // Добавляем фильтр по филиалу, если он указан
    if (branchID !== undefined && branchID !== null) {
      query.branch = branchID;
    }

    // Получаем все записи статистики
    const allStats = await db.getOutAgeStats(query, projection);
    
    // Выводим инфо для отладки
    console.log(`Total stats records fetched: ${allStats.length}`);
    
    // Отфильтруем записи для текущего и предыдущего года на основе таймлайнов
    const currentYearStats = [];
    const prevYearStats = [];

    // Добавим проверку для отладки структуры данных
    if (allStats.length > 0) {
      console.log('Пример структуры данных из первой записи:', JSON.stringify(allStats[0], null, 2));
    }

    for (const item of allStats) {
      // Проверка наличия таймлайнов
      if (!item.timelines || item.timelines.length === 0) {
        continue;
      }

      // Проверим, где находятся поля mainID и branch в структуре документа
      console.log(`Проверка полей для записи: mainID=${item.mainID}, _id=${item._id}`);
      
      // Найдем main таймлайн
      const mainTimeline = item.timelines.find(t => t.main === true);
      if (!mainTimeline || !mainTimeline.timeline || !Array.isArray(mainTimeline.timeline)) {
        continue;
      }

      // Обрабатываем только интервалы простоя из таймлайна
      const outageIntervals = mainTimeline.timeline.filter(interval => 
        interval.type === 'outage' && interval.startDate && interval.endDate
      );
      
      if (outageIntervals.length === 0) {
        continue; // Пропускаем записи без интервалов простоя
      }

      // Определяем, к какому году относится запись, проверяя даты начала интервалов простоя
      let hasCurrentYearIntervals = false;
      let hasPrevYearIntervals = false;
      
      for (const interval of outageIntervals) {
        // Проверяем интервалы для текущего года
        if (interval.startDate >= startOfYear && interval.startDate < endOfYear) {
          hasCurrentYearIntervals = true;
        }
        // Проверяем интервалы для предыдущего года
        else if (interval.startDate >= startOfPrevYear && interval.startDate < endOfPrevYear) {
          hasPrevYearIntervals = true;
        }
      }

      // Если у записи есть интервалы относящиеся к текущему году, добавляем в соответствующий массив
      if (hasCurrentYearIntervals) {
        // Создаем копию записи с отфильтрованными интервалами только для текущего года
        // Явно извлекаем mainID и branch из документа
        const mainID = item.mainID || '';
        const branch = item.branch || 0;
        
        const currentYearItem = {
          _id: item._id,  // Сохраняем MongoDB ID
          mainID, // Явно сохраняем mainID
          branch, // Явно сохраняем branch
          // Копируем остальные поля из исходного документа
          ...item,
          // Фильтруем main таймлайн для текущего года
          mainTimeline: {
            ...mainTimeline,
            timeline: outageIntervals.filter(interval => 
              interval.startDate >= startOfYear && 
              interval.startDate < endOfYear
            )
          }
        };
        // Добавляем проверку созданного объекта
        console.log(`Создан объект с mainID=${currentYearItem.mainID}, branch=${currentYearItem.branch}`);
        
        currentYearStats.push(currentYearItem);
      }
      
      // Аналогично для предыдущего года
      if (hasPrevYearIntervals) {
        prevYearStats.push({
          mainID: item.mainID,
          outAgeHours: this.calculateOutageHoursForTimelineInYear(mainTimeline, startOfPrevYear, endOfPrevYear)
        });
      }
    }
    
    // Выводим инфо для отладки
    console.log(`Current year stats records: ${currentYearStats.length}`);
    console.log(`Previous year stats records: ${prevYearStats.length}`);
    
    // Проверка структуры данных для текущего года
    if (currentYearStats.length > 0) {
      console.log('Пример записи из currentYearStats:', 
        JSON.stringify({
          mainID: currentYearStats[0].mainID,
          branch: currentYearStats[0].branch,
          hasMainTimeline: !!currentYearStats[0].mainTimeline
        }, null, 2)
      );
    }
    
    // Структуры для хранения агрегированных данных
    const monthlyOutageData = {}; // По месяцам: {month: {totalHours, uniqueItems}}
    const typeOutageData = {};    // По типам простоев: {type: {totalHours, uniqueItems}}
    const monthTypeOutageData = {}; // По месяцам и типам: {month: {type: {totalHours, uniqueItems}}}
    const monthlyBranchData = {}; // По месяцам и филиалам: {month: {branch: {totalHours, uniqueItems}}}
    
    // Вспомогательные структуры для отслеживания позиций и их месяцев/типов
    const itemMonthTypes = new Map(); // Для хранения типов простоя по позициям и месяцам
    const itemMonthBranches = new Map(); // Для хранения филиалов по позициям и месяцам
    
    // Общие суммы для сравнения с прошлым годом
    let totalOutageTime = 0;
    let uniqueItemsWithOutage = new Set(); // Уникальные позиции с простоями
    let prevYearTotalOutageTime = 0;
    let prevYearUniqueItems = new Set(); // Уникальные позиции с простоями прошлого года
    
    // Обрабатываем статистику текущего года
    for (const item of currentYearStats) {
      // Логи для отладки обработки каждой записи
      console.log(`Обрабатываем запись с mainID: ${item.mainID}, branch: ${item.branch}`);
      
      // Проверяем наличие необходимых полей
      if (!item.mainID) {
        console.log(`Предупреждение: запись не имеет mainID, пропускаем`, item);
        continue;
      }
      
      // Добавляем ID в список уникальных позиций с простоями
      uniqueItemsWithOutage.add(item.mainID);
      
      // Проверка, что mainID добавлен
      console.log(`UniqueItemsWithOutage размер после добавления: ${uniqueItemsWithOutage.size}`);
      
      // Для данной позиции создадим объекты для отслеживания месяцев, типов и филиалов
      const itemKey = item.mainID;
      if (!itemMonthTypes.has(itemKey)) {
        itemMonthTypes.set(itemKey, new Map()); // {month: Set of types}
      }
      
      if (!itemMonthBranches.has(itemKey)) {
        itemMonthBranches.set(itemKey, new Map()); // {month: Set of branches}
      }
      
      // Проверяем, что таймлайн существует
      if (!item.mainTimeline || !item.mainTimeline.timeline) {
        console.log(`Предупреждение: запись ${item.mainID} не имеет таймлайна`);
        continue;
      }
      
      // Получаем интервалы простоя из таймлайна
      const outageIntervals = item.mainTimeline.timeline;
      console.log(`Количество интервалов для ${item.mainID}: ${outageIntervals.length}`);
      
      // Для каждого интервала простоя обновляем статистику
      for (const interval of outageIntervals) {
        // Пропускаем интервалы, не имеющие имен (типов простоя)
        if (!interval.names || interval.names.length === 0) {
          console.log(`Предупреждение: интервал без имен для ${item.mainID}`);
          continue;
        }
        
        // Время простоя в часах
        const outageHours = interval.duration / 3600;
        totalOutageTime += outageHours;
        
        // Определяем месяц из времени начала интервала
        const date = new Date(interval.startDate * 1000);
        const month = date.getMonth() + 1; // 1-12
        
        console.log(`Обработка интервала для ${item.mainID}: месяц=${month}, длительность=${outageHours} часов, имена=${interval.names}`);
        
        // Получаем филиал позиции
        const branch = item.branch || 0;
        
        // Инициализируем структуры для месяца, если их еще нет
        if (!monthlyOutageData[month]) {
          console.log(`Создаем структуру для месяца ${month}`);
          monthlyOutageData[month] = { totalHours: 0, uniqueItems: new Set() };
        }
        
        if (!monthTypeOutageData[month]) {
          monthTypeOutageData[month] = {};
        }
        
        if (!monthlyBranchData[month]) {
          monthlyBranchData[month] = {};
        }
        
        // Инициализируем структуры для филиала, если их еще нет
        if (!monthlyBranchData[month][branch]) {
          monthlyBranchData[month][branch] = { totalHours: 0, uniqueItems: new Set() };
        }
        
        // Добавляем позицию в множество уникальных позиций для месяца
        console.log(`Добавляем позицию ${item.mainID} в месяц ${month}`);
        monthlyOutageData[month].uniqueItems.add(item.mainID);
        
        // Проверка, что позиция добавлена
        console.log(`После добавления в месяц ${month}, количество позиций: ${monthlyOutageData[month].uniqueItems.size}`);
        
        monthlyOutageData[month].totalHours += outageHours;
        
        // Добавляем позицию в множество уникальных позиций для филиала
        monthlyBranchData[month][branch].uniqueItems.add(item.mainID);
        monthlyBranchData[month][branch].totalHours += outageHours;
        
        // Если у данной позиции еще нет записи для этого месяца, создадим её
        if (!itemMonthBranches.get(itemKey).has(month)) {
          itemMonthBranches.get(itemKey).set(month, new Set());
        }
        itemMonthBranches.get(itemKey).get(month).add(branch);
        
        // Обрабатываем каждый тип простоя из интервала
        for (const outageType of interval.names) {
          // Инициализируем структуры для типа простоя, если их еще нет
          if (!monthTypeOutageData[month][outageType]) {
            monthTypeOutageData[month][outageType] = { totalHours: 0, uniqueItems: new Set() };
          }
          
          if (!typeOutageData[outageType]) {
            typeOutageData[outageType] = { totalHours: 0, uniqueItems: new Set() };
          }
          
          // Добавляем позицию в множество уникальных позиций для типа простоя в месяце
          monthTypeOutageData[month][outageType].uniqueItems.add(item.mainID);
          monthTypeOutageData[month][outageType].totalHours += outageHours;
          
          // Проверяем успешность добавления
          console.log(`Для типа '${outageType}' в месяце ${month}: ${monthTypeOutageData[month][outageType].uniqueItems.size} позиций`);
          
          // Добавляем позицию в множество уникальных позиций для типа простоя в году
          typeOutageData[outageType].uniqueItems.add(item.mainID);
          typeOutageData[outageType].totalHours += outageHours;
          
          // Если у данной позиции еще нет записи для этого месяца, создадим её
          if (!itemMonthTypes.get(itemKey).has(month)) {
            itemMonthTypes.get(itemKey).set(month, new Set());
          }
          itemMonthTypes.get(itemKey).get(month).add(outageType);
        }
      }
    }
    
    // Выводим информацию о количестве позиций для отладки
    console.log(`Total unique positions with outages: ${uniqueItemsWithOutage.size}`);
    for (const month in monthlyOutageData) {
      console.log(`Month ${month}: ${monthlyOutageData[month].uniqueItems.size} positions`);
    }
    for (const type in typeOutageData) {
      console.log(`Type ${type}: ${typeOutageData[type].uniqueItems.size} positions`);
    }
    
    // Подсчитываем общее время простоя для предыдущего года
    for (const item of prevYearStats) {
      if (item.outAgeHours) {
        prevYearTotalOutageTime += item.outAgeHours;
        prevYearUniqueItems.add(item.mainID);
      }
    }
    
    // Вычисляем средние значения
    
    // 1. Для месяцев и типов простоя
    const monthlyAverages = {};
    
    for (let month = 1; month <= 12; month++) {
      if (!monthlyOutageData[month] || !monthlyOutageData[month].uniqueItems || monthlyOutageData[month].uniqueItems.size === 0) {
        monthlyAverages[month] = {
          average: 0,
          byType: {},
          count: 0
        };
        continue;
      }
      
      const data = monthlyOutageData[month];
      const uniqueItemsCount = data.uniqueItems.size;
      
      console.log(`Месяц ${month}: уникальных позиций=${uniqueItemsCount}, общее время=${data.totalHours}`);
      
      // Среднее время простоя за месяц на оборудование
      const average = uniqueItemsCount > 0 ? data.totalHours / uniqueItemsCount : 0;
      
      // Средние по типам простоев для месяца
      const typeAverages = {};
      
      if (monthTypeOutageData[month]) {
        for (const type in monthTypeOutageData[month]) {
          const typeData = monthTypeOutageData[month][type];
          const typeUniqueCount = typeData.uniqueItems.size;
          
          typeAverages[type] = {
            average: typeUniqueCount > 0 ? typeData.totalHours / typeUniqueCount : 0,
            count: typeUniqueCount // Количество уникальных позиций с этим типом простоя
          };
        }
      }
      
      monthlyAverages[month] = {
        average,
        byType: typeAverages,
        count: uniqueItemsCount // Общее количество уникальных позиций с простоем в месяце
      };
    }
    
    // 2. Для филиалов по месяцам
    const monthlyBranchAverages = {};
    
    for (let month = 1; month <= 12; month++) {
      monthlyBranchAverages[month] = {};
      
      if (!monthlyBranchData[month]) {
        continue;
      }
      
      for (const branch in monthlyBranchData[month]) {
        const branchData = monthlyBranchData[month][branch];
        const branchUniqueCount = branchData.uniqueItems.size;
        
        monthlyBranchAverages[month][branch] = {
          average: branchUniqueCount > 0 ? branchData.totalHours / branchUniqueCount : 0,
          count: branchUniqueCount // Количество уникальных позиций с простоем в этом филиале за месяц
        };
      }
    }
    
    // 3. Общая статистика по типам простоя за год
    const yearlyTypeStats = [];
    
    for (const type in typeOutageData) {
      const typeData = typeOutageData[type];
      const typeUniqueCount = typeData.uniqueItems.size;
      
      yearlyTypeStats.push({
        type: type,
        totalHours: typeData.totalHours, // Общее время простоя этого типа
        averageHours: typeUniqueCount > 0 ? typeData.totalHours / typeUniqueCount : 0, // Среднее на позицию
        count: typeUniqueCount // Количество уникальных позиций с этим типом простоя
      });
    }
    
    // Сортируем типы простоев по убыванию общего времени
    yearlyTypeStats.sort((a, b) => b.totalHours - a.totalHours);
    
    // Общие средние значения
    const yearlyAverage = uniqueItemsWithOutage.size > 0 ? totalOutageTime / uniqueItemsWithOutage.size : 0;
    const prevYearAverage = prevYearUniqueItems.size > 0 ? prevYearTotalOutageTime / prevYearUniqueItems.size : 0;
    
    // Сравнение с предыдущим годом
    const difference = yearlyAverage - prevYearAverage;
    const comparisonText = prevYearUniqueItems.size > 0 ?
      `Среднее время простоя ${year} года составило (ч): ${yearlyAverage.toFixed(2)}, что на ${Math.abs(difference).toFixed(2)} ${difference >= 0 ? 'больше' : 'меньше'} чем в прошлом году.` :
      `Среднее время простоя ${year} года составило (ч): ${yearlyAverage.toFixed(2)}. Данные за прошлый год отсутствуют.`;
    
    return {
      monthlyAverages,        // Средние по месяцам с разбивкой по типам простоя
      yearlyTypeStats,        // Статистика по типам простоя за весь год
      monthlyBranchAverages,  // Средние по месяцам и филиалам
      yearlyAverage,          // Среднее за текущий год
      prevYearAverage,        // Среднее за предыдущий год
      difference,             // Разница между текущим и предыдущим годом
      comparisonText,         // Текстовое описание сравнения
      totalOutageTime,        // Общее время простоя за текущий год
      prevYearTotalOutageTime, // Общее время простоя за предыдущий год
      totalOutageItems: uniqueItemsWithOutage.size,       // Количество позиций с простоями за текущий год
      prevYearOutageItems: prevYearUniqueItems.size     // Количество позиций с простоями за предыдущий год
    };
  }

  /**
   * Рассчитывает общее время простоя в часах для таймлайна в рамках указанного года
   * @param {Object} timeline - временная шкала
   * @param {Number} startYear - начало года в Unix timestamp
   * @param {Number} endYear - конец года в Unix timestamp
   * @returns {Number} Общее время простоя в часах
   */
  calculateOutageHoursForTimelineInYear(timeline, startYear, endYear) {
    let totalOutageTime = 0;
    
    // Проверяем наличие данных таймлайна
    if (!timeline || !timeline.timeline || !Array.isArray(timeline.timeline)) {
      return 0;
    }
    
    // Обрабатываем только интервалы простоя
    for (const interval of timeline.timeline) {
      if (interval.type !== 'outage' || !interval.duration) continue;
      
      // Проверяем, что интервал относится к указанному году
      if (interval.startDate >= startYear && interval.startDate < endYear) {
        totalOutageTime += interval.duration / 3600; // Переводим в часы
      }
    }
    
    return totalOutageTime;
  }

  /**
   * Рассчитывает общее время простоя в часах для позиции в рамках указанного года
   * @param {Object} item - элемент статистики
   * @param {Number} startYear - начало года в Unix timestamp
   * @param {Number} endYear - конец года в Unix timestamp
   * @returns {Number} Общее время простоя в часах
   */
  calculateOutageHoursForYear(item, startYear, endYear) {
    let totalOutageTime = 0;
    
    // Проверяем наличие таймлайнов
    if (!item.timelines || item.timelines.length === 0) {
      return 0;
    }
    
    // Находим main таймлайн
    const mainTimeline = item.timelines.find(t => t.main === true);
    if (!mainTimeline || !mainTimeline.timeline || !Array.isArray(mainTimeline.timeline)) {
      return 0;
    }
    
    // Обрабатываем только интервалы простоя
    for (const interval of mainTimeline.timeline) {
      if (interval.type !== 'outage' || !interval.duration) continue;
      
      // Проверяем, что интервал относится к указанному году
      if (interval.startDate >= startYear && interval.startDate < endYear) {
        totalOutageTime += interval.duration / 3600; // Переводим в часы
      }
    }
    
    return totalOutageTime;
  }
}

module.exports = new OutAgeStatsService();