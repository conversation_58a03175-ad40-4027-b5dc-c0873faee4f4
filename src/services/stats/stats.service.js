const db = require("../../db");
const { DateTime } = require("luxon");

/**
 * Сервис для работы со статистикой
 */
class StatsService {

  /**
   * Получает статистику по статусам и просроченным заказам для указанных филиалов
   * @param {Array} branchIds - Массив идентификаторов филиалов
   * @param {Array} areasIds - Массив идентификаторов участков (опционально)
   * @returns {Object} Объект со статистикой
   */
  async getGeneralStats(branchIds, areasIds) {
    if (!branchIds || !Array.isArray(branchIds)) {
      throw new Error("Invalid branch IDs provided");
    }

    const overdeadline = await this.getOverDeadlineStats(branchIds);
    
    // Сбор статистики по каждому филиалу и подсчет общего количества в работе
    let inWorkCount = 0;
    let branchStats = [];

    for (let i = 0; i < branchIds.length; i++) {
      const count = await this.getBranchCount(branchIds[i]);
      const bStat = {
        id: branchIds[i],
        count: count,
      };
      branchStats.push(bStat);
      inWorkCount += count;
    }

    // Статистика по участкам
    let areaStats = [];
    if (areasIds && areasIds.length > 0) {
      areaStats = await this.getAreasCounts(branchIds, areasIds);
    }

    // Дополнительная статистика (архив, недавние отгрузки и т.д.)
    let archiveCount;
    let archiveInYearCount;
    let recentShipment;
    let outAgeHours;

    if (branchIds.length === 1) {
      archiveCount = await this.getArchiveCount(branchIds[0]);
      archiveInYearCount = await this.getArchiveInYearCount(branchIds[0]);
      recentShipment = await this.getRecentShipment(branchIds[0]);
      outAgeHours = await this.getOutAgeHours(branchIds[0]);
    } else {
      archiveCount = await this.getArchiveCount();
      archiveInYearCount = await this.getArchiveInYearCount();
      recentShipment = await this.getRecentShipment();
      outAgeHours = await this.getOutAgeHours();
    }

    // Формирование итогового объекта статистики
    return {
      inWorkCount,
      overDeadlineCount: overdeadline.overDeadlineCount,
      overDeadlineAvr: overdeadline.overDeadlineAvr,
      overDeadlineSum: overdeadline.overDeadlineSum,
      branchStats,
      areaStats,
      archiveCount,
      archiveInYearCount,
      recentShipment,
      allHours: outAgeHours.allHours,
      allOutAgeHours: outAgeHours.outAgeHours,
      countComponents: outAgeHours.countComponents,
    };
  }

  /**
   * Получает статистику рабочих часов по месяцам для указанного года
   * @param {Number} year - Год для агрегации статистики 
   * @returns {Array} Массив объектов с данными по месяцам
   */
  async getWorkHoursByMonth(year) {
    if (!year || isNaN(year)) {
      throw new Error("Invalid or missing year");
    }

    // Формируем границы года для фильтрации
    const startOfYear = `01.01.${year}`;
    const endOfYear = `31.12.${year}`;

    // Агрегационный пайплайн для MongoDB
    const pipeline = [
      {
        $unwind: "$dayStats", // Разворачиваем массив dayStats
      },
      {
        $match: {
          "dayStats.date": {
            $gte: startOfYear, // Начало года
            $lte: endOfYear, // Конец года
          },
        },
      },
      {
        $addFields: {
          month: {
            $substr: ["$dayStats.date", 3, 2], // Извлекаем месяц из строки "DD.MM.YYYY"
          },
        },
      },
      {
        $group: {
          _id: "$month", // Группируем по месяцу
          totalWorkHours: { $sum: "$dayStats.workHours" }, // Суммируем workHours
        },
      },
      {
        $sort: { _id: 1 }, // Сортируем по месяцу
      },
    ];

    const result = await db.getOutAgeStatsAggreagete(pipeline);

    // Преобразуем данные в удобный формат
    return result.map((item) => ({
      month: item._id,
      totalWorkHours: item.totalWorkHours,
    }));
  }

  /**
   * Получает статистику простоев
   * @param {Object} options - Опции запроса
   * @param {String} options.mainID - ID элемента для получения детальной статистики
   * @param {Boolean} options.full - Флаг для получения полной статистики
   * @param {Number} options.branch - ID филиала для фильтрации
   * @param {Boolean} options.archive - Флаг для фильтрации архивных элементов
   * @returns {Array|Object} Результат запроса статистики простоев
   */
  async getOutAgeStats({ mainID, full, branch, archive }) {
    if (mainID) {
      // Статистика для конкретного элемента
      const projection = full
        ? null
        : "repairNumber mainID outAgeHours timelines countWork statusHours workHours workerHours statorOutAgeHours rotorOutAgeHours bearingOutAgeHours transformerOutAgeHours";

      let query = { mainID };
      return await db.getOneOutAgeStat(query, projection) || null;
    } else {
      // Общая статистика
      const projection = full
        ? null
        : "repairNumber countComponents equipment mainID outAgeHours workHours workerHours statusHours countWork";

      let query = {};
      
      if (archive !== undefined) {
        query.archive = archive;
      }
      
      if (branch) {
        query.branch = branch;
      }

      return await db.getOutAgeStats(query, projection) || [];
    }
  }

  /**
   * Получает статистику по времени задач ССЗ по участкам
   * @param {Array} areaIds - Массив ID участков
   * @returns {Array} Статистика по времени работы
   */
  async getSSZTimeStats(areaIds) {
    if (!areaIds || !Array.isArray(areaIds)) {
      throw new Error("Invalid area IDs provided");
    }

    // Получаем статистику по указанным участкам
    const query = {
      "sszHours.area": { $elemMatch: { $in: areaIds } },
    };

    const projection = "branch sszTimes";
    const result = await db.getOutAgeStats(query, projection);

    // Обрабатываем результаты для расчета средних значений
    let newArrModels = [];

    for (let value of result) {
      let branch = value.branch;

      for (let sszTime of value.sszTimes) {
        if (areaIds.includes(sszTime.area)) {
          // Выделяем последнюю часть имени работы для группировки
          let clearNameArr = sszTime.name.split(" | ");
          let clearName = clearNameArr[clearNameArr.length - 1];

          // Ищем, существует ли уже такая работа в результатах
          let modelFind = newArrModels.find(
            (element) => element.name === clearName
          );

          let timeBranch = {
            branch: branch,
            workerTimeSum: sszTime.workerTime,
            count: 1,
          };

          if (modelFind) {
            // Если работа уже существует, обновляем статистику по филиалу
            let tb = modelFind.timeBranchs.find(
              (element) => element.branch === branch
            );

            if (tb) {
              tb.workerTimeSum += sszTime.workerTime;
              tb.count += 1;
            } else {
              modelFind.timeBranchs.push(timeBranch);
            }
          } else {
            // Если работы нет, добавляем новую
            let model = {
              name: clearName,
              timeBranchs: [timeBranch],
            };
            newArrModels.push(model);
          }
        }
      }
    }

    // Рассчитываем средние значения для каждой работы и филиала
    for (let model of newArrModels) {
      for (let timeBranch of model.timeBranchs) {
        timeBranch.workerTime = timeBranch.workerTimeSum / timeBranch.count;
      }
    }

    return newArrModels;
  }

  async getTagsStats({ branch, inWork, selectedTag }) {
    // Строим базовый запрос
    let query = {
      deleted: false,
      new: false,
    };
    
    // Добавляем фильтры
    if (branch !== undefined && branch !== null && branch !== "null") {
      query.branch = branch;
    }
    
    if (inWork !== undefined && inWork !== null && inWork !== "null") {
      query.finished = !inWork;
    }
    
    if (selectedTag !== undefined && selectedTag !== null && selectedTag !== "null") {
      query.tags = selectedTag;
    }
    
    // Проекция полей для оптимизации запроса
    const projection = "tags mainID branch finished";

    // Получаем предметы с тегами
    const items = await db.getItemsWithProjection(query, projection);
    
    // Собираем статистику по одиночным тегам
    const tagStats = {};
    const tagCombinations = {};
    let totalItemsCount = items.length;
    
    // Статистика по филиалам для выбранного тега
    const branchStats = {};
    
    // Обработка каждого предмета
    items.forEach(item => {
      if (item.tags && item.tags.length > 0) {
        // Считаем статистику по одиночным тегам
        item.tags.forEach(tag => {
          if (!tagStats[tag]) {
            tagStats[tag] = 0;
          }
          tagStats[tag]++;
        });
        
        // Если у предмета более одного тега и выбран конкретный тег, создаем уникальную комбинацию
        if (selectedTag) {
          const sortedTags = [...item.tags].sort();
          const tagKey = sortedTags.join('|');
          
          if (!tagCombinations[tagKey]) {
            tagCombinations[tagKey] = {
              tags: sortedTags,
              count: 0
            };
          }
          tagCombinations[tagKey].count++;
          
          // Собираем статистику по филиалам для выбранного тега
          const itemBranch = item.branch || 0;
          if (!branchStats[itemBranch]) {
            branchStats[itemBranch] = {
              count: 0,
              percentage: 0
            };
          }
          branchStats[itemBranch].count++;
        }
      }
    });
    
    // Преобразуем данные для ответа
    const singleTagsStats = Object.keys(tagStats).map(tag => ({
      tags: [tag],
      count: tagStats[tag],
      percentage: totalItemsCount > 0 ? Number((tagStats[tag] / totalItemsCount * 100).toFixed(2)) : 0
    }));
    
    // Преобразуем комбинации тегов для ответа
    const combinationsStats = Object.values(tagCombinations).map(combo => ({
      tags: combo.tags,
      count: combo.count,
      percentage: totalItemsCount > 0 ? Number((combo.count / totalItemsCount * 100).toFixed(2)) : 0
    }));
    
    // Рассчитываем процентное соотношение для статистики по филиалам
    if (selectedTag) {
      Object.keys(branchStats).forEach(branchId => {
        branchStats[branchId].percentage = totalItemsCount > 0 ? 
          Number((branchStats[branchId].count / totalItemsCount * 100).toFixed(2)) : 0;
      });
    }
    
    // Формируем итоговый ответ
    const result = {
      totalItems: totalItemsCount,
      stats: selectedTag ? combinationsStats : singleTagsStats
    };
    
    // Добавляем статистику по филиалам, если передан выбранный тег
    if (selectedTag) {
      result.branchStats = branchStats;
    }
    
    return result;
  }

  /**
   * Получает количество компонентов по участкам
   * @param {Array} branchIDs - Массив ID филиалов
   * @param {Array} areaIDs - Массив ID участков
   * @returns {Array} Массив с данными по количеству компонентов на каждом участке
   */
  async getAreasCounts(branchIDs, areaIDs) {
    const projection = "assembled components.currentArea components.accessArea";

    const query = {
      deleted: false,
      finished: false,
      new: false,
      branch: branchIDs,
    };

    // Добавляем условие фильтрации по участкам
    let or = {
      $or: [
        {
          "components.currentArea": { $in: areaIDs },
        },
        {
          "components.accessArea": { $elemMatch: { $in: areaIDs } },
        },
      ],
    };

    if (query["$and"]) {
      query["$and"].push(or);
    } else {
      query["$and"] = [or];
    }

    const items = await db.getItemsWithProjection(query, projection);

    // Инициализируем статистику для каждого участка
    let areaStats = areaIDs.map(id => ({
      id,
      count: 0
    }));

    // Подсчитываем количество компонентов на каждом участке
    for (let item of items) {
      if (item.components.length > 0) {
        for (let component of item.components) {
          for (let stat of areaStats) {
            // Считаем компоненты, которые находятся на участке
            if (stat.id === component.currentArea) {
              stat.count += 1;
            }

            // Считаем компоненты, которые имеют доступ к участку
            for (const accessArea of component.accessArea) {
              if (stat.id === accessArea) {
                stat.count += 1;
              }
            }
          }
          
          // Если изделие собрано, прекращаем проверку компонентов
          if (item.assembled === true) {
            break;
          }
        }
      }
    }

    return areaStats;
  }

  /**
   * Получает количество элементов в работе для филиала
   * @param {Number} branchID - ID филиала
   * @returns {Number} Количество элементов
   */
  async getBranchCount(branchID) {
    const query = {
      deleted: false,
      finished: false,
      new: false,
      branch: branchID,
    };

    return await db.getCountItems(query);
  }

  /**
   * Получает количество архивных элементов
   * @param {Number} branchID - ID филиала (опционально)
   * @returns {Number} Количество архивных элементов
   */
  async getArchiveCount(branchID) {
    let query = {
      deleted: false,
      finished: true,
    };

    if (branchID) {
      query.branch = branchID;
    }

    return await db.getCountItems(query);
  }

  /**
   * Получает количество архивных элементов за указанный год
   * @param {Number} branchID - ID филиала (опционально)
   * @param {Number} year - Год для фильтрации (по умолчанию текущий)
   * @returns {Number} Количество архивных элементов за год
   */
  async getArchiveInYearCount(branchID, year) {
    let query = {
      deleted: false,
      finished: true,
    };

    if (branchID) {
      query.branch = branchID;
    }

    if (!year) {
      year = new Date().getFullYear();
    }

    // Устанавливаем временные рамки для фильтрации
    const startDate = new Date(Date.UTC(year, 0, 1)).getTime() / 1000;
    const endDate = new Date(Date.UTC(year + 1, 0, 1)).getTime() / 1000;

    query.finishDate = {
      $gte: Math.floor(startDate),
      $lt: Math.floor(endDate),
    };

    return await db.getCountItems(query);
  }

  /**
   * Получает количество элементов с приближающейся датой отгрузки
   * @param {Number} branchID - ID филиала (опционально)
   * @param {Number} recentWeeks - Количество недель для фильтрации (по умолчанию 4)
   * @returns {Number} Количество элементов с приближающейся отгрузкой
   */
  async getRecentShipment(branchID, recentWeeks = 4) {
    const now = Math.floor(Date.now() / 1000);
    const timeAfterRecentWeek = now + 86400 * 7 * recentWeeks;

    let query = {
      deleted: false,
      finished: false,
      new: false,
      endDate: { $gt: now, $lt: timeAfterRecentWeek },
    };

    if (branchID) {
      query.branch = branchID;
    }

    return await db.getCountItems(query);
  }

  /**
   * Получает статистику по часам работы и простоя
   * @param {Number} branchID - ID филиала (опционально)
   * @returns {Object} Статистика по часам
   */
  async getOutAgeHours(branchID) {
    let query = {};

    if (branchID) {
      query.branch = branchID;
    }

    const onBoardStatResult = await db.getOnBoardStats(query);

    // Суммируем статистику
    let workHours = 0;
    let outAgeHours = 0;
    let countComponents = 0;

    for (let stat of onBoardStatResult) {
      workHours += stat.workHours || 0;
      outAgeHours += stat.outAgeHours || 0;
      countComponents += stat.countComponents || 0;
    }

    let allHours = workHours + outAgeHours;

    return { allHours, outAgeHours, countComponents };
  }

  /**
   * Получает статистику по просроченным заказам
   * @param {Array} branchIds - Массив ID филиалов
   * @returns {Object} Статистика по просроченным заказам
   */
  async getOverDeadlineStats(branchIds) {
    const projection = "endDate components.newHistory.job";

    const now = Math.floor(Date.now() / 1000);
    const correctTime = now - 86400; // Вчера

    // Запрос для получения просроченных заказов
    const queryOverDeadLine = {
      deleted: false,
      finished: false,
      new: false,
      branch: branchIds,
      "order.statusOrder": { $ne: "Дефектировка" },
      endDate: { $lt: correctTime },
      "components.newHistory.job": { $ne: 141 }, // Исключаем определённый тип работ
    };

    const itemsOverDeadLine = await db.getItemsWithProjection(
      queryOverDeadLine,
      projection
    );

    // Анализируем просроченные заказы
    let overDeadlineCount = 0;
    let overDeadlineSum = 0;

    for (let item of itemsOverDeadLine) {
      if (item.components.length > 0) {
        let component = item.components[0];
        let length = component.newHistory.length;
        
        if (length > 0) {
          let lastHistory = component.newHistory[length - 1];

          if (lastHistory.job !== 141) {
            overDeadlineCount += 1;

            // Рассчитываем, на сколько дней просрочен заказ
            const range = correctTime - item.endDate;
            const days = Math.floor(range / 86400);
            overDeadlineSum += days;
          }
        }
      }
    }

    // Рассчитываем среднее количество дней просрочки
    let overDeadlineAvr = overDeadlineCount > 0 ? overDeadlineSum / overDeadlineCount : 0;

    return { overDeadlineCount, overDeadlineSum, overDeadlineAvr };
  }

  /**
   * Получает статистику среднего времени ремонта по типам двигателей и месяцам
   * @param {Number} year - Год для анализа
   * @param {Number} branchID - ID филиала (опционально)
   * @returns {Object} Статистика среднего времени ремонта
   */
  async getAverageTimeRepair(year, branchID) {
    if (!year || isNaN(year)) {
      throw new Error("Invalid year parameter");
    }

    // Определяем временные рамки запрашиваемого года
    const startOfYear = new Date(Date.UTC(year, 0, 1)).getTime() / 1000;
    const endOfYear = new Date(Date.UTC(year + 1, 0, 1)).getTime() / 1000;
    
    // Определяем временные рамки предыдущего года для сравнения
    const startOfPrevYear = new Date(Date.UTC(year - 1, 0, 1)).getTime() / 1000;
    const endOfPrevYear = new Date(Date.UTC(year, 0, 1)).getTime() / 1000;

    // Базовый запрос для получения завершенных двигателей
    const baseQuery = {
      deleted: { $ne: true },
      new: false,
      finishDate: { $exists: true, $ne: null }
    };

    // Запрос для текущего года
    let currentYearQuery = {
      ...baseQuery,
      finishDate: { $gte: startOfYear, $lt: endOfYear }
    };
    
    // Запрос для предыдущего года (для сравнения)
    let prevYearQuery = {
      ...baseQuery,
      finishDate: { $gte: startOfPrevYear, $lt: endOfPrevYear }
    };

    // Если указан филиал, добавляем его в запросы
    if (branchID !== undefined && branchID !== null) {
      currentYearQuery.branch = branchID;
      prevYearQuery.branch = branchID;
    }
    
    // Создаем проекцию для истории, чтобы извлекать только нужные поля
    const historyProjection = {
      branch: 1,
      finishDate: 1,
      "order.name": 1,
      "components.newHistory.job": 1,
      "components.newHistory.createdAt": 1
    };

    // Получаем данные по текущему и предыдущему годам с оптимизированной проекцией
    const currentYearItems = await db.getItemsWithProjection(currentYearQuery, historyProjection);
    const prevYearItems = await db.getItemsWithProjection(prevYearQuery, historyProjection);

    // Объекты для хранения результатов
    const monthlyStats = {};      // По месяцам
    const typeStats = {};         // По типам оборудования
    const monthlyBranchStats = {}; // По месяцам и филиалам
    let totalRepairTime = 0;
    let totalRepairCount = 0;
    let prevYearTotalTime = 0;
    let prevYearRepairCount = 0;

    // Обрабатываем данные текущего года
    for (const item of currentYearItems) {
      if (!item.finishDate) continue;

      // Находим самый ранний createdAt среди компонентов
      let earliestStart = null;
      
      // Проверяем, что components существует и это массив
      if (item.components && Array.isArray(item.components)) {
        for (const component of item.components) {
          // Проверяем, что newHistory существует и это массив
          if (component.newHistory && Array.isArray(component.newHistory)) {
            for (const history of component.newHistory) {
              if (history.job === 1 && (!earliestStart || history.createdAt < earliestStart)) {
                earliestStart = history.createdAt;
              }
            }
          }
        }
      }
      
      // Если не нашли начало работ, пропускаем
      if (!earliestStart) continue;

      // Рассчитываем время ремонта в часах
      const repairTimeHours = Math.max(0, Math.round((item.finishDate - earliestStart) / 3600));
      
      // Определяем месяц завершения
      const finishDate = new Date(item.finishDate * 1000);
      const month = finishDate.getMonth() + 1; // 1-12
      
      // Определяем тип оборудования
      const equipmentType = item.order && item.order.name ? item.order.name : 'Неизвестно';
      
      // Определяем филиал
      const branch = item.branch || 0;
      
      // Добавляем в статистику по месяцам
      if (!monthlyStats[month]) {
        monthlyStats[month] = { totalTime: 0, count: 0, byType: {} };
      }
      monthlyStats[month].totalTime += repairTimeHours;
      monthlyStats[month].count++;
      
      // Добавляем в статистику по типам для каждого месяца
      if (!monthlyStats[month].byType[equipmentType]) {
        monthlyStats[month].byType[equipmentType] = { totalTime: 0, count: 0 };
      }
      monthlyStats[month].byType[equipmentType].totalTime += repairTimeHours;
      monthlyStats[month].byType[equipmentType].count++;
      
      // Добавляем в общую статистику по типам
      if (!typeStats[equipmentType]) {
        typeStats[equipmentType] = { totalTime: 0, count: 0 };
      }
      typeStats[equipmentType].totalTime += repairTimeHours;
      typeStats[equipmentType].count++;
      
      // Добавляем в статистику по месяцам и филиалам
      if (!monthlyBranchStats[month]) {
        monthlyBranchStats[month] = {};
      }
      if (!monthlyBranchStats[month][branch]) {
        monthlyBranchStats[month][branch] = { totalTime: 0, count: 0 };
      }
      monthlyBranchStats[month][branch].totalTime += repairTimeHours;
      monthlyBranchStats[month][branch].count++;
      
      // Общие суммы для текущего года
      totalRepairTime += repairTimeHours;
      totalRepairCount++;
    }
    
    // Аналогичную проверку делаем и для предыдущего года
    for (const item of prevYearItems) {
      if (!item.finishDate) continue;

      // Находим самый ранний createdAt среди компонентов
      let earliestStart = null;
      
      // Проверяем, что components существует и это массив
      if (item.components && Array.isArray(item.components)) {
        for (const component of item.components) {
          // Проверяем, что newHistory существует и это массив
          if (component.newHistory && Array.isArray(component.newHistory)) {
            for (const history of component.newHistory) {
              if (history.job === 1 && (!earliestStart || history.createdAt < earliestStart)) {
                earliestStart = history.createdAt;
              }
            }
          }
        }
      }
      
      if (!earliestStart) continue;
      
      // Рассчитываем время ремонта в часах
      const repairTimeHours = Math.max(0, Math.round((item.finishDate - earliestStart) / 3600));
      
      // Общие суммы для предыдущего года
      prevYearTotalTime += repairTimeHours;
      prevYearRepairCount++;
    }
    
    // Вычисляем средние значения для всех собранных статистик
    
    // Для месяцев
    const monthlyAverages = {};
    for (const month in monthlyStats) {
      const stats = monthlyStats[month];
      const average = stats.count > 0 ? Math.round(stats.totalTime / stats.count) : 0;
      
      const typeAverages = {};
      for (const type in stats.byType) {
        const typeData = stats.byType[type];
        typeAverages[type] = typeData.count > 0 ? Math.round(typeData.totalTime / typeData.count) : 0;
      }
      
      monthlyAverages[month] = {
        average,
        byType: typeAverages,
        count: stats.count
      };
    }
    
    // Для типов
    const typeAverages = {};
    for (const type in typeStats) {
      const stats = typeStats[type];
      typeAverages[type] = stats.count > 0 ? Math.round(stats.totalTime / stats.count) : 0;
    }
    
    // Для месяцев и филиалов
    const monthlyBranchAverages = {};
    for (const month in monthlyBranchStats) {
      monthlyBranchAverages[month] = {};
      
      for (const branch in monthlyBranchStats[month]) {
        const stats = monthlyBranchStats[month][branch];
        monthlyBranchAverages[month][branch] = {
          average: stats.count > 0 ? Math.round(stats.totalTime / stats.count) : 0,
          count: stats.count
        };
      }
    }
    
    // Заполнение всех месяцев в году
    for (let month = 1; month <= 12; month++) {
      if (!monthlyAverages[month]) {
        monthlyAverages[month] = {
          average: 0,
          byType: {},
          count: 0
        };
      }
      if (!monthlyBranchAverages[month]) {
        monthlyBranchAverages[month] = {};
      }
    }
    
    // Общие средние
    const yearlyAverage = totalRepairCount > 0 ? Math.round(totalRepairTime / totalRepairCount) : 0;
    const prevYearAverage = prevYearRepairCount > 0 ? Math.round(prevYearTotalTime / prevYearRepairCount) : 0;
    
    // Сравнение с предыдущим годом
    const difference = yearlyAverage - prevYearAverage;
    const comparisonText = prevYearRepairCount > 0
      ? `Среднее время ремонта ${year} года составило (ч): ${yearlyAverage}, что на ${Math.abs(difference)} ${difference >= 0 ? 'больше' : 'меньше'} чем в прошлом году.`
      : `Среднее время ремонта ${year} года составило (ч): ${yearlyAverage}. Данные за прошлый год отсутствуют.`;
    
    return {
      monthlyAverages,
      typeAverages,
      monthlyBranchAverages, // Новое поле - статистика по месяцам и филиалам
      yearlyAverage,
      prevYearAverage,
      difference,
      comparisonText,
      totalRepairCount,
      prevYearRepairCount
    };
  }

  /**
   * Нормализует числовое значение из строки с пробелами и запятой в число
   * Например: "2 961,00" -> 2961
   * @param {String} value - Строковое представление числа 
   * @returns {Number|null} Нормализованное число или null
   */
  normalizeNumericValue(value) {
    if (!value) return null;
    
    // Убираем пробелы и заменяем запятую на точку
    const normalized = String(value)
      .replace(/\s+/g, '')  // Убрать все пробелы
      .replace(/,/g, '.');  // Заменить запятые на точки
      
    // Преобразуем к числу и получаем целое значение
    const numValue = parseFloat(normalized);
    
    // Проверяем, является ли результат числом
    return !isNaN(numValue) ? numValue : null;
  }
  
  /**
   * Проверяет соответствие значения фильтру с учетом форматов
   * @param {String} itemValue - Значение из базы (например "2 961,00")
   * @param {String|Number} filterValue - Значение фильтра (например 2961)
   * @returns {Boolean} Соответствует ли значение фильтру
   */
  matchesFilter(itemValue, filterValue) {
    if (!itemValue || !filterValue) return false;
    
    // Нормализуем оба значения
    const normalizedItem = this.normalizeNumericValue(itemValue);
    const normalizedFilter = this.normalizeNumericValue(filterValue);
    
    // Если не удалось нормализовать какое-либо из значений, проверяем на вхождение подстроки
    if (normalizedItem === null || normalizedFilter === null) {
      return String(itemValue).toLowerCase().includes(String(filterValue).toLowerCase());
    }
    
    // Сравниваем числа с небольшой погрешностью для учета возможной разницы из-за округления
    return Math.abs(normalizedItem - normalizedFilter) < 0.01;
  }

  /**
   * Получает статистику выполнения работ за указанный год с возможностью фильтрации
   * @param {Object} options - Параметры запроса
   * @param {Number} options.year - Год для анализа (обязательно)
   * @param {Number|String} options.taskId - ID работы (обязательно)
   * @param {String} options.power - Мощность оборудования для фильтрации (опционально)
   * @param {String} options.turnovers - Обороты оборудования для фильтрации (опционально)
   * @returns {Array} Массив объектов со статистикой по работам
   */
  async getTaskStatisticsByYear({ year, taskId, power, turnovers }) {
    if (!year || isNaN(year)) {
      throw new Error("Invalid or missing year parameter");
    }

    if (!taskId) {
      throw new Error("Task ID is required");
    }

    // Определяем временные рамки запрашиваемого года для фильтрации по timestamp
    const startOfYear = new Date(Date.UTC(year, 0, 1)).getTime() / 1000;
    const endOfYear = new Date(Date.UTC(year + 1, 0, 1)).getTime() / 1000;

    // Строим запрос для получения статистики с предварительной фильтрацией по году
    const query = {
      // Используем оператор $elemMatch для поиска работ в указанном диапазоне дат
      "works": {
        $elemMatch: {
          "formattedDate": { 
            $gte: startOfYear, 
            $lt: endOfYear 
          }
        }
      }
    };

    // Если указан taskId, добавляем его в фильтр на уровне базы данных
    if (taskId) {
      // Напрямую добавляем условие для поиска по taskId
      query["works.taskId"] = taskId;
    }
    
    // Проекция для оптимизации запроса - получаем только необходимые поля
    const projection = {
      repairNumber: 1,
      mainID: 1,
      branch: 1,
      works: 1
    };
    
    // Получаем записи статистики, уже отфильтрованные по году и taskId
    const allStats = await db.getOutAgeStats(query, projection);
    
    // Собираем mainID из всех записей для последующего запроса к items
    const mainIDs = allStats.map(stat => stat.mainID).filter(Boolean);
    
    // Получаем данные оборудования из таблицы items
    const itemsProjection = "mainID order.equipment order.power order.turnovers";
    const itemsQuery = { 
      mainID: { $in: mainIDs },
      deleted: false
    };
    
    const itemsWithEquipmentData = await db.getItemsWithProjection(itemsQuery, itemsProjection);
    
    // Создаем Map для быстрого доступа к данным оборудования по mainID
    const equipmentDataMap = new Map();
    itemsWithEquipmentData.forEach(item => {
      if (item.order) {
        equipmentDataMap.set(item.mainID, {
          equipment: item.order.equipment || '',
          power: item.order.power || '',
          turnovers: item.order.turnovers || ''
        });
      }
    });
    
    // Получаем названия филиалов
    const branchNames = await this.getBranchNames();
    
    // Фильтруем и обрабатываем статистику
    const filteredStats = [];

    // Обрабатываем каждую запись статистики
    for (const stat of allStats) {
      // Проверяем наличие works массива
      if (!stat.works || !Array.isArray(stat.works) || stat.works.length === 0) {
        continue;
      }

      // Получаем данные оборудования из Map или используем данные из записи статистики
      const equipmentData = equipmentDataMap.get(stat.mainID) || {
        equipment: '',
        power: '',
        turnovers: ''
      };
      
      // Дополнительно фильтруем работы по taskId и году
      // (хотя основная фильтрация уже выполнена в запросе к базе данных)
      const matchingWorks = stat.works.filter(work => 
        (work.taskId === taskId || work.area === Number(taskId)) &&
        (work.formattedDate >= startOfYear && work.formattedDate < endOfYear)
      );
      
      // Если нашли подходящие работы, обрабатываем их
      if (matchingWorks.length > 0) {
        // Проверяем соответствие фильтрам по мощности и оборотам, если они указаны
        if ((power && !this.matchesFilter(equipmentData.power, power)) ||
            (turnovers && !this.matchesFilter(equipmentData.turnovers, turnovers))) {
          continue;
        }

        // Формируем объект с данными статистики
        const branchId = stat.branch || 0;
        const branchName = branchNames[branchId] || `Филиал ${branchId}`;
        
        // Вычисляем суммарное время работы для всех подходящих работ
        let totalWorkerTime = 0;
        
        // Суммируем время работ
        for (const work of matchingWorks) {
          totalWorkerTime += work.workerTime || 0;
        }

        // Конвертируем время из секунд в часы
        const standardHours = totalWorkerTime > 0 ? (totalWorkerTime / 3600).toFixed(2) : 0;

        filteredStats.push({
          repairNumber: stat.repairNumber || 'Н/Д',
          equipmentName: equipmentData.equipment || 'Неизвестное оборудование',
          power: equipmentData.power || 'Н/Д',
          turnovers: equipmentData.turnovers || 'Н/Д',
          standardHours: parseFloat(standardHours),
          branch: branchName,
          mainID: stat.mainID
        });
      }
    }

    // Сортируем результаты по филиалу и времени работы
    filteredStats.sort((a, b) => {
      if (a.branch !== b.branch) {
        return a.branch.localeCompare(b.branch);
      }
      return b.standardHours - a.standardHours;
    });

    return filteredStats;
  }

  /**
   * Вспомогательный метод для получения названий филиалов
   * @returns {Object} Объект вида {id: name}
   */
  async getBranchNames() {
    try {
      const branches = await db.getBranches();
      const branchNames = {};
      
      for (const branch of branches) {
        branchNames[branch.identifier] = branch.name;
      }
      
      return branchNames;
    } catch (error) {
      console.error("Error fetching branch names:", error);
      return {};
    }
  }
}

module.exports = new StatsService();
