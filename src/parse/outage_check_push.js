const db = require('./../db');
const utils = require("./../utils");
const pushes = require('./../handlers/pushes');
const { DateTime } = require("luxon");

// Проверка времени простоев, создание уведомлений о простое

async function calculateOutAgeFromLastHistory(item) {
    let maxOutAgeFromLastHistory = 99999; // устанавливаем максимальное значение

    for (const component of item.components) {
        if (component.newHistory.length > 0) { // расчет времени простоя от последней работы
            let lastHistory = component.newHistory[component.newHistory.length - 1];

            if (lastHistory.type === "job") {
                if (lastHistory.job === 141 || lastHistory.job === 138) { // если отгружен или ожидает отгрузку, пропускаем
                    continue;
                }
            }

            if (lastHistory.statuses.length > 0) {
                let options = {
                    zone: "Asia/Yekaterinburg"
                };

                let lastStatus = lastHistory.statuses[lastHistory.statuses.length - 1];
                let nowTimeSeconds = DateTime.now().setZone(options.zone).toUnixInteger();

                let startTime = DateTime.fromSeconds(lastStatus.createdAt, options);
                let nowTime = DateTime.fromSeconds(nowTimeSeconds, options);

                let currentDay = currentDayBool(startTime, nowTime);

                let outAgeFromLastHistoryToToday = 0;

                if (currentDay === true) {
                    outAgeFromLastHistoryToToday = calculateOutAgeDeltaSingleDay(
                        lastStatus.createdAt,
                        nowTimeSeconds,
                        startTime
                    );
                } else {
                    outAgeFromLastHistoryToToday = calculateTimeDeltaWithSeveralDays(
                        startTime,
                        nowTime
                    );
                }

                if (outAgeFromLastHistoryToToday > 0) {
                    let outAgeHoursHistory = outAgeFromLastHistoryToToday / 3600;
                    let outAgeDays = outAgeHoursHistory / 8;

                    if (maxOutAgeFromLastHistory > outAgeDays) {
                        maxOutAgeFromLastHistory = outAgeDays;
                    }
                }
            }
        }
    }

    if (maxOutAgeFromLastHistory === 99999) {
        maxOutAgeFromLastHistory = 0;
    }

    return maxOutAgeFromLastHistory;
}

function calculateTimeDeltaWithSeveralDays(timeFrom, timeTo) {
    let time = 0;

    let dateFrom = timeFrom;
    let dateTo = timeTo;

    // Рассчитываем конец первого дня
    let firstDayEnd = dateFrom.set({ hour: 17, minute: 0, second: 0 });
    let timeStartDay = calculateOutAgeDeltaSingleDay(
        timeFrom.toUnixInteger(),
        firstDayEnd.toUnixInteger(),
        dateFrom
    );
    time += timeStartDay;

    // Рассчитываем начало последнего дня
    let lastDayStart = dateTo.set({ hour: 8, minute: 0, second: 0 });
    let timeLastDay = calculateOutAgeDeltaSingleDay(
        lastDayStart.toUnixInteger(),
        timeTo.toUnixInteger(),
        dateTo
    );
    time += timeLastDay;

    // Рассчитываем промежуточные дни
    let startDate = dateFrom.plus({ days: 1 }).startOf('day').set({ hour: 8 });
    let endDate = dateTo.minus({ days: 1 }).startOf('day').set({ hour: 17 });

    let curDate = startDate;

    while (curDate <= endDate) {
        const dayOfWeek = curDate.weekday; // 1 (понедельник) до 7 (воскресенье)
        if (dayOfWeek === 5) { // пятница
            time += 25920; // 7 часов 12 минут в секундах (с 8:00 до 16:00 минус обед)
        } else if (dayOfWeek !== 6 && dayOfWeek !== 7) { // не суббота и не воскресенье
            time += 29520; // 8 часов 12 минут в секундах (с 8:00 до 17:00 минус обед)
        }
        curDate = curDate.plus({ days: 1 });
    }

    return time;
}

function calculateOutAgeDeltaSingleDay(from, to, date) {
    // Расчет простоя в течение дня

    let finishTime;
    if (date.weekday === 5) { // пятница
        finishTime = date.set({ hour: 16, minute: 0, second: 0 });
    } else if (date.weekday > 5) { // суббота или воскресенье
        return 0;
    } else {
        finishTime = date.set({ hour: 17, minute: 0, second: 0 });
    }

    let startTime = date.set({ hour: 8, minute: 0, second: 0 });
    let lunchStart = date.set({ hour: 12, minute: 0, second: 0 });
    let lunchStop = date.set({ hour: 12, minute: 48, second: 0 });

    let fromTime = Math.max(from, startTime.toUnixInteger());
    let toTime = Math.min(to, finishTime.toUnixInteger());

    if (fromTime >= toTime) {
        return 0;
    }

    // Исключаем обеденный перерыв
    if (fromTime < lunchStop.toUnixInteger() && toTime > lunchStart.toUnixInteger()) {
        if (fromTime < lunchStart.toUnixInteger()) {
            if (toTime > lunchStop.toUnixInteger()) {
                // Обе границы находятся вне обеда
                delta = toTime - fromTime - (lunchStop.toUnixInteger() - lunchStart.toUnixInteger());
            } else {
                // Начало до обеда, конец во время обеда
                delta = lunchStart.toUnixInteger() - fromTime;
            }
        } else if (fromTime >= lunchStart.toUnixInteger() && toTime <= lunchStop.toUnixInteger()) {
            // Обе границы внутри обеда
            return 0;
        } else {
            // Начало во время обеда, конец после обеда
            delta = toTime - lunchStop.toUnixInteger();
        }
    } else {
        delta = toTime - fromTime;
    }

    if (delta < 0) {
        return 0;
    }

    return delta;
}

async function outageCheck() {
    let users = await getUsers();

    console.log(users);

    for (let user of users) {
        let subscribeOutage = user.subscribeOutage;

        for (let value of subscribeOutage) {
            let values = value.split(":");
            let mainID = values[0];
            let days = values[1];
            let item = await getItem(mainID);

            if (item && item.components) {
                await outageCheckItem(item, Number(days), user.login);
            } else {
                console.log(`outageCheck: Item with mainID ${mainID} does not have components or is null.`);
            }
        }
    }
}

async function outageCheckItem(item, days, login) {
    let daysOutage = await calculateOutAgeFromLastHistory(item);

    let daysOutageRound = Math.round(daysOutage * 100) / 100;

    if (daysOutage > days) {
        let message = "Простой: " + daysOutageRound + " рабочих дней" + "\nРем. номер: №" + item.repairNumber + "\nОборудование: " + item.order.equipment;
        pushes.messageOutAgeItem("Уведомление о простое", message, [login]);
        console.log(message);
    } else {
        console.log(login + ": outageCheckItem : №" + item.repairNumber + " not outaged. Set days: " + days + " OutAge Current: " + daysOutageRound);
    }
}

async function getUsers() {
    let query = {
        subscribeOutage: { $exists: true, $ne: [] },
    };

    let projection = "login subscribeOutage";

    return await db.getUsersProj(query, projection);
}

async function getItem(mainID) {
    let query = {
        deleted: false,
        finished: false,
        new: false,
        mainID: mainID
    };

    const projection = "repairNumber order.equipment mainID finished branch components";

    return await db.findItem(query, null, projection);
}

function currentDayBool(dateTime1, dateTime2) {
    return dateTime1.hasSame(dateTime2, 'day');
}

module.exports = {
    outageCheck
};