
const db = require('./../db')
const utils = require("./../utils")
const {DateTime} = require("luxon");
var json2xls = require('json2xls');
const fsp = require("fs/promises");

//const pathPrefix = "/mnt/server.disk/mobil/parse/"
//const pathPrefixTest = '/Volumes/webdav.cloud.mail.ru/mobil/parse/'

const pathPrefixTest = '/Users/<USER>/Desktop/'

// Генерация отчетов

async function reportsGenerate() {

    let query = {
        'components.0': {$exists: true}
    }

    var sort = {updatedAt: -1}
    let items = await db.getItems(query,sort)

    let newItems = []

    for (const item of items) {

        if (!item.order) {
            continue
        }

        for (const comp of item.components) {

            let nameComp = utils.getNameTypeItem(comp.type)

            for (const hist of comp.newHistory) {

                let nameArr = hist.name.split(" | ")

                let newItem = {
                    repairNumber:item.repairNumber,
                    name: item.order.name,
                    equipment: item.order.equipment,
                    componentID: comp.identifier,
                    componentName:nameComp,
                    jobName:nameArr[1],
                    areaName:nameArr[0],
                    orderFinishDate:item.order.finishDate,
                    endDateUnix: item.endDate,
                    statuses:hist.statuses,
                    finished: item.finished,
                    createdAt:hist.createdAt
                }

                if (hist.type === "task") {
                    newItem.historyType = "ССЗ"
                } else if (hist.type === "job") {
                    newItem.historyType = "Статус"
                } else if (hist.type === "info") {
                    newItem.historyType = "Инфо"
                }

                if (item.finishDate) {
                    newItem.finishDate = item.finishDate
                }

                newItems.push(newItem)
            }
        }
    }

    console.log(newItems)

    let exportItems = []

    for (let i = 0; i < newItems.length; i++) {

        let item = newItems[i]

        let firstStatus = item.statuses[0]
        let lastStatus = item.statuses[item.statuses.length - 1]

        let comment = ""

        for (const status of item.statuses) {
            if (status.comment) {
                comment = status.comment
            }
        }

        let startDate;

        if (firstStatus.status === "inwork") {
            startDate = firstStatus.createdAt
        }

        let endDate;

        if (lastStatus.status === "finish") {
            endDate = lastStatus.createdAt
        }

        let minuts = 0;

        if (startDate) {
            if (endDate) {
                let nDate = endDate - startDate
                minuts = Math.floor(nDate / 60)
            }
        }

        let exItem = {
            "Участок": item.areaName,
            "Рем номер": item.repairNumber,
            "Номенклатура": item.name,
            "Оборудование": item.equipment,
            "Компонент": item.componentName,
            "Тип записи": item.historyType,
            "Работа": item.jobName,
            "Время, мин": minuts,
            "Комментарий": ""
        }

        if (comment) {
            exItem["Комментарий"] = comment
        }

        if (endDate) {
            let finishEndDate = utils.dateFromUnixToShortStr(endDate)
            exItem["Дата завершения работы"] = finishEndDate
        }

        exItem["Дата завершения по договору"] = item.orderFinishDate

        if (item.finishDate) {

            let dateFinishStr = utils.dateFromUnixToShortStr(item.finishDate)
            exItem["Дата завершения"] = dateFinishStr
        }

        if (item.finished === true) {
            exItem["Завершен"] = "Да"
        } else {
            exItem["Завершен"] = "Нет"
        }

        let dateNow = Math.floor(Date.now() / 1000)
        let endDateUnix = item.endDateUnix

        exItem["Осталось дней"] = ""
        exItem["Просрочка дней факт"] = 0

        if (item.finished === false) {

            if (dateNow < endDateUnix) {
                let delta = endDateUnix - dateNow
                let days = delta / 86400
                exItem["Осталось дней"] = days
            } else if (dateNow > endDateUnix) {
                let delta = dateNow - endDateUnix
                let days = delta / 86400
                exItem["Осталось дней"] = -days
                exItem["Просрочка дней факт"] = days
            } else {
                exItem["Осталось дней"] = 0
            }
        } else {
            if (item.finishDate) {
                if (item.finishDate > endDateUnix) {

                    let delta = item.finishDate - endDateUnix
                    let days = delta / 86400

                    exItem["Просрочка дней факт"] = days
                }
            }
        }

        exportItems.push(exItem)
    }

    console.log(exportItems)

    let xls = json2xls(items);

    exportToFile(exportItems)
}


async function exportToFile(items) {

    let xls = json2xls(items);

    let uploadPath = pathPrefixTest + 'export.xlsx'

    try {
        await fsp.writeFile(uploadPath, xls, 'binary')
        console.log("Write xml ssz to server OK")

    } catch (error) {
        console.log(error)
        return
    }

    console.log("ГОТОВО.")
}

module.exports = {
    reportsGenerate
}