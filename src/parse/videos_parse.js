const db = require('./../db');
const utils = require("./../utils");
const {DateTime} = require("luxon");
var json2xls = require('json2xls');
const fsp = require("fs/promises");
const mongoose = require("mongoose");

const videosListPath = "/z_ftp/uer-video/out.json";
//const videosListPath = "/Users/<USER>/Desktop/out.json"

async function videoParseStart() {

    // Парсинг файла с ссылками на видеофайлы

    const data = await readVideosFile();
    const json = JSON.parse(data);
    const objects = json["files"];

    const newVideoObjs = makeNewVideoObjs(objects);

    const repairNumbers = newVideoObjs.map((value, index) => value.repairNumber)

    const items = await getItems(repairNumbers)

    //console.log(items)

    await connectionItemsAndVideos(items,newVideoObjs)
}

async function connectionItemsAndVideos(items,videosObjs) {

    // Вставляем в объекты ЗПР ссылки на видео

    for (const item of items) {
        for (const obj of videosObjs) {
            if (item.repairNumber === obj.repairNumber || item.altRN.indexOf(obj.repairNumber) !== -1) {
                //console.log(item)
                //console.log(obj)
                let needUpdate = needUpdateItem(item,obj);

                if (needUpdate === true) {

                    let result = await updateItem(item.mainID,obj.videosTest)
                    //console.log(result);
                    console.log("item: " + item.repairNumber + " | Updated")
                } else {
                    //console.log("item: " + item.repairNumber + " | No need update")
                }

                break
            }
        }
    }
}

function needUpdateItem(item, videoObj) {

    if (videoObj.videosTest.length !== item.videosTest.length) {
        return true
    }

    let needUpdate = false

    for (const obj of videoObj.videosTest) {

        let found = false

        for (const currentObj of item.videosTest) {
            if (currentObj.name === obj.name) {
                found = true

                if (currentObj.url !== obj.url) {
                    needUpdate = true
                } else if (currentObj.lastEdit !== obj.lastEdit) {
                    needUpdate = true
                }
            }
        }

        if (found === false) {
            needUpdate = true
        }
    }

    return needUpdate
}

async function updateItem(mainID, videos) {
    let updateQuery = {$set: {videosTest: videos}}
    const result = await db.updateItem(mainID,updateQuery)
    //console.log(result)
    return result;
}

async function getItems(repairNumbers) {

    let query = {}

    let or = {
        "$or": [
            {
                repairNumber: {$in: repairNumbers}
            },
            {
                altRN: {$elemMatch: {$in: repairNumbers}}
            }
        ]
    }

    query["$and"] = [or]

    const projection = "repairNumber mainID videosTest altRN";
    const items = await db.getItemsWithProjection(query,projection);
    return items
}

function makeNewVideoObjs(objects) {
    let newVideoObjs = [];

    for (const obj of objects) {

        const lastEdit = utils.dateFromFullDateFormatToUnix(obj.modification_date)

        const videoObj = {
            name: obj.name,
            url: obj.link,
            lastEdit: lastEdit,
        }

        //console.log(videoObj)

        let found = false

        for (let i = 0; i < newVideoObjs.length; i++) {
            if (obj.nsz === newVideoObjs[i].repairNumber) {
                newVideoObjs[i].videosTest.push(videoObj)
                found = true
                break
            }
        }

        if (found === false) {
            const newObj = {
                repairNumber: obj.nsz,
                videosTest : [
                    videoObj
                ]
            }

            newVideoObjs.push(newObj);
        }
    }

    return newVideoObjs;
}

async function readVideosFile() {
    try {
        return await fsp.readFile(videosListPath, {encoding: "utf-8"});

    } catch (error) {
        console.log(error)
        return null
    }
}

module.exports = {
    videoParseStart
}