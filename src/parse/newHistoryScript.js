// const fsp = require("fs/promises");
// const db = require('./../db')
// const utils = require("./../utils")
// const {DateTime} = require("luxon");
// const crypto = require("crypto");

// Скрипт для перезда на новый тип хранения истории в БД (уже не актуален)

// async function createNewHistoryInDB() {

//     let query = {
//     }

//     var sort = {updatedAt: -1}
//     let items = await db.getItems(query,sort)

//     console.log(items.length)

//     for (item of items) {

//         if (item.components.length > 0) {
//             await parseComponents(item)
//         }
//     }
// }

// async function parseComponents(item) {

//     for (component of item.components) {

//         //console.log(component.history)

//         let result = await parseHistory(component.history)
//         //console.log(result)

//         // for (newHist of result) {
//         //     console.log(newHist)
//         // }

//         let lastHistory = result[result.length-1]

//         component.newHistory = result
//         component.currentArea = lastHistory.area
//         component.accessArea = [];

//         component.updatedAt = lastHistory.updatedAt
//         //console.log(component)
//     }

//     //console.log(item)

//     let result = await item.save()

//     console.log(result)
//     console.log("Saved")
// }

// async function parseHistory(histories) {

//     let newHistories = [];

//     let lastArea;
//     let lastJob;
//     let lastTask;

//     for (history of histories) {

//         if (newHistories.length === 0) {

//             if (history.task) {
//                 lastTask = history.task
//             }

//             if (history.job) {
//                 lastJob = history.job
//             }

//             lastArea = history.area

//             let model = await createNewHistoryModel(history)
//             newHistories.push(model)

//             continue
//         }

//         let jobChanged = await checkJobChanged(history,lastArea,lastJob,lastTask)

//         if (jobChanged === false) {
//             for (let i = newHistories.length-1; i >= 0; i--) {

//                 let newHist = newHistories[i]

//                 if (newHist.area === history.area) {

//                     if ((history.job) && (newHist.job)) {
//                         if (history.job === newHist.job) {

//                             let status = await makeStatusHistory(history)
//                             newHist.statuses.push(status)
//                             newHist.updatedAt = status.updatedAt
//                         }
//                     } else if ((history.task) && (newHist.task)) {
//                         if (history.task === newHist.task) {

//                             let status = await makeStatusHistory(history)
//                             newHist.statuses.push(status)
//                             newHist.updatedAt = status.updatedAt
//                         }
//                     }

//                 }
//             }
//         } else {
//             let model = await createNewHistoryModel(history)
//             newHistories.push(model)

//         }

//         if (history.task) {
//             lastTask = history.task
//         }

//         if (history.job) {
//             lastJob = history.job
//         }

//         lastArea = history.area
//     }

//     return newHistories
// }

// async function createNewHistoryModel(history) {

//     const randomString = crypto.randomBytes(8).toString('hex');

//     let newHistory = {
//         name: history.name,
//         area: history.area,
//         identifier: randomString,
//         createdAt : history.createdAt,
//         updatedAt : history.updatedAt
//     }

//     if (history.task) {
//         newHistory.task = history.task
//         newHistory.type = "task"
//     } else if (history.job) {

//         if (history.job === 0) {
//             newHistory.type = "info"
//         } else {
//             newHistory.job = history.job
//             newHistory.type = "job"
//         }
//     } else {
//         newHistory.type = "info"
//     }

//     let status = await makeStatusHistory(history)

//     newHistory.statuses = [status]

//     return newHistory
// }

// async function makeStatusHistory(history) {

//     let status = {
//         owner: history.owner,
//         createdAt : history.createdAt,
//         updatedAt : history.updatedAt
//     }

//     if (history.comment) {
//         status.comment = history.comment
//     }

//     if (history.finished === true) {
//         status.status = "finish"
//     } else if (history.paused === true) {
//         status.status = "pause"
//     } else {
//         status.status = "inwork"
//     }

//     if (history.job === 0) {
//         status.status = "info"
//     }

//     //console.log(status)

//     return status
// }

// async function checkJobChanged(history,lastArea,lastJob,lastTask) {

//     if (lastArea === history.area) {

//         if (history.task) {
//             if (history.task === lastTask) {
//                 return false
//             }
//         }

//         if (history.job) {
//             if (history.job === lastJob) {
//                 return false
//             }
//         }
//     }

//     return true
// }

// module.exports = {
//     createNewHistoryInDB
// }