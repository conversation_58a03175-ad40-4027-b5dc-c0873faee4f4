const { DateTime } = require("luxon");
const utils = require("../utils");

/**
 * Объединяет пересекающиеся интервалы временной шкалы
 * @param {Array} timelineIntervals - массив интервалов временной шкалы
 * @param {Boolean} isMainTimeline - флаг, указывающий является ли это главным таймлайном
 * @returns {Array} Объединенные интервалы
 */
function mergeComponentTimelines(timelineIntervals, isMainTimeline = false) {
    if (!timelineIntervals || timelineIntervals.length <= 1) {
        return timelineIntervals || [];
    }
    
    // Разделяем интервалы по типам
    const outageIntervals = timelineIntervals.filter(interval => interval.type === "outage");
    const workIntervals = timelineIntervals.filter(interval => interval.type === "work");
    
    // Объединяем только пересекающиеся интервалы простоя и работы
    const mergedOutageIntervals = mergeOnlyOverlappingIntervals(outageIntervals, isMainTimeline);
    const mergedWorkIntervals = mergeOnlyOverlappingIntervals(workIntervals, isMainTimeline);
    
    // Возвращаем объединенный и отсортированный массив
    return [...mergedOutageIntervals, ...mergedWorkIntervals].sort((a, b) => a.startDate - b.startDate);
}

/**
 * Объединяет только пересекающиеся интервалы одного типа с учетом рабочего времени
 * @param {Array} intervals - массив интервалов
 * @param {Boolean} shouldMergeNames - нужно ли объединять имена (true для main timeline)
 * @returns {Array} Объединенные интервалы
 */
function mergeOnlyOverlappingIntervals(intervals, shouldMergeNames = false) {
    if (!intervals || intervals.length <= 1) {
        return intervals || [];
    }
    
    // Создаем копию массива для работы
    let workingIntervals = [...intervals];
    
    // Сортируем интервалы по времени начала
    workingIntervals.sort((a, b) => a.startDate - b.startDate);
    
    // Флаг, показывающий, было ли объединение на текущей итерации
    let mergeHappened = true;
    
    // Продолжаем объединение, пока находятся пересекающиеся интервалы
    while (mergeHappened && workingIntervals.length > 1) {
        mergeHappened = false;
        
        // Проверяем каждую пару интервалов на пересечение
        for (let i = 0; i < workingIntervals.length - 1; i++) {
            for (let j = i + 1; j < workingIntervals.length; j++) {
                const interval1 = workingIntervals[i];
                const interval2 = workingIntervals[j];
                
                // Проверяем корректность интервалов
                if (interval1.startDate >= interval1.endDate || interval2.startDate >= interval2.endDate) {
                    continue;
                }
                
                // Проверяем, что интервалы одного типа
                if (interval1.type !== interval2.type) {
                    continue;
                }
                
                // Проверяем, пересекаются ли интервалы или являются ли они смежными
                // Для работ не фильтруем по рабочему времени
                if (interval1.endDate >= interval2.startDate && interval1.startDate <= interval2.endDate) {
                    // Нашли пересечение, объединяем интервалы
                    
                    // Определяем интервал с большей длительностью для приоритета имени
                    const duration1 = interval1.endDate - interval1.startDate;
                    const duration2 = interval2.endDate - interval2.startDate;
                    
                    // Определяем, какой интервал имеет большую длительность
                    const firstPriority = duration1 >= duration2 ? interval1 : interval2;
                    const secondPriority = duration1 >= duration2 ? interval2 : interval1;
                    
                    // Создаем объединенный интервал
                    const mergedInterval = {
                        type: interval1.type,
                        startDate: Math.min(interval1.startDate, interval2.startDate),
                        endDate: Math.max(interval1.endDate, interval2.endDate),
                    };
                    
                    // Если это интервал простоя, фильтруем его по рабочему времени
                    if (interval1.type === "outage") {
                        // Фильтруем простой по рабочим часам
                        const filteredInterval = filterOutageIntervalToWorkingHours(mergedInterval);
                        
                        if (!filteredInterval) {
                            // Если после фильтрации интервал не остался, пропускаем объединение
                            continue;
                        }
                        
                        // Обновляем границы и длительность интервала после фильтрации
                        mergedInterval.startDate = filteredInterval.startDate;
                        mergedInterval.endDate = filteredInterval.endDate;
                        mergedInterval.duration = filteredInterval.duration;
                    } else {
                        // Для интервалов работы просто обновляем длительность
                        mergedInterval.duration = mergedInterval.endDate - mergedInterval.startDate;
                    }
                    
                    // Объединяем имена с учетом приоритета
                    if (shouldMergeNames) {
                        // Для главного таймлайна собираем все уникальные имена
                        const uniqueNames = new Set();
                        
                        // Сначала добавляем имена из интервала с большей длительностью
                        if (firstPriority.names && firstPriority.names.length > 0) {
                            firstPriority.names.forEach(name => uniqueNames.add(name));
                        }
                        
                        // Затем добавляем имена из второго интервала
                        if (secondPriority.names && secondPriority.names.length > 0) {
                            secondPriority.names.forEach(name => uniqueNames.add(name));
                        }
                        
                        // Преобразуем Set в массив
                        mergedInterval.names = Array.from(uniqueNames);
                    } else {
                        // Для компонентных таймлайнов берем имя только из интервала с большей длительностью
                        mergedInterval.names = firstPriority.names ? [...firstPriority.names] : [];
                    }
                    
                    // Удаляем оба интервала из рабочего массива
                    workingIntervals.splice(j, 1);
                    workingIntervals.splice(i, 1);
                    
                    // Добавляем объединенный интервал
                    workingIntervals.push(mergedInterval);
                    
                    // Сортируем массив снова
                    workingIntervals.sort((a, b) => a.startDate - b.startDate);
                    
                    // Отмечаем, что произошло объединение
                    mergeHappened = true;
                    
                    // Прерываем внутренний цикл и начинаем снова
                    break;
                }
            }
            
            // Если было объединение, начинаем сначала
            if (mergeHappened) {
                break;
            }
        }
    }
    
    // Если тип интервалов - простой, проводим окончательную фильтрацию 
    // интервалов по рабочему времени
    if (workingIntervals.length > 0 && workingIntervals[0].type === "outage") {
        workingIntervals = workingIntervals.map(interval => {
            const filteredInterval = filterOutageIntervalToWorkingHours(interval);
            if (filteredInterval) {
                // Сохраняем имена и тип из оригинального интервала
                filteredInterval.names = interval.names;
                filteredInterval.type = interval.type;
                return filteredInterval;
            }
            return null;
        }).filter(Boolean); // Удаляем null элементы
    }
    
    return workingIntervals;
}

/**
 * Фильтрует интервал простоя, оставляя только время в рабочие часы
 * @param {Object} interval - интервал простоя {startDate, endDate, duration, ...}
 * @returns {Object} Отфильтрованный интервал или null, если нет пересечения с рабочим временем
 */
function filterOutageIntervalToWorkingHours(interval) {
    if (!interval || interval.startDate >= interval.endDate) {
        return null;
    }
    
    // Создаем копию интервала
    let filteredInterval = { ...interval };
    
    // Настройка для часового пояса Екатеринбурга
    const options = { zone: "Asia/Yekaterinburg" };
    
    // Преобразуем начало и конец интервала в объекты DateTime
    let startDateTime = DateTime.fromSeconds(interval.startDate, options);
    let endDateTime = DateTime.fromSeconds(interval.endDate, options);
    
    // Если интервал в пределах одного дня
    if (startDateTime.hasSame(endDateTime, 'day')) {
        // Фильтруем время в одном дне
        const result = filterOutageInSingleDay(startDateTime, endDateTime);
        if (!result) {
            return null;
        }
        
        filteredInterval.startDate = result.start.toUnixInteger();
        filteredInterval.endDate = result.end.toUnixInteger();
        filteredInterval.duration = filteredInterval.endDate - filteredInterval.startDate;
        
        return filteredInterval.duration > 0 ? filteredInterval : null;
    } 
    // Если интервал охватывает несколько дней
    else {
        // Создаем массив для хранения отфильтрованных отрезков времени
        let workingIntervals = [];
        
        // Отфильтровываем первый день (от startDateTime до конца рабочего дня)
        const firstDayResult = filterOutageInSingleDay(
            startDateTime, 
            startDateTime.set({ hour: 23, minute: 59, second: 59 })
        );
        
        if (firstDayResult) {
            // Используем фактический конец рабочего дня вместо конца дня
            const workEndTime = getWorkEndTimeForDay(startDateTime);
            const firstDayEnd = Math.min(
                firstDayResult.end.toUnixInteger(), 
                workEndTime.toUnixInteger()
            );
            
            if (firstDayResult.start.toUnixInteger() < firstDayEnd) {
                workingIntervals.push({
                    start: firstDayResult.start.toUnixInteger(),
                    end: firstDayEnd
                });
            }
        }
        
        // Проходим через каждый целый день между начальным и конечным днями
        let currentDay = startDateTime.plus({ days: 1 }).startOf('day');
        while (currentDay < endDateTime.startOf('day')) {
            // Проверяем, не является ли день выходным (суббота=6, воскресенье=7)
            if (currentDay.weekday <= 5) {
                const dayStart = currentDay.set({ hour: 8, minute: 0, second: 0 });
                const workEndTime = getWorkEndTimeForDay(currentDay);
                
                // Учитываем обеденный перерыв
                const lunchStart = currentDay.set({ hour: 12, minute: 0, second: 0 });
                const lunchEnd = currentDay.set({ hour: 12, minute: 48, second: 0 });
                
                // Добавляем интервал до обеда
                workingIntervals.push({
                    start: dayStart.toUnixInteger(),
                    end: lunchStart.toUnixInteger()
                });
                
                // Добавляем интервал после обеда до конца рабочего дня
                workingIntervals.push({
                    start: lunchEnd.toUnixInteger(),
                    end: workEndTime.toUnixInteger()
                });
            }
            
            // Переходим к следующему дню
            currentDay = currentDay.plus({ days: 1 });
        }
        
        // Отфильтровываем последний день (от начала рабочего дня до endDateTime)
        if (!startDateTime.hasSame(endDateTime, 'day')) {
            // Проверяем, не является ли последний день выходным
            if (endDateTime.weekday <= 5) {
                const workStart = endDateTime.set({ hour: 8, minute: 0, second: 0 });
                
                // Если endDateTime до начала рабочего дня, пропускаем
                if (endDateTime.toUnixInteger() > workStart.toUnixInteger()) {
                    const lastDayResult = filterOutageInSingleDay(
                        workStart,
                        endDateTime
                    );
                    
                    if (lastDayResult) {
                        workingIntervals.push({
                            start: lastDayResult.start.toUnixInteger(),
                            end: lastDayResult.end.toUnixInteger()
                        });
                    }
                }
            }
        }
        
        // Если нет рабочих интервалов, возвращаем null
        if (workingIntervals.length === 0) {
            return null;
        }
        
        // Объединяем последовательные рабочие интервалы с допуском небольших разрывов
        workingIntervals = mergeSequentialWorkIntervals(workingIntervals, 60); // 60 секунд допуск
        
        // Вычисляем общую длительность всех интервалов
        let totalDuration = 0;
        workingIntervals.forEach(interval => {
            totalDuration += interval.end - interval.start;
        });
        
        // Если после фильтрации нет значимой длительности, возвращаем null
        if (totalDuration <= 0) {
            return null;
        }
        
        // Возвращаем интервал с первым началом и последним концом, и корректным временем простоя
        filteredInterval.startDate = workingIntervals[0].start;
        filteredInterval.endDate = workingIntervals[workingIntervals.length - 1].end;
        filteredInterval.duration = totalDuration;
        filteredInterval.segments = workingIntervals; // Сохраняем отдельные сегменты для детального анализа
        
        return filteredInterval;
    }
}

/**
 * Возвращает время окончания рабочего дня для указанной даты
 * @param {DateTime} dateTime - объект DateTime с датой
 * @returns {DateTime} Время окончания рабочего дня
 */
function getWorkEndTimeForDay(dateTime) {
    if (!dateTime) return null;
    
    // Пятница заканчивается в 16:00, остальные дни в 17:00
    if (dateTime.weekday === 5) {
        return dateTime.set({ hour: 16, minute: 0, second: 0 });
    } else if (dateTime.weekday <= 4) {
        return dateTime.set({ hour: 17, minute: 0, second: 0 });
    } else {
        // Для выходных дней (6-суббота, 7-воскресенье) рабочего времени нет
        return dateTime.set({ hour: 0, minute: 0, second: 0 });
    }
}

/**
 * Объединяет последовательные рабочие интервалы с возможностью указать допустимый промежуток
 * @param {Array} intervals - массив интервалов 
 * @param {Number} maxGap - максимально допустимый промежуток между интервалами в секундах (по умолчанию 0)
 * @returns {Array} объединенные интервалы
 */
function mergeSequentialWorkIntervals(intervals, maxGap = 0) {
    if (!intervals || intervals.length <= 1) {
        return intervals || [];
    }
    
    // Сортируем интервалы по времени начала
    intervals.sort((a, b) => a.start - b.start);
    
    const result = [];
    let current = intervals[0];
    
    for (let i = 1; i < intervals.length; i++) {
        const next = intervals[i];
        
        // Проверяем, что интервалы имеют корректную длительность
        if (current.start >= current.end) {
            current = next;
            continue;
        }
        
        if (next.start >= next.end) {
            continue;
        }
        
        // Если интервалы последовательны или промежуток меньше maxGap,
        // объединяем их
        if (next.start - current.end <= maxGap) {
            current.end = Math.max(current.end, next.end);
        } else {
            result.push(current);
            current = next;
        }
    }
    
    // Добавляем последний обработанный интервал
    if (current && current.start < current.end) {
        result.push(current);
    }
    
    return result;
}

/**
 * Фильтрует время простоя в рамках одного дня, учитывая только рабочие часы
 * @param {DateTime} startDateTime - начало интервала 
 * @param {DateTime} endDateTime - конец интервала
 * @returns {Object|null} Объект с отфильтрованным началом и концом интервала
 */
function filterOutageInSingleDay(startDateTime, endDateTime) {
    // Проверка - является ли день рабочим
    const dayOfWeek = startDateTime.weekday;
    
    // В выходные дни (суббота=6, воскресенье=7) простоев нет
    if (dayOfWeek > 5) {
        return null;
    }
    
    // Определяем время работы в зависимости от дня недели
    const workStart = startDateTime.set({ hour: 8, minute: 0, second: 0 });
    
    // Для пятницы рабочий день до 16:00, для других дней - до 17:00
    const workEnd = dayOfWeek === 5 
        ? startDateTime.set({ hour: 16, minute: 0, second: 0 })
        : startDateTime.set({ hour: 17, minute: 0, second: 0 });
    
    // Исключаем обед с 12:00 до 12:48
    const lunchStart = startDateTime.set({ hour: 12, minute: 0, second: 0 });
    const lunchEnd = startDateTime.set({ hour: 12, minute: 48, second: 0 });
    
    // Если начало интервала позже конца рабочего дня или конец интервала раньше начала рабочего дня
    if (startDateTime > workEnd || endDateTime < workStart) {
        return null;
    }
    
    // Корректируем начало и конец интервала в рамках рабочего времени
    const filteredStart = startDateTime < workStart ? workStart : startDateTime;
    const filteredEnd = endDateTime > workEnd ? workEnd : endDateTime;
    
    // Проверяем, нужно ли исключить время обеда
    if (filteredStart < lunchEnd && filteredEnd > lunchStart) {
        // Интервал пересекается с обедом
        
        // Если интервал полностью лежит внутри обеденного перерыва
        if (filteredStart >= lunchStart && filteredEnd <= lunchEnd) {
            return null;
        }
        
        // Если интервал начинается до обеда и заканчивается после обеда
        if (filteredStart < lunchStart && filteredEnd > lunchEnd) {
            // Рассчитываем общую длительность без обеда
            const beforeLunchDuration = lunchStart.toUnixInteger() - filteredStart.toUnixInteger();
            const afterLunchDuration = filteredEnd.toUnixInteger() - lunchEnd.toUnixInteger();
            const totalDuration = beforeLunchDuration + afterLunchDuration;
            
            // Возвращаем интервал с указанием двух частей и общей длительности
            return {
                start: filteredStart,
                end: filteredEnd,
                lunchBreak: true,
                duration: totalDuration,
                beforeLunch: { start: filteredStart, end: lunchStart },
                afterLunch: { start: lunchEnd, end: filteredEnd }
            };
        }
        
        // Если интервал начинается во время обеда
        if (filteredStart >= lunchStart && filteredStart < lunchEnd) {
            return { 
                start: lunchEnd, 
                end: filteredEnd,
                duration: filteredEnd.toUnixInteger() - lunchEnd.toUnixInteger()
            };
        }
        
        // Если интервал заканчивается во время обеда
        if (filteredEnd > lunchStart && filteredEnd <= lunchEnd) {
            return { 
                start: filteredStart, 
                end: lunchStart,
                duration: lunchStart.toUnixInteger() - filteredStart.toUnixInteger()
            };
        }
    }
    
    // Если нет пересечения с обедом
    return { 
        start: filteredStart, 
        end: filteredEnd,
        duration: filteredEnd.toUnixInteger() - filteredStart.toUnixInteger()
    };
}

/**
 * Строит общую временную шкалу на основе временных шкал компонентов
 * @param {Array} componentTimelines - массив временных шкал компонентов
 * @param {Array} commonOutageIntervals - общие интервалы простоя
 * @returns {Object} Общая временная шкала
 */
function buildMainTimeline(componentTimelines, commonOutageIntervals) {
    if (!componentTimelines || componentTimelines.length === 0) {
        return null;
    }
    
    // Создаем общую временную шкалу
    let mainTimeline = {
        name: "Общая временная шкала",
        componentType: null, // Для общей шкалы тип компонента не указываем
        main: true,
        timeline: []
    };
    
    // Создаем объединенную коллекцию всех интервалов из всех компонентов
    let allIntervals = [];
    
    // Собираем все интервалы из временных шкал компонентов
    componentTimelines.forEach(timeline => {
        if (!timeline.timeline || !Array.isArray(timeline.timeline)) return;
        
        timeline.timeline.forEach(interval => {
            // Проверяем валидность интервала
            if (!interval || interval.startDate >= interval.endDate) return;
            
            // Для интервалов простоя применяем фильтрацию по рабочему времени
            if (interval.type === "outage") {
                const filteredInterval = filterOutageIntervalToWorkingHours({
                    startDate: interval.startDate,
                    endDate: interval.endDate,
                    type: "outage"
                });
                
                if (filteredInterval) {
                    // Копируем интервал и добавляем информацию о компоненте
                    let intervalCopy = { 
                        ...interval,
                        startDate: filteredInterval.startDate,
                        endDate: filteredInterval.endDate,
                        duration: filteredInterval.duration,
                        componentType: timeline.componentType,
                        componentName: timeline.name,
                        // Добавляем флаг приоритета для статоров/индукторов
                        isStatorOrInductor: timeline.componentType === "stator" || timeline.componentType === "inductor"
                    };
                    allIntervals.push(intervalCopy);
                }
            } else {
                // Копируем интервал и добавляем информацию о компоненте
                let intervalCopy = { 
                    ...interval,
                    componentType: timeline.componentType,
                    componentName: timeline.name,
                    // Добавляем флаг приоритета для статоров/индукторов
                    isStatorOrInductor: timeline.componentType === "stator" || timeline.componentType === "inductor"
                };
                allIntervals.push(intervalCopy);
            }
        });
    });
    
    // Если нет интервалов, возвращаем пустую временную шкалу
    if (allIntervals.length === 0) {
        return mainTimeline;
    }
    
    // Собираем все интервалы работы и простоя отдельно
    const workIntervals = allIntervals.filter(interval => interval.type === "work");
    const outageIntervals = allIntervals.filter(interval => interval.type === "outage");
    
    // Объединяем пересекающиеся интервалы работы
    const mergedWorkIntervals = mergeOnlyOverlappingIntervals(workIntervals, true);
    
    // Объединяем пересекающиеся интервалы простоя
    const mergedOutageIntervals = mergeOnlyOverlappingIntervals(outageIntervals, true);
    
    // Объединяем и сортируем все интервалы по времени начала
    mainTimeline.timeline = [...mergedWorkIntervals, ...mergedOutageIntervals]
        .sort((a, b) => a.startDate - b.startDate);
    
    // Исключаем перекрытия между работой и простоями (работа имеет приоритет)
    mainTimeline.timeline = removeWorkOutageOverlaps(mainTimeline.timeline);
    
    // Применяем финальную обработку таймлайна для исключения нестандартных ситуаций
    mainTimeline.timeline = finalizeTimelineIntervals(mainTimeline.timeline);
    
    return mainTimeline;
}

/**
 * Удаляет перекрытия интервалов работы и простоев (работа имеет приоритет)
 * @param {Array} intervals - массив интервалов
 * @returns {Array} массив интервалов без перекрытий
 */
function removeWorkOutageOverlaps(intervals) {
    if (!intervals || intervals.length <= 1) {
        return intervals || [];
    }
    
    // Сортируем интервалы по времени начала
    intervals.sort((a, b) => a.startDate - b.startDate);
    
    // Временный массив для результата
    let result = [];
    
    // Добавляем первый интервал в результат
    result.push({...intervals[0]});
    
    for (let i = 1; i < intervals.length; i++) {
        const current = intervals[i];
        
        // Проверяем последний добавленный интервал на пересечение с текущим
        let lastResult = result[result.length - 1];
        
        // Пропускаем интервалы с некорректными границами
        if (current.startDate >= current.endDate) continue;
        
        // Если интервалы не пересекаются, просто добавляем текущий интервал
        if (current.startDate >= lastResult.endDate) {
            result.push({...current});
            continue;
        }
        
        // Если текущий интервал полностью содержится в последнем
        if (current.endDate <= lastResult.endDate) {
            // Если текущий - работа, а последний - простой, разделим последний
            if (current.type === "work" && lastResult.type === "outage") {
                // Создаем интервал простоя до работы
                if (current.startDate > lastResult.startDate) {
                    const beforeWork = {
                        ...lastResult,
                        endDate: current.startDate,
                        duration: current.startDate - lastResult.startDate
                    };
                    // Заменяем последний интервал на интервал до работы
                    result[result.length - 1] = beforeWork;
                } else {
                    // Если работа начинается раньше или одновременно с простоем,
                    // удаляем начало простоя
                    result.pop();
                }
                
                // Добавляем интервал работы
                result.push({...current});
                
                // Добавляем интервал простоя после работы, если он есть
                if (current.endDate < lastResult.endDate) {
                    const afterWork = {
                        ...lastResult,
                        startDate: current.endDate,
                        duration: lastResult.endDate - current.endDate
                    };
                    result.push(afterWork);
                }
            }
            // Если текущий - простой, а последний - работа, просто пропускаем текущий
            else if (current.type === "outage" && lastResult.type === "work") {
                continue;
            }
            // Если оба интервала одного типа, объединяем их
            else {
                // Собираем все уникальные имена
                const uniqueNames = new Set([...(lastResult.names || []), ...(current.names || [])]);
                
                // Обновляем последний интервал
                lastResult.endDate = Math.max(lastResult.endDate, current.endDate);
                lastResult.duration = lastResult.endDate - lastResult.startDate;
                lastResult.names = Array.from(uniqueNames);
            }
        }
        // Если текущий интервал частично пересекается с последним
        else {
            // Если текущий - работа, а последний - простой
            if (current.type === "work" && lastResult.type === "outage") {
                // Укорачиваем последний интервал простоя
                lastResult.endDate = current.startDate;
                lastResult.duration = lastResult.endDate - lastResult.startDate;
                
                // Добавляем интервал работы
                result.push({...current});
            }
            // Если текущий - простой, а последний - работа
            else if (current.type === "outage" && lastResult.type === "work") {
                // Добавляем интервал простоя с исключением пересечения
                const adjustedOutage = {
                    ...current,
                    startDate: lastResult.endDate,
                    duration: current.endDate - lastResult.endDate
                };
                
                // Проверяем, что интервал простоя не пустой после коррекции
                if (adjustedOutage.startDate < adjustedOutage.endDate) {
                    result.push(adjustedOutage);
                }
            }
            // Если оба интервала одного типа
            else {
                // Собираем все уникальные имена
                const uniqueNames = new Set([...(lastResult.names || []), ...(current.names || [])]);
                
                // Обновляем последний интервал
                lastResult.endDate = current.endDate;
                lastResult.duration = lastResult.endDate - lastResult.startDate;
                lastResult.names = Array.from(uniqueNames);
            }
        }
    }
    
    // Возвращаем результат, отфильтровав интервалы с некорректной длительностью
    return result.filter(interval => interval.startDate < interval.endDate);
}

/**
 * Финальная обработка интервалов временной шкалы для исключения перекрытий
 * и корректного объединения последовательных интервалов
 * @param {Array} intervals - массив интервалов
 * @returns {Array} обработанные интервалы
 */
function finalizeTimelineIntervals(intervals) {
    if (!intervals || intervals.length <= 1) {
        return intervals || [];
    }
    
    // Сортируем интервалы по времени начала
    intervals.sort((a, b) => a.startDate - b.startDate);
    
    // Удаляем интервалы с нулевой длительностью
    let validIntervals = intervals.filter(i => i.startDate < i.endDate);
    
    // Объединяем последовательные интервалы одного типа
    let workIntervals = validIntervals.filter(i => i.type === "work");
    let outageIntervals = validIntervals.filter(i => i.type === "outage");
    
    // Последовательные интервалы работы объединяем даже с небольшими разрывами (например, 5 минут)
    workIntervals = combineSequentialSameTypeIntervals(workIntervals, 300);
    
    // Для последовательных интервалов простоя более строгие правила объединения
    outageIntervals = combineSequentialSameTypeIntervals(outageIntervals, 60);
    
    // Объединяем работы и простои обратно в общий список
    validIntervals = [...workIntervals, ...outageIntervals].sort((a, b) => a.startDate - b.startDate);
    
    // Проверяем и устраняем любые оставшиеся перекрытия
    // Приоритет отдается интервалам работы
    let result = [];
    let currentIdx = 0;
    
    while (currentIdx < validIntervals.length) {
        let current = validIntervals[currentIdx];
        let nextIdx = currentIdx + 1;
        
        if (nextIdx < validIntervals.length) {
            let next = validIntervals[nextIdx];
            
            // Проверяем на перекрытие
            if (current.endDate > next.startDate) {
                // Если текущий - работа, а следующий - простой
                if (current.type === "work" && next.type === "outage") {
                    // Укорачиваем или пропускаем интервал простоя
                    if (next.endDate > current.endDate) {
                        next.startDate = current.endDate;
                        next.duration = next.endDate - next.startDate;
                    } else {
                        // Простой полностью поглощается работой - пропускаем его
                        validIntervals.splice(nextIdx, 1);
                        continue; // Не увеличиваем currentIdx
                    }
                }
                // Если текущий - простой, а следующий - работа
                else if (current.type === "outage" && next.type === "work") {
                    // Укорачиваем интервал простоя
                    current.endDate = next.startDate;
                    current.duration = current.endDate - current.startDate;
                }
                // Если оба одного типа, но были пропущены при предыдущем объединении
                else if (current.type === next.type) {
                    // Объединяем интервалы
                    current.endDate = Math.max(current.endDate, next.endDate);
                    current.duration = current.endDate - current.startDate;
                    
                    // Объединяем имена
                    if (current.names && next.names) {
                        const combinedNames = new Set([...current.names, ...next.names]);
                        current.names = Array.from(combinedNames);
                    }
                    
                    // Удаляем следующий интервал
                    validIntervals.splice(nextIdx, 1);
                    continue; // Не увеличиваем currentIdx
                }
            }
        }
        
        // Проверяем, осталась ли у текущего интервала положительная длительность
        if (current.startDate < current.endDate) {
            result.push(current);
        }
        
        currentIdx++;
    }
    
    return result;
}

/**
 * Объединяет последовательные интервалы одного типа с допустимым разрывом
 * @param {Array} intervals - массив интервалов одного типа
 * @param {Number} maxGap - максимальный допустимый разрыв между интервалами в секундах
 * @returns {Array} объединенные интервалы
 */
function combineSequentialSameTypeIntervals(intervals, maxGap = 0) {
    if (!intervals || intervals.length <= 1) {
        return intervals || [];
    }
    
    // Сортируем интервалы по времени начала
    intervals.sort((a, b) => a.startDate - b.startDate);
    
    const result = [];
    let current = { ...intervals[0] };
    
    for (let i = 1; i < intervals.length; i++) {
        const next = intervals[i];
        
        // Проверяем, что интервалы одного типа
        if (current.type !== next.type) {
            continue;
        }
        
        // Если разрыв между интервалами допустим
        if (next.startDate - current.endDate <= maxGap) {
            // Объединяем интервалы
            current.endDate = Math.max(current.endDate, next.endDate);
            current.duration = current.endDate - current.startDate;
            
            // Объединяем имена
            if (current.names && next.names) {
                const combinedNames = new Set();
                // Приоритетно добавляем имена текущего интервала
                current.names.forEach(name => combinedNames.add(name));
                // Затем добавляем имена из следующего интервала
                next.names.forEach(name => combinedNames.add(name));
                current.names = Array.from(combinedNames);
            }
        } else {
            result.push(current);
            current = { ...next };
        }
    }
    
    // Добавляем последний интервал
    if (current.startDate < current.endDate) {
        result.push(current);
    }
    
    return result;
}

/**
 * Оптимизирует временную шкалу, объединяя последовательные интервалы того же типа
 * @param {Array} timeline - массив интервалов временной шкалы
 * @param {Boolean} mergeSequential - флаг для объединения последовательных интервалов
 * @returns {Array} Оптимизированная временная шкала
 */
function optimizeTimeline(timeline, mergeSequential = false) {
    if (!timeline || timeline.length <= 1) {
        return timeline || [];
    }
    
    // Если не нужно объединять последовательные интервалы, возвращаем как есть
    if (!mergeSequential) {
        return timeline;
    }
    
    // Сортируем интервалы по времени начала
    const sortedTimeline = [...timeline].sort((a, b) => a.startDate - b.startDate);
    
    const result = [];
    let current = { ...sortedTimeline[0] };
    
    for (let i = 1; i < sortedTimeline.length; i++) {
        const next = sortedTimeline[i];
        
        // Если типы совпадают и интервалы следуют друг за другом без промежутка
        if (current.type === next.type && current.endDate === next.startDate) {
            // Объединяем интервалы
            
            current.endDate = next.endDate;
            
            // При объединении определяем, у какого интервала длительность больше
            const currentDuration = current.duration;
            const nextDuration = next.duration;
            
            if (current.names && next.names) {
                // Если длительность следующего интервала больше текущего
                if (nextDuration > currentDuration) {
                    // Предпочитаем имена из следующего интервала
                    const combinedNames = [...next.names];
                    current.names.forEach(name => {
                        if (!combinedNames.includes(name)) {
                            combinedNames.push(name);
                        }
                    });
                    current.names = combinedNames;
                } else {
                    // Иначе предпочитаем имена из текущего интервала
                    const combinedNames = [...current.names];
                    next.names.forEach(name => {
                        if (!combinedNames.includes(name)) {
                            combinedNames.push(name);
                        }
                    });
                    current.names = combinedNames;
                }
            }
            
            // Пересчитываем длительность
            current.duration = current.endDate - current.startDate;
        } else {
            // Интервалы разного типа или с промежутком
            result.push(current);
            current = { ...next };
        }
    }
    
    // Добавляем последний интервал
    if (current.startDate < current.endDate) {
        result.push(current);
    }
    
    return result;
}

/**
 * Извлекает периоды активной работы из статусов
 * @param {Array} statuses - массив статусов
 * @returns {Array} массив периодов активной работы {start, end}
 */
function extractWorkPeriodsFromStatuses(statuses) {
    if (!statuses || statuses.length === 0) {
        return [];
    }
    
    // Сортируем статусы по времени
    const sortedStatuses = [...statuses].sort((a, b) => a.createdAt - b.createdAt);
    
    let workPeriods = [];
    let workStart = null;
    
    for (let i = 0; i < sortedStatuses.length; i++) {
        const status = sortedStatuses[i];
        
        // Если начало работы
        if (status.status === "inwork") {
            if (workStart === null) {
                workStart = status.createdAt;
            }
        }
        // Если окончание работы или пауза
        else if ((status.status === "finish" || status.status === "pause") && workStart !== null) {
            workPeriods.push({
                start: workStart,
                end: status.createdAt
            });
            workStart = null;
        }
    }
    
    // Если последнее событие inwork и нет завершения
    if (workStart !== null) {
        // Для незавершенных работ устанавливаем текущее время как конец
        const currentTime = Math.floor(Date.now() / 1000);
        workPeriods.push({
            start: workStart,
            end: currentTime
        });
    }
    
    return workPeriods;
}

/**
 * Определяет, находится ли компонент в состоянии работы для заданного интервала
 * @param {Object} component - компонент оборудования
 * @param {Number} startTime - начало интервала
 * @param {Number} endTime - конец интервала
 * @param {Function} getJobForIdentificator - функция для получения информации о работе по идентификатору
 * @returns {Boolean} true, если компонент работает в данном интервале
 */
function isComponentWorking(component, startTime, endTime, getJobForIdentificator) {
    if (!component.newHistory || component.newHistory.length === 0) {
        return false;
    }
    
    // Проверяем каждую историю на наличие активной работы в заданном интервале
    for (const history of component.newHistory) {
        if (history.type === "info") continue;
        
        // Пропускаем работы с типом простоя (outage)
        const jobIdentificator = history.job;
        const job = getJobForIdentificator && getJobForIdentificator(jobIdentificator);
        if (job && job.outage === true) {
            continue;
        }
        
        if (history.statuses && history.statuses.length > 0) {
            // Сортируем статусы по времени создания
            const sortedStatuses = [...history.statuses].sort((a, b) => a.createdAt - b.createdAt);
            
            // Находим активные периоды работы
            let activeWorkPeriods = [];
            let workStart = null;
            let lastStatusType = null;
            
            for (const status of sortedStatuses) {
                // Если начало работы
                if (status.status === "inwork") {
                    if (workStart === null) {
                        workStart = status.createdAt;
                    }
                    lastStatusType = "inwork";
                } 
                // Если окончание работы или пауза
                else if ((status.status === "finish" || status.status === "pause") && workStart !== null) {
                    activeWorkPeriods.push({
                        start: workStart,
                        end: status.createdAt
                    });
                    workStart = null;
                    lastStatusType = status.status;
                }
            }
            
            // Если работа не завершилась до конца истории
            if (workStart !== null) {
                // Если работа продолжается, считаем её активной до текущего момента
                const currentTime = Math.floor(Date.now() / 1000);
                activeWorkPeriods.push({
                    start: workStart,
                    end: currentTime
                });
            }
            
            // Проверяем пересечение интервалов работы с запрашиваемым интервалом
            for (const period of activeWorkPeriods) {
                // Если интервалы пересекаются
                if (period.start <= endTime && period.end >= startTime) {
                    return true;
                }
            }
        }
    }
    
    return false;
}

/**
 * Объединяет пересекающиеся интервалы
 * @param {Array} intervals - массив интервалов в формате {start, end, duration}
 * @returns {Array} Объединенные интервалы
 */
function mergeOverlappingIntervals(intervals) {
    if (!intervals || intervals.length <= 1) {
        return intervals || [];
    }
    
    // Сортируем интервалы по времени начала
    intervals.sort((a, b) => a.start - b.start);
    
    const result = [];
    let current = { ...intervals[0] };
    
    for (let i = 1; i < intervals.length; i++) {
        const next = intervals[i];
        
        // Проверяем корректность интервалов
        if (next.start >= next.end) {
            continue; // Пропускаем некорректный интервал
        }
        
        if (current.start >= current.end) {
            current = { ...next };
            continue;
        }
        
        // Если текущий интервал пересекается со следующим
        if (current.end >= next.start) {
            // Объединяем интервалы
            current.end = Math.max(current.end, next.end);
            // Пересчитываем длительность
            current.duration = current.end - current.start;
        } else {
            // Интервалы не пересекаются
            result.push(current);
            current = { ...next };
        }
    }
    
    // Добавляем последний интервал
    if (current.start < current.end) {
        result.push(current);
    }
    
    return result;
}

/**
 * Находит пересечения между двумя наборами интервалов
 * @param {Array} intervals1 - первый набор интервалов
 * @param {Array} intervals2 - второй набор интервалов
 * @returns {Array} Пересечения интервалов
 */
function findIntersections(intervals1, intervals2) {
    if (!intervals1 || !intervals2 || intervals1.length === 0 || intervals2.length === 0) {
        return [];
    }
    
    const intersections = [];
    
    // Сортируем интервалы для оптимизации
    intervals1.sort((a, b) => a.start - b.start);
    intervals2.sort((a, b) => a.start - b.start);
    
    let i = 0, j = 0;
    
    // Алгоритм "двух указателей" для поиска пересечений
    while (i < intervals1.length && j < intervals2.length) {
        const int1 = intervals1[i];
        const int2 = intervals2[j];
        
        // Пропускаем некорректные интервалы
        if (int1.start >= int1.end) {
            i++;
            continue;
        }
        if (int2.start >= int2.end) {
            j++;
            continue;
        }
        
        // Находим пересечение интервалов
        const start = Math.max(int1.start, int2.start);
        const end = Math.min(int1.end, int2.end);
        
        // Если пересечение существует
        if (start < end) {
            const duration = end - start;
            intersections.push({ start, end, duration });
        }
        
        // Продвигаем указатель для интервала, который заканчивается раньше
        if (int1.end < int2.end) {
            i++;
        } else {
            j++;
        }
    }
    
    // Объединяем пересекающиеся интервалы в результате
    return mergeOverlappingIntervals(intersections);
}

/**
 * Находит общие интервалы простоя для всех компонентов
 * @param {Array} componentOutageIntervals - массив с интервалами простоя компонентов
 * @returns {Array} Общие интервалы простоя
 */
function findCommonOutageIntervals(componentOutageIntervals) {
    if (!componentOutageIntervals || componentOutageIntervals.length === 0) {
        return [];
    }
    
    // Отфильтруем компоненты с пустыми интервалами
    const validComponents = componentOutageIntervals.filter(
        comp => comp.intervals && comp.intervals.length > 0
    );
    
    // Если нет компонентов с валидными интервалами, возвращаем пустой массив
    if (validComponents.length === 0) {
        return [];
    }
    
    // Если только один компонент с валидными интервалами, возвращаем его интервалы
    if (validComponents.length === 1) {
        return validComponents[0].intervals;
    }
    
    // Начинаем с интервалов первого компонента
    let commonIntervals = [...validComponents[0].intervals];
    
    // Для каждого следующего компонента находим пересечения
    for (let i = 1; i < validComponents.length; i++) {
        commonIntervals = findIntersections(commonIntervals, validComponents[i].intervals);
        
        // Если нет общих пересечений, прерываем поиск
        if (commonIntervals.length === 0) {
            break;
        }
    }
    
    return commonIntervals;
}

module.exports = {
    mergeComponentTimelines,
    buildMainTimeline,
    extractWorkPeriodsFromStatuses,
    isComponentWorking,
    mergeOverlappingIntervals,
    findCommonOutageIntervals,
    optimizeTimeline,
    mergeOnlyOverlappingIntervals,
    filterOutageIntervalToWorkingHours,
    mergeSequentialWorkIntervals,
    finalizeTimelineIntervals,
    combineSequentialSameTypeIntervals,
    getWorkEndTimeForDay
};
