
// const db = require('./../db')

// async function updateItems() {

//     let query = {
//     }

//     // Проекция полей для оптимизации запроса
//     const projection = "tags mainID";
//     const items = await db.getItemsWithProjection(query, projection);

//     console.log(items.length)

//     for (let item of items) {


//         let updateQuery = {
//             $set: { tags: ["default"] }
//         }

//         await db.updateItem(item.mainID, updateQuery)
//     }
// }

// module.exports = {
//     updateItems
// }