const db = require('./../db')
// Импортируем модель ItemModel
const ItemModel = require('./../models/itemModel');

// Функция для исправления дат с возможностью указания конкретной даты
async function fixIncorrectDates(specificDate = null) {
    try {
        let query = {};
        let dateDescription = 'всем записям';
        
        if (specificDate) {
            // Определяем начало и конец указанного дня
            const startOfDay = new Date(specificDate);
            startOfDay.setHours(0, 0, 0, 0);
            const startTimestamp = Math.floor(startOfDay.getTime() / 1000);
            
            const endOfDay = new Date(specificDate);
            endOfDay.setHours(23, 59, 59, 999);
            const endTimestamp = Math.floor(endOfDay.getTime() / 1000);
            
            // Формируем запрос для поиска элементов с компонентами, обновленными в целевой день
            query = {
                'components.newHistory.statuses': {
                    $elemMatch: {
                        'owner': 'Автозакрытие',
                        'status': 'finish',
                        'createdAt': { $gte: startTimestamp, $lte: endTimestamp }
                    }
                }
            };
            
            const formattedDate = startOfDay.toLocaleDateString('ru-RU');
            dateDescription = `записям за ${formattedDate}`;
            console.log(`Начинаем поиск элементов с автозакрытием за ${formattedDate}`);
        } else {
            // Устанавливаем дату начала периода (5 ноября 2024 года)
            const periodStartDate = new Date('2024-11-05T00:00:00Z').getTime() / 1000;
            console.log('Начинаем поиск элементов, обновленных после 5 ноября 2024');
        }

        // Находим все элементы по запросу
        const items = await ItemModel.find(query);

        console.log(`Найдено элементов: ${items.length}`);

        for (let item of items) {
            let itemChanged = false;

            console.log(`\nОбрабатываем элемент mainID: ${item.mainID}`);

            for (let comp of item.components) {
                let compChanged = false;

                console.log(`  Обрабатываем компонент: ${comp.identifier}`);

                for (let history of comp.newHistory) {
                    console.log(`    Обрабатываем историю: ${history.name}`);

                    // Проверяем статусы с owner "Автозакрытие" и статусом "finish"
                    for (let status of history.statuses) {
                        if (status.owner === 'Автозакрытие' && status.status === 'finish') {

                            // Сохраняем оригинальное время перед изменением
                            const originalStatusCreatedAt = status.createdAt;

                            // Проверяем, неверна ли дата (например, если время больше ожидаемого)
                            if (isDateIncorrect(status.createdAt)) {
                                console.log(`      Найден неверный статус автозакрытия с датой: ${new Date(status.createdAt * 1000).toLocaleString('ru-RU', { timeZone: 'Asia/Yekaterinburg' })}`);

                                // Корректируем дату
                                let correctedTimestamp = correctTimestamp(status.createdAt);
                                status.createdAt = correctedTimestamp;
                                status.updatedAt = correctedTimestamp;
                                compChanged = true;

                                console.log(`      Дата статуса автозакрытия исправлена на: ${new Date(correctedTimestamp * 1000).toLocaleString('ru-RU', { timeZone: 'Asia/Yekaterinburg' })}`);

                                // Теперь проверяем статусы работников
                                for (let worker of history.workers) {
                                    console.log(`        Проверяем работника: ${worker.name}`);

                                    for (let workerStatus of worker.statuses) {
                                        if (workerStatus.status === 'finish') {
                                            // Сравниваем время работника с оригинальным временем автозакрытия
                                            let timeDifference = Math.abs(workerStatus.createdAt - originalStatusCreatedAt);

                                            if (timeDifference <= 5 * 60) {
                                                console.log(`          Время закрытия работника ${worker.name} отличается от автозакрытия на ${timeDifference} секунд`);

                                                // Корректируем дату работника
                                                workerStatus.createdAt = correctedTimestamp;
                                                workerStatus.updatedAt = correctedTimestamp;
                                                compChanged = true;

                                                console.log(`          Дата статуса работника исправлена на: ${new Date(correctedTimestamp * 1000).toLocaleString('ru-RU', { timeZone: 'Asia/Yekaterinburg' })}`);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                if (compChanged) {
                    itemChanged = true;
                }
            }

            if (itemChanged) {
                await item.save();
                console.log(`Элемент с mainID: ${item.mainID} обновлён и сохранён.`);
            } else {
                console.log(`Элемент с mainID: ${item.mainID} не требует обновления.`);
            }
        }

        console.log(`Исправление дат завершено по ${dateDescription}.`);
        
        if (process.env.NODE_ENV !== 'test') {
            process.exit(0);
        }
    } catch (error) {
        console.error('Ошибка при исправлении дат:', error);
        if (process.env.NODE_ENV !== 'test') {
            process.exit(1);
        }
    }
}

// Функция для проверки, является ли дата неверной
function isDateIncorrect(timestamp) {
    // Создаем объект Date на основе timestamp
    let date = new Date(timestamp * 1000);

    // Преобразуем время в часовой пояс Екатеринбурга, добавив смещение в 5 часов от UTC
    let ekbHour = date.getUTCHours() + 5; // Екатеринбург в UTC+5
    if (ekbHour >= 24) ekbHour -= 24; // Корректируем время, если выходит за пределы 24 часов

    console.log("Проверяем дату:", date.toISOString());
    console.log("Часы в часовом поясе Екатеринбурга:", ekbHour);

    // Если время не в диапазоне 16:00-17:00 по Екатеринбургскому времени, считаем дату неверной
    return ekbHour < 16 || ekbHour > 17;
}

// Функция для корректировки метки времени
function correctTimestamp(timestamp) {
    // Создаем объект Date на основе timestamp
    let date = new Date(timestamp * 1000);
    
    // Получаем день недели (0 - воскресенье, 1 - понедельник, ..., 5 - пятница, 6 - суббота)
    const dayOfWeek = date.getDay();
    
    // Создаем новую дату на тот же день, но с нужным временем (в UTC)
    let correctedDate = new Date(date);
    
    // Если пятница (день недели 5), устанавливаем 16:00 по Екатеринбургу (11:00 UTC)
    if (dayOfWeek === 5) {
        correctedDate.setUTCHours(11, 0, 0, 0); // 16:00 по Екатеринбургу (UTC+5)
        console.log("Устанавливаем время на 16:00 (пятница)");
    } 
    // Иначе устанавливаем 17:00 по Екатеринбургу (12:00 UTC)
    else {
        correctedDate.setUTCHours(12, 0, 0, 0); // 17:00 по Екатеринбургу (UTC+5)
        console.log("Устанавливаем время на 17:00 (будний день)");
    }
    
    // Возвращаем новый timestamp в секундах
    return Math.floor(correctedDate.getTime() / 1000);
}

// Функция для исправления дат по указанной строке даты
function fixIncorrectDatesByDate(dateStr) {
    // Преобразуем строку даты в объект Date
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) {
        console.error('Некорректный формат даты. Используйте формат YYYY-MM-DD');
        return;
    }
    
    return fixIncorrectDates(date);
}

module.exports = {
    fixIncorrectDates,
    fixIncorrectDatesByDate
}