const db = require('./../db');
const utils = require("./../utils");
const { DateTime } = require("luxon");
const { performance } = require('perf_hooks');
const ItemModel = require("../models/itemModel");
const OnBoardStat = require("../models/onboardStatModel");
const timelineUtils = require('./timeline_utils');

let jobs = [];

function getJobForIdentificator(jobID) {
    for (let i = 0; i < jobs.length; i++) {
        let job = jobs[i];
        if (job.identifier === jobID) {
            return job;
        }
    }
}

async function outAgeStatsParse() {
    console.log("OutAge Stats start parse");
    let startTime = performance.now(); // подсчет скорости расчета статистики

    await db.removeAllOutAgeStats();

    jobs = await db.getJobs();

    let itemsStream = await getItems();

    let onBoardStats = new Map();

    for await (const item of itemsStream) {
        let stats = calculateWorkTimeAndOutAgeItem(item);

        if (stats.archive === false) {
            if (onBoardStats.has(item.branch)) {
                let oldStat = onBoardStats.get(item.branch);

                const stat = {
                    workHours: oldStat.workHours + stats.workHours,
                    outAgeHours: oldStat.outAgeHours + stats.outAgeHours,
                    countComponents: oldStat.countComponents + stats.countComponents,
                };

                onBoardStats.set(item.branch, stat);
            } else {
                const stat = {
                    workHours: stats.workHours,
                    outAgeHours: stats.outAgeHours,
                    countComponents: stats.countComponents,
                };

                onBoardStats.set(item.branch, stat);
            }
        }

        await db.createOutAgeStats(stats);
    }

    for (let [key, value] of onBoardStats) {
        await updateOnBoard(key, value);
    }

    console.log("OutAge Stats end parse");
    let endTime = performance.now(); // подсчет скорости расчета статистики
    console.log(`Call to OutAge Stats Parse took ${(endTime - startTime) / 1000} seconds`);
}

async function updateOnBoard(branchID, stat) {
    let query = { branch: branchID };

    let onBoardStatResult = await db.getOnBoardStat(query);

    if (onBoardStatResult instanceof OnBoardStat) {
        let id = onBoardStatResult._id;

        const query = {
            $set: {
                outAgeHours: stat.outAgeHours,
                workHours: stat.workHours,
                countComponents: stat.countComponents,
            },
        };

        await db.updateOnBoardStat(id, query);
    } else {
        let newStat = {
            outAgeHours: stat.outAgeHours,
            workHours: stat.workHours,
            countComponents: stat.countComponents,
            branch: branchID,
        };

        await db.createOnBoardStat(newStat);
    }
}

function calculateWorkTimeAndOutAgeItem(item) {
    let stats = {
        repairNumber: item.repairNumber,
        mainID: item.mainID,
        archive: item.finished,
        branch: item.branch,
    };

    if (item.order) {
        if (item.order.equipment) {
            stats.equipment = item.order.equipment;
        }
    }

    let works = [];
    let outAgeHours = 0;
    let countWork = 0;
    let countComponents = 0;
    let workHours = 0;
    let workerHours = 0;
    let statusHours = 0;
    let statorOutAgeHours = 0;
    let rotorOutAgeHours = 0;
    let bearingOutAgeHours = 0;
    let transformerOutAgeHours = 0;

    // Для хранения таймлайнов компонентов
    let componentTimelines = [];
    
    // Массив для отслеживания времени простоя каждого компонента
    let componentOutageHours = [];

    for (const component of item.components) {
        let componentType;
        countComponents += 1;
        
        // Создаем объект временной шкалы для компонента
        let componentTimeline = {
            name: utils.getNameTypeItem(component.type),
            componentType: component.type,
            main: false,
            timeline: []
        };

        switch (component.type) {
            case "stator":
            case "inductor":
                componentType = "s";
                break;
            case "rotor":
            case "anchor":
                componentType = "r";
                break;
            case "slidingBearing":
            case "rollingBearing":
                componentType = "b";
                break;
            case "transformer":
                componentType = "t";
                break;
            default:
                break;
        }

        let componentName = utils.getNameTypeItem(component.type);

        // Расчет works для текущего компонента
        const { 
            componentWorks, 
            componentWorkHours,
            componentStatusHours,
            componentWorkerHours,
            componentOutageTime,
            componentWorkCount 
        } = calculateComponentWorks(component, componentName, componentType);
        
        // Добавляем работы компонента в общий массив работ
        works.push(...componentWorks);
        
        // Обновляем счетчики
        workHours += componentWorkHours;
        statusHours += componentStatusHours;
        workerHours += componentWorkerHours;
        countWork += componentWorkCount;
        
        // Извлекаем временные шкалы работ и простоев из историй компонента
        const { 
            workTimelineIntervals, 
            outageTimelineIntervals,
            totalComponentOutage 
        } = extractComponentTimeline(
            component, 
            componentName, 
            null,
            item.finished
        );
        
        // Обновляем время простоя по типу компонента на основе временной шкалы
        // Делим на 3600 для перевода из секунд в часы
        const componentOutageHoursValue = totalComponentOutage / 3600;
        
        if (componentType === "s") {
            statorOutAgeHours += componentOutageHoursValue;
        } else if (componentType === "r") {
            rotorOutAgeHours += componentOutageHoursValue;
        } else if (componentType === "b") {
            bearingOutAgeHours += componentOutageHoursValue;
        } else if (componentType === "t") {
            transformerOutAgeHours += componentOutageHoursValue;
        }

        // Обновляем статистику по компоненту
        componentOutageHours.push({
            type: component.type,
            outageHours: componentOutageHoursValue
        });

        // Объединяем пересекающиеся интервалы работ и простоев
        componentTimeline.timeline = [
            ...workTimelineIntervals.map(interval => ({
                names: interval.names,
                type: "work",
                duration: interval.endDate - interval.startDate,
                startDate: interval.startDate,
                endDate: interval.endDate
            })),
            ...outageTimelineIntervals.map(interval => ({
                names: interval.names,
                type: "outage",
                duration: interval.endDate - interval.startDate,
                startDate: interval.startDate,
                endDate: interval.endDate
            }))
        ];
        
        // Объединяем пересекающиеся интервалы одного типа
        componentTimeline.timeline = timelineUtils.mergeComponentTimelines(componentTimeline.timeline, false);
        
        // Добавляем временную шкалу компонента в массив
        componentTimelines.push(componentTimeline);
    }

    // Строим общую временную шкалу на основе временных шкал компонентов
    let mainTimeline = timelineUtils.buildMainTimeline(componentTimelines, []);
    
    // Если mainTimeline существует и имеет таймлайн, не применяем объединение последовательных интервалов
    if (mainTimeline && mainTimeline.timeline) {
        // Объединяем только пересекающиеся интервалы в общем таймлайне
        mainTimeline.timeline = timelineUtils.mergeOnlyOverlappingIntervals(mainTimeline.timeline, true);
    }
    
    // Объединяем все таймлайны
    let timelines = [...componentTimelines];
    if (mainTimeline) {
        timelines.push(mainTimeline);
    }
    
    // Рассчитываем общее время простоя оборудования на основе таймлайна main
    if (mainTimeline && mainTimeline.timeline.length > 0) {
        let totalOutageTime = 0;
        
        for (const interval of mainTimeline.timeline) {
            if (interval.type === "outage") {
                totalOutageTime += interval.duration;
            }
        }
        
        // При расчете общего простоя делим на 3600 для перевода из секунд в часы
        outAgeHours = totalOutageTime / 3600;
    }
    
    // Если нет общей временной шкалы или она не содержит интервалов простоя,
    // вычисляем общее время простоя как сумму всех компонентных простоев
    if (outAgeHours === 0 && componentOutageHours.length > 0) {
        // Если нет общего простоя из таймлайна, используем максимальное время простоя среди компонентов
        // Это подход более реалистичный чем взять минимальный простой
        componentOutageHours.sort((a, b) => a.outageHours - b.outageHours);
        outAgeHours = componentOutageHours[0].outageHours;
        
        // Также можно рассчитать среднее время простоя компонентов
        // let totalOutage = componentOutageHours.reduce((sum, item) => sum + item.outageHours, 0);
        // outAgeHours = totalOutage / componentOutageHours.length;
    }

    let allHoursWorkAndOutAge = workHours + outAgeHours;
    let oneProcent = allHoursWorkAndOutAge > 0 ? allHoursWorkAndOutAge / 100 : 0;

    let sszTimes = [];
    let dayStats = [];

    // Обрабатываем данные работ для расчета процентов и группировки
    for (let work of works) {
        if (work.workTime > 0) {
            let workPercent = oneProcent > 0 ? (work.workTime / 3600) / oneProcent : 0;
            work.percentWork = workPercent;
        } else {
            work.percentWork = 0;
        }

        if (work.outAgeTime > 0) {
            let outAgePercent = oneProcent > 0 ? (work.outAgeTime / 3600) / oneProcent : 0;
            work.percentOutAge = outAgePercent;
        } else {
            work.percentOutAge = 0;
        }

        if (work.workerTime > 0) {
            let sszTimeModel = sszTimes.find((e) => {
                return e.name === work.name && e.area === work.area;
            });

            if (sszTimeModel) {
                sszTimeModel.workerTime += work.workerTime;
                // Если в текущей работе есть taskId, добавляем его в sszTimes
                if (work.taskId && !sszTimeModel.taskId) {
                    sszTimeModel.taskId = work.taskId;
                }
            } else {
                let model = {
                    name: work.name,
                    area: work.area,
                    workerTime: work.workerTime,
                    taskId: work.taskId || null, // Добавляем ID задачи (ССЗ)
                    formattedDate: work.formattedDate || null // Добавляем timestamp в секундах
                };
                
                sszTimes.push(model);
            }

            if (work.dateStr) {
                let dayModel = dayStats.find((e) => {
                    return e.date === work.dateStr;
                });

                if (dayModel) {
                    dayModel.workCount += 1;
                    dayModel.workHours += work.workTime / 3600;
                    dayModel.workerHours += work.workerTime / 3600;
                } else {
                    let model = {
                        date: work.dateStr,
                        formattedDate: work.formattedDate, // Добавляем timestamp в секундах
                        workCount: 1,
                        workHours: work.workTime / 3600,
                        workerHours: work.workerTime / 3600,
                    };
                    dayStats.push(model);
                }
            }
        }
    }

    stats["dayStats"] = dayStats;
    stats["sszTimes"] = sszTimes;
    stats["timelines"] = timelines;

    stats["works"] = works;
    stats["outAgeHours"] = outAgeHours;
    stats["countWork"] = countWork;
    stats["countComponents"] = countComponents;
    stats["workHours"] = workHours;
    stats["workerHours"] = workerHours;
    stats["statusHours"] = statusHours;
    stats["statorOutAgeHours"] = statorOutAgeHours;
    stats["rotorOutAgeHours"] = rotorOutAgeHours;
    stats["bearingOutAgeHours"] = bearingOutAgeHours;
    stats["transformerOutAgeHours"] = transformerOutAgeHours;

    return stats;
}

/**
 * Рассчитывает работы и время для компонента
 * @param {Object} component - компонент оборудования
 * @param {String} componentName - название компонента
 * @param {String} componentType - тип компонента (s, r, b, t)
 * @returns {Object} Объект с массивом работ и статистикой
 */
function calculateComponentWorks(component, componentName, componentType) {
    let componentWorks = [];
    let componentWorkHours = 0;
    let componentStatusHours = 0;
    let componentWorkerHours = 0;
    let componentOutageTime = 0;
    let componentWorkCount = 0;
    let previousHistoryFinishTime;

    for (const history of component.newHistory) {
        if (history.type === "info") {
            continue;
        }

        // Обрабатываем простой между историями
        if (previousHistoryFinishTime) {
            if (history.statuses.length > 0) {
                let outAgeBetweenHistories = calculateOutAgeTimeBetweenHistories(
                    history.statuses.at(0),
                    previousHistoryFinishTime,
                    componentName
                );

                if (outAgeBetweenHistories) {
                    componentOutageTime += outAgeBetweenHistories.outAgeTime;
                    componentWorks.push(outAgeBetweenHistories);
                }
            }
        }

        // Расчет статистики для текущей истории
        let workStat = calculateWorkTimeAndOutAgeHistory(history, componentName);

        if (workStat) {
            if (workStat.statusTime === 0 && workStat.workTime > 0) {
                componentWorkCount++;
            }

            componentWorkHours += workStat.workTime / 3600;
            componentStatusHours += workStat.statusTime / 3600;
            componentWorkerHours += workStat.workerTime / 3600;
            componentOutageTime += workStat.outAgeTime;
            componentWorks.push(workStat);
        }

        // Обновляем время завершения истории
        let historyFinished = false;
        for (const status of history.statuses) {
            if (status.status === "finish") {
                previousHistoryFinishTime = status.createdAt;
                historyFinished = true;
            }
        }

        if (historyFinished === false) {
            previousHistoryFinishTime = null;
        }
    }

    // Проверяем наличие длительного интервала простоя с момента последней работы
    if (component.newHistory.length > 0 && component.finished === false) {
        let lastHistory = component.newHistory[component.newHistory.length - 1];

        // Проверяем, не относится ли история к работам, которые не должны учитываться при подсчете простоев
        if (lastHistory.type === "job") {
            if (lastHistory.job === 141 || lastHistory.job === 138) {
                // Пропускаем эти работы
            } else if (lastHistory.statuses.length > 0) {
                let options = {
                    zone: "Asia/Yekaterinburg",
                };

                let lastStatus = lastHistory.statuses[lastHistory.statuses.length - 1];
                
                // Проверка на статус "finish", так как он не должен учитываться в простоях
                if (lastStatus.status !== "finish") {
                    let nowTimeSeconds = DateTime.now().setZone(options.zone).toUnixInteger();

                    let startTime = DateTime.fromSeconds(lastStatus.createdAt, options);
                    let nowTime = DateTime.fromSeconds(nowTimeSeconds, options);

                    // Проверяем, не выходной ли день сегодня
                    if (nowTime.weekday <= 5) {
                        let currentDay = currentDayBool(startTime, nowTime);

                        let outAgeFromLastHistoryToToday = 0;

                        if (currentDay === true) {
                            outAgeFromLastHistoryToToday = calculateOutAgeDeltaSingleDay(
                                lastStatus.createdAt,
                                nowTimeSeconds,
                                startTime
                            );
                        } else {
                            outAgeFromLastHistoryToToday = calculateTimeDeltaWithSeveralDays(
                                startTime,
                                nowTime
                            );
                        }

                        if (outAgeFromLastHistoryToToday > 0) {
                            let workStat = {
                                name: "Простои с последней работы",
                                workTime: 0,
                                workerTime: 0,
                                outAgeTime: outAgeFromLastHistoryToToday,
                                statusTime: 0,
                                componentName: componentName,
                            };

                            componentOutageTime += outAgeFromLastHistoryToToday;
                            componentWorks.push(workStat);
                        }
                    }
                }
            }
        }
    }

    return {
        componentWorks,
        componentWorkHours,
        componentStatusHours,
        componentWorkerHours,
        componentOutageTime,
        componentWorkCount
    };
}

/**
 * Извлекает интервалы работ и простоев компонента из его историй
 * @param {Object} component - компонент оборудования
 * @param {String} componentName - название компонента
 * @param {Number} previousHistoryFinishTime - время завершения предыдущей истории
 * @param {Boolean} isFinished - флаг завершенности позиции
 * @returns {Object} Объект с интервалами работ и простоев
 */
function extractComponentTimeline(component, componentName, previousHistoryFinishTime, isFinished) {
    // Интервалы для работ
    let workTimelineIntervals = [];
    // Интервалы для простоев
    let outageTimelineIntervals = [];
    // Общее время простоя компонента
    let totalComponentOutage = 0;
    
    if (!component.newHistory || component.newHistory.length === 0) {
        return { 
            workTimelineIntervals, 
            outageTimelineIntervals, 
            totalComponentOutage 
        };
    }
    
    let currentHistoryFinishTime = previousHistoryFinishTime;
    
    // Извлекаем все интервалы работы из историй компонента
    for (const history of component.newHistory) {
        if (history.type === "info") continue;
        
        // Обрабатываем простой между историями
        if (currentHistoryFinishTime && history.statuses && history.statuses.length > 0) {
            const firstStatus = history.statuses[0];
            
            if (firstStatus.createdAt > currentHistoryFinishTime) {
                // Используем тот же подход для расчета простоя между историями, что и в calculateOutAgeTimeBetweenHistories
                const options = { zone: "Asia/Yekaterinburg" };
                const previousHistoryDateTime = DateTime.fromSeconds(currentHistoryFinishTime, options);
                const startNewHistoryDateTime = DateTime.fromSeconds(firstStatus.createdAt, options);
                
                // Проверяем, что не в выходные дни
                if (previousHistoryDateTime.weekday <= 5) {
                    const currentDay = currentDayBool(previousHistoryDateTime, startNewHistoryDateTime);
                    let outageTime = 0;
                    
                    if (currentDay) {
                        outageTime = calculateOutAgeDeltaSingleDay(
                            currentHistoryFinishTime,
                            firstStatus.createdAt,
                            previousHistoryDateTime
                        );
                    } else {
                        outageTime = calculateTimeDeltaWithSeveralDays(
                            previousHistoryDateTime,
                            startNewHistoryDateTime
                        );
                    }
                    
                    if (outageTime > 0) {
                        // Создаем интервал простоя
                        const outageInterval = {
                            names: ["Простой между работами"],
                            type: "outage",
                            duration: outageTime,
                            startDate: currentHistoryFinishTime,
                            endDate: firstStatus.createdAt
                        };
                        
                        outageTimelineIntervals.push({
                            names: outageInterval.names,
                            startDate: outageInterval.startDate,
                            endDate: outageInterval.endDate
                        });
                        
                        totalComponentOutage += outageTime;
                    }
                }
            }
        }
        
        // Определяем тип истории (работа или простой)
        let isOutageType = false;
        if (history.type === "job") {
            const job = getJobForIdentificator(history.job);
            isOutageType = job && job.outage === true;
        }
        
        // Обрабатываем периоды работы из статусов
        if (history.statuses && history.statuses.length > 0) {
            const workPeriods = timelineUtils.extractWorkPeriodsFromStatuses(history.statuses);
            
            // Добавляем периоды работ в таймлайн
            for (const period of workPeriods) {
                const duration = period.end - period.start;
                
                if (isOutageType) {
                    // Если это работа типа "простой", фильтруем для учета только рабочего времени
                    const outageInterval = {
                        names: [history.name || "Простой"],
                        type: "outage",
                        duration: duration,
                        startDate: period.start,
                        endDate: period.end
                    };
                    
                    // Фильтруем интервал простоя
                    const filteredInterval = timelineUtils.filterOutageIntervalToWorkingHours(outageInterval);
                    
                    // Если после фильтрации остался интервал простоя
                    if (filteredInterval) {
                        outageTimelineIntervals.push({
                            names: filteredInterval.names,
                            startDate: filteredInterval.startDate,
                            endDate: filteredInterval.endDate
                        });
                        
                        totalComponentOutage += filteredInterval.duration;
                    }
                } else {
                    // Если это обычная работа, добавляем в интервалы работ
                    workTimelineIntervals.push({
                        names: [history.name || "Работа"],
                        startDate: period.start,
                        endDate: period.end
                    });
                }
            }
            
            // Обрабатываем периоды простоя (паузы) в рамках истории
            for (let i = 0; i < history.statuses.length - 1; i++) {
                const currentStatus = history.statuses[i];
                const nextStatus = history.statuses[i + 1];
                
                if (currentStatus.status === "pause" && nextStatus.status !== "pause") {
                    const pauseDuration = nextStatus.createdAt - currentStatus.createdAt;
                    
                    if (pauseDuration > 0) {
                        // Создаем интервал простоя
                        const outageInterval = {
                            names: [history.name ? `Пауза: ${history.name}` : "Пауза"],
                            type: "outage",
                            duration: pauseDuration,
                            startDate: currentStatus.createdAt,
                            endDate: nextStatus.createdAt
                        };
                        
                        // Фильтруем интервал простоя для учета только рабочего времени
                        const filteredInterval = timelineUtils.filterOutageIntervalToWorkingHours(outageInterval);
                        
                        // Если после фильтрации остался интервал простоя
                        if (filteredInterval) {
                            outageTimelineIntervals.push({
                                names: filteredInterval.names,
                                startDate: filteredInterval.startDate,
                                endDate: filteredInterval.endDate
                            });
                            
                            totalComponentOutage += filteredInterval.duration;
                        }
                    }
                }
            }
        }
        
        // Обновляем время завершения текущей истории
        for (const status of history.statuses || []) {
            if (status.status === "finish") {
                currentHistoryFinishTime = status.createdAt;
            }
        }
    }
    
    // Обрабатываем простой с момента последней истории, если позиция не завершена
    if (!isFinished && component.newHistory.length > 0) {
        const lastHistory = component.newHistory[component.newHistory.length - 1];
        
        // Пропускаем некоторые типы работ в расчете простоев
        if (lastHistory.type === "job" && (lastHistory.job === 141 || lastHistory.job === 138)) {
            // Пропускаем эти работы
        } else if (lastHistory.statuses && lastHistory.statuses.length > 0) {
            const lastStatus = lastHistory.statuses[lastHistory.statuses.length - 1];
            
            // Если последний статус не "finish", то есть текущий простой
            if (lastStatus.status !== "finish") {
                const currentTime = Math.floor(Date.now() / 1000);
                
                // Создаем интервал простоя с текущего времени
                const outageInterval = {
                    names: ["Простои с последней работы"],
                    type: "outage",
                    duration: currentTime - lastStatus.createdAt,
                    startDate: lastStatus.createdAt,
                    endDate: currentTime
                };
                
                // Фильтруем интервал простоя для учета только рабочего времени
                const filteredInterval = timelineUtils.filterOutageIntervalToWorkingHours(outageInterval);
                
                // Если после фильтрации остался интервал простоя
                if (filteredInterval) {
                    outageTimelineIntervals.push({
                        names: filteredInterval.names,
                        startDate: filteredInterval.startDate,
                        endDate: filteredInterval.endDate
                    });
                    
                    totalComponentOutage += filteredInterval.duration;
                }
            }
        }
    }
    
    return {
        workTimelineIntervals,
        outageTimelineIntervals,
        totalComponentOutage
    };
}

function calculateWorkTimeAndOutAgeHistory(history, componentName) {
    let workTime = 0;
    let outAgeTime = 0;
    let statusTime = 0;
    let workerTime = 0;
    let area = 0;
    let date;
    let taskId = null; // Добавляем поле для ID задачи (ССЗ)
    let formattedDate = null; // Добавляем поле для даты в формате timestamp

    if (history.area) {
        area = history.area;
    }

    if (history.statuses.length === 0) {
        return;
    }

    if (history.type === "info") {
        return;
    } else if (history.type === "job") {
        let jobIdentificator = history.job;
        let job = getJobForIdentificator(jobIdentificator);

        if (!job) {
            return;
        }

        let outAgeJob = false;

        if (job.outage) {
            outAgeJob = job.outage;
        }

        date = utils.dateFromUnixToShortStr(history.createdAt);
        // Сохраняем дату в формате timestamp в секундах
        formattedDate = history.createdAt; // Используем timestamp в секундах напрямую

        let results = calculateStatuses(history.statuses, false);

        if (outAgeJob === true) {
            outAgeTime += results.workTime;
            outAgeTime += results.outAgeTime;
        } else {
            statusTime += results.workTime;
            outAgeTime += results.outAgeTime;
        }
    } else if (history.type === "task") {
        let results = calculateStatuses(history.statuses, true);

        workTime = results.workTime;
        outAgeTime = results.outAgeTime;

        date = utils.dateFromUnixToShortStr(history.createdAt);
        // Сохраняем дату в формате timestamp в секундах
        formattedDate = history.createdAt; // Используем timestamp в секундах напрямую
        
        // Сохраняем ID задачи (ССЗ), если она есть
        if (history.task) {
            taskId = history.task;
        }

        for (let worker of history.workers) {
            if (worker.statuses.length > 0) {
                let workerResult = calculateStatuses(worker.statuses, true);
                workerTime += workerResult.workTime;
            }
        }
    }

    let workStat = {
        name: history.name,
        workTime: workTime,
        workerTime: workerTime,
        outAgeTime: outAgeTime,
        statusTime: statusTime,
        componentName: componentName,
        area: area,
        dateStr: date,
        formattedDate: formattedDate,  // Добавляем дату в формате timestamp в секундах
        taskId: taskId,                // Добавляем ID задачи (ССЗ)
    };

    return workStat;
}

function calculateStatuses(statuses, taskBool) {
    let workTime = 0;
    let outAgeTime = 0;

    let lastTime;
    let lastStatus;

    let options = {
        zone: "Asia/Yekaterinburg",
    };

    let date = DateTime.fromSeconds(statuses[0].createdAt, options);

    for (const status of statuses) {
        if (lastTime) {
            if (status.status === "pause") {
                if (lastStatus === "pause") {
                    continue;
                } else {
                    if (taskBool === true) {
                        let delta = calculateSSZDelta(lastTime, status.createdAt, date);
                        workTime += delta;
                    } else {
                        let timeResult = calculateStatusDelta(lastTime, status.createdAt);
                        workTime += timeResult;
                    }

                    lastStatus = status.status;
                    lastTime = status.createdAt;
                    continue;
                }
            }

            if (status.status !== "finish") {
                if (lastStatus === "pause") {
                    if (taskBool === true) {
                        let delta = calculateSSZDelta(lastTime, status.createdAt, date);
                        outAgeTime += delta;
                    } else {
                        let result = calculateStatusDelta(lastTime, status.createdAt);
                        outAgeTime += result;
                    }

                    lastTime = status.createdAt;
                    lastStatus = status.status;
                    continue;
                } else {
                    continue;
                }
            } else {
                if (lastStatus === "pause") {
                    if (taskBool === true) {
                        let delta = calculateSSZDelta(lastTime, status.createdAt, date);
                        outAgeTime += delta;
                    } else {
                        let result = calculateStatusDelta(lastTime, status.createdAt);
                        outAgeTime += result;
                    }
                } else {
                    if (taskBool === true) {
                        let delta = calculateSSZDelta(lastTime, status.createdAt, date);
                        workTime += delta;
                    } else {
                        let result = calculateStatusDelta(lastTime, status.createdAt);
                        workTime += result;
                    }
                }

                break;
            }
        } else {
            if (status.status === "finish") {
                continue;
            }

            lastTime = status.createdAt;
            lastStatus = status.status;
        }
    }

    return {
        workTime: workTime,
        outAgeTime: outAgeTime
    };
}

function calculateStatusDelta(from, to) {
    let options = {
        zone: "Asia/Yekaterinburg",
    };

    let previousHistoryDateTime = DateTime.fromSeconds(from, options);
    let startNewHistoryDateTime = DateTime.fromSeconds(to, options);

    let currentDay = currentDayBool(previousHistoryDateTime, startNewHistoryDateTime);

    if (currentDay === true) {
        let time = calculateOutAgeDeltaSingleDay(
            from,
            to,
            previousHistoryDateTime
        );
        return time;
    } else {
        let time = calculateTimeDeltaWithSeveralDays(
            previousHistoryDateTime,
            startNewHistoryDateTime
        );
        return time;
    }
}

function calculateSSZDelta(from, to, date) {
    let options = { zone: "Asia/Yekaterinburg" };

    let lanchStart = date.set({ hour: 12, minute: 0, second: 0 });
    let lanchStop = date.set({ hour: 12, minute: 48, second: 0 });

    let lanchStartUnix = lanchStart.toUnixInteger();
    let lanchStopUnix = lanchStop.toUnixInteger();

    let newFrom = from;
    let newTo = to;

    if (from < lanchStopUnix && from > lanchStartUnix) {
        newFrom = lanchStopUnix;
    }

    if (to > lanchStartUnix && to < lanchStopUnix) {
        newTo = lanchStartUnix;
    }

    if (newFrom > newTo) {
        return 0;
    }

    let delta = newTo - newFrom;

    if (newFrom <= lanchStartUnix && newTo >= lanchStopUnix) {
        delta = delta - (lanchStopUnix - lanchStartUnix);
    }

    if (delta < 0) {
        return 0;
    }

    return delta;
}

function calculateTimeDeltaWithSeveralDays(timeFrom, timeTo) {
    let time = 0;

    let options = { zone: "Asia/Yekaterinburg" };

    let dateFrom = timeFrom;
    let dateTo = timeTo;

    let firstDayEnd = dateFrom.set({ hour: 17, minute: 0, second: 0 });
    let timeStartDay = calculateOutAgeDeltaSingleDay(
        timeFrom.toUnixInteger(),
        firstDayEnd.toUnixInteger(),
        dateFrom
    );
    time += timeStartDay;

    let lastDayStart = dateTo.set({ hour: 8, minute: 0, second: 0 });
    let timeLastDay = calculateOutAgeDeltaSingleDay(
        lastDayStart.toUnixInteger(),
        timeTo.toUnixInteger(),
        dateTo
    );
    time += timeLastDay;

    let startDate = dateFrom.plus({ days: 1 }).startOf('day').set({ hour: 8 });
    let endDate = dateTo.minus({ days: 1 }).startOf('day').set({ hour: 17 });

    let curDate = startDate;

    while (curDate <= endDate) {
        const dayOfWeek = curDate.weekday;
        if (dayOfWeek === 5) {
            time += 25920; // 7 часов 12 минут в секундах
        } else if (dayOfWeek !== 6 && dayOfWeek !== 7) {
            time += 29520; // 8 часов 12 минут в секундах
        }
        curDate = curDate.plus({ days: 1 });
    }

    return time;
}

function calculateOutAgeDeltaSingleDay(from, to, date) {
    let options = { zone: "Asia/Yekaterinburg" };

    let finishTime;
    if (date.weekday === 5) {
        finishTime = date.set({ hour: 16, minute: 0, second: 0 });
    } else if (date.weekday > 5) {
        return 0;
    } else {
        finishTime = date.set({ hour: 17, minute: 0, second: 0 });
    }

    let startTime = date.set({ hour: 8, minute: 0, second: 0 });

    let lanchStart = date.set({ hour: 12, minute: 0, second: 0 });
    let lanchStop = date.set({ hour: 12, minute: 48, second: 0 });

    let lanchStartUnix = lanchStart.toUnixInteger();
    let lanchStopUnix = lanchStop.toUnixInteger();
    let finishTimeUnix = finishTime.toUnixInteger();
    let startTimeUnix = startTime.toUnixInteger();

    let newFrom = from;
    let newTo = to;

    if (from < lanchStopUnix && from > lanchStartUnix) {
        newFrom = lanchStopUnix;
    }

    if (to > lanchStartUnix && to < lanchStopUnix) {
        newTo = lanchStartUnix;
    }

    if (newFrom > newTo) {
        return 0;
    }

    if (newTo > finishTimeUnix) {
        newTo = finishTimeUnix;
    }

    if (newFrom < startTimeUnix) {
        newFrom = startTimeUnix;
    }

    let delta = newTo - newFrom;

    if (newFrom <= lanchStartUnix && newTo >= lanchStopUnix) {
        delta = delta - (lanchStopUnix - lanchStartUnix);
    }

    if (delta < 0) {
        return 0;
    }

    return delta;
}

function currentDayBool(dateTime1, dateTime2) {
    let currentDay = false;

    if (dateTime1.year === dateTime2.year) {
        if (dateTime1.month === dateTime2.month) {
            if (dateTime1.day === dateTime2.day) {
                currentDay = true;
            }
        }
    }

    return currentDay;
}

async function getItems() {
    let query = {
        deleted: false,
        new: false,
    };

    const projection = "repairNumber order.equipment mainID finished branch components";

    return await db.getItemsStreamProjection(query, projection);
}

function calculateOutAgeTimeBetweenHistories(status, previousHistoryFinishTime, componentName) {
    if (!status || !previousHistoryFinishTime) {
        return null; // Защита от некорректных входных данных
    }
    
    let options = {
        zone: "Asia/Yekaterinburg",
    };

    let previousHistoryDateTime = DateTime.fromSeconds(previousHistoryFinishTime, options);
    let startNewHistoryDateTime = DateTime.fromSeconds(status.createdAt, options);

    // Если новая история началась раньше, чем закончилась предыдущая, возвращаем null
    if (status.createdAt <= previousHistoryFinishTime) {
        return null;
    }

    let currentDay = currentDayBool(previousHistoryDateTime, startNewHistoryDateTime);

    let outAgeTime = 0;

    if (currentDay === true) {
        // Не считаем простои в выходные дни
        if (previousHistoryDateTime.weekday > 5) {
            return null;
        }

        outAgeTime = calculateOutAgeDeltaSingleDay(
            previousHistoryFinishTime,
            status.createdAt,
            previousHistoryDateTime
        );
    } else {
        outAgeTime = calculateTimeDeltaWithSeveralDays(
            previousHistoryDateTime,
            startNewHistoryDateTime
        );
    }

    if (outAgeTime <= 0) {
        return null;
    }

    // Сохраняем дату в формате timestamp в секундах для интервала между историями
    let formattedDate = previousHistoryFinishTime; // Используем timestamp в секундах

    let workStat = {
        name: "Простои между работами",
        workTime: 0,
        workerTime: 0,
        outAgeTime: outAgeTime,
        statusTime: 0,
        componentName: componentName,
        formattedDate: formattedDate  // Дата в формате timestamp в секундах
    };

    return workStat;
}

/**
 * Вспомогательная функция для расчета времени простоя между историями
 * @param {Number} startTime - время начала простоя
 * @param {Number} endTime - время окончания простоя
 * @param {String} componentName - название компонента
 * @returns {Number} время простоя в секундах
 */
function calculateOutAgeDelta(startTime, endTime, componentName) {
    if (!startTime || !endTime || startTime >= endTime) {
        return 0;
    }
    
    let options = {
        zone: "Asia/Yekaterinburg",
    };

    let startDateTime = DateTime.fromSeconds(startTime, options);
    let endDateTime = DateTime.fromSeconds(endTime, options);

    // Не считаем простои в выходные дни
    if (startDateTime.weekday > 5) {
        return 0;
    }

    let currentDay = currentDayBool(startDateTime, endDateTime);
    let outAgeTime = 0;

    if (currentDay) {
        outAgeTime = calculateOutAgeDeltaSingleDay(
            startTime,
            endTime,
            startDateTime
        );
    } else {
        outAgeTime = calculateTimeDeltaWithSeveralDays(
            startDateTime,
            endDateTime
        );
    }

    return outAgeTime;
}

module.exports = {
    outAgeStatsParse,
};