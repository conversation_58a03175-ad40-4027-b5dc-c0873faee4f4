const mongoose = require("mongoose");

const statsWorkModel = mongoose.Schema( {

    name: String,
    componentName: String,
    area: Number,
    dateStr: String, // dd.MM.yyyy
    workTime: Number,
    workerTime: Number,
    outAgeTime: Number,
    statusTime: Number,
    percentWork: Number,
    percentOutAge: Number,
    formattedDate: Number,
    taskId: String, 
})

const workerStatsModel = mongoose.Schema( {

    workerId: String,
    workCount: Number,
    workerHours: Number,
    works: [statsWorkModel],
})

const dayStatsModel = mongoose.Schema( {

    date: String, // dd.MM.yyyy
    workCount: Number,
    workHours: Number,
    workerHours: Number,
    workers: [workerStatsModel],
})

const statsSSZTimesModel = mongoose.Schema({

    name: String,
    area: Number,
    workerTime: Number,
})

const timeLineModel = mongoose.Schema({
    
    names: [String], // Статусы, причины, названия работ
    type: {
        type: String,
        enum: ["outage","work"],
        message: '{VALUE} is not supported'
    },
    duration: Number,
    startDate: Number,
    endDate: Number,
})

const statsMainTimeLineModel = mongoose.Schema({
    
    name: String,
    componentType: {
        type: String,
        validate: {
            validator: function(v) {
                return v === null || ["stator","rotor","slidingBearing","rollingBearing","transformer","inductor","anchor"].includes(v);
            },
            message: props => `${props.value} is not a valid component type`
        },
        required: false
    },
    main: Boolean,

    timeline: [timeLineModel],
})

const statsOutAgeModelSchema = mongoose.Schema({
    repairNumber: String,
    mainID: String,
    equipment: String,
    archive: Boolean,
    branch: Number,
    outAgeHours: Number,
    statusHours: Number,

    works: [statsWorkModel],
    sszTimes: [statsSSZTimesModel],
    dayStats: [dayStatsModel],
    timelines: [statsMainTimeLineModel],

    countWork: Number,
    countComponents: Number,
    workHours: Number,
    workerHours: Number,
    statorOutAgeHours: Number,
    rotorOutAgeHours: Number,
    bearingOutAgeHours: Number,
    transformerOutAgeHours: Number,

    createdAt: Number,
    updatedAt: Number
}, {
    timestamps: { currentTime: () => Math.floor(Date.now() / 1000) }
})

const OutAgeStat = mongoose.model('OutAgeStat', statsOutAgeModelSchema)
module.exports = OutAgeStat