const mongoose = require("mongoose");

const onBoardStatModelSchema = mongoose.Schema({
    outAgeHours: Number,
    workHours: Number,
    branch: Number,
    countComponents: Number,

    createdAt: Number,
    updatedAt: Number
}, {
    timestamps: { currentTime: () => Math.floor(Date.now() / 1000) }
})

const OnBoardStat = mongoose.model('OnBoardStat', onBoardStatModelSchema)
module.exports = OnBoardStat