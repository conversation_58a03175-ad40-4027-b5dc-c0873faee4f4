const mongoose = require('mongoose')

const jobSszModelSchema = mongoose.Schema({
    id: String,
    name: String,

    createdAt: Number,
    updatedAt: Number
}, {
    timestamps: { currentTime: () => Math.floor(Date.now() / 1000) }
})

const workerSszModelSchema = mongoose.Schema({
    id: String,
    name: String,
    count: String,

    createdAt: Number,
    updatedAt: Number
}, {
    timestamps: { currentTime: () => Math.floor(Date.now() / 1000) }
})

const taskSszModelSchema = mongoose.Schema({
    mainID: String,
    plan: String,
    fact: String,
    job: jobSszModelSchema,
    workers: [workerSszModelSchema],

    createdAt: Number,
    updatedAt: Number
}, {
    timestamps: { currentTime: () => Math.floor(Date.now() / 1000) }
})

const areaSszModelSchema = mongoose.Schema({
    id: String,
    name: String,

    createdAt: Number,
    updatedAt: Number
}, {
    timestamps: { currentTime: () => Math.floor(Date.now() / 1000) }
})

const sszModelSchema = mongoose.Schema({
    id: String,
    number: String,
    date: String,
    idBranch: String,
    areaSsz: areaSszModelSchema,
    tasks: [taskSszModelSchema],
    workers: [workerSszModelSchema],
    version: String,
    updateDate: String,
    updateDateNumbers: Number,
    createdAt: Number,
    updatedAt: Number
}, {
    timestamps: { currentTime: () => Math.floor(Date.now() / 1000) }
})

const Task = mongoose.model('Task', sszModelSchema)
module.exports = Task