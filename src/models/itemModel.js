const workers = require('./workerModel')

const mongoose = require('mongoose')

const videoModelScheme = mongoose.Schema({
    url : String,
    name : String,
    lastEdit : Number,
    createdAt: Number,
    updatedAt: Number
}, {
    timestamps: { currentTime: () => Math.floor(Date.now() / 1000) }
})

const photoModelScheme = mongoose.Schema({
    url : String,
    type : String,
    owner : String,
    area : Number,
    job : Number,
    comment : String,
    createdAt: Number,
    updatedAt: Number
}, {
    timestamps: { currentTime: () => Math.floor(Date.now() / 1000) }
})

const measureModelSchema = mongoose.Schema({
    frontBearing : Number,
    rearBearing : Number,

    frontShaft : Number,
    rearShaft : Number,

    frontShield : Number,
    rearShield : Number,
    
    createdAt: Number,
    updatedAt: Number
}, {
    timestamps: { currentTime: () => Math.floor(Date.now() / 1000) }
})

const orderBranchModelSchema = mongoose.Schema({
    idBranch : String,
    name : String,

    createdAt: Number,
    updatedAt: Number
}, {
    timestamps: { currentTime: () => Math.floor(Date.now() / 1000) }
})

const orderClientModelSchema = mongoose.Schema({
    idClient : String,
    name : String,
    inn : String,

    createdAt: Number,
    updatedAt: Number
}, {
    timestamps: { currentTime: () => Math.floor(Date.now() / 1000) }
})

const orderModelSchema = mongoose.Schema({
    id : String,
    number : String,
    date : String,
    finishDate : String,
    statusOrder : String,
    manager : String,

    branch: orderBranchModelSchema,
    client: orderClientModelSchema,

    rn : String, // repairNumber
    rnOGK : String,
    name : String,
    equipment : String,
    power: String,
    voltage: String,
    turnovers: String,
    weight: String,
    voltageVN: String,
    voltageNN: String,
    voltageSN: String,
    amperage: String,
    version: String,
    updateDate: String,
    updateDateNumbers: Number,
    status : String,
    comment: String,

    createdAt: Number,
    updatedAt: Number
}, {
    timestamps: { currentTime: () => Math.floor(Date.now() / 1000) }
})

const componentStatusModel = mongoose.Schema( {

    comment: String,
    owner: String,

    status: {
        type: String,
        enum: ["inwork","finish","pause","info"], // расширить при добавлении новых типов
        message: '{VALUE} is not supported'
    },

    createdAt: Number,
    updatedAt: Number

}, {
    timestamps: { currentTime: () => Math.floor(Date.now() / 1000) }
})

const newHistoryModelSchema = mongoose.Schema({

    name: String,

    area: Number,
    job: Number,
    task: String,
    taskID: String,
    identifier: String,
    histMainID: String, // если в двигателе объединено несколько ЗПР, то этот параметр поможет определить к какому ЗПР относится работа (ССЗ)

    type: {
        type: String,
        enum: ["task","job","info"], // расширить при добавлении новых типов
        message: '{VALUE} is not supported'
    },

    statuses: [componentStatusModel],
    workers: [workers.schema],

    createdAt: Number,
    updatedAt: Number
}, {
    timestamps: { currentTime: () => Math.floor(Date.now() / 1000) }
})

const itemComponentModelSchema = mongoose.Schema({
    repairNumber: String,
    identifier: {
        type: String,
        required: true,
        index: false
    },
    type: {
        type: String,
        enum: ["stator","rotor","slidingBearing","rollingBearing","transformer","inductor","anchor"], // расширить при добавлении новых типов
        message: '{VALUE} is not supported'
    },

    currentArea : Number,
    accessArea : [Number],
    newHistory : [newHistoryModelSchema],

    createdAt: Number,
    updatedAt: Number
}, {
    timestamps: { currentTime: () => Math.floor(Date.now() / 1000) }
})

// Define enum at the top of the file
const TagEnum = {
    DEFAULT : "default",
    URGENT : "urgent",
    IMPORTANT : "important",
    ON_HOLD : "on_hold",
    WARRANTY : "warranty"
}

const itemModelSchema = mongoose.Schema({
    repairNumber: {
        type: String,
        required: true
    },
    mainID: {
        type: String,
        index: true,
        unique: true
    },
    altIds : [String],
    altRN : [String],
    major: Boolean,
    endDate: Number,
    finished: {
        type: Boolean,
        default: false
    },
    mark: {
        type: Boolean,
        default: false
    },
    deleted: {
        type: Boolean,
        default: false
    },
    finishDate: Number,
    finish1cDate: Number,
    branch: {
        type: Number,
    },
    order: orderModelSchema,
    photos: [photoModelScheme],
    videosTest: [videoModelScheme],
    new: {
        type: Boolean,
        default: false
    },
    assembled : {
        type: Boolean,
        default: false
    },
    tags: [{
        type: String,
        enum: Object.values(TagEnum)
    }],
    measure: measureModelSchema,
    components: [itemComponentModelSchema],
    createdAt: Number,
    updatedAt: Number
}, {
    timestamps: { currentTime: () => Math.floor(Date.now() / 1000) }
})

const Item = mongoose.model('Item', itemModelSchema)
module.exports = Item