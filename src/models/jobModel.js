const mongoose = require('mongoose')

const jobModelSchema = mongoose.Schema({
    identifier: {
        type: Number,
        unique: true,
        required: true
    },
    name: String,
    outage : { // считать статус простоем или нет
        type: Boolean,
        default: false
    },
    createdAt: Number,
    updatedAt: Number
}, {
    timestamps: { currentTime: () => Math.floor(Date.now() / 1000) }
})

const Job = mongoose.model('Job', jobModelSchema)
module.exports = Job