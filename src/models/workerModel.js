const mongoose = require("mongoose");

const workerStatusModel = mongoose.Schema( {

    status: {
        type: String,
        enum: ["inwork","finish","pause"], // расширить при добавлении новых типов
        message: '{VALUE} is not supported'
    },

    createdAt: Number,
    updatedAt: Number

}, {
    timestamps: { currentTime: () => Math.floor(Date.now() / 1000) }
})

const workerModelSchema = mongoose.Schema({
    id: String,
    name: String,
    areas: [String],
    branch: String,

    statuses: [workerStatusModel],

    createdAt: Number,
    updatedAt: Number
}, {
    timestamps: { currentTime: () => Math.floor(Date.now() / 1000) }
})

const Worker = mongoose.model('Worker', workerModelSchema)
module.exports = Worker