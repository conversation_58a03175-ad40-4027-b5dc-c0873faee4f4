const mongoose = require('mongoose')

const userTaskStatusModelScheme = mongoose.Schema( {

    comment: String,
    owner: String,

    status: {
        type: String,
        enum: ["created","inwork","finished"], // расширить при добавлении новых типов
        message: '{VALUE} is not supported'
    },

    createdAt: Number,
    updatedAt: Number

}, {
    timestamps: { currentTime: () => Math.floor(Date.now() / 1000) }
})

const userTaskModelSchema = mongoose.Schema({

    area:Number,
    branch:Number,
    nameArea: String,
    login: String,

    statuses: [userTaskStatusModelScheme],

    createdAt: Number,
    updatedAt: Number
}, {
    timestamps: { currentTime: () => Math.floor(Date.now() / 1000) }
})

const UserTask = mongoose.model('UserTask', userTaskModelSchema)
module.exports = UserTask