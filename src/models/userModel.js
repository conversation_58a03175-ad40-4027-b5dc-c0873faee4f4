const mongoose = require('mongoose')

const userModelSchema = mongoose.Schema({
    name: String,
    login: {
        type: String,
        index: true,
        unique: true,
        lowercase: true // Always convert to lowercase
    },
    password: String,
    role: {
        type: String,
        enum: ["admin","manager","director","user","client","block","station"], // расширить при добавлении новых типов
        message: '{VALUE} is not supported'
    },
    userType: {
        type: String,
        enum: ["distributor","controlMaster","mainEngineer","headArea","pdo","empty"], // расширить при добавлении новых типов
        message: '{VALUE} is not supported'
    },
    area:Number,
    branch:Number,
    subscribe:[String],
    subscribeSSZ:[String],
    subscribeZPR: {
        type: Boolean,
        default: false
    },
    subscribeItems: [String],
    subscribeOutage: [String],
    simpleJobAdd : {
        type: Boolean,
        default: false
    },
    createdAt: Number,
    updatedAt: Number
}, {
    timestamps: {
        currentTime: () => Math.floor(Date.now() / 1000)
    }
})

const User = mongoose.model('User', userModelSchema)
module.exports = User