const mongoose = require('mongoose')

const branchModelSchema = mongoose.Schema({
    identifier: {
        type: Number,
        unique: true,
        required: true
    },
    name: String,
    shortName : {
        type: String,
        unique: true
    },
    idBranch : String,
    createdAt: Number,
    updatedAt: Number
}, {
    timestamps: { currentTime: () => Math.floor(Date.now() / 1000) }
})

const Branch = mongoose.model('Branch', branchModelSchema)
module.exports = Branch