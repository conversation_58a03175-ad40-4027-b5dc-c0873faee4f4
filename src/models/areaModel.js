const mongoose = require('mongoose')

const areaModelSchema = mongoose.Schema({
    identifier: {
        type: Number,
        unique: true,
        required: true
    },
    name: String,
    jobs: [Number],
    branch: {
        type: Number,
        required: true
    },
    start: {
        type: Boolean,
        default: false
    },
    idArea: {
        type: String
    },
    stationCallLogins : [String],
    createdAt: Number,
    updatedAt: Number
}, {
    timestamps: { currentTime: () => Math.floor(Date.now() / 1000) }
})

const Area = mongoose.model('Area', areaModelSchema)
module.exports = Area