// initialize database connection
const mongoose = require("mongoose");
const { credentials } = require("../config");

const connectionString = credentials.mongo.connectionString;

if (!connectionString) {
  console.error("MongoDB connection string missing!");
  process.exit(1);
}

console.log("DB connect to: " + credentials.mongo.dbName);

mongoose.connect(connectionString, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});
const db = mongoose.connection;
db.on("error", (err) => {
  console.error("MongoDB error: " + err.message);
  process.exit(1);
});
db.once("open", () => console.log("MongoDB connection established"));

const AreaModel = require("./models/areaModel");
const BranchModel = require("./models/branchModel");
const ItemModel = require("./models/itemModel");
const JobModel = require("./models/jobModel");
const UserModel = require("./models/userModel");
const TaskModel = require("./models/sszModel");
const WorkerModel = require("./models/workerModel");
const NotificationModel = require("./models/notificationModel");
const UserTaskModel = require("./models/userTaskModel");
const OutAgeStatModel = require("./models/statsOutAgeModel");
const OnBoardStatModel = require("./models/onboardStatModel");

module.exports = {
  // Методы для работы с участками
  getAreas: async (query = {}) => AreaModel.find(query),
  getArea: async (query = {}) => AreaModel.findOne(query),
  findLastArea: async (query = {}) =>
    AreaModel.findOne(query).sort({ identifier: -1 }),
  addArea: async (area = {}) => AreaModel.create(area),
  editArea: async (area) => {
    await AreaModel.updateOne({ identifier: area.identifier }, { $set: area });
  },

  // Методы для работы с статусами (job старое название)
  getJobs: async (query = {}) => JobModel.find(query),
  getJob: async (query = {}) => JobModel.findOne(query),
  findLastJob: async (query = {}) =>
    JobModel.findOne(query).sort({ identifier: -1 }),
  addJob: async (job = {}) => JobModel.create(job),
  editJob: async (job) => {
    await JobModel.updateOne({ identifier: job.identifier }, { $set: job });
  },

  // Методы для работы c филиалами
  getBranches: async (query = {}) => BranchModel.find(query),
  getBranch: async (query = {}) => BranchModel.findOne(query),
  findLastBranch: async (query = {}) =>
    BranchModel.findOne(query).sort({ identifier: -1 }),
  addBranch: async (branch = {}) => BranchModel.create(branch),
  editBranch: async (branch) => {
    await BranchModel.updateOne(
      { identifier: branch.identifier },
      { $set: branch }
    );
  },

  // Методы для работы c двигателями (или ЗПР - заказы на производство)
  getItems: async (query, sort, limit, offset, projection) =>
    ItemModel.find(query, projection).sort(sort).skip(offset).limit(limit),
  //getItemsAggregate: async (pipeline = []) => ItemModel.aggregate(pipeline),
  getItemsStreamProjection: (query, projection) =>
    ItemModel.find(query, projection).cursor(),
  findItem: async (query, sort, projection) =>
    ItemModel.findOne(query, projection).sort(sort),
  getItem: async (mainID) => ItemModel.findOne({ mainID: mainID }),
  getItemOld: async (repairNumber = {}) =>
    ItemModel.findOne({ repairNumber: repairNumber }),
  addItem: async (item = {}) => ItemModel.create(item),
  editItem: async (item = {}) =>
    ItemModel.updateOne({ mainID: item.mainID }, { $set: item }),
  updateItem: async (mainID, update) =>
    ItemModel.updateOne({ mainID: mainID }, update),
  getItemsWithProjection: async (query, projection) =>
    ItemModel.find(query, projection),
  getCountItems: async (query) => ItemModel.count(query),

  // Методы для работы с пользователями

  getUsers: async (query = {}) => UserModel.find(query),
  getUsersProj: async (query, projection) => UserModel.find(query, projection),
  getUser: async (query = {}) => UserModel.findOne(query),
  editUser: async (login, update) =>
    UserModel.updateOne({ login: login }, update),
  createUser: async (user = {}) => UserModel.create(user),

  // Методы для работы с ССЗ (сменосуточные задания)

  createTask: async (task = {}) => TaskModel.create(task),
  getTask: async (query, sort = {}) => TaskModel.findOne(query).sort(sort),
  getTasks: async (query = {}, projection = null, limit = 0) => {
    if (projection) {
      return limit > 0 
        ? TaskModel.find(query, projection).limit(limit) 
        : TaskModel.find(query, projection);
    }
    return TaskModel.find(query);
  },
  removeTask: async (query = {}) => TaskModel.findOneAndRemove(query),

  // Методы для работы с сотрудниками

  createWorker: async (worker = {}) => WorkerModel.create(worker),
  getWorker: async (query = {}) => WorkerModel.findOne(query),
  getWorkers: async (query = {}) => WorkerModel.find(query),

  // Методы для работы с уведомлениями

  getNotifications: async (query, limit, offset) =>
    NotificationModel.find(query)
      .sort({ createdAt: -1 })
      .skip(offset)
      .limit(limit),
  createNotification: async (notif = {}) => NotificationModel.create(notif),

  // Методы для работы с заданиями пользователя

  getUserTasks: async (query, limit) =>
    UserTaskModel.find(query).sort({ createdAt: -1 }).limit(limit),
  editUserTask: async (id, update) =>
    UserTaskModel.updateOne({ _id: id }, update),
  createUserTask: async (task = {}) => UserTaskModel.create(task),

  // Методы для работы с статистикой работ/простоев

  getOutAgeStats: async (query, projections) =>
    OutAgeStatModel.find(query, projections),
  getOutAgeStatsAggreagete: async (pipeline, options) =>
    OutAgeStatModel.aggregate(pipeline, options),
  getOneOutAgeStat: async (query, projections) =>
    OutAgeStatModel.findOne(query, projections),
  createOutAgeStats: async (stats) => OutAgeStatModel.create(stats),
  removeAllOutAgeStats: async () => OutAgeStatModel.deleteMany({}),

  // Методы для работы с статистикой (Onboard)

  createOnBoardStat: async (stat) => OnBoardStatModel.create(stat),
  getOnBoardStat: async (query) => OnBoardStatModel.findOne(query),
  getOnBoardStats: async (query) => OnBoardStatModel.find(query),
  updateOnBoardStat: async (id, update) =>
    OnBoardStatModel.updateOne({ _id: id }, update),
};
