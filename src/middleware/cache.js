const redisClient = require('../utils/redis');
const { credentials } = require('../../config');

/**
 * Middleware для кеширования ответов API
 * @param {String} resourceKey - Ключ ресурса для кеширования
 * @param {Number} ttl - Время жизни кеша в секундах
 * @returns {Function} Express middleware
 */
const cacheMiddleware = (resourceKey, defaultTtl = 3600) => {
  // Проверяем, включено ли кеширование для данного ресурса в конфигурации
  const isEnabled = credentials.redis?.caching?.enabled !== false;
  
  if (!isEnabled) {
    // Если кеширование отключено, просто передаем управление дальше
    return (req, res, next) => next();
  }

  return async (req, res, next) => {
    try {
      // Создаем уникальный ключ на основе ресурса и параметров запроса
      const cacheKey = redisClient.generateCacheKey(resourceKey, req.body);
      
      // Проверяем, есть ли данные в кеше
      const cachedData = await redisClient.get(cacheKey);
      if (cachedData) {
        console.log(`Cache hit for ${resourceKey}`);
        return res.send(cachedData);
      }

      // Сохраняем оригинальный метод res.send
      const originalSend = res.send;

      // Перехватываем ответы для сохранения в кеш
      res.send = function(body) {
        if (res.statusCode === 200 && body) {
          const ttl = credentials.redis?.caching?.[resourceKey] || defaultTtl;
          redisClient.set(cacheKey, body, ttl)
            .catch(err => console.error(`Cache set error for ${cacheKey}:`, err));
        }
        
        // Вызываем оригинальный метод
        return originalSend.apply(this, arguments);
      };

      next();
    } catch (error) {
      console.error('Cache middleware error:', error);
      next();
    }
  };
};

/**
 * Middleware для очистки кеша при изменении данных
 * @param {String} pattern - Шаблон ключа для очистки
 */
const clearCache = (pattern) => {
  return async (req, res, next) => {
    try {
      const originalSend = res.send;
      
      // Перехватываем ответ для очистки кеша после успешного обновления
      res.send = function(body) {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          redisClient.clearByPattern(pattern)
            .catch(err => console.error(`Cache clear error for ${pattern}:`, err));
        }
        
        // Вызываем оригинальный метод
        return originalSend.apply(this, arguments);
      };
      
      next();
    } catch (error) {
      console.error('Clear cache middleware error:', error);
      next();
    }
  };
};

module.exports = {
  cacheMiddleware,
  clearCache
};
