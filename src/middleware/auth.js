const auth = require('../handlers/auth');

// Фабрика middleware для авторизации с различными уровнями доступа
const authMiddleware = {
  // Базовая проверка секретного ключа приложения
  appKey: auth.checkAppKey,
  
  // Проверка аутентификации пользователя
  user: auth.authCheck,
  
  // Проверка прав администратора
  admin: [auth.authCheck, auth.checkAdminRole],
  
  // Проверка прав администратора, менеджера или директора
  adminManagerDirector: [auth.authCheck, auth.checkAdminManagerDirectorRole],
  
  // Кастомная авторизация с комбинацией проверок
  custom: (checks) => {
    if (!Array.isArray(checks)) {
      checks = [checks];
    }
    return checks;
  }
};

module.exports = authMiddleware;
