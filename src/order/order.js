const fs = require('fs');
var convert = require('xml-js');
const db = require('../db')
const ItemModel = require('../models/itemModel')
const utils = require('../utils')
const pushes = require('../handlers/pushes')
const util = require('util');
const readFileAsync = util.promisify(fs.readFile);

const pathPrefix = "/z_ftp/uer-data/mobil/zpr/" // /mnt/server.disk/mobil/zpr/

// async function fixAllItems() { // скрипт для каких либо фиксов в истории работ

//     let query = {}

//     var sort = {updatedAt: -1}
//     const items = await db.getItems(query, sort)

//     console.log(items.length)

//     for (let j = 0; j < items.length; j++) {
//         let item = items[j];

//         let needArchive = true

//         let lastDate = 0;

//         if (item.components.length === 0) {
//             needArchive = false
//         } else {
//             for (let i = 0; i < item.components.length; i++) {
//                 let comp = item.components[i];

//                 let lastHistory = comp.history[comp.history.length - 1]

//                 if (lastHistory.job !== 138) { // 138 - это ИД работы Отгрузки, если все компоненты отгружены то в архив
//                     needArchive = false
//                 } else {
//                     let dateLastUpdate = comp.updatedAt

//                     if (dateLastUpdate >= lastDate) {
//                         lastDate = dateLastUpdate
//                     }
//                 }
//             }
//         }

//         let updateDateUnix;

//         if (item.order) {
//             if (item.order.updateDate) {
//                 let dateStr = item.order.updateDate.split(" ")[0]
//                 let updateDateArr = dateStr.split(".")
//                 let updateDateParsed = Date.parse(updateDateArr[2] + "-" + updateDateArr[1] + "-" + updateDateArr[0]);
//                 updateDateUnix = Math.floor(updateDateParsed / 1000)
//             }
//         }

//         if (item.order) {
//             if (item.order.status === "G") { // если статус заказа Готов
//                 console.log("Статус позиции готов >")
//                 item.finish1cDate = updateDateUnix
//             }
//         }

//         console.log("Need Archive:" + needArchive)

//         if (needArchive === true) {
//             item.finished = true
//             item.new = false

//             if (lastDate > 0) {
//                 item.finishDate = lastDate
//             }

//         } else {
//             item.finished = false
//         }

//         if (item.new === true) {
//             //console.log("Item New - True")
//         } else {
//             item.new = false
//             console.log("Item New - False")
//         }

//         const result = await item.save();
//     }
// }

async function readUpdateFile(updateRewrite) {

    log("Начало обработки журнала ЗПР");

    // Получаем timestamp начала текущего дня
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    // Преобразуем в формат даты, используемый в журнале (ГГГММДДЧЧММСС)
    let lastUpdate = parseInt(today.getFullYear().toString() +
        (today.getMonth() + 1).toString().padStart(2, '0') +
        today.getDate().toString().padStart(2, '0') +
        "000000");
    
    log(`Используем дату начала текущего дня: ${lastUpdate}`);

    if (updateRewrite) {
        lastUpdate = updateRewrite;
        log(`Переопределена дата обновления: ${lastUpdate}`);
    }

    // Преобразование строки lastUpdate в объект Date
    let lastUpdateDate = new Date(
        parseInt(String(lastUpdate).slice(0, 4)),
        parseInt(String(lastUpdate).slice(4, 6)) - 1,
        parseInt(String(lastUpdate).slice(6, 8)),
        parseInt(String(lastUpdate).slice(8, 10)),
        parseInt(String(lastUpdate).slice(10, 12)),
        parseInt(String(lastUpdate).slice(12, 14))
    );

    //lastUpdateDate.setMinutes(lastUpdateDate.getMinutes() - 15); // фикс если последние даты в журнале перепутаны и сохранены в неправильном порядке

    //console.log("Start Read zprout: " + Date.now())

    let zproutPath = pathPrefix + 'zprout.txt';

    // Добавление проверки наличия файла перед чтением
    if (fs.existsSync(zproutPath)) {
        log(`Файл ${zproutPath} найден. Продолжаем...`);
    } else {
        log(`Файл ${zproutPath} не найден. Ожидание...`);
        return;
    }

    try {
        const data = await readFileAsync(zproutPath);
        log(`Файл журнала ЗПР прочитан успешно`);

        let fileRows = data.toString('utf8').split('\r\n');
        log(`Всего строк в журнале: ${fileRows.length}`);

        // Создаем Map для хранения уникальных заказов, используя ID заказа как ключ
        let uniqueOrders = new Map();  // Ключ: ID, Значение: {row: строка, time: время, status: статус}

        for (let i = fileRows.length-1; i > 0; i--) {
            let row = fileRows[i];
            if (!row.trim()) continue;  // Пропускаем пустые строки

            let objArr = row.split(";");
            if (objArr.length < 5) continue;  // Проверка на правильный формат строки

            let time = String(objArr[1]);
            let id = objArr[2];
            let status = objArr[4];
            
            if (parseInt(time) <= lastUpdate) {
                log(`Достигнута дата начала текущего дня (${lastUpdate}), прекращаем обработку журнала`);
                break; // Прекращаем обработку, так как все оставшиеся записи будут еще старее
            }

            // Если заказ с таким ID уже встречался, обновляем только если новая запись новее
            if (!uniqueOrders.has(id) || parseInt(time) > parseInt(uniqueOrders.get(id).time)) {
                uniqueOrders.set(id, { row: objArr, time: time, status: status });
            }
        }

        log(`Найдено ${uniqueOrders.size} уникальных заказов для обработки`);
        
        // Обрабатываем каждый уникальный заказ
        let processed = 0;
        let errors = 0;
        let skipped = 0;

        for (const [id, data] of uniqueOrders.entries()) {
            let objArr = data.row;
            let time = data.time;  
            let status = data.status;
            let path = objArr[3];
            let comment = objArr[5];

            // Проверяем, есть ли уже этот заказ в базе с более новой датой обновления
            const existingItem = await db.findItem({"mainID": id});
            if (existingItem && existingItem.order && existingItem.order.updateDateNumbers >= parseInt(time)) {
                //log(`Заказ ${id} уже существует с более новой или равной датой обновления (${existingItem.order.updateDateNumbers}), пропускаем`);
                skipped++;
                continue;
            }

            let pathArr = String(path).split("doc\\");

            if (pathArr[1] === undefined) {
                log(`Некорректный путь для заказа ${id}: ${path}`);
                skipped++;
                continue;
            }

            let pathXml = pathPrefix + "doc/" + pathArr[1];

            // Проверяем существование файла
            if (!fs.existsSync(pathXml)) {
                log(`Файл для заказа ${id} не найден: ${pathXml} - будет обработан в следующем цикле`);
                skipped++;
                continue;
            }

            try {
                let success = await readXmlForPath(pathXml, time, status, comment);
                if (success) {
                    processed++;
                    log(`Заказ ${id} успешно обработан`);
                } else {
                    log(`Ошибка при обработке заказа ${id}: файл не удалось прочитать`);
                    errors++;
                }
            } catch (error) {
                log(`Ошибка при обработке заказа ${id}: ${error.message}`);
                errors++;
            }
        }

        log(`Обработка завершена. Обработано: ${processed}, пропущено: ${skipped}, ошибок: ${errors}`);
    } catch (err) {
        console.error("Ошибка при чтении файла ZPROUT.txt:", err);
    } 
}

// Добавим функцию логирования для лучшего отслеживания процесса
const log = (message) => {
    console.log(`[${new Date().toISOString()}] ${message}`);
};

async function readXmlForPath(path, time, status, comment) {

    if (fs.existsSync(path)) {
        //console.log(`Файл ${path} найден. Продолжаем...`);
    } else {
        console.log(`Файл ${path} не найден. Ожидание...`);
        return false;
    }

    try {
        const data = await readFileAsync(path);

        let str = data.toString()

        let splitStr = str.split("</КоммерческаяИнформация>")
        let newStr = splitStr[0] + "</КоммерческаяИнформация>"

        let options = {compact: true, trim: true};
        let json = convert.xml2js(newStr, options)

        let body = json["КоммерческаяИнформация"]

        //console.log(body)

        if (!body["Ремномер"]) {
            console.log("Ошибка нет ремномера: " + path)
            console.log("Пропуск файла: " + path)
            return true
        }

        let order = {
            id: body["Ид"]["_text"],
            number: body["Номер"]["_text"],
            date: body["Дата"]["_text"],
            finishDate: body["ДатаИсполнения"]["_text"],
            branch: {
                idBranch: body["Филиал"]["ИдФилиал"]["_text"],
                name: body["Филиал"]["НаименованиеФилиал"]["_text"]
            },
            rn: body["Ремномер"]["_text"],
            rnOGK: body["РемномерОГК"]["_text"],
            name: body["Номенклатура"]["_text"],
            equipment: body["Оборудование"]["_text"],
            power: body["Мощность"]["_text"],
            voltage: body["Напряжение"]["_text"],
            turnovers: body["Обороты"]["_text"],
            weight: body["Вес"]["_text"],
            voltageVN: body["НапряжениеВН"]["_text"],
            voltageNN: body["НапряжениеНН"]["_text"],
            voltageSN: body["НапряжениеСН"]["_text"],
            amperage: body["Ток"]["_text"],
            version: body["_attributes"]["ВерсияСхемы"],
            updateDate: body["_attributes"]["ДатаФормирования"],
            updateDateNumbers: Number(time),
            status: status,
            comment: comment
        }

        if (body["Контрагент"]) {

            let client = {}

            if (body["Контрагент"]["ИдКонтрагент"]) {
                client.idClient = body["Контрагент"]["ИдКонтрагент"]["_text"]
            }

            if (body["Контрагент"]["НаименованиеКонтрагент"]) {
                client.name = body["Контрагент"]["НаименованиеКонтрагент"]["_text"]
            }

            if (body["Контрагент"]["ИННКонтрагент"]) {
                client.inn = body["Контрагент"]["ИННКонтрагент"]["_text"]
            }

            order.client = client
        }

        if (body["Менеджер"]) {
            order.manager = body["Менеджер"]["_text"]
        }

        if (body["Статус"]) {
            order.statusOrder = body["Статус"]["_text"]
        }

        order = utils.clearEmpties(order)

        //console.log(order)

        //console.log("End read: " + path)

        await orderLogic(order)
        return true
    } catch (err) {
        console.error("Ошибка при чтении файла XML:", err);
    }

    return false
}

async function orderLogic(order) {

    let mainID = order.id
    const item = await db.getItem(mainID)

    if (item) {
        //console.log("Есть в базе")
        let result = await editOrder(order,item)
        console.log("Редактируем заказ: ", result)
        return result
    }

    let result = await editOrder(order)
    console.log("Добавляем новый заказ: ", result)
    return result;
}

async function editOrder(order,item) {

    const branches = await db.getBranches()

    let finishDate = order.finishDate
    let branchID = order.branch.idBranch
    let rn = order.rn
    let ourBranchID;

    let finishDateArr = finishDate.split(".")
    let date = Date.parse(finishDateArr[2] + "-" + finishDateArr[1] + "-" + finishDateArr[0]);
    let unixEndDate = Math.floor(date / 1000)

    for (let i = 0; i < branches.length; i++) {
        let branch = branches[i]
        if (branch.idBranch === branchID) {
            ourBranchID = branch.identifier
            break
        }
    }

    let updateDateUnix;

    if (order.updateDate) {
        let dateStr = order.updateDate.split(" ")[0]
        let updateDateArr = dateStr.split(".")
        let updateDateParsed = Date.parse(updateDateArr[2] + "-" + updateDateArr[1] + "-" + updateDateArr[0]);
        updateDateUnix = Math.floor(updateDateParsed / 1000)
    }

    if (item) {

        if (order.status === "G") { // если статус заказа Готов
            if (updateDateUnix) {
                item.finish1cDate = updateDateUnix
            }
        }

        if (order.status === "D") { // убрать в удаленные
            item.deleted = true
        }

        if (order.status === "E") { // статус закрыт, убрать в архив
            item.finished = true
        }

        item.order = order

        if (!item.mainID) {
            item.mainID = order.id
        }

        item.repairNumber = rn
        item.branch = ourBranchID
        item.endDate = unixEndDate

        const result = await item.save();

        if (result instanceof ItemModel) {
            //console.log("editOrder success")

            return true
        } else {
            //console.log("editOrder fail")
            return false
        }

    } else {

        //console.log("add item func")

        let newItem = {
            components: [],
            mainID: order.id,
            repairNumber: rn,
            order: order,
            endDate: unixEndDate,
            branch: ourBranchID,
            finished: false,
            deleted: false,
            tags: ["default"],
            new: true,
            major: false
        }

        if (order.status === "G") { // если статус заказа Готов
            if (updateDateUnix) {
                newItem.finish1cDate = updateDateUnix
            }
        }

        if (order.status === "D") {
            newItem.deleted = true
            newItem.new = false
        }

        if (order.status === "E") { // статус закрыт, убрать в архив
            newItem.finished = true
        }

        const result = await db.addItem(newItem)

        if (result instanceof ItemModel) {

            await pushes.newZPRNotification(newItem)
            return true
        }

        return false
    }
}

module.exports = {
    readUpdateFile,
}