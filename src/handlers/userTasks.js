const db = require('../db')
const pushes = require('./pushes')
const mongoose = require('mongoose')

module.exports = {

    async get(req,res) {

        let body = req.body

        let login = body.login
        const cursor = body.cursor
        const limit = parseInt(body.limit)

        //console.log(body)

        let query = {
            login: login
        }

        let models

        if (cursor) {

            query.createdAt = {
                $lt: cursor,
            }

            models = await db.getUserTasks(query,limit+1)

        } else {
            models = await db.getUserTasks(query,limit+1)
        }

        //console.log(query)

        const max = models.length === limit + 1
        let nextCursor = null
        if (max) {
            const record = models[limit]
            nextCursor = record.createdAt
            models.pop()
        }

        let result = {
            data: models,
            nextCursor: nextCursor,
            max: max
        }

        //console.log(result.data)
        // console.log(result)

        res.send(result)
    },

    async getCurrent(req,res) {
        let body = req.body
        let area = body.area
        let branch = body.branch

        let query = {
            area: area,
            branch: branch,
            "statuses.status" : {$ne: "finished"} // "created","inwork","finished"
        }

        let models = await db.getUserTasks(query,20)
        res.send(models)
    },

    async create(req,res) {

        let body = req.body

        let login = body.login
        let area = body.area
        let branch = body.branch
        let nameArea = body.nameArea
        let owner = body.owner

        let status = {
            owner: owner,
            status: "created",
        }

        let task = {
            login: login,
            area: area,
            branch: branch,
            nameArea: nameArea,
            statuses: [status]
        }

        let result = await db.createUserTask(task)
        console.log(result)

        let title = "Новый вызов - " + nameArea
        let message = "Требуется ваше участие на участке - " + nameArea

        await pushes.sendNotificationWith(login,title,message)

        res.send(result)
    },

    async updateStatus(req,res) {
        let body = req.body
        let taskId = body.id
        let status = body.status

        let id = new mongoose.Types.ObjectId(taskId)
        let updateQuery = {$push: {statuses: status}}

        let result = await db.editUserTask(id,updateQuery)

        res.send(result)
    }
}