const fs = require('fs');
const db = require('../db')
const photoUpload = require('../../photoUpload')
const ItemModel = require('../models/itemModel')
const formidable = require('formidable');
const path = require("path");
//const pushes = require("./pushes");
const mongoose = require('mongoose')
const crypto = require('crypto');

// const pathForPhoto = '/z_ftp/uer-data/mobil/foto/zpr' // /mnt/server.disk/mobil/foto/zpr/

const pathForPhoto = '/Users/<USER>/Image/temp' // test

const isFileValid = (file) => {
    const type = file.mimetype.split("/").pop();
    const validTypes = ["jpg", "jpeg"];
    if (validTypes.indexOf(type) === -1) {
        return false;
    }
    return true;
};

function fixID(mainID) {
    let newID = mainID

    if (mainID.indexOf("/") !== -1) { // fix for ID with slash
        let textArr = mainID.split("/")
        newID = textArr[0] + "_" + textArr[1]
    }

    return newID
}

function makeDirSync(filePath) {
    const directoryname = path.dirname(filePath);
    console.log(directoryname)
    if (fs.existsSync(directoryname)) {
        return;
    }
    fs.mkdirSync(directoryname, { recursive: true });
};

module.exports = {

    async upload(req, res) {

        let uploadPath = pathForPhoto //__dirname + '/../../../uploads'

        makeDirSync(uploadPath + "/x")

        console.log(uploadPath)

        let options = {
            maxFileSize: 50 * 1024 * 1024, // 50mb
            uploadDir: uploadPath
        }

        const form = formidable(options);

        form.parse(req, (err, fields, files) => {
            if (err) {
                console.log(err)
                res.status(400)
                res.send("Error parse photo data")
                return;
            }

            console.log(fields)
            let count = fields.count
            let mainID = fields.mainID

            let validPhotos = [];

            for (let i = 1; i <= count; i++) {
                let file = files[i]

                let isValid = isFileValid(file)

                let newID = fixID(mainID)

                const randomString = crypto.randomBytes(8).toString('hex');
                let newpath = uploadPath + path.join('/', newID, (randomString + ".jpg"))

                console.log(newpath)
                makeDirSync(newpath)

                if (isValid) {

                    try {
                        // renames the file in the directory
                        fs.renameSync(file.filepath, newpath);
                        file.filepath = newpath

                        validPhotos.push(file)
                        //console.log("rename")
                    } catch (error) {
                        console.log(error)
                    }
                }
            }

            updateItemModelWithPhoto(validPhotos, fields, res)

            //console.log(files)

            //res.json({ fields, files });
        });
    },

    async editCommentToPhoto(req, res) {
        const { id: photoID, mainID, comment } = req.body;

        if (!photoID) {
            return res.status(400).send("Error. PhotoID is required.");
        }

        if (!mainID) {
            return res.status(400).send("Error. MainID is required.");
        }

        if (!comment) {
            return res.status(400).send("Error. Comment is required.");
        }

        if (!mongoose.Types.ObjectId.isValid(photoID)) {
            return res.status(400).send("Error. Invalid PhotoID format.");
        }

        try {
            // Получаем документ по mainID
            let item = await db.getItem(mainID);

            if (!item) {
                return res.status(404).send("Error. Item not found.");
            }

            // Находим элемент в массиве photos с нужным _id
            const photo = item.photos.find(p => p._id.toString() === photoID);

            if (!photo) {
                return res.status(404).send("Error. Photo not found in item.");
            }

            // Обновляем комментарий для найденного элемента
            photo.comment = comment;

            // Сохраняем изменения в документе
            await item.save();

            res.send(item);
        } catch (error) {
            console.error('Error updating comment:', error);
            res.status(500).send("Server error.");
        }
    }
}

async function updateItemModelWithPhoto(photos, fields, res) {

    let mainID = fields.mainID
    let owner = fields.owner
    let area = fields.area
    let job = fields.job
    let comment = fields.comment

    let item = await db.getItem(mainID)

    if (!item) {
        res.status(400)
        res.send("Error. Item not found.")
        return
    }

    let urls = [];

    for (let i = 0; i < photos.length; i++) {

        let id = new mongoose.Types.ObjectId()

        let path = photos[i].filepath.split("zpr/")

        urls.push(path[1])

        let photoModel = {
            _id: id,
            url: path[1],
            owner: owner
        }

        if (comment) {
            photoModel.comment = comment
        }

        if (area) {
            photoModel.area = area
        }
        if (job) {
            photoModel.job = job
        }

        if (item.photos) {
            if (item.photos.length > 0) {
                item.photos.push(photoModel)
            } else {
                item.photos = photoModel
            }
        } else {
            item.photos = [photoModel]
        }
    }

    const result = await item.save();
    //console.log(result)
    //res.send(result)

    if (result instanceof ItemModel) {
        //console.log(result)
        res.send(result)
        //photoUpload.uploadPhotoTo1c(urls,fields.mainID)
    }
}
