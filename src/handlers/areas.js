const db = require('../db')
//const AreaModel = require('../models/areaModel.js')

module.exports = {

    async add(req,res) {

        const body = req.body;
        var area = body.area;

        if (!area) {
            res.status(400)
            res.send("Not area property")
            return
        }

        if (!area.name) {
            res.status(400)
            res.send("Not area name")
            return
        }

        if (!area.branch) {
            res.status(400)
            res.send("Not area branch")
            return
        }

        const areaFind = await db.findLastArea()

        if (!areaFind) {
            res.status(500)
            res.send("Server Error: Area not finded")
            return
        }

        area.identifier = areaFind.identifier + 1

        const result = await db.addArea(area)
        res.send(result)
    },

    async edit(req,res) {
        const body = req.body;
        const area = body.area;
        await db.editArea(area)
        res.status(200)
        res.send("OK")
    },

    async get(req,res) {
        const areas = await db.getAreas()
        res.send(areas)
    }
}