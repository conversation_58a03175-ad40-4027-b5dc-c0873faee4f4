const db = require('../db')

module.exports = {

    async add(req,res) {

        const body = req.body;
        const name = body.name;
        const outage = body.outage;

        if (!name) {
            res.status(400)
            res.send("Not name property")
            return
        }

        const jobSearch = await db.getJob({name:name})

        if (jobSearch) {
            res.status(400)
            res.send("Job with this name already exist")
            return
        }

        const jobFind = await db.findLastJob()

        if (!jobFind) {
            res.status(400)
            res.send("Job not finded")
            return
        }

        let newJob = {
            identifier:jobFind.identifier + 1,
            name:name,
        };

        if (outage) {
            newJob.outage = outage;
        }

        const result = await db.addJob(newJob)
        res.send(result)
    },

    async edit(req,res) {
        const body = req.body;
        const job = body.job;
        await db.editJob(job)
        res.status(200)
        res.send("OK")
    },

    async get(req,res) {
        const jobs = await db.getJobs()
        res.send(jobs)
    }
}