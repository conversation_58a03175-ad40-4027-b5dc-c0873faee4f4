const db = require('../db')

module.exports = {

    async getOne(req,res) {
        const login = req.body.login
        const query = {login: login}
        const user = await db.getUser(query)
        res.send(user)
    },

    async get(req,res) {

        let body = req.body
        let logins = body.logins

        const query = {role: {$ne: "client"} }

        if (logins) {
            if (logins.length > 0) {
                query.login = {$in: logins}
            }
        }

        if ((req.role === "admin") || (req.role === "manager")) {
            const users = await db.getUsers(query)
            res.send(users)
            return
        }

        const projection = "login role area branch userType";
        const users = await db.getUsersProj(query,projection)
        res.send(users)
    },

    async create(req,res) {
        const body = req.body;
        const newUser = body.user;
        const login = newUser.login;

        const user = await db.getUser({login:login})

        if (user) {
            res.status(400)
            res.send("User exist")
        } else {
            const result = await db.createUser(newUser)
            res.send(result)
        }
    },

    async edit(req,res) {
        const body = req.body;
        const user = body.user;
        const login = user.login;
        const query = {$set: user};
        await db.editUser(login,query)
        res.status(200)
        res.send("OK")
    },

    async subscribeChange(req,res) {

        const body = req.body
        const login = body.login
        const addIds = body.addIds
        const delIds = body.delIds

        if (delIds.length > 0) {
            const delQuery = {
                $pull:{subscribe:{$in : delIds}}
            };

            const result = await db.editUser(login,delQuery)

            console.log(result)
        }

        if (addIds.length > 0) {
            const addQuery = {
                $addToSet:{subscribe:{$each: addIds}}
            };

            const result = await db.editUser(login,addQuery)

            console.log(result)
        }

        res.status(200)
        res.send("OK");
    },

    async subscribeItemChange(req,res) {
        const body = req.body;
        const login = body.login;
        const mainID = body.mainID;
        const subscribeBool = body.subscribe;

        if (subscribeBool === true) {

            const addQuery = {
                $addToSet:{subscribeItems:mainID}
            };

            const result = await db.editUser(login,addQuery)

            console.log(result)
        } else {
            const delQuery = {
                $pull:{subscribeItems: mainID}
            };

            const result = await db.editUser(login,delQuery)

            console.log(result)
        }

        res.status(200)
        res.send("OK");
    },

    async subscribeOutageChange(req,res) {
        const body = req.body;
        const login = body.login;
        const idAndDays = body.idAndDays;
        const subscribeBool = body.subscribe;
        const mainID = idAndDays.split(":")[0]

        const user = await db.getUser({login:login})

        const subscribeOutageArr = user.subscribeOutage;

        let findedValue;

        if (subscribeOutageArr.length > 0) {
            for (const value of subscribeOutageArr) {
                if (value.includes(mainID)) {
                    findedValue = value;
                    break;
                }
            }
        }

        if (findedValue) {
            const delQuery = {
                $pull:{subscribeOutage: findedValue}
            };

            const result = await db.editUser(login,delQuery)
            console.log(result)
        }

        if (subscribeBool === true) {
            const addQuery = {
                $addToSet:{subscribeOutage:idAndDays}
            };

            const result = await db.editUser(login,addQuery)

            console.log(result)
        }

        res.status(200)
        res.send("OK");
    },

    async subscribeSSZChange(req,res) {

        const body = req.body
        const login = body.login
        const addIds = body.addIds
        const delIds = body.delIds

        if (delIds.length > 0) {
            const delQuery = {
                $pull:{subscribeSSZ:{$in : delIds}}
            };

            const result = await db.editUser(login,delQuery)

            console.log(result)
        }

        if (addIds.length > 0) {
            const addQuery = {
                $addToSet:{subscribeSSZ:{$each: addIds}}
            };

            const result = await db.editUser(login,addQuery)

            console.log(result)
        }

        res.status(200)
        res.send("OK");
    },

    async subscribeZPRChange(req,res) {
        const body = req.body
        const login = body.login
        const boolValue = body.value // bool on/off

        const query = {
            $set:{subscribeZPR:boolValue}
        };

        const result = await db.editUser(login,query)

        console.log(result)

        res.status(200)
        res.send("OK");
    }
}