const db = require('../db')

const secret = "hf94jsD9as34DvbCzx59gFhtOvbLqw20x5x1z";

module.exports = {

    checkAppKey(req, res, next) {

        //console.log("checkAppKey");

        res.type('application/json');

        let secretHeader = req.header("secretKey")

        let secretKey = ""

        if (req.body) {
            if (req.body.secretKey) {
                secretKey = req.body.secretKey
            }
        }

        if (secretHeader) {
            secretKey = secretHeader
        }

        if (secretKey === secret) {
            //console.log("Check App Key Success");
            next();
            return;
        }

        //console.log("Check App Key Error");
        res.status(400);
        res.send('Bad Request');
    },

    async checkAdminRole(req, res, next) {

        if (req.role === "admin") {
            next()
            return
        }

        res.status(401)
        res.send("Access denied")
    },

    async checkAdminManagerDirectorRole(req, res, next) {
        if (req.role === "admin" || req.role === "manager" || req.role === "director") {
            next()
            return
        }

        res.status(401)
        res.send("Access denied")
    },

    async auth(req, res) {
        const login = req.body.login
        const password = req.body.password

        const user = await checkUserForLogin(login,password)

        if (user) {
            res.status(200)
            res.send(user)
            //console.log("auth Success")
            return
        }

        //console.log("auth error")

        res.status(401)
        res.send("Unauthorized")
    },

    async authCheck(req, res, next) {
        let auth = req.body.auth

        let authHeader = req.header("auth")

        if (authHeader) {
            auth = authHeader
        }

        if ((!auth) || !(typeof auth === "string")) {
            res.status(401)
            res.send("Unauthorized")
            return;
        }

        const text = Buffer.from(auth, 'base64').toString('ascii')

        //console.log(text)

        if (text) {
            const arr = text.split(":");

            //console.log(arr)

            if (arr.length === 2) {
                const login = arr[0]
                const pass = arr[1]

                //console.log(login,pass)

                const user = await checkUserForLogin(login,pass)

                if (user) {

                    if (user.role === "block") {
                        res.status(401)
                        res.send("User is block")
                        return
                    }

                    //console.log("auth pass")
                    req.role = user.role

                    next()
                    return
                }
            }
        }

        res.status(401)
        res.send("Unauthorized")
    }
}

async function checkUserForLogin(login,password) {

    if (!login) {
        return
    }

    if (!password) {
        return
    }

    if (login.length === 0) {
        return
    }

    if (password.length === 0) {
        return
    }

    const user = await db.getUser({login:login})

    if (user) {
        if (user.password === password) {
            return user
        }
    }
}