const db = require('../db')

module.exports = {

    async get(req,res) {

        let body = req.body

        let login = body.login
        const cursor = body.cursor
        const limit = parseInt(body.limit)

        //console.log(body)

        let query = {
            owners: login
        }

        let models

        if (cursor) {

            query.createdAt = {
                $lt: cursor,
            }

            models = await db.getNotifications(query,limit+1)

        } else {
            models = await db.getNotifications(query,limit+1)
        }

        //console.log(query)

        const max = models.length === limit + 1
        let nextCursor = null
        if (max) {
            const record = models[limit]
            nextCursor = record.createdAt
            models.pop()
        }

        let result = {
            data: models,
            nextCursor: nextCursor,
            max: max
        }

        //console.log(result.data)
       // console.log(result)

        res.send(result)
    },

    async getNew(req,res) {
        let body = req.body
        let login = body.login
        let offset = parseInt(body.offset)
        let limit = parseInt(body.limit)

        let query = {
            owners: login
        }

        let models = await db.getNotifications(query,limit,offset);

        res.send(models)
    }
}