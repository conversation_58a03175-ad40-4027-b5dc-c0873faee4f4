const db = require('../db')
const https = require("https");
const utils = require("./../utils")
const oneSignalAppID = "************************************";
const oneSignalAppIDFlutter = "************************************";

module.exports = {

    async send(req, res) {

        const { login, message, from } = req.body;
        configPushMsg(login, message, from)

        res.status(200)
        res.send("OK")
    },

    async addItemNotification(item) {

        if (!item) {
            return;
        }

        const component = item.components[0];
        if (!component) {
            return;
        }

        const { area, job, name } = component.newHistory[0];

        const repairNumber = item.repairNumber;
        const title = name;
        const clientName = item.order.client.name;
        const equipment = item.order.equipment;

        const typeStr = item.components.map(component => utils.getNameTypeItem(component.type)).join(', ');

        const message = "Двигатель №" + repairNumber + "\nСостав: " + typeStr + "\nКонтрагент: " + clientName + "\nОборудование: " + equipment;
        const subscribeID = area + "_" + job;

        preparedUsersForNotification(item.mainID, subscribeID, title, message);
    },

    async addComponentHistoryNotification(component, history, item) {

        if (!component || !history) {
            return;
        }

        const { area, job, name, task, statuses } = history;
        const lastStatus = statuses[statuses.length-1]

        const title = name;
        let message = "";

        const type = utils.getNameTypeItem(component.type)
        message = `${type} №${item.repairNumber}`;

        const clientName = item.order.client.name;
        const equipment = item.order.equipment;

        if (clientName) {
            message += `\nКонтрагент: ${clientName}`;
        }

        if (equipment) {
            message += `\nОборудование: ${equipment}`;
        }

        if (history.type !== "info") {

            const statusMap = {
                inwork: "Выполняется",
                finish: "Завершено",
                pause: "Приостановлена",
            };

            if (lastStatus.status in statusMap) {
                message += `\nСтатус: ${statusMap[lastStatus.status]}`;
            }

            // if (lastStatus.status === "inwork") {
            //     message += "\nСтатус: Выполняется";
            // } else if (lastStatus.status === "finish") {
            //     message += "\nСтатус: Завершено";
            // } else if (lastStatus.status === "pause") {
            //     message += "\nСтатус: Приостановлена";
            // }
        }

        if (lastStatus.comment) {
            message += `\nКомментарий: ${lastStatus.comment}`;
        } else {
            message += `\nОбновил: ${lastStatus.owner}`;
        }

        let subscribeID, subscribeSSZ, owner;

        if (job) {
            subscribeID = `${area}_${job}`;
        } else if (task) {
            subscribeSSZ = String(area);
        } else {
            subscribeID = `${area}_0`;
            owner = lastStatus.owner;
        }

        preparedUsersForNotification(item.mainID, subscribeID, title, message, subscribeSSZ, owner);
    },

    async newZPRNotification(item) {

        const { order, repairNumber } = item;
        const { client, name, equipment } = order;
        const clientName = client.name;

        let title = "Новый заказ на производство"
        let message = "Рем номер: " + repairNumber
        message += "\nКлиент: " + clientName
        message += "\nНаименование: " + name
        message += "\nОборудование: " + equipment

        preparedUsersForNotification(null,null, title, message, null, null, item.branch);
    },

    async sendNotificationWith(login,title,message) {

        prepareMessage(title, message,[login])

        await createNotif(title,message,[login])
    },

    async preparedNotificationForAccessComponentChange(areaAccessNewIDs, item, component) {

        const type = utils.getNameTypeItem(component.type);

        const query = {
            "identifier": component.currentArea,
        }

        const area = await db.getArea(query);

        const equipment = item.order.equipment;

        const title = "Предоставлен доступ";
        const message = "Ремномер: №" + item.repairNumber + "\n" + "Оборудование: " + equipment + "\n" + "Компонент: " + type + "\n" + "С участка: " + area.name;

        preparedUsersForNotification(null,null, title, message, null, null, null, areaAccessNewIDs);
    },

    async messageOutAgeItem(title, message, logins) {
        await createNotif(title,message,logins);
        makeAndSendMessage(title,message,logins, true)
        makeAndSendMessage(title,message,logins, false)
    }
}

function prepareMessage(title, message, logins) {
    makeAndSendMessage(title,message,logins, true)
    makeAndSendMessage(title,message,logins, false)
}

function configPushMsg(login, message, from) {

    const title = message ? `Сообщение от ${from}` : "Тестовое уведомление";
    const preparedMessage = message || "Тест";

    prepareMessage(title, preparedMessage, [login]);

    if (message) {
        createNotif(title, preparedMessage, [login], from);
    }
}

async function preparedUsersForNotification(mainID, subscribeID, title, message, subscribeSSZ, excludeLogin, subscribeZPRBranch, areaAccessNewIDs) {

    //console.log(subscribeID);
    //console.log(title);
    //console.log(message);

    let query = {}

    if (subscribeID) {
        query = {
            subscribe: subscribeID
        };
    } else if (subscribeSSZ) {
        query = {
            subscribeSSZ: subscribeSSZ
        };
    } else if (subscribeZPRBranch > 0) {
        query = {
            branch: {$in: [0,subscribeZPRBranch]},
            subscribeZPR: true
        }
    } else if (areaAccessNewIDs) {
        query = {
            area: {$in: areaAccessNewIDs},
        }
    }

    const projection = "login";
    const users = await db.getUsersProj(query,projection)

    let usersArr = [];

    if (mainID) {
        const usersItemSubscribe = await getUserForItemSubscribe(mainID)

        if (usersItemSubscribe) {
            usersArr = users.concat(usersItemSubscribe);
        }
    } else {
        usersArr = users
    }

    //console.log("users: " + users)

    if (excludeLogin) {
        usersArr = usersArr.filter((user) => user.login !== excludeLogin);
    }

    let logins = usersArr.map((user) => user.login);

    logins = [...new Set(logins)];

    //console.log(logins);

    if (logins.length > 0) {

        prepareMessage(title,message,logins);
        await createNotif(title,message,logins);
    }
}

async function getUserForItemSubscribe(mainID) {
    let query = {subscribeItems:{ $in: mainID}};
    const projection = "login";
    const users = await db.getUsersProj(query,projection)
    return users;
}

async function createNotif(title,message,owners,from) {
    let notif = {
        owners: owners,
        message: message,
        title: title
    }

    if (from) {
        notif.from = from
    }

    const result = await db.createNotification(notif)
    console.log(result)
}


function makeAndSendMessage(title, message, logins, flutter) {

    let oneSignalID;
    let authID;

    if (flutter === true) {
        oneSignalID = oneSignalAppIDFlutter;
        authID = "Basic ************************************************"
    } else {
        oneSignalID = oneSignalAppID;
        authID = "Basic ************************************************"
    }

    const data = {
        app_id: oneSignalID,
        headings: {"en": title, "ru": title},
        contents: {"en": message, "ru": message},
        include_external_user_ids: logins
    };

    const headers = {
        "Content-Type": "application/json; charset=utf-8",
        "Authorization": authID
    };

    console.log("Push notification: ", data.headings, data.contents, flutter)

    sendNotification(headers, data)
}

const sendNotification = function (headers, data) {

    const options = {
        host: "onesignal.com",
        port: 443,
        path: "/api/v1/notifications",
        method: "POST",
        headers: headers
    };

    const req = https.request(options, function (res) {
        res.on('data', function (data) {
            console.log("Response:");
            const dataObj = JSON.parse(data);
            console.log(dataObj);
        });
    });

    req.on('error', function (e) {
        console.log("ERROR:");
        console.log(e);
    });

    req.write(JSON.stringify(data));
    req.end();
};