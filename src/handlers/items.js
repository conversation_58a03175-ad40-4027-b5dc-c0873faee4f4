const db = require('../db')
const pushes = require('./pushes')
const ItemModel = require('../models/itemModel')

const utils = require("../utils")
//var json2xls = require('json2xls');
//let xlsx = require("json-as-xlsx")

async function getItem(componentID) {

    let query = {
        "deleted": false,
        "components.identifier": componentID
    }

    let item = await db.findItem(query)

    if (!item) {
        console.log("Error: Item not found")
        return
    }

    if (item.components.length === 0) {
        console.log("Error: Item not have components")
        return
    }

    if (item.finished === true) {
        console.log("Error: Item in archive")
        return
    }

    return item
}

function checkWorkerStatuses(workers, owner) {

    let status
    let needStatusReturn = true

    for (const worker of workers) {

        if (worker.statuses) {
            let length = worker.statuses.length

            if (length === 0) {
                continue
            }

            let lastStatus = worker.statuses[length - 1]

            if (!status) {
                status = lastStatus.status
            } else if (status) {
                if (status === lastStatus.status) {
                    continue
                } else {
                    needStatusReturn = false
                }
            }
        }
    }

    if (status === "inwork") {
        needStatusReturn = true
    }

    if (needStatusReturn === true) {

        let statusHistory = {
            owner: owner,
            status: status
        }

        return statusHistory
    }
}

module.exports = {

    async edit(req, res) {
        const body = req.body;
        const item = body.item;

        const result = await db.editItem(item)

        if (result instanceof ItemModel) { // TODO: result not ItemModel fix this
            pushes.addItemNotification(result)
        }

        res.send(result)
    },

    async updateItem(req, res) {
        const body = req.body;
        const mainID = body.mainID;

        console.log("updateItem: " + req.body)

        if (!mainID) {
            res.status(400);
            res.send("Error: updateItem: mainID is required");
            return;
        }

        delete body.mainID; // Remove mainID from body before update
        let updateQuery = { $set: body };

        const result = await db.updateItem(mainID, updateQuery);
        res.send(result);
    },

    async mergeItems(req, res) {

        const body = req.body
        const mainID = body.mainID
        const altMainID = body.altMainID

        let mainItem = await db.getItem(mainID)
        let altItem = await db.getItem(altMainID)

        if (mainItem.deleted === true || altItem.deleted === true) {
            res.status(400)
            res.send("Error: mergeItems: Deleted: Для объединения обратитесь к администратору")
            return
        }

        let mainComponentsLength = 0
        let altComponentsLength = 0

        if (mainItem.components) {
            mainComponentsLength = mainItem.components.length
        }

        if (altItem.components) {
            altComponentsLength = altItem.components.length
        }

        if (altComponentsLength > 0 && mainComponentsLength > 0) {
            res.status(400)
            res.send("Error: mergeItems: LengthComponent: Для объединения обратитесь к администратору")
            return
        }

        if (mainItem.new === true) {
            if (altItem.new === true) {
                mainItem.new = true
            } else {
                mainItem.new = false
            }
        } else {
            mainItem.new = false
        }

        altItem.deleted = true
        altItem.new = false

        if (mainItem.altIds) {
            mainItem.altIds.push(altMainID)
        } else {
            mainItem.altIds = [altMainID]
        }

        if (altItem.altIds) {
            if (altItem.altIds.length > 0) {
                mainItem.altIds = mainItem.altIds.concat(altItem.altIds);
            }
        }

        if (mainComponentsLength === 0 && altComponentsLength > 0) {
            mainItem.components = altItem.components
        }

        let mainPhotoLength = 0
        let altPhotoLength = 0

        if (mainItem.photos) {
            mainPhotoLength = mainItem.photos.length
        }

        if (altItem.photos) {
            altPhotoLength = altItem.photos.length
        }

        if (mainPhotoLength > 0 && altPhotoLength > 0) {
            mainItem.photos = mainItem.photos.concat(altItem.photos);
        } else if (altPhotoLength > 0) {
            mainItem.photos = altItem.photos
        }

        if (mainItem.altRN) {
            if (mainItem.altRN.length > 0) {
                mainItem.altRN.push(altItem.repairNumber)
            } else {
                mainItem.altRN = [altItem.repairNumber]
            }
        }

        let result = await mainItem.save();
        await altItem.save();

        res.send(result)
    },

    async addNewHistory(req, res) {

        const body = req.body;
        const componentID = body.componentID
        const history = body.history;

        let item = await getItem(componentID)

        if (!item) {
            res.status(400)
            res.send("Error: addNewHistory: Item not found")
            return
        }

        let component;

        // ----

        let addHistToAll = false // добавить новую работу ко всем компонентам или нет

        if (item.assembled) {
            if (item.assembled === true) {
                if (history.job) {
                    addHistToAll = true
                }
            }
        }

        for (let i = 0; i < item.components.length; i++) {
            let comp = item.components[i];
            //console.log(comp)

            if (addHistToAll === true || comp.identifier === componentID) {
                item.components[i].newHistory.push(history)

                if (comp.identifier === componentID) {
                    component = comp
                }
            }
        }

        // ----

        let needArchive = true
        let needFinishDate = true

        for (let i = 0; i < item.components.length; i++) {
            let comp = item.components[i];

            let lastHistory = comp.newHistory[comp.newHistory.length - 1]

            if (lastHistory.job !== 138) { // 138 - это ИД работы Отгрузки, если все компоненты отгружены то в архив
                needArchive = false
            }

            if (lastHistory.job !== 141) { // 141 - ожидает отгрузки, значит готов и можно выставить дату завершения
                needFinishDate = false
            }
        }

        //console.log("Need Archive:" + needArchive)

        if (needArchive === true) {
            item.finished = true // если нужно заархивировать то ставим что статус завершен

            if (!item.finishDate) { // выставляем дату завершения если её нет
                item.finishDate = Math.floor(Date.now() / 1000)
            }
        }

        if (needFinishDate === true) {
            item.finishDate = Math.floor(Date.now() / 1000)
        }

        const result = await item.save();
        //console.log(result)
        res.send(result)

        if (result instanceof ItemModel) {
            if (component, history) {
                pushes.addComponentHistoryNotification(component, history, item)
            }
        }
    },

    async changeStatusHistory(req, res) {

        const body = req.body;
        const componentID = body.componentID
        const identifier = body.identifier; // history identifier
        const statusObj = body.statusObj;

        let item = await getItem(componentID)

        if (!item) {
            res.status(400)
            res.send("Error: changeStatusHistory: Item not found")
            return
        }

        let component;
        let history;

        let addStatToAll = false // добавить новый статус ко всем подходящим историям если история является работой

        if (item.assembled) {
            if (item.assembled === true) {
                addStatToAll = true
            }
        }

        for (let i = 0; i < item.components.length; i++) {
            let comp = item.components[i];
            //console.log(comp)
            if (addStatToAll === true || comp.identifier === componentID) {

                for (let j = 0; j < comp.newHistory.length; j++) {

                    let hist = comp.newHistory[j]

                    if (hist.identifier === identifier) {

                        item.components[i].newHistory[j].statuses.push(statusObj)
                        history = item.components[i].newHistory[j]
                        break
                    }
                }

                if (comp.identifier === componentID) {
                    component = comp
                }
            }
        }

        const result = await item.save();
        //console.log(result)
        res.send(result)

        if (result instanceof ItemModel) {
            if (component, history) {
                pushes.addComponentHistoryNotification(component, history, item)
            }
        }
    },

    async changeStatusWorkers(req, res) {
        const body = req.body;
        const componentID = body.componentID
        const identifier = body.identifier; // history identifier
        const workers = body.workers;
        const owner = body.owner

        let item = await getItem(componentID)

        if (!item) {
            res.status(400)
            res.send("Error: changeStatusHistory: Item not found")
            return
        }

        let component;
        let history;

        let status = checkWorkerStatuses(workers, owner)

        for (let i = 0; i < item.components.length; i++) {
            let comp = item.components[i];
            //console.log(comp)
            if (comp.identifier === componentID) {

                for (let j = 0; j < comp.newHistory.length; j++) {

                    let hist = comp.newHistory[j]

                    if (hist.identifier === identifier) {

                        let statuses = item.components[i].newHistory[j].statuses
                        let length = item.components[i].newHistory[j].statuses.length
                        let lastStatus = statuses[length - 1]

                        if (status) {
                            if (status.status !== lastStatus.status) {
                                item.components[i].newHistory[j].statuses.push(status)
                            }
                        }

                        item.components[i].newHistory[j].workers = workers
                        history = item.components[i].newHistory[j]
                        break
                    }
                }

                component = comp
            }
        }

        const result = await item.save();
        //console.log(result)
        res.send(result)

        if (result instanceof ItemModel) {
            if (component, history, status) {
                pushes.addComponentHistoryNotification(component, history, item)
            }
        }
    },

    async transferToArea(req, res) {

        const body = req.body
        const componentID = body.componentID
        const transferArea = body.transferArea
        const history = body.history

        let item = await getItem(componentID)

        if (!item) {
            res.status(400)
            res.send("Error: transferToArea: Item not found")
            return
        }

        let transferAll = false // передать все

        if (item.assembled) {
            if (item.assembled === true) {
                transferAll = true
            }
        }

        let component;

        for (let i = 0; i < item.components.length; i++) {
            let comp = item.components[i];
            //console.log(comp)
            if (transferAll === true || comp.identifier === componentID) {

                item.components[i].currentArea = transferArea
                item.components[i].newHistory.push(history)

                if (comp.identifier === componentID) {
                    component = comp
                }

                //console.log(comp)
            }
        }

        const result = await item.save();
        //console.log(result)
        res.send(result)

        if (result instanceof ItemModel) {
            if (component, history) {
                pushes.addComponentHistoryNotification(component, history, item)
            }
        }
    },

    async changeAccessArea(req, res) {

        const body = req.body
        const componentID = body.componentID
        const accessAreas = body.accessAreas

        let item = await getItem(componentID)

        if (!item) {
            res.status(400)
            res.send("Error: changeAccessArea: Item not found")
            return
        }

        let newAreaAccessIDs = [];
        let component;

        for (let i = 0; i < item.components.length; i++) {

            let comp = item.components[i];

            //console.log(comp)
            if (comp.identifier === componentID) {

                component = comp;

                for (let value of accessAreas) {
                    if (comp.accessArea.includes(value) === false) {
                        newAreaAccessIDs.push(value)
                    }
                }

                item.components[i].accessArea = accessAreas
                break
                //console.log(comp)
            }
        }

        const result = await item.save();

        pushes.preparedNotificationForAccessComponentChange(newAreaAccessIDs, item, component)

        //console.log(result)
        res.send(result)
    },

    async changeAssembled(req, res) {

        const body = req.body
        const mainID = body.mainID
        const assembled = body.assembled
        const history = body.history

        let query = {
            "mainID": mainID
        }

        const item = await db.findItem(query)

        if (item) {

            item.assembled = assembled

            if (history) {
                for (let i = 0; i < item.components.length; i++) {
                    item.components[i].newHistory.push(history)
                }
            }

            const result = await item.save();
            res.send(result)

        } else {
            res.status(400)
            res.send("Don't get item")
        }
    },

    async get(req, res) {
        let query = {}
        // const options = {
        //    sort: { title: 1 },
        // };

        let search = req.body.search

        //console.log(req);
        console.log(search);

        let sort = { updatedAt: -1 };
        let limit = null;
        let offset = null;
        let projection = null;

        if (search) {

            const regexEn = /[a-zA-Z]/
            const regexRu = /[А-Яа-я]/

            if (search.searchField) {
                query.repairNumber = { $regex: search.searchField };
            }

            if (search.searchClient) {

                if (regexEn.test(search.searchClient) || regexRu.test(search.searchClient)) {

                    let regexQuery = search.searchClient

                    let regex = new RegExp(regexQuery, "i");

                    query = {
                        "order.client.name": regex
                    }
                }
            }

            if (search.searchEquipment) {

                let regexQuery = search.searchEquipment

                let regex = new RegExp(regexQuery, "i");

                query = {
                    "order.equipment": regex
                }
            }

            if (search.overDeadline) {
                if (search.overDeadline === true) {
                    const now = Math.floor(Date.now() / 1000)
                    const correctTime = now - 86400
                    query.endDate = { $lt: correctTime }
                    query["order.statusOrder"] = { $ne: "Дефектировка" }
                    query["components.newHistory.job"] = { $ne: 141 } // 141 - id job
                }
            }

            if (search.recentShipmentWeeks) {
                const now = Math.floor(Date.now() / 1000)
                const recentWeek = search.recentShipmentWeeks;
                const timeAfterRecentWeek = now + (86400 * 7 * recentWeek)

                query.endDate = { $gt: now, $lt: timeAfterRecentWeek }
            }

            if (search.repairNumber) {
                query.repairNumber = search.repairNumber
            }

            if (search.componentID) {
                query.components = {
                    $elemMatch: { identifier: search.componentID }
                }
            }

            if (search.mainID) {
                query.mainID = search.mainID

                let or = {
                    "$or": [
                        {
                            mainID: search.mainID
                        },
                        {
                            altIds: { $in: search.mainID }
                        }
                    ]
                }

                if (query["$and"]) {
                    query["$and"].push(or)
                } else {
                    query["$and"] = [or]
                }
            }

            if (search.mainIDArr) {
                let or = {
                    "$or": [
                        {
                            mainID: { $in: search.mainIDArr }
                        },
                        {
                            altIds: { $elemMatch: { $in: search.mainIDArr } }
                        }
                    ]
                }

                if (query["$and"]) {
                    query["$and"].push(or)
                } else {
                    query["$and"] = [or]
                }
            }

            if (search.branch) {
                query.branch = search.branch
            }

            if (search.area) {

                let or = {
                    "$or": [{
                        "components.currentArea": search.area
                    }, {
                        "components.accessArea": search.area
                    }]
                }

                if (query["$and"]) {
                    query["$and"].push(or)
                } else {
                    query["$and"] = [or]
                }
            }

            if (search.new) {
                query.new = search.new
            } else if (search.new === false) {
                query.new = false
            }

            if (search.finished) {
                query.finished = search.finished
            } else if (search.finished === false) {
                query.finished = false
            }

            if (search.archiveInYear) {
                const year = new Date().getFullYear();
                const startDate = new Date(Date.UTC(year, 0, 1)).getTime() / 1000;
                const endDate = new Date(Date.UTC(year + 1, 0, 1)).getTime() / 1000;

                query["finishDate"] = {
                    $gt: Math.floor(startDate),
                    $lt: Math.floor(endDate),
                };
            }

            if (search.orderName) {
                query["order.name"] = search.orderName
            }

            if (search.orderEquipment) {
                query["order.equipment"] = search.orderEquipment
            }

            if (search.orderClientName) {
                query["order.client.name"] = search.orderClientName
            }

            if (search.limit) {
                limit = search.limit
            }

            if (search.offset) {
                offset = search.offset
            }

            if (search.createdAtSort) {
                sort = { createdAt: -1 }
            }

            if (search.projection) {
                projection = search.projection
            }
        }

        query.deleted = false

        //console.log(query)

        const items = await db.getItems(query, sort, limit, offset, projection)

        console.log(items)

        res.send(items)
    },

    async getLastSyncDate(req, res) {

        let query = {
            //"order.updatedAt": {$gt: 20220320161600}
        }

        let sort = {
            "order.updatedAt": -1
        }

        const item = await db.findItem(query, sort)

        if (item) {

            let date = String(item.order.updatedAt)
            res.send(date)

        } else {
            res.status(400)
            res.send("Don't get item")
        }
    },

    async getReports(req, res) {

        let body = req.body

        let json = await reportsGenerate(body)
        res.send(json)
    }
}

async function reportsGenerate(body) {

    let query = {
        'deleted': false,
        'components.0': { $exists: true }
    }

    if (body.mainID) {
        query.mainID = body.mainID
    } else {
        if (body.branch) {
            query.branch = body.branch
        }

        if (body.archive === true) {
            query.finished = true
        } else {
            query.finished = false
        }
    }

    var sort = { updatedAt: -1 }

    let items = await db.getItems(query, sort)

    let newItems = []

    for (const item of items) {

        if (!item.order) {
            continue
        }

        for (const comp of item.components) {

            let nameComp = utils.getNameTypeItem(comp.type)

            for (const hist of comp.newHistory) {

                let nameArr = hist.name.split(" | ")

                let newItem = {
                    repairNumber: item.repairNumber,
                    name: item.order.name,
                    equipment: item.order.equipment,
                    componentID: comp.identifier,
                    componentName: nameComp,
                    jobName: nameArr[1],
                    areaName: nameArr[0],
                    orderFinishDate: item.order.finishDate,
                    endDateUnix: item.endDate,
                    statuses: hist.statuses,
                    finished: item.finished,
                    createdAt: hist.createdAt
                }

                if (hist.type === "task") {
                    newItem.historyType = "ССЗ"
                } else if (hist.type === "job") {
                    newItem.historyType = "Статус"
                } else if (hist.type === "info") {
                    newItem.historyType = "Инфо"
                }

                if (item.finishDate) {
                    newItem.finishDate = item.finishDate
                }

                newItems.push(newItem)
            }
        }
    }

    //console.log(newItems)

    let exportItems = []

    for (let i = 0; i < newItems.length; i++) {

        let item = newItems[i]

        let firstStatus = item.statuses[0]
        let lastStatus = item.statuses[item.statuses.length - 1]

        let comment = ""

        for (const status of item.statuses) {
            if (status.comment) {
                comment = status.comment
            }
        }

        let startDate;

        if (firstStatus.status === "inwork") {
            startDate = firstStatus.createdAt
        }

        let endDate;

        if (lastStatus.status === "finish") {
            endDate = lastStatus.createdAt
        }

        let hours = "0";

        if (startDate) {
            if (endDate) {
                let nDate = endDate - startDate
                let tempHours = nDate / 3600
                hours = String(financial(tempHours))
            }
        }

        let exItem = {
            "area": item.areaName,
            "repairNumber": item.repairNumber,
            "name": item.name,
            "equipment": item.equipment,
            "component": item.componentName,
            "type": item.historyType,
            "job": item.jobName,
            "time": hours,
            "endDateJob": "",
            "comment": "",
            "finishDate": ""
        }

        if (comment) {
            exItem["comment"] = comment
        }

        if (endDate) {
            let finishEndDate = utils.dateFromUnixToShortStr(endDate)
            exItem["endDateJob"] = finishEndDate
        }

        exItem["endDateOrder"] = item.orderFinishDate

        if (item.finishDate) {

            let dateFinishStr = utils.dateFromUnixToShortStr(item.finishDate)
            exItem["finishDate"] = dateFinishStr
        }

        if (item.finished === true) {
            exItem["finished"] = "Да"
        } else {
            exItem["finished"] = "Нет"
        }

        let dateNow = Math.floor(Date.now() / 1000)
        let endDateUnix = item.endDateUnix // дата завершения по договору в формате unix

        exItem["daysLeft"] = ""
        exItem["outDate"] = "0"

        if (item.finished === false) {

            if (dateNow < endDateUnix) {
                let delta = endDateUnix - dateNow
                let days = delta / 86400
                exItem["daysLeft"] = String(Math.round(days))
            } else if (dateNow > endDateUnix) {
                let delta = dateNow - endDateUnix
                let days = delta / 86400
                exItem["daysLeft"] = String(- Math.round(days))
                exItem["outDate"] = String(Math.round(days))
            } else {
                exItem["daysLeft"] = "0"
            }
        } else {
            if (item.finishDate) {
                if (item.finishDate > endDateUnix) {

                    let delta = item.finishDate - endDateUnix
                    let days = delta / 86400

                    exItem["outDate"] = String(Math.round(days))
                } else if (item.finishDate < endDateUnix) {
                    let delta = endDateUnix - item.finishDate
                    let days = delta / 86400

                    exItem["outDate"] = String(- Math.round(days))
                }
            }
        }

        exportItems.push(exItem)
    }

    //console.log(exportItems)

    return exportItems
}

function financial(x) {
    return Number.parseFloat(x).toFixed(2);
}