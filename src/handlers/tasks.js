const db = require('../db')
const {DateTime} = require("luxon");

module.exports = { // ssz tasks

    async get(req,res) {

        const body = req.body

        let options = {
            zone: "Asia/Yekaterinburg"
        };

        let timeNowUnix = Math.floor(Date.now() / 1000)
        let dateRequestStr = DateTime.fromSeconds(timeNowUnix, options).toFormat('dd.LL.yyyy');

        if (body.dateStr) {
            dateRequestStr = body.dateStr
        }

        let query = {
            date: dateRequestStr
        }

        if (body.idArea) {
            query['areaSsz.id'] = body.idArea
        }

        if (body.idBranch) {
            query['idBranch'] = body.idBranch
        }

        let idsArr = [];

        let item;

        if (body.mainID) {

            idsArr.push(body.mainID);

            item = await db.getItem(body.mainID)

            if (item.altIds) {
                if (item.altIds.length > 0) {

                    idsArr = idsArr.concat(item.altIds);

                    query.tasks = { $elemMatch: {mainID: idsArr} }
                }
            }

            if (!query.tasks) {
                query.tasks = { $elemMatch: { mainID: body.mainID } }
            }
        }

        const tasks = await db.getTasks(query)

        let newTasks = [];

        //console.log(tasks);

        if (tasks) {
            for (const task of tasks) {

                for (const jobTask of task.tasks) {

                    if (body.mainID) {
                        if (idsArr.includes(jobTask.mainID) === false) {
                            continue
                        }
                    }

                    let newTask = {
                        id: task.id,
                        number: task.number,
                        date: task.date,
                        idBranch: task.idBranch,
                        idArea: task.areaSsz.id,
                        mainID: jobTask.mainID,
                        idJob: jobTask.job.id,
                        name: jobTask.job.name
                    }

                    if (item) {
                        for (let component of item.components) {
                            for (let history of component.newHistory) {

                                if (history === null) {
                                    continue;
                                }

                                if (history.taskID === task.id && history.task === jobTask.job.id) {

                                    let statuses = history.statuses;

                                    if (statuses.length > 0) {
                                        let statusModel = statuses[statuses.length - 1]

                                        let lastStatus = statusModel.status

                                        if (lastStatus != null) {
                                            newTask.lastStatus = lastStatus;
                                        }
                                        break;
                                    }
                                }
                            }
                        }
                    }

                    if (jobTask.workers) {
                        newTask.workers = jobTask.workers
                    } else {
                        newTask.workers = task.workers
                    }

                    newTasks.push(newTask)
                }
            }
        }

        //console.log(newTasks)

        res.send(newTasks)
    },

    async getInfoTasksSSZ(req,res) {
        const body = req.body

        let options = {
            zone: "Asia/Yekaterinburg"
        };

        let timeNowUnix = Math.floor(Date.now() / 1000)
        let dateRequestStr = DateTime.fromSeconds(timeNowUnix, options).toFormat('dd.LL.yyyy');

        if (body.dateStr) {
            dateRequestStr = body.dateStr
        }

        let query = {
            date: dateRequestStr
        }

        if (body.idArea) {
            query['areaSsz.id'] = body.idArea
        }

        if (body.idBranch) {
            query['idBranch'] = body.idBranch
        }

        const tasks = await db.getTasks(query)

        let mainIDs = [];
        let newTasks = [];

        for (const task of tasks) {
            for (const jobTask of task.tasks) {

                let newTask = {
                    id: task.id,
                    number: task.number,
                    date: task.date,
                    idBranch: task.idBranch,
                    idArea: task.areaSsz.id,
                    mainID: jobTask.mainID,
                    idJob: jobTask.job.id,
                    name: jobTask.job.name
                }

                if (jobTask.workers) {
                    newTask.workers = jobTask.workers
                } else {
                    newTask.workers = task.workers
                }

                newTasks.push(newTask)

                if (mainIDs.includes(jobTask.mainID) === true) {
                    continue;
                }
                mainIDs.push(jobTask.mainID)
            }
        }

        let itemQuery = {};

        let or = {
            "$or": [
                {
                    mainID: {$in: mainIDs}
                },
                {
                    altIds: {$elemMatch: {$in: mainIDs}}
                }
            ]
        }

        itemQuery["$and"] = [or]
        itemQuery.new = false
        itemQuery.deleted = false

        let projection = "altIds mainID repairNumber new order.equipment components.newHistory.taskID components.newHistory.task components.newHistory.statuses.status"

        const items = await db.getItemsWithProjection(itemQuery, projection);

        // count inwork, count pause, count finished, count not start; count all tasks; repairNumber main

        let viewModels = [];

        for (let item of items) {

            let itemTask = [];

            for (let task of newTasks) {
                if (item.altIds.includes(task.mainID) === true || item.mainID === task.mainID) {
                    itemTask.push(task)
                }
            }

            let obj = {
                repairNumber: item.repairNumber,
                mainID: item.mainID,
                notAdd: item.new,
                tasksCount: itemTask.length,
                finishCount: 0,
                inworkCount: 0,
                pauseCount: 0,
            }

            if (item.order) {
                if (item.order.equipment) {
                    obj.equipment = item.order.equipment;
                }
            }

            for (let component of item.components) {
                for (let history of component.newHistory) {

                    if (history === null) {
                        continue;
                    }

                    for (let task of itemTask) {
                        if (history.taskID === task.id && history.task === task.idJob) {

                            let statuses = history.statuses;

                            if (statuses.length > 0) {
                                let statusModel = statuses[statuses.length - 1]

                                let lastStatus = statusModel.status

                                if (lastStatus != null) {
                                    if (lastStatus === "inwork") {
                                        obj.inworkCount += 1;
                                    } else if (lastStatus === "finish") {
                                        obj.finishCount += 1;
                                    } else if (lastStatus === "pause") {
                                        obj.pauseCount += 1;
                                    }
                                }
                                break;
                            }
                        }
                    }
                }
            }

            viewModels.push(obj)
        }

        res.send(viewModels)
    },

    async searchTasksByName(req, res) {
        const body = req.body
        
        if (!body.query || body.query.trim() === '') {
            return res.status(400).send({ error: 'Search text is required' })
        }
        
        const searchText = body.query.trim()
        const searchRegex = new RegExp(searchText, 'i')
        
        // Задаем проекцию для выборки только необходимых полей
        const projection = {
            "tasks.job.id": 1,
            "tasks.job.name": 1
        }
        
        // Ограничиваем количество документов для обработки
        const limit = body.limit || 15
        
        try {
            // Оптимизированный запрос с проекцией и лимитом
            const tasks = await db.getTasks(
                { "tasks.job.name": searchRegex },
                projection,
                limit
            )
            
            // Создаём Map для хранения уникальных результатов (по id)
            const uniqueJobsMap = new Map()
            
            // Эффективная обработка результатов
            tasks.forEach(task => {
                if (!task.tasks) return
                
                task.tasks.forEach(taskItem => {
                    if (!taskItem.job || !taskItem.job.name || !taskItem.job.id) return
                    
                    if (searchRegex.test(taskItem.job.name)) {
                        // Используем id как ключ для обеспечения уникальности
                        uniqueJobsMap.set(taskItem.job.id, {
                            id: taskItem.job.id,
                            name: taskItem.job.name
                        })
                    }
                })
            })
            
            // Преобразуем Map в массив объектов
            const results = Array.from(uniqueJobsMap.values())
            
            res.send(results)
        } catch (error) {
            console.error('Error in searchTasksByName:', error)
            res.status(500).send({ error: 'An error occurred while searching tasks' })
        }
    }
}