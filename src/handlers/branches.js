const db = require('../db')

module.exports = {

    async add(req,res) {

        const body = req.body;
        const name = body.name;
        const shortName = body.shortName;
        const idBranch = body.idBranch;

        if (!name || !shortName) {
            res.status(400)
            res.send("Not name or short name property")
            return
        }

        if (!idBranch) {
            res.status(400)
            res.send("Not id branch property")
            return
        }

        const searchBranch = await db.getBranch({name:name})

        if (searchBranch) {
            res.status(400)
            res.send("Branch with that name already exist")
            return
        }

        const branchFind = await db.findLastBranch()

        if (!branchFind) {
            res.status(500)
            res.send("Server Error: Branch not finded")
            return
        }

        const newBranch = {
            identifier:branchFind.identifier + 1,
            name:name,
            shortName: shortName,
            idBranch: idBranch,
        };

        const result = await db.addBranch(newBranch)
        res.send(result)
    },

    async edit(req,res) {
        const body = req.body;
        const branch = body.branch;
        await db.editBranch(branch)
        res.status(200)
        res.send("OK")
    },

    async get(req,res) {
        const branches = await db.getBranches()
        res.send(branches)
    }
}