const { createClient } = require('redis');
const { credentials } = require('../../config');

class RedisClient {
  constructor() {
    this.client = null;
    this.connected = false;
    this.isInitialized = false;
  }

  /**
   * Инициализирует соединение с Redis сервером
   */
  async initialize() {
    if (this.isInitialized) return;
    
    try {
      if (!credentials.redis || !credentials.redis.url) {
        console.log('Redis configuration not found or disabled, skipping connection');
        return;
      }

      this.client = createClient({
        url: credentials.redis.url
      });

      this.client.on('error', (err) => {
        console.error('Redis client error:', err);
        this.connected = false;
      });

      this.client.on('connect', () => {
        console.log('Redis client connected');
        this.connected = true;
      });

      await this.client.connect();
      this.isInitialized = true;
    } catch (error) {
      console.error('Failed to initialize Redis:', error);
      this.connected = false;
    }
  }

  /**
   * Получает значение из кеша по ключу
   * @param {String} key - Ключ кеша
   * @returns {Object|null} Сохраненное значение или null
   */
  async get(key) {
    if (!this.connected || !this.client) return null;
    
    try {
      const data = await this.client.get(key);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error(`Error getting cache for key ${key}:`, error);
      return null;
    }
  }

  /**
   * Сохраняет значение в кеш
   * @param {String} key - Ключ кеша
   * @param {Object} value - Значение для сохранения
   * @param {Number} ttl - Время жизни в секундах
   * @returns {Boolean} Успешность операции
   */
  async set(key, value, ttl = 3600) {
    if (!this.connected || !this.client) return false;

    try {
      const serialized = JSON.stringify(value);
      await this.client.set(key, serialized, { EX: ttl });
      return true;
    } catch (error) {
      console.error(`Error setting cache for key ${key}:`, error);
      return false;
    }
  }

  /**
   * Удаляет значение из кеша
   * @param {String} key - Ключ кеша
   * @returns {Boolean} Успешность операции
   */
  async delete(key) {
    if (!this.connected || !this.client) return false;
    
    try {
      await this.client.del(key);
      return true;
    } catch (error) {
      console.error(`Error deleting cache for key ${key}:`, error);
      return false;
    }
  }

  /**
   * Очищает кеш по шаблону ключа
   * @param {String} pattern - Шаблон ключа
   * @returns {Boolean} Успешность операции
   */
  async clearByPattern(pattern) {
    if (!this.connected || !this.client) return false;
    
    try {
      const keys = await this.client.keys(pattern);
      if (keys.length > 0) {
        await this.client.del(keys);
      }
      return true;
    } catch (error) {
      console.error(`Error clearing cache by pattern ${pattern}:`, error);
      return false;
    }
  }

  /**
   * Генерирует ключ кеша на основе имени ресурса и параметров
   * @param {String} resource - Имя ресурса
   * @param {Object} params - Параметры запроса
   * @returns {String} Ключ кеша
   */
  generateCacheKey(resource, params = {}) {
    const paramStr = Object.entries(params)
      .filter(([_, val]) => val !== undefined)
      .map(([key, val]) => {
        const value = typeof val === 'object' ? JSON.stringify(val) : val;
        return `${key}:${value}`;
      })
      .join('|');

    return `${resource}:${paramStr || 'default'}`;
  }

  /**
   * Закрывает соединение с Redis
   */
  async close() {
    if (this.client && this.connected) {
      try {
        await this.client.quit();
        console.log('Redis connection closed');
      } catch (error) {
        console.error('Error closing Redis connection:', error);
      }
    }
  }
}

// Создаем синглтон для использования во всем приложении
const redisClient = new RedisClient();

// Инициализируем соединение при импорте модуля
(async () => {
  await redisClient.initialize();
})();

module.exports = redisClient;
