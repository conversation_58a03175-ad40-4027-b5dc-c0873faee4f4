const auth = require('../middleware/auth');

/**
 * Фабрика для создания маршрутов с настраиваемой авторизацией
 * @param {Express.Application} router - Express router объект
 * @param {String} prefix - префикс API
 */
function RouteFactory(router, prefix) {
  this.router = router;
  this.prefix = prefix || '';

  /**
   * Добавляет маршрут с указанным методом, путем, авторизацией и обработчиком
   * @param {String} method - HTTP метод (get, post, put, delete)
   * @param {String} path - путь маршрута
   * @param {String|Array} authType - тип авторизации или массив middleware
   * @param {Function} handler - функция обработчик запроса
   */
  this.addRoute = function (method, path, authType, handler) {
    const fullPath = this.prefix + path;
    let middleware = [];

    // Базовая проверка секретного ключа всегда присутствует
    middleware.push(auth.appKey);

    // Добавляем middleware авторизации в зависимости от типа
    // if (authType) {
    //   if (typeof authType === 'string') {
    //     // Если это строка, ищем соответствующий тип в auth middleware
    //     if (auth[authType]) {
    //       const authMiddleware = auth[authType];
    //       if (Array.isArray(authMiddleware)) {
    //         middleware = [...middleware, ...authMiddleware];
    //       } else {
    //         middleware.push(authMiddleware);
    //       }
    //     } else {
    //       console.warn(`Warning: auth type "${authType}" not found for route ${fullPath}`);
    //     }
    //   } else if (Array.isArray(authType)) {
    //     // Если это массив, добавляем все middleware
    //     middleware = [...middleware, ...authType];
    //   } else if (typeof authType === 'function') {
    //     // Если это функция, добавляем как middleware
    //     middleware.push(authType);
    //   }
    // }

    // Регистрируем маршрут с собранными middleware
    this.router[method](fullPath, ...middleware, handler);

    // Логирование для отладки
    console.log(`Route registered: [${method.toUpperCase()}] ${fullPath}`);
  };

  // Удобные методы для разных HTTP методов
  this.get = function (path, authType, handler) {
    this.addRoute('get', path, authType, handler);
  };

  this.post = function (path, authType, handler) {
    this.addRoute('post', path, authType, handler);
  };

  this.put = function (path, authType, handler) {
    this.addRoute('put', path, authType, handler);
  };

  this.delete = function (path, authType, handler) {
    this.addRoute('delete', path, authType, handler);
  };

  this.options = function (path, authType, handler) {
    this.addRoute('options', path, authType, handler);
  };
}

module.exports = RouteFactory;
