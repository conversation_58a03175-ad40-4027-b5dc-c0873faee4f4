name: uer_flutter
description: UER Flutter App.
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.1.0+6

environment:
  sdk: ">=3.0.0 <4.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  archive: ^3.4.9
  cupertino_icons: ^1.0.8
  flutter_riverpod: ^2.5.1
  riverpod_annotation: ^2.3.5
  intl: any
  window_manager: ^0.4.3
  universal_io: ^2.2.2
  shared_preferences: ^2.2.3
  go_router: ^14.1.4
  infinite_scroll_pagination: ^4.0.0
  freezed_annotation: ^2.4.4
  json_annotation: ^4.9.0
  dio: ^5.4.3
  path_provider: ^2.1.3
  share_plus: ^10.1.3
  image_picker: ^1.1.2
  file_picker: ^8.0.3
  image: ^4.2.0
  package_info_plus: ^8.0.0
  http: ^1.2.1
  http_parser: ^4.0.2
  tuple: ^2.0.2
  extended_image: ^9.0.7
  permission_handler: ^11.3.1
  flutter_layout_grid: ^2.0.7
  media_kit: ^1.1.10
  media_kit_video: ^1.2.4 # For video rendering.
  media_kit_libs_video: ^1.0.4
  video_player_web_hls: ^1.2.1
  sliver_tools: ^0.2.12
  qr_mobile_vision: ^5.0.1
  mobile_scanner: ^6.0.3
  logger: ^2.3.0
  cached_network_image: ^3.3.1
  calendar_date_picker2: ^1.0.3
  qr_flutter: ^4.1.0
  onesignal_flutter: ^5.2.9
  universal_html: ^2.2.4
  fl_chart: ^0.70.0
  excel: ^4.0.3
  screenshot: ^3.0.0
  pdf: ^3.10.8
  numberpicker: ^2.1.2
  web: ^1.1.0
  flutter_dotenv: ^5.2.1
  simple_barcode_scanner: 0.3.0
  # firebase_core: ^3.11.0
  # firebase_analytics: ^11.4.2
  # appmetrica_plugin: ^3.1.0
  flutter_svg: ^2.0.10+1
  flutter_gen: ^5.7.0
  # compress
  flutter_image_compress: ^2.4.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.14.3
  freezed: ^2.5.7
  json_serializable: ^6.9.0
  go_router_builder: any
  build_runner: any
  flutter_gen_runner: ^5.8.0
  build_verify: any
  custom_lint: ^0.6.1
  riverpod_generator: ^2.3.9
  riverpod_lint: ^2.3.9

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - .env
    - assets/icon/
    - assets/icons/
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  fonts:
    - family: Inter
      fonts:
        - asset: assets/fonts/InterVariable.ttf
          style: normal
    - family: JetBrainsMono
      fonts:
        - asset: assets/fonts/JetBrainsMonoVariable.ttf
          style: normal
