module.exports = {
  apps: [
    {
      name: "uer",
      script: "./uer.js", // Путь к собранному JavaScript файлу основного приложения
      instances: 1,
      exec_mode: "fork", // Режим кластера для балансировки нагрузки
      env: {
        NODE_ENV: "production"
      }
    },
    {
      name: "uer-cron-jobs",
      script: "./crons/cronJobs.js", // Путь к собранному JavaScript файлу cron-заданий
      instances: 1, // Запуск одного экземпляра скрипта cron-заданий
      exec_mode: "fork", // Режим fork для скрипта cron-заданий
      env: {
        NODE_ENV: "production"
      }
    }
  ]
};