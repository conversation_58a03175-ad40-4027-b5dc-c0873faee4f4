// // File generated by FlutterFire CLI.
// // ignore_for_file: type=lint
// import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
// import 'package:flutter/foundation.dart'
//     show defaultTargetPlatform, kIsWeb, TargetPlatform;

// /// Default [FirebaseOptions] for use with your Firebase apps.
// ///
// /// Example:
// /// ```dart
// /// import 'firebase_options.dart';
// /// // ...
// /// await Firebase.initializeApp(
// ///   options: DefaultFirebaseOptions.currentPlatform,
// /// );
// /// ```
// class DefaultFirebaseOptions {
//   static FirebaseOptions get currentPlatform {
//     if (kIsWeb) {
//       return web;
//     }
//     switch (defaultTargetPlatform) {
//       case TargetPlatform.android:
//         return android;
//       case TargetPlatform.iOS:
//         return ios;
//       case TargetPlatform.macOS:
//         return macos;
//       case TargetPlatform.windows:
//         return windows;
//       case TargetPlatform.linux:
//         throw UnsupportedError(
//           'DefaultFirebaseOptions have not been configured for linux - '
//           'you can reconfigure this by running the FlutterFire CLI again.',
//         );
//       default:
//         throw UnsupportedError(
//           'DefaultFirebaseOptions are not supported for this platform.',
//         );
//     }
//   }

//   static const FirebaseOptions web = FirebaseOptions(
//     apiKey: 'AIzaSyAPPLQMc1LAIcCEfw3_wYgisMUDsnLzUCs',
//     appId: '1:394878484157:web:2b9f1633433934b4b701d4',
//     messagingSenderId: '394878484157',
//     projectId: 'uer-app-d8cdb',
//     authDomain: 'uer-app-d8cdb.firebaseapp.com',
//     storageBucket: 'uer-app-d8cdb.appspot.com',
//     measurementId: 'G-C5SKC1M62B',
//   );

//   static const FirebaseOptions android = FirebaseOptions(
//     apiKey: 'AIzaSyBMK3WJZeiys6vNS5fR9oRZJ7Y0bwCXlzQ',
//     appId: '1:394878484157:android:00a2e63f110103fbb701d4',
//     messagingSenderId: '394878484157',
//     projectId: 'uer-app-d8cdb',
//     storageBucket: 'uer-app-d8cdb.appspot.com',
//   );

//   static const FirebaseOptions ios = FirebaseOptions(
//     apiKey: 'AIzaSyCW-js_YO-xChu4Wlv2eaJFnjGbZJyA4iQ',
//     appId: '1:394878484157:ios:1970bddeeaafd646b701d4',
//     messagingSenderId: '394878484157',
//     projectId: 'uer-app-d8cdb',
//     storageBucket: 'uer-app-d8cdb.appspot.com',
//     iosBundleId: 'com.as.UERFlutter.OneSignalNotificationServiceExtension',
//   );

//   static const FirebaseOptions macos = FirebaseOptions(
//     apiKey: 'AIzaSyCW-js_YO-xChu4Wlv2eaJFnjGbZJyA4iQ',
//     appId: '1:394878484157:ios:caa58a4477a9887eb701d4',
//     messagingSenderId: '394878484157',
//     projectId: 'uer-app-d8cdb',
//     storageBucket: 'uer-app-d8cdb.appspot.com',
//     iosBundleId: 'com.example.uerFlutter',
//   );

//   static const FirebaseOptions windows = FirebaseOptions(
//     apiKey: 'AIzaSyAPPLQMc1LAIcCEfw3_wYgisMUDsnLzUCs',
//     appId: '1:394878484157:web:338f61b11d05783eb701d4',
//     messagingSenderId: '394878484157',
//     projectId: 'uer-app-d8cdb',
//     authDomain: 'uer-app-d8cdb.firebaseapp.com',
//     storageBucket: 'uer-app-d8cdb.appspot.com',
//     measurementId: 'G-Z8R6GJ252X',
//   );
// }
