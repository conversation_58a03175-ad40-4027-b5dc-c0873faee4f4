enum RoutesName<String> {
  loading,
  auth,
  main,
  infoSSZ,
  infoSSZDetail,
  concern,
  mainUser,
  branch,
  area,
  component,
  commonItems,
  newItems,
  overdeadlineItems,
  item,
  itemInfo,
  itemStats,
  settings,
  mainPhotos,
  photos,
  photoDetail,
  videos,
  videoDetail,
  qrScan,
  selectJob,
  statusSsz,
  workerStatus,
  selectAreaAccess,
  selectAreaList,
  receiving,
  qrGenerator,
  mergeItems,
  pushSSZSettings,
  pushEventSettings,
  pushEventDetailSettings,
  statusSettings,
  statusAddEdit,
  commentsPage,
  concernBranchStats,
  selectBranchList,
  notificationHistory,
  sszHoursStats,
  tutorialList,
  tutorialDetail,
  branchAddEdit,
  branchsList,
  areaAddEdit,
  areasList,
  usersList,
  userAddEdit,

  statsOrders,
  workStats,
}

extension RoutesNameExtension on RoutesName {
  String get path {
    switch (this) {
      case RoutesName.loading:
        return '/';
      case RoutesName.auth:
        return '/auth';
      case RoutesName.main:
        return '/main';
      case RoutesName.mainUser:
        return '/mainUser';
      case RoutesName.concern:
        return '/concern';
      case RoutesName.infoSSZ:
        return '/infoSSZ';
      case RoutesName.infoSSZDetail:
        return 'infoSSZDetail';
      case RoutesName.settings:
        return '/settings';
      case RoutesName.branch:
        return 'branch/:bid';
      case RoutesName.area:
        return 'area/:aid';
      case RoutesName.component:
        return 'component/:cid';
      case RoutesName.commonItems:
        return 'commonItems';
      case RoutesName.newItems:
        return 'newItems';
      case RoutesName.overdeadlineItems:
        return 'overdeadlineItems';
      case RoutesName.item:
        return 'item/:mid';
      case RoutesName.itemInfo:
        return 'itemInfo';
      case RoutesName.itemStats:
        return "itemStats";
      case RoutesName.mainPhotos:
        return "/mainPhotos";
      case RoutesName.photos:
        return "photos";
      case RoutesName.photoDetail:
        return "photoDetail";
      case RoutesName.videos:
        return "videos";
      case RoutesName.videoDetail:
        return "videoDetail";
      case RoutesName.qrScan:
        return "qrScan";
      case RoutesName.selectJob:
        return "selectJob";
      case RoutesName.statusSsz:
        return "statusSsz";
      case RoutesName.workerStatus:
        return "workerStatus";
      case RoutesName.selectAreaAccess:
        return "selectAreaAccess";
      case RoutesName.selectAreaList:
        return "selectAreaList";
      case RoutesName.receiving:
        return "receiving";
      case RoutesName.qrGenerator:
        return "qrGenerator";
      case RoutesName.mergeItems:
        return "mergeItems";
      case RoutesName.pushSSZSettings:
        return "pushSSZSettings";
      case RoutesName.pushEventSettings:
        return "pushEventSettings";
      case RoutesName.pushEventDetailSettings:
        return "pushEventDetailSettings";
      case RoutesName.statusSettings:
        return "statusSettings";
      case RoutesName.statusAddEdit:
        return "statusAddEdit";
      case RoutesName.commentsPage:
        return "commentsPage";
      case RoutesName.concernBranchStats:
        return "concernBranchStats";
      case RoutesName.selectBranchList:
        return "selectBranchList";
      case RoutesName.notificationHistory:
        return "notificationHistory";
      case RoutesName.sszHoursStats:
        return "sszHoursStats";
      case RoutesName.tutorialList:
        return "tutorialList";
      case RoutesName.tutorialDetail:
        return "tutorialDetail";
      case RoutesName.branchAddEdit:
        return "branchAddEdit";
      case RoutesName.branchsList:
        return "branchsList";
      case RoutesName.areaAddEdit:
        return "areaAddEdit";
      case RoutesName.areasList:
        return "areasList";
      case RoutesName.userAddEdit:
        return "userAddEdit";
      case RoutesName.usersList:
        return "usersList";
      case RoutesName.statsOrders:
        return "statsOrders";
      case RoutesName.workStats:
        return "workStats";
      default:
        return "";
    }
  }

  String get named {
    switch (this) {
      case RoutesName.loading:
        return '/';
      default:
        return name;
    }
  }
}
