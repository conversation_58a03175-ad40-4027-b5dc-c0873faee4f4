import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:uer_flutter/app/core/providers/states/user/user_state.dart';
import 'router_notifier.dart';
import 'routes_name.dart';

import 'extra_codec.dart';
import 'routes.dart';

part 'router_provider.g.dart';

final GlobalKey<NavigatorState> _rootNavigatorKey =
    GlobalKey<NavigatorState>(debugLabel: 'root');

@riverpod
GoRouter router(RouterRef ref) {
  return GoRouter(
    extraCodec: const MyExtraCodec(),
    navigatorKey: _rootNavigatorKey,
    initialLocation: RoutesName.auth.path,
    // Keep this to `true` if want to know what's going on under the hood
    debugLogDiagnostics: true,
    redirect: (context, state) {
      // We want to READ the state, here.
      // Go<PERSON><PERSON><PERSON> is already aware of state changes through `refreshListenable`
      final userState = ref.read(currentUserProvider);

      // From here we can use the state and implement our custom logic
      final areWeLoggingIn = state.uri.toString() == RoutesName.auth.path;
      final areWeLoadPage = state.uri.toString() == RoutesName.loading.path;

      return userState.maybeWhen(loading: () {
        // if (areWeLoggingIn) {
        //   return "/";
        // }
        return null;
      }, auth: (user) {
        if (areWeLoggingIn) return RoutesName.loading.path;
        return null;
      }, error: (e) {
        if (areWeLoadPage) {
          return RoutesName.auth.path;
        }
        return null;
      }, notAuth: () {
        return areWeLoggingIn ? null : RoutesName.auth.path;
      }, orElse: () {
        return null;
      });
    },
    // This is crucial to make the router work with Riverpod.
    refreshListenable: RouterNotifier(ref),
    routes: mainRoutes,
  );
}
