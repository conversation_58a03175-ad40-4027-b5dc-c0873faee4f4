import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:uer_flutter/app/features/concern/widgets/orders_page.dart';
import 'package:uer_flutter/app/features/concern/widgets/work_stats_screen.dart';
import 'package:uer_flutter/app/features/photos/index.dart';
import 'package:uer_flutter/app/features/settings/Areas/area_add_edit_page.dart';
import 'package:uer_flutter/app/features/settings/Areas/areas_settings_page.dart';
import 'package:uer_flutter/app/features/settings/Branchs/branch_add_edit_page.dart';
import 'package:uer_flutter/app/features/settings/Branchs/branchs_settings_page.dart';
import 'package:uer_flutter/app/features/settings/Users/<USER>';
import 'package:uer_flutter/app/features/settings/Users/<USER>';
import 'package:uer_flutter/app/routes/models/select_area_complex_data.dart';
import 'package:uer_flutter/app/routes/models/status_ssz_complex_data.dart';

import '../core/models/area/area_model.dart';
import '../core/models/branch/branch_model.dart';
import '../core/models/component/component_model.dart';
import '../core/models/item/item_model.dart';
import '../core/models/status/status_model.dart';
import '../core/models/user/user_model.dart';
import '../core/models/video_test/video_test_model.dart';
import '../features/area/area_components/area_page.dart';
import '../features/auth/auth_page.dart';
import '../features/auth/load_page.dart';
import '../features/branch/branch_page.dart';
import '../features/common_items/common_items_page.dart';
import '../features/component/component_page.dart';
import '../features/info_ssz/detail/info_ssz_detail_page.dart';
import '../features/info_ssz/main/info_ssz_page.dart';
import '../features/item/comments_page/comments_page.dart';
import '../features/item/item_info_full/item_info_full_page.dart';
import '../features/item/item_page.dart';
import '../features/item/merge_items/merge_items_page.dart';
import '../features/items/deadline_items/overdeadline_items_page.dart';
import '../features/items/new_items/new_items_page.dart';
import '../features/photos/photo_detail.dart';
import '../features/photos/photos_page.dart';
import '../features/push_history/push_history_page.dart';
import '../features/qr/qr_gen/qr_generate_page.dart';
import '../features/qr/qr_scan/scan_qr_alt_page.dart';
import '../features/qr/qr_scan/scan_qr_windows_page.dart';
import '../features/receiving/receiving_page.dart';
import '../features/select_area/select_area_list_page.dart';
import '../features/select_branch/select_branch_list_page.dart';
//import '../features/qr/qr_scan/scan_qr_page.dart';
import '../features/select_tasks/area_access/select_area_access_page.dart';
import '../features/select_tasks/select_job/select_job_page.dart';
import '../features/select_tasks/status_ssz/status_ssz_page.dart';
import '../features/select_tasks/worker_status/worker_status_page.dart';
import '../features/settings/PushEventDetailSettings/push_event_detail_settings_page.dart';
import '../features/settings/PushEventSettings/push_event_settings_page.dart';
import '../features/settings/PushSSZSettings/push_ssz_settings_page.dart';
import '../features/settings/StatusSettings/StatusAddEdit/status_add_edit_page.dart';
import '../features/settings/StatusSettings/status_settings_page.dart';
import '../features/settings/Tutorials/tutorial_detail_page.dart';
import '../features/settings/Tutorials/tutorial_list_page.dart';
import '../features/settings/settings_page.dart';
import '../features/stats/concern_branch_stats/concern_branch_stats_page.dart';
import '../features/stats/item_stats/item_stats_page.dart';
import '../features/stats/ssz_hours_stats/ssz_hours_stats_page.dart';
import '../features/videos/video_player.dart';
import '../features/videos/videos_test_page.dart';
import '../helpers/platform_check.dart';
import '../main_page.dart';
import 'bottom_bar.dart';
import 'models/common_items_complex_data.dart';
import 'models/photo_detail_complex_data.dart';
import 'models/video_detail_complex_data.dart';
import 'models/worker_status_complex_data.dart';
import 'routes_name.dart';

// TODO: Переделать роутинг на Autoroute (проще и меньше кода)

// Сбор данных маршрутных переходовы
void routeAnalytics(
  String path,
  String name,
  Map<String, Object> parameters,
) async {
  // await FirebaseAnalytics.instance.logScreenView(
  //   screenName: name,
  //   parameters: {'path': path, ...parameters},
  // );
  // await AppMetrica.reportEventWithMap('Переход на другой маршрут', {
  //   'targetScreenName': name,
  //   'path': path,
  //   ...parameters,
  // });
}

final GlobalKey<NavigatorState> _shellNavigatorKey =
    GlobalKey<NavigatorState>(debugLabel: 'shell');

List<RouteBase> mainRoutes = [
  StatefulShellRoute.indexedStack(
      builder: (context, state, navigationShell) {
        // Return the widget that implements the custom shell (in this case
        // using a BottomNavigationBar). The StatefulNavigationShell is passed
        // to be able access the state of the shell and to navigate to other
        // branches in a stateful way.
        return ScaffoldWithNavBar(navigationShell: navigationShell);
      },
      branches: [
        StatefulShellBranch(
            navigatorKey: _shellNavigatorKey, routes: mainTabRoutes),
        StatefulShellBranch(routes: photosTabRoutes),
        StatefulShellBranch(routes: sszTabRoutes),
        StatefulShellBranch(routes: settingsTabRoutes)
      ]),
  GoRoute(
    path: RoutesName.auth.path,
    name: RoutesName.auth.named,
    builder: (BuildContext context, GoRouterState state) {
      routeAnalytics(state.path ?? '', state.name ?? '', {
        'full_path': state.uri.toString(),
        'extra': state.extra.toString(),
      });
      return const AuthPage();
    },
  ),
  GoRoute(
    path: RoutesName.loading.path,
    name: RoutesName.loading.named,
    builder: (BuildContext context, GoRouterState state) {
      routeAnalytics(state.path ?? '', state.name ?? '', {
        'full_path': state.uri.toString(),
        'extra': state.extra.toString(),
      });
      return const LoadPage();
    },
  ),
];

List<RouteBase> mainTabRoutes = [
  GoRoute(
    path: RoutesName.main.path,
    name: RoutesName.main.named,
    // builder: (BuildContext context, GoRouterState state) {
    //   return NoTransitionPage(child: const MainPage());
    // },
    pageBuilder: (BuildContext context, GoRouterState state) {
      return const NoTransitionPage(child: MainPage());
    },
    routes: <RouteBase>[
      // The details screen to display stacked on the inner Navigator.
      // This will cover screen A but not the application shell.
      GoRoute(
        path: RoutesName.component.path,
        name: RoutesName.component.named,
        builder: (BuildContext context, GoRouterState state) {
          final cid = state.pathParameters['cid']!;
          routeAnalytics(state.path ?? '', state.name ?? '', {
            'full_path': state.uri.toString(),
            'extra': state.extra.toString(),
          });
          return ComponentPage(componentID: cid);
        },
      ),

      GoRoute(
        path: RoutesName.branch.path,
        name: RoutesName.branch.named,
        builder: (BuildContext context, GoRouterState state) {
          final bid = state.pathParameters['bid']!;
          routeAnalytics(state.path ?? '', state.name ?? '', {
            'full_path': state.uri.toString(),
            'extra': state.extra.toString(),
          });
          return MainBranchPage(branchID: int.parse(bid));
        },
      ),

      GoRoute(
        path: RoutesName.area.path,
        name: RoutesName.area.named,
        builder: (BuildContext context, GoRouterState state) {
          final aid = state.pathParameters['aid']!;
          routeAnalytics(state.path ?? '', state.name ?? '', {
            'full_path': state.uri.toString(),
            'extra': state.extra.toString(),
          });
          return AreaPage(areaID: int.parse(aid));
        },
      ),

      GoRoute(
        path: RoutesName.commonItems.path,
        name: RoutesName.commonItems.named,
        builder: (BuildContext context, GoRouterState state) {
          final data = state.extra as CommonItemsPageComplexData;
          routeAnalytics(state.path ?? '', state.name ?? '', {
            'full_path': state.uri.toString(),
            'extra': state.extra.toString(),
          });
          return CommonItemsPage(
              title: data.title, searchModel: data.searchModel);
        },
      ),

      GoRoute(
        path: RoutesName.newItems.path,
        name: RoutesName.newItems.named,
        builder: (BuildContext context, GoRouterState state) {
          var branchID = _getQueryParam(state, "branchID");
          routeAnalytics(state.path ?? '', state.name ?? '', {
            'full_path': state.uri.toString(),
            'extra': state.extra.toString(),
          });
          int? parsedBranchID = branchID != null && branchID.isNotEmpty
              ? int.tryParse(branchID)
              : null;

          return NewItemsPage(currentBranchID: parsedBranchID);
        },
      ),

      GoRoute(
        path: RoutesName.receiving.path,
        name: RoutesName.receiving.named,
        builder: (BuildContext context, GoRouterState state) {
          String mainID = state.extra as String;
          routeAnalytics(state.path ?? '', state.name ?? '', {
            'full_path': state.uri.toString(),
            'extra': state.extra.toString(),
          });
          return ReceivingPage(mainID: mainID);
        },
      ),

      GoRoute(
        path: RoutesName.qrGenerator.path,
        name: RoutesName.qrGenerator.named,
        builder: (BuildContext context, GoRouterState state) {
          routeAnalytics(state.path ?? '', state.name ?? '', {
            'full_path': state.uri.toString(),
            'extra': state.extra.toString(),
          });
          return QRGeneratePage(
            mainID: _getQueryParam(state, 'mid'),
            componentID: _getQueryParam(state, 'cid'),
          );
        },
      ),

      GoRoute(
        path: RoutesName.overdeadlineItems.path,
        name: RoutesName.overdeadlineItems.named,
        builder: (BuildContext context, GoRouterState state) {
          int? bid = state.extra as int?;
          routeAnalytics(state.path ?? '', state.name ?? '', {
            'full_path': state.uri.toString(),
            'extra': state.extra.toString(),
          });
          return OverDeadLineItemsPage(currentBranchID: bid);
        },
      ),

      GoRoute(
          path: RoutesName.item.path,
          name: RoutesName.item.named,
          builder: (BuildContext context, GoRouterState state) {
            final mid = state.pathParameters['mid']!;
            routeAnalytics(state.path ?? '', state.name ?? '', {
              'full_path': state.uri.toString(),
              'extra': state.extra.toString(),
            });
            return ItemPage(mainID: mid);
          },
          routes: [
            GoRoute(
              path: RoutesName.commentsPage.path,
              name: RoutesName.commentsPage.named,
              builder: (BuildContext context, GoRouterState state) {
                var components = state.extra as List<ComponentModel>;
                routeAnalytics(state.path ?? '', state.name ?? '', {
                  'full_path': state.uri.toString(),
                  'extra': state.extra.toString(),
                });
                return CommentsPage(components: components);
              },
            ),
          ]),

      GoRoute(
        path: RoutesName.itemInfo.path,
        name: RoutesName.itemInfo.named,
        builder: (BuildContext context, GoRouterState state) {
          ItemModel item = state.extra as ItemModel;
          routeAnalytics(state.path ?? '', state.name ?? '', {
            'full_path': state.uri.toString(),
            'extra': state.extra.toString(),
          });
          return ItemInfoFullPage(item: item);
        },
      ),

      GoRoute(
        path: RoutesName.itemStats.path,
        name: RoutesName.itemStats.named,
        builder: (BuildContext context, GoRouterState state) {
          var mainID = state.extra as String;
          routeAnalytics(state.path ?? '', state.name ?? '', {
            'full_path': state.uri.toString(),
            'extra': state.extra.toString(),
          });
          return ItemStatsPage(mainID: mainID);
        },
      ),

      GoRoute(
        path: RoutesName.concernBranchStats.path,
        name: RoutesName.concernBranchStats.named,
        builder: (BuildContext context, GoRouterState state) {
          int? branchID = state.extra as int?;
          routeAnalytics(state.path ?? '', state.name ?? '', {
            'full_path': state.uri.toString(),
            'extra': state.extra.toString(),
          });
          return ConcernBranchStatsPage(branch: branchID);
        },
      ),

      GoRoute(
        path: RoutesName.photos.path,
        name: RoutesName.photos.named,
        builder: (BuildContext context, GoRouterState state) {
          ItemModel item = state.extra as ItemModel;
          routeAnalytics(state.path ?? '', state.name ?? '', {
            'full_path': state.uri.toString(),
            'extra': state.extra.toString(),
          });
          return PhotosPage(item: item);
        },
      ),

      GoRoute(
        path: RoutesName.photoDetail.path,
        name: RoutesName.photoDetail.named,
        builder: (BuildContext context, GoRouterState state) {
          var obj = state.extra as PhotoDetailPageComplexData;
          routeAnalytics(state.path ?? '', state.name ?? '', {
            'full_path': state.uri.toString(),
            'extra': state.extra.toString(),
          });
          return PhotoDetail(
            photos: obj.photos,
            initialIndex: obj.initialIndex,
          );
        },
      ),

      GoRoute(
        path: RoutesName.videos.path,
        name: RoutesName.videos.named,
        builder: (BuildContext context, GoRouterState state) {
          List<VideoTestModel> videos = state.extra as List<VideoTestModel>;
          routeAnalytics(state.path ?? '', state.name ?? '', {
            'full_path': state.uri.toString(),
            'extra': state.extra.toString(),
          });
          return VideosTestPage(
            videos: videos,
          );
        },
      ),

      GoRoute(
        path: RoutesName.videoDetail.path,
        name: RoutesName.videoDetail.named,
        builder: (BuildContext context, GoRouterState state) {
          var obj = state.extra as VideoDetailPageComplexData;
          routeAnalytics(state.path ?? '', state.name ?? '', {
            'full_path': state.uri.toString(),
            'extra': state.extra.toString(),
          });
          return VideoPlayerPage(
            url: obj.url,
          );
        },
      ),

      GoRoute(
        path: RoutesName.qrScan.path,
        name: RoutesName.qrScan.named,
        builder: (BuildContext context, GoRouterState state) {
          routeAnalytics(state.path ?? '', state.name ?? '', {
            'full_path': state.uri.toString(),
            'extra': state.extra.toString(),
          });
          if (isWeb() == true) {
            return const ScanQRAltPage();
          } else if (isMacOS() == true) {
            return const ScanQRAltPage();
          } else if (isWindows() == true) {
            return const WindowsQRScanPage(); // Используем новую страницу для Windows
          } else {
            return const ScanQRAltPage();
          }
        },
      ),

      GoRoute(
        path: RoutesName.selectJob.path,
        name: RoutesName.selectJob.named,
        builder: (BuildContext context, GoRouterState state) {
          routeAnalytics(state.path ?? '', state.name ?? '', {
            'full_path': state.uri.toString(),
            'extra': state.extra.toString(),
          });
          return SelectJobPage(
            mainID: _getQueryParam(state, "mainID")!,
            componentID: _getQueryParam(state, "componentID")!,
            areaID: int.parse(_getQueryParam(state, "areaID")!),
          );
        },
      ),

      GoRoute(
        path: RoutesName.statusSsz.path,
        name: RoutesName.statusSsz.named,
        builder: (BuildContext context, GoRouterState state) {
          var obj = state.extra as StatusSSZPageComplexData;
          routeAnalytics(state.path ?? '', state.name ?? '', {
            'full_path': state.uri.toString(),
            'extra': state.extra.toString(),
          });
          return StatusSszPage(
              mainID: obj.mainID,
              idArea: obj.idArea,
              idAreaStr: obj.idAreaStr,
              histories: obj.histories);
        },
      ),

      GoRoute(
        path: RoutesName.workerStatus.path,
        name: RoutesName.workerStatus.named,
        builder: (BuildContext context, GoRouterState state) {
          var obj = state.extra as WorkerStatusPageComplexData;
          routeAnalytics(state.path ?? '', state.name ?? '', {
            'full_path': state.uri.toString(),
            'extra': state.extra.toString(),
          });
          return WorkerStatusPage(
            history: obj.history,
            task: obj.task,
          );
        },
      ),

      GoRoute(
        path: RoutesName.selectAreaAccess.path,
        name: RoutesName.selectAreaAccess.named,
        builder: (BuildContext context, GoRouterState state) {
          var data = state.extra as SelectAreaAccessPageComplexData;
          routeAnalytics(state.path ?? '', state.name ?? '', {
            'full_path': state.uri.toString(),
            'extra': state.extra.toString(),
          });
          return SelectAreaAccessPage(
              branchID: data.branchID,
              areaID: data.areaID,
              componentID: data.componentID,
              selectedAreas: data.selectedAreas);
        },
      ),

      GoRoute(
        path: RoutesName.selectAreaList.path,
        name: RoutesName.selectAreaList.named,
        builder: (BuildContext context, GoRouterState state) {
          var branchID = _getQueryParam(state, "branchID");
          var removeAreaID = _getQueryParam(state, "removeAreaID");

          // Преобразование параметров запроса в числовые значения, если они не пусты
          int? parsedBranchID = branchID != null && branchID.isNotEmpty
              ? int.tryParse(branchID)
              : null;

          int? parsedRemoveAreaID =
              removeAreaID != null && removeAreaID.isNotEmpty
                  ? int.tryParse(removeAreaID)
                  : null;

          routeAnalytics(state.path ?? '', state.name ?? '', {
            'full_path': state.uri.toString(),
            'extra': state.extra.toString(),
          });
          // Возвращение виджета
          return SelectAreaListPage(
            branchID: parsedBranchID,
            removeAreaID: parsedRemoveAreaID,
          );
        },
      ),

      GoRoute(
        path: RoutesName.notificationHistory.path,
        name: RoutesName.notificationHistory.named,
        builder: (BuildContext context, GoRouterState state) {
          routeAnalytics(state.path ?? '', state.name ?? '', {
            'full_path': state.uri.toString(),
            'extra': state.extra.toString(),
          });
          return const PushHistoryPage();
        },
      ),

      GoRoute(
        path: RoutesName.selectBranchList.path,
        name: RoutesName.selectBranchList.named,
        builder: (BuildContext context, GoRouterState state) {
          var removeBranchID = state.extra as int?;
          routeAnalytics(state.path ?? '', state.name ?? '', {
            'full_path': state.uri.toString(),
            'extra': state.extra.toString(),
          });
          return SelectBranchListPage(removeBranchID: removeBranchID);
        },
      ),

      GoRoute(
        path: RoutesName.mergeItems.path,
        name: RoutesName.mergeItems.named,
        builder: (BuildContext context, GoRouterState state) {
          var mainID = state.extra as String;
          routeAnalytics(state.path ?? '', state.name ?? '', {
            'full_path': state.uri.toString(),
            'extra': state.extra.toString(),
          });
          return MergeItemsPage(mainID: mainID);
        },
      ),

      GoRoute(
        path: RoutesName.sszHoursStats.path,
        name: RoutesName.sszHoursStats.named,
        builder: (BuildContext context, GoRouterState state) {
          routeAnalytics(state.path ?? '', state.name ?? '', {
            'full_path': state.uri.toString(),
            'extra': state.extra.toString(),
          });
          return const SSZHoursStatsPage();
        },
      ),

      GoRoute(
        path: RoutesName.statsOrders.path,
        name: RoutesName.statsOrders.named,
        builder: (BuildContext context, GoRouterState state) {
          var branchId = state.extra as int?;
          routeAnalytics(state.path ?? '', state.name ?? '', {
            'full_path': state.uri.toString(),
            'extra': state.extra.toString(),
          });
          return OrdersPage(branchId: branchId);
        },
      ),

      GoRoute(
        path: RoutesName.workStats.path,
        name: RoutesName.workStats.named,
        builder: (BuildContext context, GoRouterState state) {
          // var branchId = state.extra as int?;
          // routeAnalytics(state.path ?? '', state.name ?? '', {
          //   'full_path': state.uri.toString(),
          //   'extra': state.extra.toString(),
          // });
          return WorkStatsScreen();
        },
      )
    ],
  ),
];

List<RouteBase> photosTabRoutes = [
  GoRoute(
    path: RoutesName.mainPhotos.path,
    name: RoutesName.mainPhotos.named,
    // builder: (BuildContext context, GoRouterState state) {
    //   return const MainPhotosPage();
    // },
    pageBuilder: (BuildContext context, GoRouterState state) {
      routeAnalytics(state.path ?? '', state.name ?? '', {
        'full_path': state.uri.toString(),
        'extra': state.extra.toString(),
      });
      return const NoTransitionPage(child: MainPhotosPage());
    },
    routes: List.empty(),
  ),
];

List<RouteBase> sszTabRoutes = [
  GoRoute(
    path: RoutesName.infoSSZ.path,
    name: RoutesName.infoSSZ.named,
    // builder: (BuildContext context, GoRouterState state) {
    //   return const SettingsPage();
    // },
    pageBuilder: (BuildContext context, GoRouterState state) {
      routeAnalytics(state.path ?? '', state.name ?? '', {
        'full_path': state.uri.toString(),
        'extra': state.extra.toString(),
      });
      return const NoTransitionPage(child: InfoSSZPage());
    },
    routes: <RouteBase>[
      /// Same as "/a/details", but displayed on the root Navigator by
      /// specifying [parentNavigatorKey]. This will cover both screen B
      /// and the application shell.
      GoRoute(
        path: RoutesName.infoSSZDetail.path,
        name: RoutesName.infoSSZDetail.named,
        pageBuilder: (BuildContext context, GoRouterState state) {
          routeAnalytics(state.path ?? '', state.name ?? '', {
            'full_path': state.uri.toString(),
            'extra': state.extra.toString(),
          });
          return NoTransitionPage(
              child: InfoSSZDetailPage(
            mainID: _getQueryParam(state, "mainID")!,
            dateStr: _getQueryParam(state, "dateStr")!,
            repairNumber: _getQueryParam(state, "repairNumber")!,
            areaID: _getQueryParam(state, "areaID"),
            branchID: _getQueryParam(state, "branchID"),
          ));
        },
      ),
    ],
  ),
];

List<RouteBase> settingsTabRoutes = [
  GoRoute(
    path: RoutesName.settings.path,
    name: RoutesName.settings.named,
    // builder: (BuildContext context, GoRouterState state) {
    //   return const SettingsPage();
    // },
    pageBuilder: (BuildContext context, GoRouterState state) {
      routeAnalytics(state.path ?? '', state.name ?? '', {
        'full_path': state.uri.toString(),
        'extra': state.extra.toString(),
      });
      return const NoTransitionPage(child: SettingsPage());
    },
    routes: <RouteBase>[
      GoRoute(
        path: RoutesName.pushSSZSettings.path,
        name: RoutesName.pushSSZSettings.named,
        builder: (BuildContext context, GoRouterState state) {
          routeAnalytics(state.path ?? '', state.name ?? '', {
            'full_path': state.uri.toString(),
            'extra': state.extra.toString(),
          });
          return const PushSSZSettingsPage();
        },
      ),
      GoRoute(
        path: RoutesName.tutorialList.path,
        name: RoutesName.tutorialList.named,
        builder: (BuildContext context, GoRouterState state) {
          routeAnalytics(state.path ?? '', state.name ?? '', {
            'full_path': state.uri.toString(),
            'extra': state.extra.toString(),
          });
          return const TutorialListPage();
        },
        routes: [
          GoRoute(
            path: RoutesName.tutorialDetail.path,
            name: RoutesName.tutorialDetail.named,
            builder: (BuildContext context, GoRouterState state) {
              routeAnalytics(state.path ?? '', state.name ?? '', {
                'full_path': state.uri.toString(),
                'extra': state.extra.toString(),
              });
              return TutorialDetailPage(
                  name: _getQueryParam(state, "name")!,
                  url: _getQueryParam(state, "url")!);
            },
          ),
        ],
      ),
      GoRoute(
          path: RoutesName.pushEventSettings.path,
          name: RoutesName.pushEventSettings.named,
          builder: (BuildContext context, GoRouterState state) {
            routeAnalytics(state.path ?? '', state.name ?? '', {
              'full_path': state.uri.toString(),
              'extra': state.extra.toString(),
            });
            return const PushEventSettingsPage();
          },
          routes: [
            GoRoute(
              path: RoutesName.pushEventDetailSettings.path,
              name: RoutesName.pushEventDetailSettings.named,
              builder: (BuildContext context, GoRouterState state) {
                var areaID = state.extra as int;
                routeAnalytics(state.path ?? '', state.name ?? '', {
                  'full_path': state.uri.toString(),
                  'extra': state.extra.toString(),
                });
                return PushEventDetailSettingsPage(areaID: areaID);
              },
            ),
          ]),
      GoRoute(
          path: RoutesName.statusSettings.path,
          name: RoutesName.statusSettings.named,
          builder: (BuildContext context, GoRouterState state) {
            var models = state.extra as List<StatusModel>?;
            routeAnalytics(state.path ?? '', state.name ?? '', {
              'full_path': state.uri.toString(),
              'extra': state.extra.toString(),
            });
            return StatusSettingsPage(
              selectedModels: models,
            );
          },
          routes: [
            GoRoute(
              path: RoutesName.statusAddEdit.path,
              name: RoutesName.statusAddEdit.named,
              builder: (BuildContext context, GoRouterState state) {
                var status = state.extra as StatusModel?;
                routeAnalytics(state.path ?? '', state.name ?? '', {
                  'full_path': state.uri.toString(),
                  'extra': state.extra.toString(),
                });
                return StatusAddEditPage(status: status);
              },
            ),
          ]),
      GoRoute(
          path: RoutesName.branchsList.path,
          name: RoutesName.branchsList.named,
          builder: (BuildContext context, GoRouterState state) {
            routeAnalytics(state.path ?? '', state.name ?? '', {
              'full_path': state.uri.toString(),
              'extra': state.extra.toString(),
            });
            return const BranchsSettingsPage();
          },
          routes: [
            GoRoute(
              path: RoutesName.branchAddEdit.path,
              name: RoutesName.branchAddEdit.named,
              builder: (BuildContext context, GoRouterState state) {
                var branch = state.extra as BranchModel?;
                routeAnalytics(state.path ?? '', state.name ?? '', {
                  'full_path': state.uri.toString(),
                  'extra': state.extra.toString(),
                });
                return BranchAddEditPage(branch: branch);
              },
            ),
          ]),
      GoRoute(
          path: RoutesName.areasList.path,
          name: RoutesName.areasList.named,
          builder: (BuildContext context, GoRouterState state) {
            routeAnalytics(state.path ?? '', state.name ?? '', {
              'full_path': state.uri.toString(),
              'extra': state.extra.toString(),
            });
            return const AreasSettingsPage();
          },
          routes: [
            GoRoute(
              path: RoutesName.areaAddEdit.path,
              name: RoutesName.areaAddEdit.named,
              builder: (BuildContext context, GoRouterState state) {
                var area = state.extra as AreaModel?;
                routeAnalytics(state.path ?? '', state.name ?? '', {
                  'full_path': state.uri.toString(),
                  'extra': state.extra.toString(),
                });
                return AreaAddEditPage(area: area);
              },
            ),
          ]),
      GoRoute(
          path: RoutesName.usersList.path,
          name: RoutesName.usersList.named,
          builder: (BuildContext context, GoRouterState state) {
            routeAnalytics(state.path ?? '', state.name ?? '', {
              'full_path': state.uri.toString(),
              'extra': state.extra.toString(),
            });
            return const UsersSettingsPage();
          },
          routes: [
            GoRoute(
              path: RoutesName.userAddEdit.path,
              name: RoutesName.userAddEdit.named,
              builder: (BuildContext context, GoRouterState state) {
                var user = state.extra as UserModel?;
                routeAnalytics(state.path ?? '', state.name ?? '', {
                  'full_path': state.uri.toString(),
                  'extra': state.extra.toString(),
                });
                return UserAddEditPage(user: user);
              },
            ),
          ]),
    ],
  ),
];

String? _getQueryParam(GoRouterState state, String paramName) =>
    state.uri.queryParameters[paramName];
