import 'dart:convert';

import 'package:uer_flutter/app/core/models/photo/photo_model.dart';
import 'package:uer_flutter/app/core/models/task_ssz/task_ssz_model.dart';
import 'package:uer_flutter/app/routes/models/select_area_complex_data.dart';
import 'package:uer_flutter/app/routes/models/status_ssz_complex_data.dart';

import '../core/models/component_history/component_history_model.dart';
import '../core/models/search/search_model.dart';

import '../features/videos/video_player.dart';
import 'models/common_items_complex_data.dart';
import 'models/photo_detail_complex_data.dart';
import 'models/video_detail_complex_data.dart';
import 'models/worker_status_complex_data.dart';

class MyExtraCodec extends Codec<Object?, Object?> {
  /// Create a codec.
  const MyExtraCodec();
  @override
  Converter<Object?, Object?> get decoder => const _MyExtraDecoder();

  @override
  Converter<Object?, Object?> get encoder => const _MyExtraEncoder();
}

class _MyExtraDecoder extends Converter<Object?, Object?> {
  const _MyExtraDecoder();
  @override
  Object? convert(Object? input) {
    if (input == null) {
      return null;
    }
    final List<Object?> inputAsList = input as List<Object?>;
    if (inputAsList[0] == 'CommonItemsPageComplexData') {
      return CommonItemsPageComplexData(
          title: inputAsList[1]! as String,
          searchModel: inputAsList[2]! as SearchModel);
    }
    if (inputAsList[0] == 'SelectAreaAccessPageComplexData') {
      return SelectAreaAccessPageComplexData(
          branchID: inputAsList[1]! as int,
          areaID: inputAsList[2]! as int,
          componentID: inputAsList[3]! as String,
          selectedAreas: inputAsList[4]! as List<int>);
    }
    if (inputAsList[0] == 'PhotoDetailPageComplexData') {
      return PhotoDetailPageComplexData(
        photos: inputAsList[1]! as List<PhotoModel>,
        initialIndex: inputAsList[2]! as int,
      );
    }
    if (inputAsList[0] == 'VideoDetailPageComplexData') {
      return VideoDetailPageComplexData(
        url: inputAsList[1]! as String,
      );
    }
    if (inputAsList[0] == 'StatusSSZPageComplexData') {
      return StatusSSZPageComplexData(
          mainID: inputAsList[1]! as String,
          idArea: inputAsList[2]! as int,
          histories: inputAsList[3]! as List<ComponentHistoryModel>,
          idAreaStr: inputAsList.length > 4 ? inputAsList[4] as String? : null);
    }
    if (inputAsList[0] == 'WorkerStatusPageComplexData') {
      return WorkerStatusPageComplexData(
        history: inputAsList.length > 1
            ? inputAsList[1] as ComponentHistoryModel?
            : null,
        task: inputAsList.length > 2 ? inputAsList[2] as TaskSSZModel? : null,
      );
    }
    // if (inputAsList[0] == 'ComplexData2') {
    //   return ComplexData2(inputAsList[1]! as String);
    // }
    //throw FormatException('Unable tp parse input: $input');
    return input;
  }
}

class _MyExtraEncoder extends Converter<Object?, Object?> {
  const _MyExtraEncoder();
  @override
  Object? convert(Object? input) {
    if (input == null) {
      return null;
    }
    switch (input.runtimeType) {
      case CommonItemsPageComplexData:
        return <Object?>[
          'CommonItemsPageComplexData',
          (input as CommonItemsPageComplexData).title,
          input.searchModel
        ];
      case SelectAreaAccessPageComplexData:
        return <Object?>[
          'SelectAreaAccessPageComplexData',
          (input as SelectAreaAccessPageComplexData).branchID,
          input.areaID,
          input.componentID,
          input.selectedAreas
        ];
      case PhotoDetailPageComplexData:
        return <Object?>[
          'PhotoDetailPageComplexData',
          (input as PhotoDetailPageComplexData).photos,
          input.initialIndex
        ];
      case VideoDetailPageComplexData:
        return <Object?>[
          'VideoDetailPageComplexData',
          (input as VideoDetailPageComplexData).url,
        ];
      case StatusSSZPageComplexData:
        return <Object?>[
          'StatusSSZPageComplexData',
          (input as StatusSSZPageComplexData).mainID,
          input.idArea,
          input.histories,
          input.idAreaStr
        ];
      case WorkerStatusPageComplexData:
        return <Object?>[
          'WorkerStatusPageComplexData',
          (input as WorkerStatusPageComplexData).history,
          input.task
        ];
      // case ComplexData2:
      //   return <Object?>['ComplexData2', (input as ComplexData2).data];
      default:
        return input;
      //throw FormatException('Cannot encode type ${input.runtimeType}');
    }
  }
}

// /// A complex class.
// class ComplexData1 {
//   /// Create a complex object.
//   ComplexData1(this.data);

//   /// The data.
//   final String data;

//   @override
//   String toString() => 'ComplexData1(data: $data)';
// }
