import '../../core/models/component_history/component_history_model.dart';
import '../../core/models/task_ssz/task_ssz_model.dart';

class WorkerStatusPageComplexData {
  final ComponentHistoryModel? history;
  final TaskSSZModel? task;

  WorkerStatusPageComplexData({required this.history, required this.task});

  @override
  String toString() =>
      'WorkerStatusPageComplexData(history: $history, task: $task)';
}
