class SelectAreaAccessPageComplexData {
  final int branchID;
  final int areaID;
  final String componentID;
  final List<int> selectedAreas;

  SelectAreaAccessPageComplexData(
      {required this.branchID,
      required this.areaID,
      required this.componentID,
      required this.selectedAreas});

  @override
  String toString() =>
      'SelectAreaAccessPageComplexData(branchID: $branchID, areaID: $areaID, componentID: $componentID, selectedAreas: $selectedAreas)';
}
