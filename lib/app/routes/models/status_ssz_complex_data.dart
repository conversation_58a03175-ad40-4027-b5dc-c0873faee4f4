import '../../core/models/component_history/component_history_model.dart';

class StatusSSZPageComplexData {
  final String mainID;
  final int idArea;
  final List<ComponentHistoryModel> histories;
  String? idAreaStr;

  StatusSSZPageComplexData({
    required this.mainID,
    required this.idArea,
    required this.histories,
    this.idAreaStr,
  });

  @override
  String toString() =>
      'StatusSSZPageComplexData(mainID: $mainID, idArea: $idArea, histories: $histories, idAreaStr: $idAreaStr)';
}
