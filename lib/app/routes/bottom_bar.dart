import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:uer_flutter/app/widgets/bars/bottom_bar.dart';
import 'package:uer_flutter/app/widgets/bars/rail.dart';

import '../core/providers/common/common_providers.dart';

class ScaffoldWithNavBar extends ConsumerWidget {
  const ScaffoldWithNavBar({
    required this.navigationShell,
    super.key,
  });

  final StatefulNavigationShell navigationShell;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;
    final showNavBar = ref.watch(showBottomBarProvider);

    return Scaffold(
      body: Row(children: [
        if (isLandscape)
          CustomNavigationRail(
            currentIndex: navigationShell.currentIndex,
            onSelected: (index) => _onItemTapped(index, context),
          ),
        Expanded(child: navigationShell),
      ]),
      bottomNavigationBar: showNavBar == true && !isLandscape
          ? CustomBottomNavigationBar(
              currentIndex: navigationShell.currentIndex,
              onTap: (int idx) => _onItemTapped(idx, context),
            )
          : const SizedBox(),
    );
  }

  void _onItemTapped(int index, BuildContext context) {
    // When navigating to a new branch, it's recommended to use the goBranch
    // method, as doing so makes sure the last navigation state of the
    // Navigator for the branch is restored.
    navigationShell.goBranch(
      index,
      // A common pattern when using bottom navigation bars is to support
      // navigating to the initial location when tapping the item that is
      // already active. This example demonstrates how to support this behavior,
      // using the initialLocation parameter of goBranch.
      initialLocation: index == navigationShell.currentIndex,
      //initialLocation: true,
    );
  }
}
