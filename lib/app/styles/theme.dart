import 'package:flutter/material.dart';
import 'package:uer_flutter/app/styles/color_schemes.dart';
import 'package:uer_flutter/app/styles/colors.dart';
import 'package:uer_flutter/app/styles/widgets_themes/app_bar.dart';
import 'package:uer_flutter/app/styles/widgets_themes/bottom_navigation_bar.dart';
import 'package:uer_flutter/app/styles/widgets_themes/card.dart';
import 'package:uer_flutter/app/styles/widgets_themes/divider.dart';
import 'package:uer_flutter/app/styles/widgets_themes/elevated_button.dart';
import 'package:uer_flutter/app/styles/widgets_themes/icon_button.dart';
import 'package:uer_flutter/app/styles/widgets_themes/input_decoration.dart';

class AppThemes {
  static final lightTheme = ThemeData(
    brightness: Brightness.light,
    colorScheme: AppColorSchemes.lightScheme,
    scaffoldBackgroundColor: AppLightColors.prepreWhite,
    fontFamily: 'Inter',

    // widget themes
    appBarTheme: CustomAppBarTheme.lightTheme,
    dividerTheme: CustomDividerThemeData.lightTheme,
    bottomNavigationBarTheme: CustomBottomNavigationBarThemeData.lightTheme,
    cardTheme: CustomCardThemeData.lightTheme,
    elevatedButtonTheme: CustomElevatedButtonThemeData.lightTheme,
    inputDecorationTheme: CustomInputDecorationTheme.lightTheme,
    iconButtonTheme: CustomIconButtonTheme.lightTheme,
  );

  // TODO: сделать поддержку темной темы
}
