import 'package:flutter/material.dart';
import 'package:uer_flutter/app/styles/colors.dart';
import 'package:uer_flutter/app/styles/fonts.dart';

class CustomInputDecorationTheme {
  static final lightTheme = InputDecorationTheme(
    hoverColor: AppLightColors.prepreWhite,
    constraints: const BoxConstraints(maxHeight: 44.0),
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(1000.0),
      borderSide: const BorderSide(
        width: 1.0,
        color: AppLightColors.lightStroke,
      ),
    ),
    enabledBorder: OutlineInputBorder(
      borderSide: const BorderSide(
        width: 1.0,
        color: AppLightColors.lightStroke,
        style: BorderStyle.solid,
      ),
      borderRadius: BorderRadius.circular(1000.0),
    ),
    focusedBorder: OutlineInputBorder(
      borderSide: const BorderSide(
        width: 1.0,
        color: AppLightColors.lightStroke,
        style: BorderStyle.solid,
      ),
      borderRadius: BorderRadius.circular(1000.0),
    ),
    errorBorder: OutlineInputBorder(
      borderSide: const BorderSide(
        width: 1.0,
        color: AppLightColors.lightStroke,
        style: BorderStyle.solid,
      ),
      borderRadius: BorderRadius.circular(1000.0),
    ),
    focusedErrorBorder: OutlineInputBorder(
      borderSide: const BorderSide(
        width: 1.0,
        color: AppLightColors.error,
        style: BorderStyle.solid,
      ),
      borderRadius: BorderRadius.circular(1000.0),
    ),
    filled: true,
    fillColor: AppLightColors.white,
    labelStyle: AppFonts.bodyLarge,
    counterStyle: AppFonts.labelSmall,
    hintStyle: AppFonts.bodyLarge.merge(
      const TextStyle(color: AppLightColors.medium),
    ),
    errorStyle: AppFonts.bodyLarge.merge(
      const TextStyle(
        color: AppLightColors.error,
      ),
    ),
    contentPadding: const EdgeInsets.symmetric(
      horizontal: 16.0,
      vertical: 10.0,
    ),
  );
}
