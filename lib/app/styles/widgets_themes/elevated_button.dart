import 'package:flutter/material.dart';
import 'package:uer_flutter/app/styles/colors.dart';
import 'package:uer_flutter/app/styles/fonts.dart';

class CustomElevatedButtonThemeData {
  static final lightTheme = ElevatedButtonThemeData(
    style: ButtonStyle(
      padding: const WidgetStatePropertyAll(EdgeInsets.symmetric(
        horizontal: 16.0,
        vertical: 12.0,
      )),
      shape: const WidgetStatePropertyAll(
        StadiumBorder(
          side: BorderSide(
            color: AppLightColors.lightStroke,
            width: 1.0,
          ),
        ),
      ),
      backgroundColor: const WidgetStatePropertyAll(AppLightColors.white),
      foregroundColor: const WidgetStatePropertyAll(AppLightColors.black),
      textStyle: WidgetStatePropertyAll(AppFonts.titleMedium.merge(
        const TextStyle(
          fontVariations: [FontVariation.weight(550)],
        ),
      )),
      elevation: const WidgetStatePropertyAll(0),
    ),
  );
}
