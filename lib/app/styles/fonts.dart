import 'package:flutter/material.dart';

class AppFonts {
  static const String commonFontFamily = 'Inter';
  static const String monoFontFamily = 'JetBrainsMono';
  static const double _commonLetterSpacingPercentage = -1.0;

  // "Body" and "Label" differ only in line height

  // Title
  static const titleLarge = TextStyle(
    fontFamily: commonFontFamily,
    fontSize: 22.0,
    height: 1.2,
    letterSpacing: (_commonLetterSpacingPercentage / 100) * 22.0,
    fontVariations: [
      FontVariation.weight(500),
    ],
  );

  static const titleMedium = TextStyle(
    fontFamily: commonFontFamily,
    fontSize: 20.0,
    height: 1.2,
    letterSpacing: (_commonLetterSpacingPercentage / 100) * 20.0,
    fontVariations: [
      FontVariation.weight(500),
    ],
  );

  static const titleSmall = TextStyle(
    fontFamily: commonFontFamily,
    fontSize: 18.0,
    height: 1.2,
    letterSpacing: (_commonLetterSpacingPercentage / 100) * 18.0,
    fontVariations: [
      FontVariation.weight(500),
    ],
  );

  // Label
  static const labelLarge = TextStyle(
    fontFamily: commonFontFamily,
    fontSize: 16.0,
    height: 1.0,
    letterSpacing: (_commonLetterSpacingPercentage / 100) * 16.0,
    fontVariations: [
      FontVariation.weight(450),
    ],
  );

  static const labelMedium = TextStyle(
    fontFamily: commonFontFamily,
    fontSize: 14.0,
    height: 1.0,
    letterSpacing: (_commonLetterSpacingPercentage / 100) * 14.0,
    fontVariations: [
      FontVariation.weight(450),
    ],
  );

  static const labelSmall = TextStyle(
    fontFamily: commonFontFamily,
    fontSize: 12.0,
    height: 1.0,
    letterSpacing: (_commonLetterSpacingPercentage / 100) * 12.0,
    fontVariations: [
      FontVariation.weight(450),
    ],
  );

  // Body
  static const bodyLarge = TextStyle(
    fontFamily: commonFontFamily,
    fontSize: 16.0,
    height: 1.2,
    letterSpacing: (_commonLetterSpacingPercentage / 100) * 16.0,
    fontVariations: [
      FontVariation.weight(450),
    ],
  );

  static const bodyMedium = TextStyle(
    fontFamily: commonFontFamily,
    fontSize: 14.0,
    height: 1.2,
    letterSpacing: (_commonLetterSpacingPercentage / 100) * 14.0,
    fontVariations: [
      FontVariation.weight(450),
    ],
  );

  static const bodySmall = TextStyle(
    fontFamily: commonFontFamily,
    fontSize: 12.0,
    height: 1.2,
    letterSpacing: (_commonLetterSpacingPercentage / 100) * 12.0,
    fontVariations: [
      FontVariation.weight(450),
    ],
  );
}
