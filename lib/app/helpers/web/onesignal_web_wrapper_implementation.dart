// ignore: avoid_web_libraries_in_flutter
import 'dart:js' as js;

import 'package:flutter/foundation.dart';

import 'onesignal_web_wrapper.dart';

class OnesignalWebWrapperImpl extends OnesignalWebWrapper {
  @override
  void setExternalUserId({required String uid}) {
    //print("Flutter call setExternalUserId");

    if (kReleaseMode) {
      js.context.callMethod('setExternalUserId', [uid]);
    }

    return;
  }

  @override
  void disablePush() {
    //print("Flutter call disablePush");

    if (kReleaseMode) {
      js.context.callMethod('disablePush', []);
    }

    return;
  }

  @override
  void notificationPromptWeb() {
    if (kReleaseMode) {
      js.context.callMethod('notificationPromptWeb', []);
    }

    return;
  }
}

OnesignalWebWrapper getWrapper() => OnesignalWebWrapperImpl();
