import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:image_picker/image_picker.dart';

import 'platform_check.dart';

typedef PhotoHelperResultCallback = Function(List<Uint8List>);

List<PopupMenuItem<int>> getMenuList(PhotoHelperResultCallback resultCallback) {
  List<PopupMenuItem<int>> menuList = [];

  if (stationComputer() == true) {
    menuList = [
      PopupMenuItem<int>(
          value: 1,
          child: const Text('Выбрать файл'),
          onTap: () async {
            var result = await showPickImage(ImageSource.gallery);
            resultCallback(result);
          }),
    ];
  } else {
    menuList = [
      PopupMenuItem<int>(
          value: 0,
          child: const Text('Открыть камеру'),
          onTap: () async {
            var result = await showPickImage(ImageSource.camera);
            resultCallback(result);
          }),
      PopupMenuItem<int>(
          value: 1,
          child: const Text('Открыть галерею'),
          onTap: () async {
            var result = await showPickImage(ImageSource.gallery);
            resultCallback(result);
          }),
    ];
  }

  return menuList;
}

Future<List<Uint8List>> showPickImage(ImageSource source) async {
  List<Uint8List> imagesArr = [];

  if (stationComputer() == true) {
    Future<FilePickerResult?> pickFiles() async {
      return FilePicker.platform.pickFiles(
        type: FileType.custom, // todo: test FileType.image
        withData: true,
        allowMultiple: true,
        allowedExtensions: ['jpg', 'png', 'jpeg'],
      );
    }

    FilePickerResult? result = await Future(pickFiles);

    for (PlatformFile image in result?.files ?? []) {
      if (image.bytes == null) {
        continue;
      }

      imagesArr.add(image.bytes!);
    }
  } else {
    final ImagePicker picker = ImagePicker();

    if (source == ImageSource.camera) {
      final XFile? image =
          await picker.pickImage(source: source, requestFullMetadata: false);

      if (image == null) {
        return [];
      }

      imagesArr.add(await image.readAsBytes());
    } else {
      final List<XFile> listImage = await picker.pickMultiImage();

      for (var image in listImage) {
        imagesArr.add(await image.readAsBytes());
      }
    }
  }

  //print(imagesArr.length);

  List<Uint8List> imagesNew = [];

  for (var tempImgData in imagesArr) {
    try {
      final image = await FlutterImageCompress.compressWithList(
        tempImgData,
        minHeight: 1920,
        minWidth: 1080,
        quality: 70,
      );
      imagesNew.add(image);
    } catch (e) {
      print('Error compressing image: $e');
    }
  }

  return imagesNew;

  //addNewPhoto(imagesNew);
}
