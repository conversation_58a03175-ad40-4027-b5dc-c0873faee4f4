import 'dart:math';

String getRandomString(int length) {
  const ch = 'AaBbCcDdEeFfGgHhIiJjKkLlMmNnOoPpQqRrSsTtUuVvWwXxYyZz';
  Random r = Random();
  return String.fromCharCodes(
      Iterable.generate(length, (_) => ch.codeUnitAt(r.nextInt(ch.length))));
}

class WordForms {
  // Использовать для вывода правильных форм слов при разных значениях цифр
  final Map<String, String> dictValue;

  WordForms(this.dictValue);

  static WordForms empty = WordForms({"1": "", "2": "", "5": ""});
  static WordForms order =
      WordForms({"1": "Заказ", "2": "Заказа", "5": "Заказов"});
  static WordForms component =
      WordForms({"1": "Компонент", "2": "Компонента", "5": "Компонентов"});

  static String wordForm(WordForms word, int number, bool lowerCase) {
    int adjustedNumber = number > 99 ? _lastTwoNumber(number) : number;

    if (adjustedNumber == 0 || (adjustedNumber >= 5 && adjustedNumber <= 20)) {
      return _returnForm(word.dictValue, "5", lowerCase);
    } else if (adjustedNumber == 1) {
      return _returnForm(word.dictValue, "1", lowerCase);
    } else if (adjustedNumber >= 2 && adjustedNumber <= 4) {
      return _returnForm(word.dictValue, "2", lowerCase);
    } else {
      int lastDigit = _lastNumber(adjustedNumber);

      switch (lastDigit) {
        case 1:
          return _returnForm(word.dictValue, "1", lowerCase);
        case 0:
        case 5:
        case 6:
        case 7:
        case 8:
        case 9:
          return _returnForm(word.dictValue, "5", lowerCase);
        case 2:
        case 3:
        case 4:
          return _returnForm(word.dictValue, "2", lowerCase);
        default:
          return "Error";
      }
    }
  }

  static String _returnForm(
      Map<String, String> wordDict, String key, bool lowerCase) {
    return lowerCase ? wordDict[key]!.toLowerCase() : wordDict[key]!;
  }

  static int _lastTwoNumber(int number) =>
      int.parse(number.toString().substring(number.toString().length - 2));

  static int _lastNumber(int number) => number % 10;
}
