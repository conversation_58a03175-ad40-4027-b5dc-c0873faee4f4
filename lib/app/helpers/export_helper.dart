import 'dart:typed_data';

import 'package:excel/excel.dart';
import 'package:share_plus/share_plus.dart';
import 'platform_check.dart';

import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;

import 'empty_download.dart' if (dart.library.html) 'download.dart';

class Export {
  static void exportPDF(String name, Uint8List image) async {
    final pdf = pw.Document();

    pdf.addPage(pw.Page(
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) {
          return pw.Container(
            alignment: pw.Alignment.topCenter,
            child: pw.Image(pw.MemoryImage(image)),
          ); // Center
        }));

    if (isWeb() == true) {
      var fileBytes = await pdf.save();

      download(fileBytes, // takes bytes
          downloadName: "$name.pdf");
    } else {
      var fileBytes = await pdf.save();

      var file =
          XFile.fromData(fileBytes, name: name, mimeType: "application/pdf");
      await Share.shareXFiles(
        [file],
      );
    }
  }

  static void exportJPG(String name, Uint8List image) async {
    if (isWeb() == true) {
      download(image, // takes bytes
          downloadName: "$name.jpg");
    } else {
      var file = XFile.fromData(image, name: name, mimeType: "image/jpeg");
      await Share.shareXFiles(
        [file],
      );
    }
  }

  static void exportExcel(String name, List<List<dynamic>> data) async {
    var excel = Excel.createExcel();

    excel.rename("Sheet1", name);

    Sheet sheetObject = excel[name];

    CellStyle cellStyleHeader = CellStyle(
        //backgroundColorHex: '#1AFF1A',
        bold: true,
        fontSize: 17,
        fontFamily: getFontFamily(FontFamily.Arial));

    CellStyle cellStyle = CellStyle(
        //backgroundColorHex: '#1AFF1A',
        fontSize: 15,
        fontFamily: getFontFamily(FontFamily.Arial));

    for (var i = 0; i < data.length; i++) {
      var rowArr = data[i];

      for (var k = 0; k < rowArr.length; k++) {
        var rowData = rowArr[k];

        var cell = sheetObject
            .cell(CellIndex.indexByColumnRow(columnIndex: k, rowIndex: i));

        cell.value = rowData;

        if (i == 0) {
          cell.cellStyle = cellStyleHeader;
        } else {
          cell.cellStyle = cellStyle;
        }

        sheetObject.setColumnAutoFit(k);
      }
    }

    if (isWeb() == true) {
      var _ = excel.save(fileName: '$name.xlsx');

      //download(await file.readAsBytes(), // takes bytes
      //    downloadName: "${widget.name}.jpg");
    } else {
      var fileBytes = excel.save();

      if (fileBytes != null) {
        final list = Uint8List.fromList(fileBytes);
        var file = XFile.fromData(list,
            name: name,
            mimeType:
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        await Share.shareXFiles(
          [file],
        );
      }

      //sharePositionOrigin: box!.localToGlobal(Offset.zero) & box.size

      // var directory = await getApplicationDocumentsDirectory();

      // File(join('$directory/output_file_name.xlsx'))
      //   ..createSync(recursive: true)
      //   ..writeAsBytesSync(fileBytes);
    }
  }
}
