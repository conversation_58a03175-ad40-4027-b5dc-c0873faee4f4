import 'package:intl/intl.dart';

extension DateTimeExtension on DateTime {
  String timeAgo({bool numericDates = false}) {
    var timeFormatter = DateFormat('HH:mm', "ru");

    final date2 = DateTime.now();
    final difference = date2.difference(this);

    if (difference.inDays >= 2) {
      var yearDiff = date2.year - year;

      var newFormater = DateFormat("");

      if (yearDiff == 0) {
        newFormater = DateFormat("dd MMMM", "ru");
      } else {
        newFormater = DateFormat("dd MMMM yyyy", "ru");
      }

      return "${newFormater.format(this)} в ${timeFormatter.format(this)}";
    } else if (difference.inDays >= 1 && difference.inDays < 2) {
      return "Вчера в ${timeFormatter.format(this)}";
    } else if (difference.inHours >= 5 &&
        difference.inHours < 24 &&
        day != date2.day) {
      return "Вчера в ${timeFormatter.format(this)}";
    } else if (difference.inHours >= 5 &&
        difference.inHours < 24 &&
        day == date2.day) {
      return "Сегодня в ${timeFormatter.format(this)}";
    } else if (difference.inHours >= 2) {
      return '${difference.inHours} часа назад';
    } else if (difference.inHours >= 1) {
      return (numericDates) ? '1 час назад' : 'Час назад';
    } else if (difference.inMinutes >= 2) {
      return '${difference.inMinutes} минуты назад';
    } else if (difference.inMinutes >= 1) {
      return (numericDates) ? '1 минуту назад' : 'Минуту назад';
    } else if (difference.inSeconds >= 3) {
      return '${difference.inSeconds} секунд назад';
    } else {
      return 'Только что';
    }
  }

  String dateShortString() {
    return dateWithFormat("dd.MM.yy");
  }

  String dateShortWithTimeString() {
    return dateWithFormat("dd.MM.yy HH:mm");
  }

  String dateShortStringForGetInfoSSZ() {
    return dateWithFormat("dd.MM.yyyy");
  }

  String dateWithFormat(String format) {
    var newFormatter = DateFormat(format, "ru");
    return newFormatter.format(this);
  }

  int getDaysOverDeadline(double deadline, double? finishDate) {
    var now = (DateTime.now().millisecondsSinceEpoch / 1000).roundToDouble();

    if (finishDate != null) {
      now = finishDate;
    }

    if (now <= deadline) {
      return 0;
    }

    final range = now - deadline;
    final days = (range / 86400).floor();
    return days;
  }
}
