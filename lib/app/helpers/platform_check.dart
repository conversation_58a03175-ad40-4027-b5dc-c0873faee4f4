import 'package:universal_io/io.dart';
import 'package:flutter/foundation.dart' show kIsWeb;

bool stationComputer() {
  if (Platform.isWindows || Platform.isLinux || Platform.isMacOS || kIsWeb) {
    return true;
  } else if (Platform.isIOS || Platform.isAndroid) {
    return false;
  }
  return true;
}

bool isWebOrWindows() {
  if (Platform.isWindows || kIsWeb) {
    return true;
  }
  return false;
}

bool isWindows() {
  return Platform.isWindows;
}

bool isMacOS() {
  if (Platform.isMacOS) {
    return true;
  }
  return false;
}

bool isLinux() {
  if (Platform.isLinux) {
    return true;
  }
  return false;
}

bool isWeb() {
  return kIsWeb;
}

bool isWebOrAndroidOriOS() {
  if (Platform.isAndroid || Platform.isIOS || kIsWeb) {
    return true;
  }
  return false;
}

bool isAndroidOriOS() {
  if (Platform.isAndroid || Platform.isIOS) {
    return true;
  }
  return false;
}
