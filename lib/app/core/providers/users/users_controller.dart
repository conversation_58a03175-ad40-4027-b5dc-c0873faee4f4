import 'dart:async';

import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:uer_flutter/app/core/models/user/user_model.dart';
import 'package:uer_flutter/app/core/providers/notifier_mouted.dart';

import '../../../core/services/api/service_provider.dart';

part 'users_controller.g.dart';

@riverpod
class UsersController extends _$UsersController with NotifierMounted {
  @override
  FutureOr<List<UserModel>> build(List<String>? logins) async {
    ref.onDispose(setUnmounted);

    final users = await _fetchUsers(logins);

    return users ?? [];
  }

  Future<List<UserModel>?> _fetchUsers(List<String>? logins) async {
    try {
      var users = await ref.read(userRepositoryProvider).getUsers(logins);
      return users;
    } catch (e) {
      //showInSnackBar(e.toString(), context);
      // await AppMetrica.reportError(
      //   message: 'Ошибка получения пользователей',
      //   errorDescription: AppMetricaErrorDescription(
      //     StackTrace.current,
      //     message: e.toString(),
      //   ),
      // );
      return null;
    }
  }

  Future<bool> createUser(UserModel user) async {
    final result = await ref.read(userRepositoryProvider).createUser(user);

    if (result == true) {
      await updateUsers();
    }

    return result;
  }

  Future<bool> editUser(UserModel user) async {
    final result = await ref.read(userRepositoryProvider).editUser(user);

    if (result == true) {
      await updateUsers();
    }

    return result;
  }

  Future<void> updateUsers() async {
    state = const AsyncValue.loading();

    var newState = await AsyncValue.guard(() async {
      List<UserModel>? users = await _fetchUsers(logins) ?? [];
      return users;
    });

    if (mounted) {
      state = newState;
    }
  }
}
