// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'users_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$usersControllerHash() => r'9e607b728f7fed53385cee73b232c10f9ff3c019';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$UsersController
    extends BuildlessAutoDisposeAsyncNotifier<List<UserModel>> {
  late final List<String>? logins;

  FutureOr<List<UserModel>> build(
    List<String>? logins,
  );
}

/// See also [UsersController].
@ProviderFor(UsersController)
const usersControllerProvider = UsersControllerFamily();

/// See also [UsersController].
class UsersControllerFamily extends Family<AsyncValue<List<UserModel>>> {
  /// See also [UsersController].
  const UsersControllerFamily();

  /// See also [UsersController].
  UsersControllerProvider call(
    List<String>? logins,
  ) {
    return UsersControllerProvider(
      logins,
    );
  }

  @override
  UsersControllerProvider getProviderOverride(
    covariant UsersControllerProvider provider,
  ) {
    return call(
      provider.logins,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'usersControllerProvider';
}

/// See also [UsersController].
class UsersControllerProvider extends AutoDisposeAsyncNotifierProviderImpl<
    UsersController, List<UserModel>> {
  /// See also [UsersController].
  UsersControllerProvider(
    List<String>? logins,
  ) : this._internal(
          () => UsersController()..logins = logins,
          from: usersControllerProvider,
          name: r'usersControllerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$usersControllerHash,
          dependencies: UsersControllerFamily._dependencies,
          allTransitiveDependencies:
              UsersControllerFamily._allTransitiveDependencies,
          logins: logins,
        );

  UsersControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.logins,
  }) : super.internal();

  final List<String>? logins;

  @override
  FutureOr<List<UserModel>> runNotifierBuild(
    covariant UsersController notifier,
  ) {
    return notifier.build(
      logins,
    );
  }

  @override
  Override overrideWith(UsersController Function() create) {
    return ProviderOverride(
      origin: this,
      override: UsersControllerProvider._internal(
        () => create()..logins = logins,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        logins: logins,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<UsersController, List<UserModel>>
      createElement() {
    return _UsersControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is UsersControllerProvider && other.logins == logins;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, logins.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin UsersControllerRef
    on AutoDisposeAsyncNotifierProviderRef<List<UserModel>> {
  /// The parameter `logins` of this provider.
  List<String>? get logins;
}

class _UsersControllerProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<UsersController,
        List<UserModel>> with UsersControllerRef {
  _UsersControllerProviderElement(super.provider);

  @override
  List<String>? get logins => (origin as UsersControllerProvider).logins;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
