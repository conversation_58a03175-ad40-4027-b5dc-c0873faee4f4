import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tuple/tuple.dart';
import '../models/item/item_model.dart';
import '../../features/component/component_page_controller.dart';
import '../../features/item/item_page_controller.dart';
import '../../features/main_user/main_user_controller.dart';

void updateItemInProvidersWithRef(ItemModel item, Ref ref) {
  if (item.mainID != null) {
    var exists = ref.exists(itemPageControllerProvider(item.mainID!));

    if (exists == true) {
      ref
          .read(itemPageControllerProvider(item.mainID!).notifier)
          .updateData(item);
    }
  }

  if (ref.exists(mainUserControllerProvider)) {
    ref.read(mainUserControllerProvider.notifier).updateItem(item);
  }

  if (item.components.isNotEmpty) {
    for (var component in item.components) {
      var exists =
          ref.exists(componentPageControllerProvider(component.identifier));

      if (exists == true) {
        var notifier = ref.read(
            componentPageControllerProvider(component.identifier).notifier);
        notifier.updateData(Tuple2(item, component));
      }
    }
  }
}

void updateItemInProvidersWithWidgetRef(ItemModel item, WidgetRef ref) {
  if (item.mainID != null) {
    var exists = ref.exists(itemPageControllerProvider(item.mainID!));

    if (exists == true) {
      ref
          .read(itemPageControllerProvider(item.mainID!).notifier)
          .updateData(item);
    }
  }

  if (ref.exists(mainUserControllerProvider)) {
    ref.read(mainUserControllerProvider.notifier).updateItem(item);
  }

  if (item.components.isNotEmpty) {
    for (var component in item.components) {
      var exists =
          ref.exists(componentPageControllerProvider(component.identifier));

      if (exists == true) {
        var notifier = ref.read(
            componentPageControllerProvider(component.identifier).notifier);
        notifier.updateData(Tuple2(item, component));
      }
    }
  }
}
