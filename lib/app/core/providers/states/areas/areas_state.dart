import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../helpers/logger.dart';
import '../../../models/area/area_model.dart';
import '../../../services/api/area/area_api_service.dart';
import '../../../services/api/service_provider.dart';
import '../user/user_state.dart';

// TODO: переделать на провайдер с аннотацией @riverpod

final areasProvider =
    StateNotifierProvider<AreasStateNotifier, List<AreaModel>>((ref) {
  var areaNotifier = AreasStateNotifier(ref.watch(areaRepositoryProvider));

  var userState = ref.watch(currentUserProvider);
  userState.maybeWhen(
      auth: (user) {
        areaNotifier.getAreas();
      },
      orElse: () {});

  return areaNotifier;
});

class AreasStateNotifier extends StateNotifier<List<AreaModel>> {
  final AreaApi apiClient;
  AreasStateNotifier(this.apiClient) : super([]);

  int _delayTime = 1;

  getAreas() async {
    try {
      var areas = await apiClient.getAreas();
      //print(areas);
      state = areas;
    } catch (e) {
      Timer(Duration(seconds: _delayTime), () {
        getAreas();
      });

      _delayTime = _delayTime * 2;
    }
  }

  Future<bool> addArea(AreaModel area) async {
    try {
      var success = await apiClient.addArea(area);
      if (success == true) {
        getAreas();
        return true;
      }
    } catch (e) {
      // await AppMetrica.reportError(
      //   message: 'Ошибка добавления участка',
      //   errorDescription: AppMetricaErrorDescription(
      //     StackTrace.current,
      //     message: e.toString(),
      //   ),
      // );
      logger.e("Error: addArea", error: e);
    }

    return false;
  }

  Future<bool> editArea(AreaModel area) async {
    try {
      var success = await apiClient.editArea(area);
      if (success == true) {
        getAreas();
        return true;
      }
    } catch (e) {
      // await AppMetrica.reportError(
      //   message: 'Ошибка изменения участка',
      //   errorDescription: AppMetricaErrorDescription(
      //     StackTrace.current,
      //     message: e.toString(),
      //   ),
      // );
      logger.e("Error: editArea", error: e);
    }

    return false;
  }
}

// import 'package:riverpod_annotation/riverpod_annotation.dart';
// import 'package:uer_flutter/models/status/status_model.dart';

// part 'areas_state.g.dart';

// @riverpod
// class Areas extends _$Areas {
//   @override
//   FutureOr<List<AreaModel>> build() {
//     return _fetchAreas();
//   }

//   Future<List<AreaModel>> _fetchAreas() async {
//     var areaRepository = ref.watch(areaRepositoryProvider);
//     return await areaRepository.getAreas();
//   }
// }
