import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../helpers/logger.dart';
import '../../../models/branch/branch_model.dart';
import '../../../services/api/branch/branch_api_service.dart';
import '../../../services/api/service_provider.dart';
import '../user/user_state.dart';

// TODO: переделать на провайдер с аннотацией @riverpod

//dependent sources
final branchesProvider =
    StateNotifierProvider<BranchesStateNotifier, List<BranchModel>>((ref) {
  var branchNotifier =
      BranchesStateNotifier(ref.watch(branchRepositoryProvider));

  var userState = ref.watch(currentUserProvider);
  userState.maybeWhen(
      auth: (user) {
        branchNotifier.getBranches();
      },
      orElse: () {});

  return branchNotifier;
});

class BranchesStateNotifier extends StateNotifier<List<BranchModel>> {
  final BranchApi apiClient;
  BranchesStateNotifier(this.apiClient) : super([]);

  int _delayTime = 1;

  getBranches() async {
    try {
      var branchs = await apiClient.getBranchs();
      //print(branchs);
      state = branchs;
    } catch (e) {
      logger.e("Error: getBranches", error: e);

      Timer(Duration(seconds: _delayTime), () {
        getBranches();
      });

      _delayTime = _delayTime * 2;
    }
  }

  Future<bool> addBranch(BranchModel branch) async {
    try {
      var result = await apiClient.addBranch(branch);

      if (result == true) {
        getBranches();
        return true;
      }
    } catch (e) {
      // await AppMetrica.reportError(
      //   message: 'Ошибка добавления филиала',
      //   errorDescription: AppMetricaErrorDescription(
      //     StackTrace.current,
      //     message: e.toString(),
      //   ),
      // );
      logger.e("Error: addBranch", error: e);
    }

    return false;
  }

  Future<bool> editBranch(BranchModel branch) async {
    try {
      var result = await apiClient.editBranch(branch);

      if (result == true) {
        getBranches();
        return true;
      }
    } catch (e) {
      // await AppMetrica.reportError(
      //   message: 'Ошибка изменения филиала',
      //   errorDescription: AppMetricaErrorDescription(
      //     StackTrace.current,
      //     message: e.toString(),
      //   ),
      // );
      logger.e("Error: editBranch", error: e);
    }

    return false;
  }
}
