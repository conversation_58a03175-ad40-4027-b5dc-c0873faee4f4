import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:uer_flutter/app/core/services/settings_data_manager.dart';

import '../../../../helpers/logger.dart';
import '../../../models/user/user_model.dart';
import '../../../services/api/onesignal/onesignal_auth.dart';
import '../../../services/api/service_provider.dart';
import '../../../services/api/user/user_api_service.dart';

part 'user_state.freezed.dart';

// TODO: переделать на провайдер с аннотацией @riverpod

final currentUserProvider =
    StateNotifierProvider<UserStateNotifier, UserModelState>((ref) {
  return UserStateNotifier(ref.watch(userRepositoryProvider));
});

class UserStateNotifier extends StateNotifier<UserModelState> {
  final UserApi apiClient;
  UserStateNotifier(this.apiClient) : super(const UserModelState.notAuth());

  _getUser(String login) async {
    try {
      state = const UserModelState.loading();
      var user = await apiClient.getUser(login);
      oneSignalLogin(user.login);
      state = UserModelState.auth(user);
    } catch (e) {
      logger.e("Error: getUser", error: e);
      state = UserModelState.error("$e");
    }
  }

  auth(String login, String password) async {
    try {
      state = const UserModelState.loading();
      var user = await apiClient.auth(login, password);
      oneSignalLogin(user.login);
      await SettingsDataManager.shared.saveAuthData(user.login, user.password!);
      state = UserModelState.auth(user);
      // await FirebaseAnalytics.instance
      //     .logLogin(loginMethod: 'login', parameters: {
      //   'user_login': user.login,
      //   'user_role': user.role.name,
      // });
      // await AppMetrica.reportEventWithMap('Авторизация', {
      //   'Имя': user.name ?? 'Неизвестно',
      //   'Логин': user.login,
      //   'Роль': user.role.name,
      // });
    } catch (e) {
      logger.e("Error: auth", error: e);
      state = UserModelState.error("$e");
      // await FirebaseAnalytics.instance
      //     .logEvent(name: 'login_error', parameters: {
      //   'user_login': login,
      //   'error': e,
      // });
      // await AppMetrica.reportError(
      //   message: 'Ошибка авторизации',
      //   errorDescription: AppMetricaErrorDescription(
      //     StackTrace.current,
      //     message: e.toString(),
      //   ),
      // );
    }
  }

  void oneSignalLogin(String login) {
    OneSignalAuth().loginToOneSignal(login);
  }

  // void changeSubscribeItem(String mainID, bool subscribe) {

  // }

  // void changeSubscribeOutAge(String mainID, int days, bool subscribe) {}

  updateCurrentUser() async {
    // or checkAuth
    var login = await SettingsDataManager.shared.getUserLogin();
    if (login?.isNotEmpty == true) {
      // _getUser(login!);
      try {
        var user = await apiClient.getUser(login!);
        state = UserModelState.auth(user);
      } catch (e) {
        // await AppMetrica.reportError(
        //   message: 'Ошибка обновления пользователя',
        //   errorDescription: AppMetricaErrorDescription(
        //     StackTrace.current,
        //     message: e.toString(),
        //   ),
        // );
        logger.e("Error: getUser", error: e);
        state = UserModelState.error("$e");
      }
    }
  }

  updateUserWithUser(UserModel user) {
    state = UserModelState.auth(user);
  }

  logout() async {
    await SettingsDataManager.shared.removeUserLoginData();
    OneSignalAuth().removeLoginOneSignal();
    state = const UserModelState.notAuth();
  }
}

@freezed
class UserModelState with _$UserModelState {
  const factory UserModelState.notAuth() = _UserModelNotAuth;
  const factory UserModelState.loading() = _UserModelLoading;
  const factory UserModelState.auth(UserModel user) = _UserModelAuth;
  const factory UserModelState.error(String message) = _UserModelLoadedError;
}
