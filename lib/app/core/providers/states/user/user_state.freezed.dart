// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$UserModelState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() notAuth,
    required TResult Function() loading,
    required TResult Function(UserModel user) auth,
    required TResult Function(String message) error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? notAuth,
    TResult? Function()? loading,
    TResult? Function(UserModel user)? auth,
    TResult? Function(String message)? error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? notAuth,
    TResult Function()? loading,
    TResult Function(UserModel user)? auth,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_UserModelNotAuth value) notAuth,
    required TResult Function(_UserModelLoading value) loading,
    required TResult Function(_UserModelAuth value) auth,
    required TResult Function(_UserModelLoadedError value) error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_UserModelNotAuth value)? notAuth,
    TResult? Function(_UserModelLoading value)? loading,
    TResult? Function(_UserModelAuth value)? auth,
    TResult? Function(_UserModelLoadedError value)? error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_UserModelNotAuth value)? notAuth,
    TResult Function(_UserModelLoading value)? loading,
    TResult Function(_UserModelAuth value)? auth,
    TResult Function(_UserModelLoadedError value)? error,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserModelStateCopyWith<$Res> {
  factory $UserModelStateCopyWith(
          UserModelState value, $Res Function(UserModelState) then) =
      _$UserModelStateCopyWithImpl<$Res, UserModelState>;
}

/// @nodoc
class _$UserModelStateCopyWithImpl<$Res, $Val extends UserModelState>
    implements $UserModelStateCopyWith<$Res> {
  _$UserModelStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserModelState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$UserModelNotAuthImplCopyWith<$Res> {
  factory _$$UserModelNotAuthImplCopyWith(_$UserModelNotAuthImpl value,
          $Res Function(_$UserModelNotAuthImpl) then) =
      __$$UserModelNotAuthImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$UserModelNotAuthImplCopyWithImpl<$Res>
    extends _$UserModelStateCopyWithImpl<$Res, _$UserModelNotAuthImpl>
    implements _$$UserModelNotAuthImplCopyWith<$Res> {
  __$$UserModelNotAuthImplCopyWithImpl(_$UserModelNotAuthImpl _value,
      $Res Function(_$UserModelNotAuthImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserModelState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$UserModelNotAuthImpl implements _UserModelNotAuth {
  const _$UserModelNotAuthImpl();

  @override
  String toString() {
    return 'UserModelState.notAuth()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$UserModelNotAuthImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() notAuth,
    required TResult Function() loading,
    required TResult Function(UserModel user) auth,
    required TResult Function(String message) error,
  }) {
    return notAuth();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? notAuth,
    TResult? Function()? loading,
    TResult? Function(UserModel user)? auth,
    TResult? Function(String message)? error,
  }) {
    return notAuth?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? notAuth,
    TResult Function()? loading,
    TResult Function(UserModel user)? auth,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (notAuth != null) {
      return notAuth();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_UserModelNotAuth value) notAuth,
    required TResult Function(_UserModelLoading value) loading,
    required TResult Function(_UserModelAuth value) auth,
    required TResult Function(_UserModelLoadedError value) error,
  }) {
    return notAuth(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_UserModelNotAuth value)? notAuth,
    TResult? Function(_UserModelLoading value)? loading,
    TResult? Function(_UserModelAuth value)? auth,
    TResult? Function(_UserModelLoadedError value)? error,
  }) {
    return notAuth?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_UserModelNotAuth value)? notAuth,
    TResult Function(_UserModelLoading value)? loading,
    TResult Function(_UserModelAuth value)? auth,
    TResult Function(_UserModelLoadedError value)? error,
    required TResult orElse(),
  }) {
    if (notAuth != null) {
      return notAuth(this);
    }
    return orElse();
  }
}

abstract class _UserModelNotAuth implements UserModelState {
  const factory _UserModelNotAuth() = _$UserModelNotAuthImpl;
}

/// @nodoc
abstract class _$$UserModelLoadingImplCopyWith<$Res> {
  factory _$$UserModelLoadingImplCopyWith(_$UserModelLoadingImpl value,
          $Res Function(_$UserModelLoadingImpl) then) =
      __$$UserModelLoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$UserModelLoadingImplCopyWithImpl<$Res>
    extends _$UserModelStateCopyWithImpl<$Res, _$UserModelLoadingImpl>
    implements _$$UserModelLoadingImplCopyWith<$Res> {
  __$$UserModelLoadingImplCopyWithImpl(_$UserModelLoadingImpl _value,
      $Res Function(_$UserModelLoadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserModelState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$UserModelLoadingImpl implements _UserModelLoading {
  const _$UserModelLoadingImpl();

  @override
  String toString() {
    return 'UserModelState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$UserModelLoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() notAuth,
    required TResult Function() loading,
    required TResult Function(UserModel user) auth,
    required TResult Function(String message) error,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? notAuth,
    TResult? Function()? loading,
    TResult? Function(UserModel user)? auth,
    TResult? Function(String message)? error,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? notAuth,
    TResult Function()? loading,
    TResult Function(UserModel user)? auth,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_UserModelNotAuth value) notAuth,
    required TResult Function(_UserModelLoading value) loading,
    required TResult Function(_UserModelAuth value) auth,
    required TResult Function(_UserModelLoadedError value) error,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_UserModelNotAuth value)? notAuth,
    TResult? Function(_UserModelLoading value)? loading,
    TResult? Function(_UserModelAuth value)? auth,
    TResult? Function(_UserModelLoadedError value)? error,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_UserModelNotAuth value)? notAuth,
    TResult Function(_UserModelLoading value)? loading,
    TResult Function(_UserModelAuth value)? auth,
    TResult Function(_UserModelLoadedError value)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class _UserModelLoading implements UserModelState {
  const factory _UserModelLoading() = _$UserModelLoadingImpl;
}

/// @nodoc
abstract class _$$UserModelAuthImplCopyWith<$Res> {
  factory _$$UserModelAuthImplCopyWith(
          _$UserModelAuthImpl value, $Res Function(_$UserModelAuthImpl) then) =
      __$$UserModelAuthImplCopyWithImpl<$Res>;
  @useResult
  $Res call({UserModel user});

  $UserModelCopyWith<$Res> get user;
}

/// @nodoc
class __$$UserModelAuthImplCopyWithImpl<$Res>
    extends _$UserModelStateCopyWithImpl<$Res, _$UserModelAuthImpl>
    implements _$$UserModelAuthImplCopyWith<$Res> {
  __$$UserModelAuthImplCopyWithImpl(
      _$UserModelAuthImpl _value, $Res Function(_$UserModelAuthImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserModelState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? user = null,
  }) {
    return _then(_$UserModelAuthImpl(
      null == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as UserModel,
    ));
  }

  /// Create a copy of UserModelState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserModelCopyWith<$Res> get user {
    return $UserModelCopyWith<$Res>(_value.user, (value) {
      return _then(_value.copyWith(user: value));
    });
  }
}

/// @nodoc

class _$UserModelAuthImpl implements _UserModelAuth {
  const _$UserModelAuthImpl(this.user);

  @override
  final UserModel user;

  @override
  String toString() {
    return 'UserModelState.auth(user: $user)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserModelAuthImpl &&
            (identical(other.user, user) || other.user == user));
  }

  @override
  int get hashCode => Object.hash(runtimeType, user);

  /// Create a copy of UserModelState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserModelAuthImplCopyWith<_$UserModelAuthImpl> get copyWith =>
      __$$UserModelAuthImplCopyWithImpl<_$UserModelAuthImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() notAuth,
    required TResult Function() loading,
    required TResult Function(UserModel user) auth,
    required TResult Function(String message) error,
  }) {
    return auth(user);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? notAuth,
    TResult? Function()? loading,
    TResult? Function(UserModel user)? auth,
    TResult? Function(String message)? error,
  }) {
    return auth?.call(user);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? notAuth,
    TResult Function()? loading,
    TResult Function(UserModel user)? auth,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (auth != null) {
      return auth(user);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_UserModelNotAuth value) notAuth,
    required TResult Function(_UserModelLoading value) loading,
    required TResult Function(_UserModelAuth value) auth,
    required TResult Function(_UserModelLoadedError value) error,
  }) {
    return auth(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_UserModelNotAuth value)? notAuth,
    TResult? Function(_UserModelLoading value)? loading,
    TResult? Function(_UserModelAuth value)? auth,
    TResult? Function(_UserModelLoadedError value)? error,
  }) {
    return auth?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_UserModelNotAuth value)? notAuth,
    TResult Function(_UserModelLoading value)? loading,
    TResult Function(_UserModelAuth value)? auth,
    TResult Function(_UserModelLoadedError value)? error,
    required TResult orElse(),
  }) {
    if (auth != null) {
      return auth(this);
    }
    return orElse();
  }
}

abstract class _UserModelAuth implements UserModelState {
  const factory _UserModelAuth(final UserModel user) = _$UserModelAuthImpl;

  UserModel get user;

  /// Create a copy of UserModelState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserModelAuthImplCopyWith<_$UserModelAuthImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UserModelLoadedErrorImplCopyWith<$Res> {
  factory _$$UserModelLoadedErrorImplCopyWith(_$UserModelLoadedErrorImpl value,
          $Res Function(_$UserModelLoadedErrorImpl) then) =
      __$$UserModelLoadedErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$UserModelLoadedErrorImplCopyWithImpl<$Res>
    extends _$UserModelStateCopyWithImpl<$Res, _$UserModelLoadedErrorImpl>
    implements _$$UserModelLoadedErrorImplCopyWith<$Res> {
  __$$UserModelLoadedErrorImplCopyWithImpl(_$UserModelLoadedErrorImpl _value,
      $Res Function(_$UserModelLoadedErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserModelState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$UserModelLoadedErrorImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$UserModelLoadedErrorImpl implements _UserModelLoadedError {
  const _$UserModelLoadedErrorImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'UserModelState.error(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserModelLoadedErrorImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of UserModelState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserModelLoadedErrorImplCopyWith<_$UserModelLoadedErrorImpl>
      get copyWith =>
          __$$UserModelLoadedErrorImplCopyWithImpl<_$UserModelLoadedErrorImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() notAuth,
    required TResult Function() loading,
    required TResult Function(UserModel user) auth,
    required TResult Function(String message) error,
  }) {
    return error(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? notAuth,
    TResult? Function()? loading,
    TResult? Function(UserModel user)? auth,
    TResult? Function(String message)? error,
  }) {
    return error?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? notAuth,
    TResult Function()? loading,
    TResult Function(UserModel user)? auth,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_UserModelNotAuth value) notAuth,
    required TResult Function(_UserModelLoading value) loading,
    required TResult Function(_UserModelAuth value) auth,
    required TResult Function(_UserModelLoadedError value) error,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_UserModelNotAuth value)? notAuth,
    TResult? Function(_UserModelLoading value)? loading,
    TResult? Function(_UserModelAuth value)? auth,
    TResult? Function(_UserModelLoadedError value)? error,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_UserModelNotAuth value)? notAuth,
    TResult Function(_UserModelLoading value)? loading,
    TResult Function(_UserModelAuth value)? auth,
    TResult Function(_UserModelLoadedError value)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class _UserModelLoadedError implements UserModelState {
  const factory _UserModelLoadedError(final String message) =
      _$UserModelLoadedErrorImpl;

  String get message;

  /// Create a copy of UserModelState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserModelLoadedErrorImplCopyWith<_$UserModelLoadedErrorImpl>
      get copyWith => throw _privateConstructorUsedError;
}
