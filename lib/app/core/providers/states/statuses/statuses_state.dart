import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../helpers/logger.dart';
import '../../../models/status/status_model.dart';
import '../../../services/api/service_provider.dart';
import '../../../services/api/status/status_api_service.dart';
import '../user/user_state.dart';

// TODO: переделать на провайдер с аннотацией @riverpod

//dependent sources
final statusesProvider =
    StateNotifierProvider<StatusesStateNotifier, List<StatusModel>>((ref) {
  var statusesNotifier =
      StatusesStateNotifier(ref.watch(statusRepositoryProvider));

  var userState = ref.watch(currentUserProvider);
  userState.maybeWhen(
      auth: (user) {
        statusesNotifier.getStatuses();
      },
      orElse: () {});

  return statusesNotifier;
});

class StatusesStateNotifier extends StateNotifier<List<StatusModel>> {
  final StatusApi apiClient;
  StatusesStateNotifier(this.apiClient) : super([]);

  int _delayTime = 1;

  getStatuses() async {
    try {
      var statuses = await apiClient.getStatuses();
      //print(statuses);
      state = statuses;
    } catch (e) {
      logger.e("Error: getStatuses", error: e);
      Timer(Duration(seconds: _delayTime), () {
        getStatuses();
      });

      _delayTime = _delayTime * 2;
    }
  }

  Future<bool> addStatus(StatusModel status) async {
    try {
      var result =
          await apiClient.addStatus(status.name, status.outage ?? false);

      if (result == true) {
        getStatuses();
        return true;
      }
    } catch (e) {
      // await AppMetrica.reportError(
      //   message: 'Ошибка добавления статуса',
      //   errorDescription: AppMetricaErrorDescription(
      //     StackTrace.current,
      //     message: e.toString(),
      //   ),
      // );
      logger.e("Error: addStatus", error: e);
    }

    return false;
  }

  Future<bool> editStatus(StatusModel status) async {
    try {
      var result = await apiClient.editStatus(status);

      if (result == true) {
        getStatuses();
        return true;
      }
    } catch (e) {
      // await AppMetrica.reportError(
      //   message: 'Ошибка изменения статуса',
      //   errorDescription: AppMetricaErrorDescription(
      //     StackTrace.current,
      //     message: e.toString(),
      //   ),
      // );
      logger.e("Error: editStatus", error: e);
    }

    return false;
  }
}
