// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'common_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$currentUserModelHash() => r'51cac3db12457fba09bb56765124fc99db8e1ca4';

/// See also [currentUserModel].
@ProviderFor(currentUserModel)
final currentUserModelProvider = AutoDisposeProvider<UserModel?>.internal(
  currentUserModel,
  name: r'currentUserModelProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentUserModelHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CurrentUserModelRef = AutoDisposeProviderRef<UserModel?>;
String _$currentUserAreaHash() => r'c65f1bcc6ad67ede26ee96671922554f3002dcdb';

/// See also [currentUserArea].
@ProviderFor(currentUserArea)
final currentUserAreaProvider = AutoDisposeProvider<AreaModel?>.internal(
  currentUserArea,
  name: r'currentUserAreaProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentUserAreaHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CurrentUserAreaRef = AutoDisposeProviderRef<AreaModel?>;
String _$currentUserBranchHash() => r'5913030bfa7783de0336f1124f37233cb458084b';

/// See also [currentUserBranch].
@ProviderFor(currentUserBranch)
final currentUserBranchProvider = AutoDisposeProvider<BranchModel?>.internal(
  currentUserBranch,
  name: r'currentUserBranchProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentUserBranchHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CurrentUserBranchRef = AutoDisposeProviderRef<BranchModel?>;
String _$getStatusesForAreaHash() =>
    r'd42ef12a4225612e314ab18dbd4bc9ab539fae1a';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [getStatusesForArea].
@ProviderFor(getStatusesForArea)
const getStatusesForAreaProvider = GetStatusesForAreaFamily();

/// See also [getStatusesForArea].
class GetStatusesForAreaFamily extends Family<List<StatusModel>> {
  /// See also [getStatusesForArea].
  const GetStatusesForAreaFamily();

  /// See also [getStatusesForArea].
  GetStatusesForAreaProvider call(
    String? idAreaStr,
    int? idArea,
  ) {
    return GetStatusesForAreaProvider(
      idAreaStr,
      idArea,
    );
  }

  @override
  GetStatusesForAreaProvider getProviderOverride(
    covariant GetStatusesForAreaProvider provider,
  ) {
    return call(
      provider.idAreaStr,
      provider.idArea,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'getStatusesForAreaProvider';
}

/// See also [getStatusesForArea].
class GetStatusesForAreaProvider
    extends AutoDisposeProvider<List<StatusModel>> {
  /// See also [getStatusesForArea].
  GetStatusesForAreaProvider(
    String? idAreaStr,
    int? idArea,
  ) : this._internal(
          (ref) => getStatusesForArea(
            ref as GetStatusesForAreaRef,
            idAreaStr,
            idArea,
          ),
          from: getStatusesForAreaProvider,
          name: r'getStatusesForAreaProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$getStatusesForAreaHash,
          dependencies: GetStatusesForAreaFamily._dependencies,
          allTransitiveDependencies:
              GetStatusesForAreaFamily._allTransitiveDependencies,
          idAreaStr: idAreaStr,
          idArea: idArea,
        );

  GetStatusesForAreaProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.idAreaStr,
    required this.idArea,
  }) : super.internal();

  final String? idAreaStr;
  final int? idArea;

  @override
  Override overrideWith(
    List<StatusModel> Function(GetStatusesForAreaRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: GetStatusesForAreaProvider._internal(
        (ref) => create(ref as GetStatusesForAreaRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        idAreaStr: idAreaStr,
        idArea: idArea,
      ),
    );
  }

  @override
  AutoDisposeProviderElement<List<StatusModel>> createElement() {
    return _GetStatusesForAreaProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is GetStatusesForAreaProvider &&
        other.idAreaStr == idAreaStr &&
        other.idArea == idArea;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, idAreaStr.hashCode);
    hash = _SystemHash.combine(hash, idArea.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin GetStatusesForAreaRef on AutoDisposeProviderRef<List<StatusModel>> {
  /// The parameter `idAreaStr` of this provider.
  String? get idAreaStr;

  /// The parameter `idArea` of this provider.
  int? get idArea;
}

class _GetStatusesForAreaProviderElement
    extends AutoDisposeProviderElement<List<StatusModel>>
    with GetStatusesForAreaRef {
  _GetStatusesForAreaProviderElement(super.provider);

  @override
  String? get idAreaStr => (origin as GetStatusesForAreaProvider).idAreaStr;
  @override
  int? get idArea => (origin as GetStatusesForAreaProvider).idArea;
}

String _$areaForIDHash() => r'b2512158133be043823cc39c30df7b06699acca3';

/// See also [areaForID].
@ProviderFor(areaForID)
const areaForIDProvider = AreaForIDFamily();

/// See also [areaForID].
class AreaForIDFamily extends Family<AreaModel?> {
  /// See also [areaForID].
  const AreaForIDFamily();

  /// See also [areaForID].
  AreaForIDProvider call(
    int? areaID,
  ) {
    return AreaForIDProvider(
      areaID,
    );
  }

  @override
  AreaForIDProvider getProviderOverride(
    covariant AreaForIDProvider provider,
  ) {
    return call(
      provider.areaID,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'areaForIDProvider';
}

/// See also [areaForID].
class AreaForIDProvider extends AutoDisposeProvider<AreaModel?> {
  /// See also [areaForID].
  AreaForIDProvider(
    int? areaID,
  ) : this._internal(
          (ref) => areaForID(
            ref as AreaForIDRef,
            areaID,
          ),
          from: areaForIDProvider,
          name: r'areaForIDProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$areaForIDHash,
          dependencies: AreaForIDFamily._dependencies,
          allTransitiveDependencies: AreaForIDFamily._allTransitiveDependencies,
          areaID: areaID,
        );

  AreaForIDProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.areaID,
  }) : super.internal();

  final int? areaID;

  @override
  Override overrideWith(
    AreaModel? Function(AreaForIDRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: AreaForIDProvider._internal(
        (ref) => create(ref as AreaForIDRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        areaID: areaID,
      ),
    );
  }

  @override
  AutoDisposeProviderElement<AreaModel?> createElement() {
    return _AreaForIDProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is AreaForIDProvider && other.areaID == areaID;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, areaID.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin AreaForIDRef on AutoDisposeProviderRef<AreaModel?> {
  /// The parameter `areaID` of this provider.
  int? get areaID;
}

class _AreaForIDProviderElement extends AutoDisposeProviderElement<AreaModel?>
    with AreaForIDRef {
  _AreaForIDProviderElement(super.provider);

  @override
  int? get areaID => (origin as AreaForIDProvider).areaID;
}

String _$areaForIDAreaStrHash() => r'71819d2408ab469d13ac05a477f5730011e73f05';

/// See also [areaForIDAreaStr].
@ProviderFor(areaForIDAreaStr)
const areaForIDAreaStrProvider = AreaForIDAreaStrFamily();

/// See also [areaForIDAreaStr].
class AreaForIDAreaStrFamily extends Family<AreaModel?> {
  /// See also [areaForIDAreaStr].
  const AreaForIDAreaStrFamily();

  /// See also [areaForIDAreaStr].
  AreaForIDAreaStrProvider call(
    String? idAreaStr,
  ) {
    return AreaForIDAreaStrProvider(
      idAreaStr,
    );
  }

  @override
  AreaForIDAreaStrProvider getProviderOverride(
    covariant AreaForIDAreaStrProvider provider,
  ) {
    return call(
      provider.idAreaStr,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'areaForIDAreaStrProvider';
}

/// See also [areaForIDAreaStr].
class AreaForIDAreaStrProvider extends AutoDisposeProvider<AreaModel?> {
  /// See also [areaForIDAreaStr].
  AreaForIDAreaStrProvider(
    String? idAreaStr,
  ) : this._internal(
          (ref) => areaForIDAreaStr(
            ref as AreaForIDAreaStrRef,
            idAreaStr,
          ),
          from: areaForIDAreaStrProvider,
          name: r'areaForIDAreaStrProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$areaForIDAreaStrHash,
          dependencies: AreaForIDAreaStrFamily._dependencies,
          allTransitiveDependencies:
              AreaForIDAreaStrFamily._allTransitiveDependencies,
          idAreaStr: idAreaStr,
        );

  AreaForIDAreaStrProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.idAreaStr,
  }) : super.internal();

  final String? idAreaStr;

  @override
  Override overrideWith(
    AreaModel? Function(AreaForIDAreaStrRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: AreaForIDAreaStrProvider._internal(
        (ref) => create(ref as AreaForIDAreaStrRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        idAreaStr: idAreaStr,
      ),
    );
  }

  @override
  AutoDisposeProviderElement<AreaModel?> createElement() {
    return _AreaForIDAreaStrProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is AreaForIDAreaStrProvider && other.idAreaStr == idAreaStr;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, idAreaStr.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin AreaForIDAreaStrRef on AutoDisposeProviderRef<AreaModel?> {
  /// The parameter `idAreaStr` of this provider.
  String? get idAreaStr;
}

class _AreaForIDAreaStrProviderElement
    extends AutoDisposeProviderElement<AreaModel?> with AreaForIDAreaStrRef {
  _AreaForIDAreaStrProviderElement(super.provider);

  @override
  String? get idAreaStr => (origin as AreaForIDAreaStrProvider).idAreaStr;
}

String _$branchForIDBranchStrHash() =>
    r'3acb8db9cd0be258c512ea5c5f6fa3da28059545';

/// See also [branchForIDBranchStr].
@ProviderFor(branchForIDBranchStr)
const branchForIDBranchStrProvider = BranchForIDBranchStrFamily();

/// See also [branchForIDBranchStr].
class BranchForIDBranchStrFamily extends Family<BranchModel?> {
  /// See also [branchForIDBranchStr].
  const BranchForIDBranchStrFamily();

  /// See also [branchForIDBranchStr].
  BranchForIDBranchStrProvider call(
    String? idBranchStr,
  ) {
    return BranchForIDBranchStrProvider(
      idBranchStr,
    );
  }

  @override
  BranchForIDBranchStrProvider getProviderOverride(
    covariant BranchForIDBranchStrProvider provider,
  ) {
    return call(
      provider.idBranchStr,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'branchForIDBranchStrProvider';
}

/// See also [branchForIDBranchStr].
class BranchForIDBranchStrProvider extends AutoDisposeProvider<BranchModel?> {
  /// See also [branchForIDBranchStr].
  BranchForIDBranchStrProvider(
    String? idBranchStr,
  ) : this._internal(
          (ref) => branchForIDBranchStr(
            ref as BranchForIDBranchStrRef,
            idBranchStr,
          ),
          from: branchForIDBranchStrProvider,
          name: r'branchForIDBranchStrProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$branchForIDBranchStrHash,
          dependencies: BranchForIDBranchStrFamily._dependencies,
          allTransitiveDependencies:
              BranchForIDBranchStrFamily._allTransitiveDependencies,
          idBranchStr: idBranchStr,
        );

  BranchForIDBranchStrProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.idBranchStr,
  }) : super.internal();

  final String? idBranchStr;

  @override
  Override overrideWith(
    BranchModel? Function(BranchForIDBranchStrRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: BranchForIDBranchStrProvider._internal(
        (ref) => create(ref as BranchForIDBranchStrRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        idBranchStr: idBranchStr,
      ),
    );
  }

  @override
  AutoDisposeProviderElement<BranchModel?> createElement() {
    return _BranchForIDBranchStrProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is BranchForIDBranchStrProvider &&
        other.idBranchStr == idBranchStr;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, idBranchStr.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin BranchForIDBranchStrRef on AutoDisposeProviderRef<BranchModel?> {
  /// The parameter `idBranchStr` of this provider.
  String? get idBranchStr;
}

class _BranchForIDBranchStrProviderElement
    extends AutoDisposeProviderElement<BranchModel?>
    with BranchForIDBranchStrRef {
  _BranchForIDBranchStrProviderElement(super.provider);

  @override
  String? get idBranchStr =>
      (origin as BranchForIDBranchStrProvider).idBranchStr;
}

String _$branchForIDHash() => r'eda7b2d4522344592fc6575e1aea673bf8932f72';

/// See also [branchForID].
@ProviderFor(branchForID)
const branchForIDProvider = BranchForIDFamily();

/// See also [branchForID].
class BranchForIDFamily extends Family<BranchModel?> {
  /// See also [branchForID].
  const BranchForIDFamily();

  /// See also [branchForID].
  BranchForIDProvider call(
    int? branchID,
  ) {
    return BranchForIDProvider(
      branchID,
    );
  }

  @override
  BranchForIDProvider getProviderOverride(
    covariant BranchForIDProvider provider,
  ) {
    return call(
      provider.branchID,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'branchForIDProvider';
}

/// See also [branchForID].
class BranchForIDProvider extends AutoDisposeProvider<BranchModel?> {
  /// See also [branchForID].
  BranchForIDProvider(
    int? branchID,
  ) : this._internal(
          (ref) => branchForID(
            ref as BranchForIDRef,
            branchID,
          ),
          from: branchForIDProvider,
          name: r'branchForIDProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$branchForIDHash,
          dependencies: BranchForIDFamily._dependencies,
          allTransitiveDependencies:
              BranchForIDFamily._allTransitiveDependencies,
          branchID: branchID,
        );

  BranchForIDProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.branchID,
  }) : super.internal();

  final int? branchID;

  @override
  Override overrideWith(
    BranchModel? Function(BranchForIDRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: BranchForIDProvider._internal(
        (ref) => create(ref as BranchForIDRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        branchID: branchID,
      ),
    );
  }

  @override
  AutoDisposeProviderElement<BranchModel?> createElement() {
    return _BranchForIDProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is BranchForIDProvider && other.branchID == branchID;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, branchID.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin BranchForIDRef on AutoDisposeProviderRef<BranchModel?> {
  /// The parameter `branchID` of this provider.
  int? get branchID;
}

class _BranchForIDProviderElement
    extends AutoDisposeProviderElement<BranchModel?> with BranchForIDRef {
  _BranchForIDProviderElement(super.provider);

  @override
  int? get branchID => (origin as BranchForIDProvider).branchID;
}

String _$statusForIDHash() => r'150dc23feb7cc0545c5a7cc2d04afc24e1023c0d';

/// See also [statusForID].
@ProviderFor(statusForID)
const statusForIDProvider = StatusForIDFamily();

/// See also [statusForID].
class StatusForIDFamily extends Family<StatusModel?> {
  /// See also [statusForID].
  const StatusForIDFamily();

  /// See also [statusForID].
  StatusForIDProvider call(
    int? statusID,
  ) {
    return StatusForIDProvider(
      statusID,
    );
  }

  @override
  StatusForIDProvider getProviderOverride(
    covariant StatusForIDProvider provider,
  ) {
    return call(
      provider.statusID,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'statusForIDProvider';
}

/// See also [statusForID].
class StatusForIDProvider extends AutoDisposeProvider<StatusModel?> {
  /// See also [statusForID].
  StatusForIDProvider(
    int? statusID,
  ) : this._internal(
          (ref) => statusForID(
            ref as StatusForIDRef,
            statusID,
          ),
          from: statusForIDProvider,
          name: r'statusForIDProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$statusForIDHash,
          dependencies: StatusForIDFamily._dependencies,
          allTransitiveDependencies:
              StatusForIDFamily._allTransitiveDependencies,
          statusID: statusID,
        );

  StatusForIDProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.statusID,
  }) : super.internal();

  final int? statusID;

  @override
  Override overrideWith(
    StatusModel? Function(StatusForIDRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: StatusForIDProvider._internal(
        (ref) => create(ref as StatusForIDRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        statusID: statusID,
      ),
    );
  }

  @override
  AutoDisposeProviderElement<StatusModel?> createElement() {
    return _StatusForIDProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is StatusForIDProvider && other.statusID == statusID;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, statusID.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin StatusForIDRef on AutoDisposeProviderRef<StatusModel?> {
  /// The parameter `statusID` of this provider.
  int? get statusID;
}

class _StatusForIDProviderElement
    extends AutoDisposeProviderElement<StatusModel?> with StatusForIDRef {
  _StatusForIDProviderElement(super.provider);

  @override
  int? get statusID => (origin as StatusForIDProvider).statusID;
}

String _$showBottomBarHash() => r'3fdeb6f9d2521160ee86c928c1661e6d0b15f71c';

/// See also [ShowBottomBar].
@ProviderFor(ShowBottomBar)
final showBottomBarProvider =
    AutoDisposeNotifierProvider<ShowBottomBar, bool>.internal(
  ShowBottomBar.new,
  name: r'showBottomBarProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$showBottomBarHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ShowBottomBar = AutoDisposeNotifier<bool>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
