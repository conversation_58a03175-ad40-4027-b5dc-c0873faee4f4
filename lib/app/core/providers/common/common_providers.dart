import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:uer_flutter/app/core/models/area/area_model.dart';
import 'package:uer_flutter/app/core/models/branch/branch_model.dart';
import 'package:uer_flutter/app/core/providers/states/areas/areas_state.dart';
import 'package:uer_flutter/app/core/providers/states/branches/branches_state.dart';
import 'package:uer_flutter/app/core/providers/states/user/user_state.dart';
import 'package:uer_flutter/app/helpers/logger.dart';

import '../../models/status/status_model.dart';
import '../../models/user/user_model.dart';
import '../states/statuses/statuses_state.dart';

part 'common_providers.g.dart';

@riverpod
class ShowBottomBar extends _$ShowBottomBar {
  @override
  bool build() => true;

  void show() {
    state = true;
  }

  void hide() {
    state = false;
  }
}

@riverpod
UserModel? currentUserModel(CurrentUserModelRef ref) {
  var userState = ref.watch(currentUserProvider);
  var user = userState.maybeWhen(auth: (user) => user, orElse: () => null);

  if (user != null) {
    return user;
  }

  return null;
}

@riverpod
AreaModel? currentUserArea(CurrentUserAreaRef ref) {
  var userState = ref.watch(currentUserProvider);
  var areaID =
      userState.maybeWhen(auth: (user) => user.area ?? 0, orElse: () => 0);
  var areas = ref.watch(areasProvider);

  if (areaID == 0) {
    return null;
  }

  if (areas.isEmpty) {
    return null;
  }

  try {
    var area = areas.singleWhere((element) => element.identifier == areaID);
    return area;
  } catch (e) {
    // AppMetrica.reportError(
    //   message: 'Ошибка провайдера текущего пользователя, участка',
    //   errorDescription: AppMetricaErrorDescription(
    //     StackTrace.current,
    //     message: e.toString(),
    //   ),
    // );
    logger.e("Error! currentUserArea provider", error: e);
  }

  return null;
}

@riverpod
BranchModel? currentUserBranch(CurrentUserBranchRef ref) {
  var userState = ref.watch(currentUserProvider);
  var branchID =
      userState.maybeWhen(auth: (user) => user.branch ?? 0, orElse: () => 0);
  var branches = ref.watch(branchesProvider);

  if (branchID == 0) {
    return null;
  }

  if (branches.isEmpty) {
    return null;
  }

  try {
    var branch =
        branches.singleWhere((element) => element.identifier == branchID);
    return branch;
  } catch (e) {
    // AppMetrica.reportError(
    //   message: 'Ошибка провайдера текущего пользователя, филиала',
    //   errorDescription: AppMetricaErrorDescription(
    //     StackTrace.current,
    //     message: e.toString(),
    //   ),
    // );
    logger.e("Error! currentUserBranch provider", error: e);
  }

  return null;
}

@riverpod
List<StatusModel> getStatusesForArea(
    GetStatusesForAreaRef ref, String? idAreaStr, int? idArea) {
  AreaModel? area;

  if (idAreaStr != null) {
    area = ref.watch(areaForIDAreaStrProvider(idAreaStr));
  } else if (idArea != null) {
    area = ref.watch(areaForIDProvider(idArea));
  }

  var jobIds = area?.jobs;

  if (jobIds?.isEmpty == true) {
    return [];
  }

  var statuses = ref.watch(statusesProvider);

  var areaStatuses = statuses.where((status) {
    for (var id in jobIds!) {
      if (id == status.identifier) {
        return true;
      }
    }

    return false;
  }).toList();

  return areaStatuses;
}

@riverpod
AreaModel? areaForID(AreaForIDRef ref, int? areaID) {
  if (areaID == null) {
    return null;
  }

  var areas = ref.watch(areasProvider);

  for (var area in areas) {
    if (area.identifier == areaID) {
      return area;
    }
  }
  return null;
}

@riverpod
AreaModel? areaForIDAreaStr(AreaForIDAreaStrRef ref, String? idAreaStr) {
  if (idAreaStr == null) {
    return null;
  }

  var areas = ref.watch(areasProvider);

  for (var area in areas) {
    if (area.idArea == idAreaStr) {
      return area;
    }
  }
  return null;
}

@riverpod
BranchModel? branchForIDBranchStr(
    BranchForIDBranchStrRef ref, String? idBranchStr) {
  if (idBranchStr == null) {
    return null;
  }

  var branchs = ref.watch(branchesProvider);

  for (var branch in branchs) {
    if (branch.idBranch == idBranchStr) {
      return branch;
    }
  }
  return null;
}

@riverpod
BranchModel? branchForID(BranchForIDRef ref, int? branchID) {
  if (branchID == null) {
    return null;
  }

  var branchs = ref.watch(branchesProvider);

  for (var branch in branchs) {
    if (branch.identifier == branchID) {
      return branch;
    }
  }
  return null;
}

@riverpod
StatusModel? statusForID(StatusForIDRef ref, int? statusID) {
  if (statusID == null) {
    return null;
  }

  var statuses = ref.watch(statusesProvider);

  for (var status in statuses) {
    if (status.identifier == statusID) {
      return status;
    }
  }
  return null;
}
