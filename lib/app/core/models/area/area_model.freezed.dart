// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'area_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AreaModel _$AreaModelFromJson(Map<String, dynamic> json) {
  return _AreaModel.fromJson(json);
}

/// @nodoc
mixin _$AreaModel {
  int get identifier => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  int get branch => throw _privateConstructorUsedError;
  List<int> get jobs => throw _privateConstructorUsedError; // statuses
  bool? get start => throw _privateConstructorUsedError;
  String? get idArea => throw _privateConstructorUsedError;
  List<String>? get stationCallLogins => throw _privateConstructorUsedError;

  /// Serializes this AreaModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AreaModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AreaModelCopyWith<AreaModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AreaModelCopyWith<$Res> {
  factory $AreaModelCopyWith(AreaModel value, $Res Function(AreaModel) then) =
      _$AreaModelCopyWithImpl<$Res, AreaModel>;
  @useResult
  $Res call(
      {int identifier,
      String name,
      int branch,
      List<int> jobs,
      bool? start,
      String? idArea,
      List<String>? stationCallLogins});
}

/// @nodoc
class _$AreaModelCopyWithImpl<$Res, $Val extends AreaModel>
    implements $AreaModelCopyWith<$Res> {
  _$AreaModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AreaModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? identifier = null,
    Object? name = null,
    Object? branch = null,
    Object? jobs = null,
    Object? start = freezed,
    Object? idArea = freezed,
    Object? stationCallLogins = freezed,
  }) {
    return _then(_value.copyWith(
      identifier: null == identifier
          ? _value.identifier
          : identifier // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      branch: null == branch
          ? _value.branch
          : branch // ignore: cast_nullable_to_non_nullable
              as int,
      jobs: null == jobs
          ? _value.jobs
          : jobs // ignore: cast_nullable_to_non_nullable
              as List<int>,
      start: freezed == start
          ? _value.start
          : start // ignore: cast_nullable_to_non_nullable
              as bool?,
      idArea: freezed == idArea
          ? _value.idArea
          : idArea // ignore: cast_nullable_to_non_nullable
              as String?,
      stationCallLogins: freezed == stationCallLogins
          ? _value.stationCallLogins
          : stationCallLogins // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AreaModelImplCopyWith<$Res>
    implements $AreaModelCopyWith<$Res> {
  factory _$$AreaModelImplCopyWith(
          _$AreaModelImpl value, $Res Function(_$AreaModelImpl) then) =
      __$$AreaModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int identifier,
      String name,
      int branch,
      List<int> jobs,
      bool? start,
      String? idArea,
      List<String>? stationCallLogins});
}

/// @nodoc
class __$$AreaModelImplCopyWithImpl<$Res>
    extends _$AreaModelCopyWithImpl<$Res, _$AreaModelImpl>
    implements _$$AreaModelImplCopyWith<$Res> {
  __$$AreaModelImplCopyWithImpl(
      _$AreaModelImpl _value, $Res Function(_$AreaModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of AreaModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? identifier = null,
    Object? name = null,
    Object? branch = null,
    Object? jobs = null,
    Object? start = freezed,
    Object? idArea = freezed,
    Object? stationCallLogins = freezed,
  }) {
    return _then(_$AreaModelImpl(
      identifier: null == identifier
          ? _value.identifier
          : identifier // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      branch: null == branch
          ? _value.branch
          : branch // ignore: cast_nullable_to_non_nullable
              as int,
      jobs: null == jobs
          ? _value._jobs
          : jobs // ignore: cast_nullable_to_non_nullable
              as List<int>,
      start: freezed == start
          ? _value.start
          : start // ignore: cast_nullable_to_non_nullable
              as bool?,
      idArea: freezed == idArea
          ? _value.idArea
          : idArea // ignore: cast_nullable_to_non_nullable
              as String?,
      stationCallLogins: freezed == stationCallLogins
          ? _value._stationCallLogins
          : stationCallLogins // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class _$AreaModelImpl extends _AreaModel with DiagnosticableTreeMixin {
  const _$AreaModelImpl(
      {required this.identifier,
      required this.name,
      required this.branch,
      required final List<int> jobs,
      this.start,
      this.idArea,
      final List<String>? stationCallLogins})
      : _jobs = jobs,
        _stationCallLogins = stationCallLogins,
        super._();

  factory _$AreaModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$AreaModelImplFromJson(json);

  @override
  final int identifier;
  @override
  final String name;
  @override
  final int branch;
  final List<int> _jobs;
  @override
  List<int> get jobs {
    if (_jobs is EqualUnmodifiableListView) return _jobs;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_jobs);
  }

// statuses
  @override
  final bool? start;
  @override
  final String? idArea;
  final List<String>? _stationCallLogins;
  @override
  List<String>? get stationCallLogins {
    final value = _stationCallLogins;
    if (value == null) return null;
    if (_stationCallLogins is EqualUnmodifiableListView)
      return _stationCallLogins;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AreaModel(identifier: $identifier, name: $name, branch: $branch, jobs: $jobs, start: $start, idArea: $idArea, stationCallLogins: $stationCallLogins)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'AreaModel'))
      ..add(DiagnosticsProperty('identifier', identifier))
      ..add(DiagnosticsProperty('name', name))
      ..add(DiagnosticsProperty('branch', branch))
      ..add(DiagnosticsProperty('jobs', jobs))
      ..add(DiagnosticsProperty('start', start))
      ..add(DiagnosticsProperty('idArea', idArea))
      ..add(DiagnosticsProperty('stationCallLogins', stationCallLogins));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AreaModelImpl &&
            (identical(other.identifier, identifier) ||
                other.identifier == identifier) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.branch, branch) || other.branch == branch) &&
            const DeepCollectionEquality().equals(other._jobs, _jobs) &&
            (identical(other.start, start) || other.start == start) &&
            (identical(other.idArea, idArea) || other.idArea == idArea) &&
            const DeepCollectionEquality()
                .equals(other._stationCallLogins, _stationCallLogins));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      identifier,
      name,
      branch,
      const DeepCollectionEquality().hash(_jobs),
      start,
      idArea,
      const DeepCollectionEquality().hash(_stationCallLogins));

  /// Create a copy of AreaModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AreaModelImplCopyWith<_$AreaModelImpl> get copyWith =>
      __$$AreaModelImplCopyWithImpl<_$AreaModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AreaModelImplToJson(
      this,
    );
  }
}

abstract class _AreaModel extends AreaModel {
  const factory _AreaModel(
      {required final int identifier,
      required final String name,
      required final int branch,
      required final List<int> jobs,
      final bool? start,
      final String? idArea,
      final List<String>? stationCallLogins}) = _$AreaModelImpl;
  const _AreaModel._() : super._();

  factory _AreaModel.fromJson(Map<String, dynamic> json) =
      _$AreaModelImpl.fromJson;

  @override
  int get identifier;
  @override
  String get name;
  @override
  int get branch;
  @override
  List<int> get jobs; // statuses
  @override
  bool? get start;
  @override
  String? get idArea;
  @override
  List<String>? get stationCallLogins;

  /// Create a copy of AreaModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AreaModelImplCopyWith<_$AreaModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AreaListModel _$AreaListModelFromJson(Map<String, dynamic> json) {
  return _AreaListModel.fromJson(json);
}

/// @nodoc
mixin _$AreaListModel {
  List<AreaModel> get data => throw _privateConstructorUsedError;

  /// Serializes this AreaListModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AreaListModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AreaListModelCopyWith<AreaListModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AreaListModelCopyWith<$Res> {
  factory $AreaListModelCopyWith(
          AreaListModel value, $Res Function(AreaListModel) then) =
      _$AreaListModelCopyWithImpl<$Res, AreaListModel>;
  @useResult
  $Res call({List<AreaModel> data});
}

/// @nodoc
class _$AreaListModelCopyWithImpl<$Res, $Val extends AreaListModel>
    implements $AreaListModelCopyWith<$Res> {
  _$AreaListModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AreaListModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
  }) {
    return _then(_value.copyWith(
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<AreaModel>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AreaListModelImplCopyWith<$Res>
    implements $AreaListModelCopyWith<$Res> {
  factory _$$AreaListModelImplCopyWith(
          _$AreaListModelImpl value, $Res Function(_$AreaListModelImpl) then) =
      __$$AreaListModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<AreaModel> data});
}

/// @nodoc
class __$$AreaListModelImplCopyWithImpl<$Res>
    extends _$AreaListModelCopyWithImpl<$Res, _$AreaListModelImpl>
    implements _$$AreaListModelImplCopyWith<$Res> {
  __$$AreaListModelImplCopyWithImpl(
      _$AreaListModelImpl _value, $Res Function(_$AreaListModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of AreaListModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
  }) {
    return _then(_$AreaListModelImpl(
      null == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as List<AreaModel>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AreaListModelImpl
    with DiagnosticableTreeMixin
    implements _AreaListModel {
  _$AreaListModelImpl(final List<AreaModel> data) : _data = data;

  factory _$AreaListModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$AreaListModelImplFromJson(json);

  final List<AreaModel> _data;
  @override
  List<AreaModel> get data {
    if (_data is EqualUnmodifiableListView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_data);
  }

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AreaListModel(data: $data)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'AreaListModel'))
      ..add(DiagnosticsProperty('data', data));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AreaListModelImpl &&
            const DeepCollectionEquality().equals(other._data, _data));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_data));

  /// Create a copy of AreaListModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AreaListModelImplCopyWith<_$AreaListModelImpl> get copyWith =>
      __$$AreaListModelImplCopyWithImpl<_$AreaListModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AreaListModelImplToJson(
      this,
    );
  }
}

abstract class _AreaListModel implements AreaListModel {
  factory _AreaListModel(final List<AreaModel> data) = _$AreaListModelImpl;

  factory _AreaListModel.fromJson(Map<String, dynamic> json) =
      _$AreaListModelImpl.fromJson;

  @override
  List<AreaModel> get data;

  /// Create a copy of AreaListModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AreaListModelImplCopyWith<_$AreaListModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
