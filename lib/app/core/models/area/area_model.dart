import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter/foundation.dart';
// import '../branch/branch_model.dart';
// import '../job/job_model.dart';

part 'area_model.freezed.dart';
part 'area_model.g.dart';

@freezed
class AreaModel with _$AreaModel {
  const AreaModel._();
  @JsonSerializable(explicitToJson: true, includeIfNull: false)
  const factory AreaModel({
    required int identifier,
    required String name,
    required int branch,
    required List<int> jobs, // statuses
    bool? start,
    String? idArea,
    List<String>? stationCallLogins,
  }) = _AreaModel;

  factory AreaModel.fromJson(Map<String, Object?> json) =>
      _$AreaModelFromJson(json);

  // Branch? getBranch() {
  //   return Utils.getBranchForID(branchID: this.branch);
  // }

  // List<Job> getJobs() {
  //   return Utils.getJobsForArea(area: this);
  // }
}

@freezed
class AreaListModel with _$AreaListModel {
  factory AreaListModel(List<AreaModel> data) = _AreaListModel;

  factory AreaListModel.fromJson(Map<String, dynamic> json) =>
      _$AreaListModelFromJson(json);
}
