// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'area_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AreaModelImpl _$$AreaModelImplFromJson(Map<String, dynamic> json) =>
    _$AreaModelImpl(
      identifier: (json['identifier'] as num).toInt(),
      name: json['name'] as String,
      branch: (json['branch'] as num).toInt(),
      jobs: (json['jobs'] as List<dynamic>)
          .map((e) => (e as num).toInt())
          .toList(),
      start: json['start'] as bool?,
      idArea: json['idArea'] as String?,
      stationCallLogins: (json['stationCallLogins'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$$AreaModelImplToJson(_$AreaModelImpl instance) =>
    <String, dynamic>{
      'identifier': instance.identifier,
      'name': instance.name,
      'branch': instance.branch,
      'jobs': instance.jobs,
      if (instance.start case final value?) 'start': value,
      if (instance.idArea case final value?) 'idArea': value,
      if (instance.stationCallLogins case final value?)
        'stationCallLogins': value,
    };

_$AreaListModelImpl _$$AreaListModelImplFromJson(Map<String, dynamic> json) =>
    _$AreaListModelImpl(
      (json['data'] as List<dynamic>)
          .map((e) => AreaModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$AreaListModelImplToJson(_$AreaListModelImpl instance) =>
    <String, dynamic>{
      'data': instance.data,
    };
