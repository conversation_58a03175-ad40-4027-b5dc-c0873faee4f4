// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'status_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$StatusModelImpl _$$StatusModelImplFromJson(Map<String, dynamic> json) =>
    _$StatusModelImpl(
      identifier: (json['identifier'] as num).toInt(),
      name: json['name'] as String,
      outage: json['outage'] as bool?,
    );

Map<String, dynamic> _$$StatusModelImplToJson(_$StatusModelImpl instance) =>
    <String, dynamic>{
      'identifier': instance.identifier,
      'name': instance.name,
      if (instance.outage case final value?) 'outage': value,
    };

_$StatusListModelImpl _$$StatusListModelImplFromJson(
        Map<String, dynamic> json) =>
    _$StatusListModelImpl(
      (json['data'] as List<dynamic>)
          .map((e) => StatusModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$StatusListModelImplToJson(
        _$StatusListModelImpl instance) =>
    <String, dynamic>{
      'data': instance.data,
    };
