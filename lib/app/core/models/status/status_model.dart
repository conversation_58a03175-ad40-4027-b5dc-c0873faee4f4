import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter/foundation.dart';

import '../../../helpers/constants.dart';

part 'status_model.freezed.dart';
part 'status_model.g.dart';

@freezed
class StatusModel with _$StatusModel {
  const StatusModel._();
  @JsonSerializable(explicitToJson: true, includeIfNull: false)
  const factory StatusModel({
    required int identifier,
    required String name,
    bool? outage, // считать простоем или нет
  }) = _StatusModel;

  isShipped() {
    if (identifier == SHIPMENT_JOB_ID) {
      // статус отгружено
      return true;
    }
    return false;
  }

  factory StatusModel.fromJson(Map<String, Object?> json) =>
      _$StatusModelFromJson(json);
}

@freezed
class StatusListModel with _$StatusListModel {
  factory StatusListModel(List<StatusModel> data) = _StatusListModel;

  factory StatusListModel.fromJson(Map<String, dynamic> json) =>
      _$StatusListModelFromJson(json);
}
