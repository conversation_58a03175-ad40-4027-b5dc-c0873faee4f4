import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter/foundation.dart';
import 'user_task_status_model.dart';

part 'user_task_model.freezed.dart';
part 'user_task_model.g.dart';

@freezed
class UserTaskModel with _$UserTaskModel {
  const UserTaskModel._();
  @JsonSerializable(explicitToJson: true, includeIfNull: false)
  const factory UserTaskModel({
    @JsonKey(name: '_id') required int id, // _id

    required int area,
    required int branch,
    required String login,
    required String nameArea,
    List<UserTaskStatusModel>? statuses,
    double? createdAt,
    double? updatedAt,
  }) = _UserTaskModel;

  factory UserTaskModel.fromJson(Map<String, Object?> json) =>
      _$UserTaskModelFromJson(json);
}

@freezed
class UserTaskListModel with _$UserTaskListModel {
  factory UserTaskListModel(List<UserTaskModel> data) = _UserTaskListModel;

  factory UserTaskListModel.fromJson(Map<String, dynamic> json) =>
      _$UserTaskListModelFromJson(json);
}
