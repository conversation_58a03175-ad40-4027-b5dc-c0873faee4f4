import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter/foundation.dart';
import '../enums/user_task_status_enum.dart';

part 'user_task_status_model.freezed.dart';
part 'user_task_status_model.g.dart';

@freezed
class UserTaskStatusModel with _$UserTaskStatusModel {
  const UserTaskStatusModel._();
  @JsonSerializable(explicitToJson: true, includeIfNull: false)
  const factory UserTaskStatusModel({
    required String owner,
    required UserTaskStatusType status,
    String? comment,
    double? createdAt,
    double? updatedAt,
  }) = _UserTaskStatusModel;

  factory UserTaskStatusModel.fromJson(Map<String, Object?> json) =>
      _$UserTaskStatusModelFromJson(json);
}
