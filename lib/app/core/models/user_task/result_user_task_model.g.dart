// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'result_user_task_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ResultUserTaskModelImpl _$$ResultUserTaskModelImplFromJson(
        Map<String, dynamic> json) =>
    _$ResultUserTaskModelImpl(
      data: (json['data'] as List<dynamic>?)
          ?.map((e) => UserTaskModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      max: json['max'] as bool?,
      nextCursor: (json['nextCursor'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$ResultUserTaskModelImplToJson(
        _$ResultUserTaskModelImpl instance) =>
    <String, dynamic>{
      if (instance.data?.map((e) => e.toJson()).toList() case final value?)
        'data': value,
      if (instance.max case final value?) 'max': value,
      if (instance.nextCursor case final value?) 'nextCursor': value,
    };
