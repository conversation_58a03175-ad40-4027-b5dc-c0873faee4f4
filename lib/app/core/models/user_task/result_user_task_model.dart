import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter/foundation.dart';
import 'user_task_model.dart';

part 'result_user_task_model.freezed.dart';
part 'result_user_task_model.g.dart';

@freezed
class ResultUserTaskModel with _$ResultUserTaskModel {
  const ResultUserTaskModel._();
  @JsonSerializable(explicitToJson: true, includeIfNull: false)
  const factory ResultUserTaskModel({
    List<UserTaskModel>? data,
    bool? max,
    double? nextCursor,
  }) = _ResultUserTaskModel;

  factory ResultUserTaskModel.fromJson(Map<String, Object?> json) =>
      _$ResultUserTaskModelFromJson(json);
}

// struct ResultModel<Element: Codable> : Codable,DictionaryEncodable {
//     var nextCursor: Double?
//     var max: Bool?
//     var data : [Element]?
// }
