// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'result_user_task_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ResultUserTaskModel _$ResultUserTaskModelFromJson(Map<String, dynamic> json) {
  return _ResultUserTaskModel.fromJson(json);
}

/// @nodoc
mixin _$ResultUserTaskModel {
  List<UserTaskModel>? get data => throw _privateConstructorUsedError;
  bool? get max => throw _privateConstructorUsedError;
  double? get nextCursor => throw _privateConstructorUsedError;

  /// Serializes this ResultUserTaskModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ResultUserTaskModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ResultUserTaskModelCopyWith<ResultUserTaskModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ResultUserTaskModelCopyWith<$Res> {
  factory $ResultUserTaskModelCopyWith(
          ResultUserTaskModel value, $Res Function(ResultUserTaskModel) then) =
      _$ResultUserTaskModelCopyWithImpl<$Res, ResultUserTaskModel>;
  @useResult
  $Res call({List<UserTaskModel>? data, bool? max, double? nextCursor});
}

/// @nodoc
class _$ResultUserTaskModelCopyWithImpl<$Res, $Val extends ResultUserTaskModel>
    implements $ResultUserTaskModelCopyWith<$Res> {
  _$ResultUserTaskModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ResultUserTaskModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = freezed,
    Object? max = freezed,
    Object? nextCursor = freezed,
  }) {
    return _then(_value.copyWith(
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<UserTaskModel>?,
      max: freezed == max
          ? _value.max
          : max // ignore: cast_nullable_to_non_nullable
              as bool?,
      nextCursor: freezed == nextCursor
          ? _value.nextCursor
          : nextCursor // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ResultUserTaskModelImplCopyWith<$Res>
    implements $ResultUserTaskModelCopyWith<$Res> {
  factory _$$ResultUserTaskModelImplCopyWith(_$ResultUserTaskModelImpl value,
          $Res Function(_$ResultUserTaskModelImpl) then) =
      __$$ResultUserTaskModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<UserTaskModel>? data, bool? max, double? nextCursor});
}

/// @nodoc
class __$$ResultUserTaskModelImplCopyWithImpl<$Res>
    extends _$ResultUserTaskModelCopyWithImpl<$Res, _$ResultUserTaskModelImpl>
    implements _$$ResultUserTaskModelImplCopyWith<$Res> {
  __$$ResultUserTaskModelImplCopyWithImpl(_$ResultUserTaskModelImpl _value,
      $Res Function(_$ResultUserTaskModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ResultUserTaskModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = freezed,
    Object? max = freezed,
    Object? nextCursor = freezed,
  }) {
    return _then(_$ResultUserTaskModelImpl(
      data: freezed == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as List<UserTaskModel>?,
      max: freezed == max
          ? _value.max
          : max // ignore: cast_nullable_to_non_nullable
              as bool?,
      nextCursor: freezed == nextCursor
          ? _value.nextCursor
          : nextCursor // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class _$ResultUserTaskModelImpl extends _ResultUserTaskModel
    with DiagnosticableTreeMixin {
  const _$ResultUserTaskModelImpl(
      {final List<UserTaskModel>? data, this.max, this.nextCursor})
      : _data = data,
        super._();

  factory _$ResultUserTaskModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ResultUserTaskModelImplFromJson(json);

  final List<UserTaskModel>? _data;
  @override
  List<UserTaskModel>? get data {
    final value = _data;
    if (value == null) return null;
    if (_data is EqualUnmodifiableListView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final bool? max;
  @override
  final double? nextCursor;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ResultUserTaskModel(data: $data, max: $max, nextCursor: $nextCursor)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'ResultUserTaskModel'))
      ..add(DiagnosticsProperty('data', data))
      ..add(DiagnosticsProperty('max', max))
      ..add(DiagnosticsProperty('nextCursor', nextCursor));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ResultUserTaskModelImpl &&
            const DeepCollectionEquality().equals(other._data, _data) &&
            (identical(other.max, max) || other.max == max) &&
            (identical(other.nextCursor, nextCursor) ||
                other.nextCursor == nextCursor));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_data), max, nextCursor);

  /// Create a copy of ResultUserTaskModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ResultUserTaskModelImplCopyWith<_$ResultUserTaskModelImpl> get copyWith =>
      __$$ResultUserTaskModelImplCopyWithImpl<_$ResultUserTaskModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ResultUserTaskModelImplToJson(
      this,
    );
  }
}

abstract class _ResultUserTaskModel extends ResultUserTaskModel {
  const factory _ResultUserTaskModel(
      {final List<UserTaskModel>? data,
      final bool? max,
      final double? nextCursor}) = _$ResultUserTaskModelImpl;
  const _ResultUserTaskModel._() : super._();

  factory _ResultUserTaskModel.fromJson(Map<String, dynamic> json) =
      _$ResultUserTaskModelImpl.fromJson;

  @override
  List<UserTaskModel>? get data;
  @override
  bool? get max;
  @override
  double? get nextCursor;

  /// Create a copy of ResultUserTaskModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ResultUserTaskModelImplCopyWith<_$ResultUserTaskModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
