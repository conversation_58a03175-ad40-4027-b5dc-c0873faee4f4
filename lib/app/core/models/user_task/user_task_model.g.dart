// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_task_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$UserTaskModelImpl _$$UserTaskModelImplFromJson(Map<String, dynamic> json) =>
    _$UserTaskModelImpl(
      id: (json['_id'] as num).toInt(),
      area: (json['area'] as num).toInt(),
      branch: (json['branch'] as num).toInt(),
      login: json['login'] as String,
      nameArea: json['nameArea'] as String,
      statuses: (json['statuses'] as List<dynamic>?)
          ?.map((e) => UserTaskStatusModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      createdAt: (json['createdAt'] as num?)?.toDouble(),
      updatedAt: (json['updatedAt'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$UserTaskModelImplToJson(_$UserTaskModelImpl instance) =>
    <String, dynamic>{
      '_id': instance.id,
      'area': instance.area,
      'branch': instance.branch,
      'login': instance.login,
      'nameArea': instance.nameArea,
      if (instance.statuses?.map((e) => e.toJson()).toList() case final value?)
        'statuses': value,
      if (instance.createdAt case final value?) 'createdAt': value,
      if (instance.updatedAt case final value?) 'updatedAt': value,
    };

_$UserTaskListModelImpl _$$UserTaskListModelImplFromJson(
        Map<String, dynamic> json) =>
    _$UserTaskListModelImpl(
      (json['data'] as List<dynamic>)
          .map((e) => UserTaskModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$UserTaskListModelImplToJson(
        _$UserTaskListModelImpl instance) =>
    <String, dynamic>{
      'data': instance.data,
    };
