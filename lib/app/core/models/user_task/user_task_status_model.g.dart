// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_task_status_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$UserTaskStatusModelImpl _$$UserTaskStatusModelImplFromJson(
        Map<String, dynamic> json) =>
    _$UserTaskStatusModelImpl(
      owner: json['owner'] as String,
      status: $enumDecode(_$UserTaskStatusTypeEnumMap, json['status']),
      comment: json['comment'] as String?,
      createdAt: (json['createdAt'] as num?)?.toDouble(),
      updatedAt: (json['updatedAt'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$UserTaskStatusModelImplToJson(
        _$UserTaskStatusModelImpl instance) =>
    <String, dynamic>{
      'owner': instance.owner,
      'status': _$UserTaskStatusTypeEnumMap[instance.status]!,
      if (instance.comment case final value?) 'comment': value,
      if (instance.createdAt case final value?) 'createdAt': value,
      if (instance.updatedAt case final value?) 'updatedAt': value,
    };

const _$UserTaskStatusTypeEnumMap = {
  UserTaskStatusType.created: 'created',
  UserTaskStatusType.inwork: 'inwork',
  UserTaskStatusType.finished: 'finished',
};
