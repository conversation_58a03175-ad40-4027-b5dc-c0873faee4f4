// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_task_status_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

UserTaskStatusModel _$UserTaskStatusModelFromJson(Map<String, dynamic> json) {
  return _UserTaskStatusModel.fromJson(json);
}

/// @nodoc
mixin _$UserTaskStatusModel {
  String get owner => throw _privateConstructorUsedError;
  UserTaskStatusType<dynamic> get status => throw _privateConstructorUsedError;
  String? get comment => throw _privateConstructorUsedError;
  double? get createdAt => throw _privateConstructorUsedError;
  double? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this UserTaskStatusModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UserTaskStatusModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserTaskStatusModelCopyWith<UserTaskStatusModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserTaskStatusModelCopyWith<$Res> {
  factory $UserTaskStatusModelCopyWith(
          UserTaskStatusModel value, $Res Function(UserTaskStatusModel) then) =
      _$UserTaskStatusModelCopyWithImpl<$Res, UserTaskStatusModel>;
  @useResult
  $Res call(
      {String owner,
      UserTaskStatusType<dynamic> status,
      String? comment,
      double? createdAt,
      double? updatedAt});
}

/// @nodoc
class _$UserTaskStatusModelCopyWithImpl<$Res, $Val extends UserTaskStatusModel>
    implements $UserTaskStatusModelCopyWith<$Res> {
  _$UserTaskStatusModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserTaskStatusModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? owner = null,
    Object? status = null,
    Object? comment = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_value.copyWith(
      owner: null == owner
          ? _value.owner
          : owner // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as UserTaskStatusType<dynamic>,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as double?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UserTaskStatusModelImplCopyWith<$Res>
    implements $UserTaskStatusModelCopyWith<$Res> {
  factory _$$UserTaskStatusModelImplCopyWith(_$UserTaskStatusModelImpl value,
          $Res Function(_$UserTaskStatusModelImpl) then) =
      __$$UserTaskStatusModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String owner,
      UserTaskStatusType<dynamic> status,
      String? comment,
      double? createdAt,
      double? updatedAt});
}

/// @nodoc
class __$$UserTaskStatusModelImplCopyWithImpl<$Res>
    extends _$UserTaskStatusModelCopyWithImpl<$Res, _$UserTaskStatusModelImpl>
    implements _$$UserTaskStatusModelImplCopyWith<$Res> {
  __$$UserTaskStatusModelImplCopyWithImpl(_$UserTaskStatusModelImpl _value,
      $Res Function(_$UserTaskStatusModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserTaskStatusModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? owner = null,
    Object? status = null,
    Object? comment = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_$UserTaskStatusModelImpl(
      owner: null == owner
          ? _value.owner
          : owner // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as UserTaskStatusType<dynamic>,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as double?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class _$UserTaskStatusModelImpl extends _UserTaskStatusModel
    with DiagnosticableTreeMixin {
  const _$UserTaskStatusModelImpl(
      {required this.owner,
      required this.status,
      this.comment,
      this.createdAt,
      this.updatedAt})
      : super._();

  factory _$UserTaskStatusModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserTaskStatusModelImplFromJson(json);

  @override
  final String owner;
  @override
  final UserTaskStatusType<dynamic> status;
  @override
  final String? comment;
  @override
  final double? createdAt;
  @override
  final double? updatedAt;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'UserTaskStatusModel(owner: $owner, status: $status, comment: $comment, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'UserTaskStatusModel'))
      ..add(DiagnosticsProperty('owner', owner))
      ..add(DiagnosticsProperty('status', status))
      ..add(DiagnosticsProperty('comment', comment))
      ..add(DiagnosticsProperty('createdAt', createdAt))
      ..add(DiagnosticsProperty('updatedAt', updatedAt));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserTaskStatusModelImpl &&
            (identical(other.owner, owner) || other.owner == owner) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.comment, comment) || other.comment == comment) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, owner, status, comment, createdAt, updatedAt);

  /// Create a copy of UserTaskStatusModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserTaskStatusModelImplCopyWith<_$UserTaskStatusModelImpl> get copyWith =>
      __$$UserTaskStatusModelImplCopyWithImpl<_$UserTaskStatusModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserTaskStatusModelImplToJson(
      this,
    );
  }
}

abstract class _UserTaskStatusModel extends UserTaskStatusModel {
  const factory _UserTaskStatusModel(
      {required final String owner,
      required final UserTaskStatusType<dynamic> status,
      final String? comment,
      final double? createdAt,
      final double? updatedAt}) = _$UserTaskStatusModelImpl;
  const _UserTaskStatusModel._() : super._();

  factory _UserTaskStatusModel.fromJson(Map<String, dynamic> json) =
      _$UserTaskStatusModelImpl.fromJson;

  @override
  String get owner;
  @override
  UserTaskStatusType<dynamic> get status;
  @override
  String? get comment;
  @override
  double? get createdAt;
  @override
  double? get updatedAt;

  /// Create a copy of UserTaskStatusModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserTaskStatusModelImplCopyWith<_$UserTaskStatusModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
