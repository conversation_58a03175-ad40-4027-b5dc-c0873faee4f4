// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_task_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

UserTaskModel _$UserTaskModelFromJson(Map<String, dynamic> json) {
  return _UserTaskModel.fromJson(json);
}

/// @nodoc
mixin _$UserTaskModel {
  @JsonKey(name: '_id')
  int get id => throw _privateConstructorUsedError; // _id
  int get area => throw _privateConstructorUsedError;
  int get branch => throw _privateConstructorUsedError;
  String get login => throw _privateConstructorUsedError;
  String get nameArea => throw _privateConstructorUsedError;
  List<UserTaskStatusModel>? get statuses => throw _privateConstructorUsedError;
  double? get createdAt => throw _privateConstructorUsedError;
  double? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this UserTaskModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UserTaskModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserTaskModelCopyWith<UserTaskModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserTaskModelCopyWith<$Res> {
  factory $UserTaskModelCopyWith(
          UserTaskModel value, $Res Function(UserTaskModel) then) =
      _$UserTaskModelCopyWithImpl<$Res, UserTaskModel>;
  @useResult
  $Res call(
      {@JsonKey(name: '_id') int id,
      int area,
      int branch,
      String login,
      String nameArea,
      List<UserTaskStatusModel>? statuses,
      double? createdAt,
      double? updatedAt});
}

/// @nodoc
class _$UserTaskModelCopyWithImpl<$Res, $Val extends UserTaskModel>
    implements $UserTaskModelCopyWith<$Res> {
  _$UserTaskModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserTaskModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? area = null,
    Object? branch = null,
    Object? login = null,
    Object? nameArea = null,
    Object? statuses = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      area: null == area
          ? _value.area
          : area // ignore: cast_nullable_to_non_nullable
              as int,
      branch: null == branch
          ? _value.branch
          : branch // ignore: cast_nullable_to_non_nullable
              as int,
      login: null == login
          ? _value.login
          : login // ignore: cast_nullable_to_non_nullable
              as String,
      nameArea: null == nameArea
          ? _value.nameArea
          : nameArea // ignore: cast_nullable_to_non_nullable
              as String,
      statuses: freezed == statuses
          ? _value.statuses
          : statuses // ignore: cast_nullable_to_non_nullable
              as List<UserTaskStatusModel>?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as double?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UserTaskModelImplCopyWith<$Res>
    implements $UserTaskModelCopyWith<$Res> {
  factory _$$UserTaskModelImplCopyWith(
          _$UserTaskModelImpl value, $Res Function(_$UserTaskModelImpl) then) =
      __$$UserTaskModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: '_id') int id,
      int area,
      int branch,
      String login,
      String nameArea,
      List<UserTaskStatusModel>? statuses,
      double? createdAt,
      double? updatedAt});
}

/// @nodoc
class __$$UserTaskModelImplCopyWithImpl<$Res>
    extends _$UserTaskModelCopyWithImpl<$Res, _$UserTaskModelImpl>
    implements _$$UserTaskModelImplCopyWith<$Res> {
  __$$UserTaskModelImplCopyWithImpl(
      _$UserTaskModelImpl _value, $Res Function(_$UserTaskModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserTaskModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? area = null,
    Object? branch = null,
    Object? login = null,
    Object? nameArea = null,
    Object? statuses = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_$UserTaskModelImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      area: null == area
          ? _value.area
          : area // ignore: cast_nullable_to_non_nullable
              as int,
      branch: null == branch
          ? _value.branch
          : branch // ignore: cast_nullable_to_non_nullable
              as int,
      login: null == login
          ? _value.login
          : login // ignore: cast_nullable_to_non_nullable
              as String,
      nameArea: null == nameArea
          ? _value.nameArea
          : nameArea // ignore: cast_nullable_to_non_nullable
              as String,
      statuses: freezed == statuses
          ? _value._statuses
          : statuses // ignore: cast_nullable_to_non_nullable
              as List<UserTaskStatusModel>?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as double?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class _$UserTaskModelImpl extends _UserTaskModel with DiagnosticableTreeMixin {
  const _$UserTaskModelImpl(
      {@JsonKey(name: '_id') required this.id,
      required this.area,
      required this.branch,
      required this.login,
      required this.nameArea,
      final List<UserTaskStatusModel>? statuses,
      this.createdAt,
      this.updatedAt})
      : _statuses = statuses,
        super._();

  factory _$UserTaskModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserTaskModelImplFromJson(json);

  @override
  @JsonKey(name: '_id')
  final int id;
// _id
  @override
  final int area;
  @override
  final int branch;
  @override
  final String login;
  @override
  final String nameArea;
  final List<UserTaskStatusModel>? _statuses;
  @override
  List<UserTaskStatusModel>? get statuses {
    final value = _statuses;
    if (value == null) return null;
    if (_statuses is EqualUnmodifiableListView) return _statuses;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final double? createdAt;
  @override
  final double? updatedAt;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'UserTaskModel(id: $id, area: $area, branch: $branch, login: $login, nameArea: $nameArea, statuses: $statuses, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'UserTaskModel'))
      ..add(DiagnosticsProperty('id', id))
      ..add(DiagnosticsProperty('area', area))
      ..add(DiagnosticsProperty('branch', branch))
      ..add(DiagnosticsProperty('login', login))
      ..add(DiagnosticsProperty('nameArea', nameArea))
      ..add(DiagnosticsProperty('statuses', statuses))
      ..add(DiagnosticsProperty('createdAt', createdAt))
      ..add(DiagnosticsProperty('updatedAt', updatedAt));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserTaskModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.area, area) || other.area == area) &&
            (identical(other.branch, branch) || other.branch == branch) &&
            (identical(other.login, login) || other.login == login) &&
            (identical(other.nameArea, nameArea) ||
                other.nameArea == nameArea) &&
            const DeepCollectionEquality().equals(other._statuses, _statuses) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      area,
      branch,
      login,
      nameArea,
      const DeepCollectionEquality().hash(_statuses),
      createdAt,
      updatedAt);

  /// Create a copy of UserTaskModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserTaskModelImplCopyWith<_$UserTaskModelImpl> get copyWith =>
      __$$UserTaskModelImplCopyWithImpl<_$UserTaskModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserTaskModelImplToJson(
      this,
    );
  }
}

abstract class _UserTaskModel extends UserTaskModel {
  const factory _UserTaskModel(
      {@JsonKey(name: '_id') required final int id,
      required final int area,
      required final int branch,
      required final String login,
      required final String nameArea,
      final List<UserTaskStatusModel>? statuses,
      final double? createdAt,
      final double? updatedAt}) = _$UserTaskModelImpl;
  const _UserTaskModel._() : super._();

  factory _UserTaskModel.fromJson(Map<String, dynamic> json) =
      _$UserTaskModelImpl.fromJson;

  @override
  @JsonKey(name: '_id')
  int get id; // _id
  @override
  int get area;
  @override
  int get branch;
  @override
  String get login;
  @override
  String get nameArea;
  @override
  List<UserTaskStatusModel>? get statuses;
  @override
  double? get createdAt;
  @override
  double? get updatedAt;

  /// Create a copy of UserTaskModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserTaskModelImplCopyWith<_$UserTaskModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

UserTaskListModel _$UserTaskListModelFromJson(Map<String, dynamic> json) {
  return _UserTaskListModel.fromJson(json);
}

/// @nodoc
mixin _$UserTaskListModel {
  List<UserTaskModel> get data => throw _privateConstructorUsedError;

  /// Serializes this UserTaskListModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UserTaskListModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserTaskListModelCopyWith<UserTaskListModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserTaskListModelCopyWith<$Res> {
  factory $UserTaskListModelCopyWith(
          UserTaskListModel value, $Res Function(UserTaskListModel) then) =
      _$UserTaskListModelCopyWithImpl<$Res, UserTaskListModel>;
  @useResult
  $Res call({List<UserTaskModel> data});
}

/// @nodoc
class _$UserTaskListModelCopyWithImpl<$Res, $Val extends UserTaskListModel>
    implements $UserTaskListModelCopyWith<$Res> {
  _$UserTaskListModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserTaskListModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
  }) {
    return _then(_value.copyWith(
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<UserTaskModel>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UserTaskListModelImplCopyWith<$Res>
    implements $UserTaskListModelCopyWith<$Res> {
  factory _$$UserTaskListModelImplCopyWith(_$UserTaskListModelImpl value,
          $Res Function(_$UserTaskListModelImpl) then) =
      __$$UserTaskListModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<UserTaskModel> data});
}

/// @nodoc
class __$$UserTaskListModelImplCopyWithImpl<$Res>
    extends _$UserTaskListModelCopyWithImpl<$Res, _$UserTaskListModelImpl>
    implements _$$UserTaskListModelImplCopyWith<$Res> {
  __$$UserTaskListModelImplCopyWithImpl(_$UserTaskListModelImpl _value,
      $Res Function(_$UserTaskListModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserTaskListModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
  }) {
    return _then(_$UserTaskListModelImpl(
      null == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as List<UserTaskModel>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserTaskListModelImpl
    with DiagnosticableTreeMixin
    implements _UserTaskListModel {
  _$UserTaskListModelImpl(final List<UserTaskModel> data) : _data = data;

  factory _$UserTaskListModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserTaskListModelImplFromJson(json);

  final List<UserTaskModel> _data;
  @override
  List<UserTaskModel> get data {
    if (_data is EqualUnmodifiableListView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_data);
  }

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'UserTaskListModel(data: $data)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'UserTaskListModel'))
      ..add(DiagnosticsProperty('data', data));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserTaskListModelImpl &&
            const DeepCollectionEquality().equals(other._data, _data));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_data));

  /// Create a copy of UserTaskListModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserTaskListModelImplCopyWith<_$UserTaskListModelImpl> get copyWith =>
      __$$UserTaskListModelImplCopyWithImpl<_$UserTaskListModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserTaskListModelImplToJson(
      this,
    );
  }
}

abstract class _UserTaskListModel implements UserTaskListModel {
  factory _UserTaskListModel(final List<UserTaskModel> data) =
      _$UserTaskListModelImpl;

  factory _UserTaskListModel.fromJson(Map<String, dynamic> json) =
      _$UserTaskListModelImpl.fromJson;

  @override
  List<UserTaskModel> get data;

  /// Create a copy of UserTaskListModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserTaskListModelImplCopyWith<_$UserTaskListModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
