// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'task_ssz_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$TaskSSZModelImpl _$$TaskSSZModelImplFromJson(Map<String, dynamic> json) =>
    _$TaskSSZModelImpl(
      id: json['id'] as String,
      number: json['number'] as String,
      date: json['date'] as String,
      idBranch: json['idBranch'] as String,
      idArea: json['idArea'] as String,
      mainID: json['mainID'] as String,
      idJob: json['idJob'] as String,
      name: json['name'] as String,
      lastStatus:
          $enumDecodeNullable(_$HistoryStatusTypeEnumMap, json['lastStatus']),
      workers: (json['workers'] as List<dynamic>)
          .map((e) => WorkerModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$TaskSSZModelImplToJson(_$TaskSSZModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'number': instance.number,
      'date': instance.date,
      'idBranch': instance.idBranch,
      'idArea': instance.idArea,
      'mainID': instance.mainID,
      'idJob': instance.idJob,
      'name': instance.name,
      if (_$HistoryStatusTypeEnumMap[instance.lastStatus] case final value?)
        'lastStatus': value,
      'workers': instance.workers.map((e) => e.toJson()).toList(),
    };

const _$HistoryStatusTypeEnumMap = {
  HistoryStatusType.inwork: 'inwork',
  HistoryStatusType.finish: 'finish',
  HistoryStatusType.pause: 'pause',
  HistoryStatusType.info: 'info',
};

_$TaskSSZListModelImpl _$$TaskSSZListModelImplFromJson(
        Map<String, dynamic> json) =>
    _$TaskSSZListModelImpl(
      (json['data'] as List<dynamic>)
          .map((e) => TaskSSZModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$TaskSSZListModelImplToJson(
        _$TaskSSZListModelImpl instance) =>
    <String, dynamic>{
      'data': instance.data,
    };
