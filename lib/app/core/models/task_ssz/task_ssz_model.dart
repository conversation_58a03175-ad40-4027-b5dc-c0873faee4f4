import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import '../worker/worker_model.dart';

import '../enums/history_status_enum.dart';

part 'task_ssz_model.freezed.dart';
part 'task_ssz_model.g.dart';

@freezed
class TaskSSZModel with _$TaskSSZModel {
  const TaskSSZModel._();
  @JsonSerializable(explicitToJson: true, includeIfNull: false)
  const factory TaskSSZModel(
      {required String id,
      required String number,
      required String date,
      required String idBranch,
      required String idArea,
      required String mainID,
      required String idJob,
      required String name,
      HistoryStatusType? lastStatus,
      required List<WorkerModel> workers}) = _TaskSSZModel;

  factory TaskSSZModel.fromJson(Map<String, Object?> json) =>
      _$TaskSSZModelFromJson(json);
}

@freezed
class TaskSSZListModel with _$TaskSSZListModel {
  factory TaskSSZListModel(List<TaskSSZModel> data) = _TaskSSZListModel;

  factory TaskSSZListModel.fromJson(Map<String, dynamic> json) =>
      _$TaskSSZListModelFromJson(json);
}
