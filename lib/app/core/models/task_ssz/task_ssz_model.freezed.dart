// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'task_ssz_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

TaskSSZModel _$TaskSSZModelFromJson(Map<String, dynamic> json) {
  return _TaskSSZModel.fromJson(json);
}

/// @nodoc
mixin _$TaskSSZModel {
  String get id => throw _privateConstructorUsedError;
  String get number => throw _privateConstructorUsedError;
  String get date => throw _privateConstructorUsedError;
  String get idBranch => throw _privateConstructorUsedError;
  String get idArea => throw _privateConstructorUsedError;
  String get mainID => throw _privateConstructorUsedError;
  String get idJob => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  HistoryStatusType<dynamic>? get lastStatus =>
      throw _privateConstructorUsedError;
  List<WorkerModel> get workers => throw _privateConstructorUsedError;

  /// Serializes this TaskSSZModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TaskSSZModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TaskSSZModelCopyWith<TaskSSZModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TaskSSZModelCopyWith<$Res> {
  factory $TaskSSZModelCopyWith(
          TaskSSZModel value, $Res Function(TaskSSZModel) then) =
      _$TaskSSZModelCopyWithImpl<$Res, TaskSSZModel>;
  @useResult
  $Res call(
      {String id,
      String number,
      String date,
      String idBranch,
      String idArea,
      String mainID,
      String idJob,
      String name,
      HistoryStatusType<dynamic>? lastStatus,
      List<WorkerModel> workers});
}

/// @nodoc
class _$TaskSSZModelCopyWithImpl<$Res, $Val extends TaskSSZModel>
    implements $TaskSSZModelCopyWith<$Res> {
  _$TaskSSZModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TaskSSZModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? number = null,
    Object? date = null,
    Object? idBranch = null,
    Object? idArea = null,
    Object? mainID = null,
    Object? idJob = null,
    Object? name = null,
    Object? lastStatus = freezed,
    Object? workers = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      number: null == number
          ? _value.number
          : number // ignore: cast_nullable_to_non_nullable
              as String,
      date: null == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as String,
      idBranch: null == idBranch
          ? _value.idBranch
          : idBranch // ignore: cast_nullable_to_non_nullable
              as String,
      idArea: null == idArea
          ? _value.idArea
          : idArea // ignore: cast_nullable_to_non_nullable
              as String,
      mainID: null == mainID
          ? _value.mainID
          : mainID // ignore: cast_nullable_to_non_nullable
              as String,
      idJob: null == idJob
          ? _value.idJob
          : idJob // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      lastStatus: freezed == lastStatus
          ? _value.lastStatus
          : lastStatus // ignore: cast_nullable_to_non_nullable
              as HistoryStatusType<dynamic>?,
      workers: null == workers
          ? _value.workers
          : workers // ignore: cast_nullable_to_non_nullable
              as List<WorkerModel>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TaskSSZModelImplCopyWith<$Res>
    implements $TaskSSZModelCopyWith<$Res> {
  factory _$$TaskSSZModelImplCopyWith(
          _$TaskSSZModelImpl value, $Res Function(_$TaskSSZModelImpl) then) =
      __$$TaskSSZModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String number,
      String date,
      String idBranch,
      String idArea,
      String mainID,
      String idJob,
      String name,
      HistoryStatusType<dynamic>? lastStatus,
      List<WorkerModel> workers});
}

/// @nodoc
class __$$TaskSSZModelImplCopyWithImpl<$Res>
    extends _$TaskSSZModelCopyWithImpl<$Res, _$TaskSSZModelImpl>
    implements _$$TaskSSZModelImplCopyWith<$Res> {
  __$$TaskSSZModelImplCopyWithImpl(
      _$TaskSSZModelImpl _value, $Res Function(_$TaskSSZModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of TaskSSZModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? number = null,
    Object? date = null,
    Object? idBranch = null,
    Object? idArea = null,
    Object? mainID = null,
    Object? idJob = null,
    Object? name = null,
    Object? lastStatus = freezed,
    Object? workers = null,
  }) {
    return _then(_$TaskSSZModelImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      number: null == number
          ? _value.number
          : number // ignore: cast_nullable_to_non_nullable
              as String,
      date: null == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as String,
      idBranch: null == idBranch
          ? _value.idBranch
          : idBranch // ignore: cast_nullable_to_non_nullable
              as String,
      idArea: null == idArea
          ? _value.idArea
          : idArea // ignore: cast_nullable_to_non_nullable
              as String,
      mainID: null == mainID
          ? _value.mainID
          : mainID // ignore: cast_nullable_to_non_nullable
              as String,
      idJob: null == idJob
          ? _value.idJob
          : idJob // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      lastStatus: freezed == lastStatus
          ? _value.lastStatus
          : lastStatus // ignore: cast_nullable_to_non_nullable
              as HistoryStatusType<dynamic>?,
      workers: null == workers
          ? _value._workers
          : workers // ignore: cast_nullable_to_non_nullable
              as List<WorkerModel>,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class _$TaskSSZModelImpl extends _TaskSSZModel with DiagnosticableTreeMixin {
  const _$TaskSSZModelImpl(
      {required this.id,
      required this.number,
      required this.date,
      required this.idBranch,
      required this.idArea,
      required this.mainID,
      required this.idJob,
      required this.name,
      this.lastStatus,
      required final List<WorkerModel> workers})
      : _workers = workers,
        super._();

  factory _$TaskSSZModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$TaskSSZModelImplFromJson(json);

  @override
  final String id;
  @override
  final String number;
  @override
  final String date;
  @override
  final String idBranch;
  @override
  final String idArea;
  @override
  final String mainID;
  @override
  final String idJob;
  @override
  final String name;
  @override
  final HistoryStatusType<dynamic>? lastStatus;
  final List<WorkerModel> _workers;
  @override
  List<WorkerModel> get workers {
    if (_workers is EqualUnmodifiableListView) return _workers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_workers);
  }

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'TaskSSZModel(id: $id, number: $number, date: $date, idBranch: $idBranch, idArea: $idArea, mainID: $mainID, idJob: $idJob, name: $name, lastStatus: $lastStatus, workers: $workers)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'TaskSSZModel'))
      ..add(DiagnosticsProperty('id', id))
      ..add(DiagnosticsProperty('number', number))
      ..add(DiagnosticsProperty('date', date))
      ..add(DiagnosticsProperty('idBranch', idBranch))
      ..add(DiagnosticsProperty('idArea', idArea))
      ..add(DiagnosticsProperty('mainID', mainID))
      ..add(DiagnosticsProperty('idJob', idJob))
      ..add(DiagnosticsProperty('name', name))
      ..add(DiagnosticsProperty('lastStatus', lastStatus))
      ..add(DiagnosticsProperty('workers', workers));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TaskSSZModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.number, number) || other.number == number) &&
            (identical(other.date, date) || other.date == date) &&
            (identical(other.idBranch, idBranch) ||
                other.idBranch == idBranch) &&
            (identical(other.idArea, idArea) || other.idArea == idArea) &&
            (identical(other.mainID, mainID) || other.mainID == mainID) &&
            (identical(other.idJob, idJob) || other.idJob == idJob) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.lastStatus, lastStatus) ||
                other.lastStatus == lastStatus) &&
            const DeepCollectionEquality().equals(other._workers, _workers));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      number,
      date,
      idBranch,
      idArea,
      mainID,
      idJob,
      name,
      lastStatus,
      const DeepCollectionEquality().hash(_workers));

  /// Create a copy of TaskSSZModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TaskSSZModelImplCopyWith<_$TaskSSZModelImpl> get copyWith =>
      __$$TaskSSZModelImplCopyWithImpl<_$TaskSSZModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TaskSSZModelImplToJson(
      this,
    );
  }
}

abstract class _TaskSSZModel extends TaskSSZModel {
  const factory _TaskSSZModel(
      {required final String id,
      required final String number,
      required final String date,
      required final String idBranch,
      required final String idArea,
      required final String mainID,
      required final String idJob,
      required final String name,
      final HistoryStatusType<dynamic>? lastStatus,
      required final List<WorkerModel> workers}) = _$TaskSSZModelImpl;
  const _TaskSSZModel._() : super._();

  factory _TaskSSZModel.fromJson(Map<String, dynamic> json) =
      _$TaskSSZModelImpl.fromJson;

  @override
  String get id;
  @override
  String get number;
  @override
  String get date;
  @override
  String get idBranch;
  @override
  String get idArea;
  @override
  String get mainID;
  @override
  String get idJob;
  @override
  String get name;
  @override
  HistoryStatusType<dynamic>? get lastStatus;
  @override
  List<WorkerModel> get workers;

  /// Create a copy of TaskSSZModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TaskSSZModelImplCopyWith<_$TaskSSZModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

TaskSSZListModel _$TaskSSZListModelFromJson(Map<String, dynamic> json) {
  return _TaskSSZListModel.fromJson(json);
}

/// @nodoc
mixin _$TaskSSZListModel {
  List<TaskSSZModel> get data => throw _privateConstructorUsedError;

  /// Serializes this TaskSSZListModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TaskSSZListModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TaskSSZListModelCopyWith<TaskSSZListModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TaskSSZListModelCopyWith<$Res> {
  factory $TaskSSZListModelCopyWith(
          TaskSSZListModel value, $Res Function(TaskSSZListModel) then) =
      _$TaskSSZListModelCopyWithImpl<$Res, TaskSSZListModel>;
  @useResult
  $Res call({List<TaskSSZModel> data});
}

/// @nodoc
class _$TaskSSZListModelCopyWithImpl<$Res, $Val extends TaskSSZListModel>
    implements $TaskSSZListModelCopyWith<$Res> {
  _$TaskSSZListModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TaskSSZListModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
  }) {
    return _then(_value.copyWith(
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<TaskSSZModel>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TaskSSZListModelImplCopyWith<$Res>
    implements $TaskSSZListModelCopyWith<$Res> {
  factory _$$TaskSSZListModelImplCopyWith(_$TaskSSZListModelImpl value,
          $Res Function(_$TaskSSZListModelImpl) then) =
      __$$TaskSSZListModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<TaskSSZModel> data});
}

/// @nodoc
class __$$TaskSSZListModelImplCopyWithImpl<$Res>
    extends _$TaskSSZListModelCopyWithImpl<$Res, _$TaskSSZListModelImpl>
    implements _$$TaskSSZListModelImplCopyWith<$Res> {
  __$$TaskSSZListModelImplCopyWithImpl(_$TaskSSZListModelImpl _value,
      $Res Function(_$TaskSSZListModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of TaskSSZListModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
  }) {
    return _then(_$TaskSSZListModelImpl(
      null == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as List<TaskSSZModel>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TaskSSZListModelImpl
    with DiagnosticableTreeMixin
    implements _TaskSSZListModel {
  _$TaskSSZListModelImpl(final List<TaskSSZModel> data) : _data = data;

  factory _$TaskSSZListModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$TaskSSZListModelImplFromJson(json);

  final List<TaskSSZModel> _data;
  @override
  List<TaskSSZModel> get data {
    if (_data is EqualUnmodifiableListView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_data);
  }

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'TaskSSZListModel(data: $data)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'TaskSSZListModel'))
      ..add(DiagnosticsProperty('data', data));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TaskSSZListModelImpl &&
            const DeepCollectionEquality().equals(other._data, _data));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_data));

  /// Create a copy of TaskSSZListModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TaskSSZListModelImplCopyWith<_$TaskSSZListModelImpl> get copyWith =>
      __$$TaskSSZListModelImplCopyWithImpl<_$TaskSSZListModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TaskSSZListModelImplToJson(
      this,
    );
  }
}

abstract class _TaskSSZListModel implements TaskSSZListModel {
  factory _TaskSSZListModel(final List<TaskSSZModel> data) =
      _$TaskSSZListModelImpl;

  factory _TaskSSZListModel.fromJson(Map<String, dynamic> json) =
      _$TaskSSZListModelImpl.fromJson;

  @override
  List<TaskSSZModel> get data;

  /// Create a copy of TaskSSZListModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TaskSSZListModelImplCopyWith<_$TaskSSZListModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
