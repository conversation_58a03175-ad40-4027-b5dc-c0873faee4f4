// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'report_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ReportModelImpl _$$ReportModelImplFromJson(Map<String, dynamic> json) =>
    _$ReportModelImpl(
      area: json['area'] as String,
      repairNumber: json['repairNumber'] as String,
      name: json['name'] as String,
      equipment: json['equipment'] as String,
      component: json['component'] as String,
      type: json['type'] as String,
      job: json['job'] as String,
      time: json['time'] as String,
      comment: json['comment'] as String,
      endDateJob: json['endDateJob'] as String,
      endDateOrder: json['endDateOrder'] as String,
      finishDate: json['finishDate'] as String,
      finished: json['finished'] as String,
      daysLeft: json['daysLeft'] as String,
      outDate: json['outDate'] as String,
    );

Map<String, dynamic> _$$ReportModelImplToJson(_$ReportModelImpl instance) =>
    <String, dynamic>{
      'area': instance.area,
      'repairNumber': instance.repairNumber,
      'name': instance.name,
      'equipment': instance.equipment,
      'component': instance.component,
      'type': instance.type,
      'job': instance.job,
      'time': instance.time,
      'comment': instance.comment,
      'endDateJob': instance.endDateJob,
      'endDateOrder': instance.endDateOrder,
      'finishDate': instance.finishDate,
      'finished': instance.finished,
      'daysLeft': instance.daysLeft,
      'outDate': instance.outDate,
    };

_$ReportListModelImpl _$$ReportListModelImplFromJson(
        Map<String, dynamic> json) =>
    _$ReportListModelImpl(
      (json['data'] as List<dynamic>)
          .map((e) => ReportModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$ReportListModelImplToJson(
        _$ReportListModelImpl instance) =>
    <String, dynamic>{
      'data': instance.data,
    };
