import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter/foundation.dart';

part 'report_model.freezed.dart';
part 'report_model.g.dart';

@freezed
class ReportModel with _$ReportModel {
  const ReportModel._();
  @JsonSerializable(explicitToJson: true, includeIfNull: false)
  const factory ReportModel({
    required String area,
    required String repairNumber,
    required String name,
    required String equipment,
    required String component,
    required String type,
    required String job,
    required String time,
    required String comment,
    required String endDateJob,
    required String endDateOrder,
    required String finishDate,
    required String finished,
    required String daysLeft,
    required String outDate,
  }) = _ReportModel;

  factory ReportModel.fromJson(Map<String, Object?> json) =>
      _$ReportModelFromJson(json);
}

@freezed
abstract class ReportListModel with _$ReportListModel {
  factory ReportListModel(List<ReportModel> data) = _ReportListModel;

  factory ReportListModel.fromJson(Map<String, dynamic> json) =>
      _$ReportListModelFromJson(json);
}
