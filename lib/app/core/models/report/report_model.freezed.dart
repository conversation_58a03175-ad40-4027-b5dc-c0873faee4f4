// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'report_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ReportModel _$ReportModelFromJson(Map<String, dynamic> json) {
  return _ReportModel.fromJson(json);
}

/// @nodoc
mixin _$ReportModel {
  String get area => throw _privateConstructorUsedError;
  String get repairNumber => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get equipment => throw _privateConstructorUsedError;
  String get component => throw _privateConstructorUsedError;
  String get type => throw _privateConstructorUsedError;
  String get job => throw _privateConstructorUsedError;
  String get time => throw _privateConstructorUsedError;
  String get comment => throw _privateConstructorUsedError;
  String get endDateJob => throw _privateConstructorUsedError;
  String get endDateOrder => throw _privateConstructorUsedError;
  String get finishDate => throw _privateConstructorUsedError;
  String get finished => throw _privateConstructorUsedError;
  String get daysLeft => throw _privateConstructorUsedError;
  String get outDate => throw _privateConstructorUsedError;

  /// Serializes this ReportModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ReportModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ReportModelCopyWith<ReportModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ReportModelCopyWith<$Res> {
  factory $ReportModelCopyWith(
          ReportModel value, $Res Function(ReportModel) then) =
      _$ReportModelCopyWithImpl<$Res, ReportModel>;
  @useResult
  $Res call(
      {String area,
      String repairNumber,
      String name,
      String equipment,
      String component,
      String type,
      String job,
      String time,
      String comment,
      String endDateJob,
      String endDateOrder,
      String finishDate,
      String finished,
      String daysLeft,
      String outDate});
}

/// @nodoc
class _$ReportModelCopyWithImpl<$Res, $Val extends ReportModel>
    implements $ReportModelCopyWith<$Res> {
  _$ReportModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ReportModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? area = null,
    Object? repairNumber = null,
    Object? name = null,
    Object? equipment = null,
    Object? component = null,
    Object? type = null,
    Object? job = null,
    Object? time = null,
    Object? comment = null,
    Object? endDateJob = null,
    Object? endDateOrder = null,
    Object? finishDate = null,
    Object? finished = null,
    Object? daysLeft = null,
    Object? outDate = null,
  }) {
    return _then(_value.copyWith(
      area: null == area
          ? _value.area
          : area // ignore: cast_nullable_to_non_nullable
              as String,
      repairNumber: null == repairNumber
          ? _value.repairNumber
          : repairNumber // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      equipment: null == equipment
          ? _value.equipment
          : equipment // ignore: cast_nullable_to_non_nullable
              as String,
      component: null == component
          ? _value.component
          : component // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      job: null == job
          ? _value.job
          : job // ignore: cast_nullable_to_non_nullable
              as String,
      time: null == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as String,
      comment: null == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String,
      endDateJob: null == endDateJob
          ? _value.endDateJob
          : endDateJob // ignore: cast_nullable_to_non_nullable
              as String,
      endDateOrder: null == endDateOrder
          ? _value.endDateOrder
          : endDateOrder // ignore: cast_nullable_to_non_nullable
              as String,
      finishDate: null == finishDate
          ? _value.finishDate
          : finishDate // ignore: cast_nullable_to_non_nullable
              as String,
      finished: null == finished
          ? _value.finished
          : finished // ignore: cast_nullable_to_non_nullable
              as String,
      daysLeft: null == daysLeft
          ? _value.daysLeft
          : daysLeft // ignore: cast_nullable_to_non_nullable
              as String,
      outDate: null == outDate
          ? _value.outDate
          : outDate // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ReportModelImplCopyWith<$Res>
    implements $ReportModelCopyWith<$Res> {
  factory _$$ReportModelImplCopyWith(
          _$ReportModelImpl value, $Res Function(_$ReportModelImpl) then) =
      __$$ReportModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String area,
      String repairNumber,
      String name,
      String equipment,
      String component,
      String type,
      String job,
      String time,
      String comment,
      String endDateJob,
      String endDateOrder,
      String finishDate,
      String finished,
      String daysLeft,
      String outDate});
}

/// @nodoc
class __$$ReportModelImplCopyWithImpl<$Res>
    extends _$ReportModelCopyWithImpl<$Res, _$ReportModelImpl>
    implements _$$ReportModelImplCopyWith<$Res> {
  __$$ReportModelImplCopyWithImpl(
      _$ReportModelImpl _value, $Res Function(_$ReportModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ReportModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? area = null,
    Object? repairNumber = null,
    Object? name = null,
    Object? equipment = null,
    Object? component = null,
    Object? type = null,
    Object? job = null,
    Object? time = null,
    Object? comment = null,
    Object? endDateJob = null,
    Object? endDateOrder = null,
    Object? finishDate = null,
    Object? finished = null,
    Object? daysLeft = null,
    Object? outDate = null,
  }) {
    return _then(_$ReportModelImpl(
      area: null == area
          ? _value.area
          : area // ignore: cast_nullable_to_non_nullable
              as String,
      repairNumber: null == repairNumber
          ? _value.repairNumber
          : repairNumber // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      equipment: null == equipment
          ? _value.equipment
          : equipment // ignore: cast_nullable_to_non_nullable
              as String,
      component: null == component
          ? _value.component
          : component // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      job: null == job
          ? _value.job
          : job // ignore: cast_nullable_to_non_nullable
              as String,
      time: null == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as String,
      comment: null == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String,
      endDateJob: null == endDateJob
          ? _value.endDateJob
          : endDateJob // ignore: cast_nullable_to_non_nullable
              as String,
      endDateOrder: null == endDateOrder
          ? _value.endDateOrder
          : endDateOrder // ignore: cast_nullable_to_non_nullable
              as String,
      finishDate: null == finishDate
          ? _value.finishDate
          : finishDate // ignore: cast_nullable_to_non_nullable
              as String,
      finished: null == finished
          ? _value.finished
          : finished // ignore: cast_nullable_to_non_nullable
              as String,
      daysLeft: null == daysLeft
          ? _value.daysLeft
          : daysLeft // ignore: cast_nullable_to_non_nullable
              as String,
      outDate: null == outDate
          ? _value.outDate
          : outDate // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class _$ReportModelImpl extends _ReportModel with DiagnosticableTreeMixin {
  const _$ReportModelImpl(
      {required this.area,
      required this.repairNumber,
      required this.name,
      required this.equipment,
      required this.component,
      required this.type,
      required this.job,
      required this.time,
      required this.comment,
      required this.endDateJob,
      required this.endDateOrder,
      required this.finishDate,
      required this.finished,
      required this.daysLeft,
      required this.outDate})
      : super._();

  factory _$ReportModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ReportModelImplFromJson(json);

  @override
  final String area;
  @override
  final String repairNumber;
  @override
  final String name;
  @override
  final String equipment;
  @override
  final String component;
  @override
  final String type;
  @override
  final String job;
  @override
  final String time;
  @override
  final String comment;
  @override
  final String endDateJob;
  @override
  final String endDateOrder;
  @override
  final String finishDate;
  @override
  final String finished;
  @override
  final String daysLeft;
  @override
  final String outDate;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ReportModel(area: $area, repairNumber: $repairNumber, name: $name, equipment: $equipment, component: $component, type: $type, job: $job, time: $time, comment: $comment, endDateJob: $endDateJob, endDateOrder: $endDateOrder, finishDate: $finishDate, finished: $finished, daysLeft: $daysLeft, outDate: $outDate)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'ReportModel'))
      ..add(DiagnosticsProperty('area', area))
      ..add(DiagnosticsProperty('repairNumber', repairNumber))
      ..add(DiagnosticsProperty('name', name))
      ..add(DiagnosticsProperty('equipment', equipment))
      ..add(DiagnosticsProperty('component', component))
      ..add(DiagnosticsProperty('type', type))
      ..add(DiagnosticsProperty('job', job))
      ..add(DiagnosticsProperty('time', time))
      ..add(DiagnosticsProperty('comment', comment))
      ..add(DiagnosticsProperty('endDateJob', endDateJob))
      ..add(DiagnosticsProperty('endDateOrder', endDateOrder))
      ..add(DiagnosticsProperty('finishDate', finishDate))
      ..add(DiagnosticsProperty('finished', finished))
      ..add(DiagnosticsProperty('daysLeft', daysLeft))
      ..add(DiagnosticsProperty('outDate', outDate));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ReportModelImpl &&
            (identical(other.area, area) || other.area == area) &&
            (identical(other.repairNumber, repairNumber) ||
                other.repairNumber == repairNumber) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.equipment, equipment) ||
                other.equipment == equipment) &&
            (identical(other.component, component) ||
                other.component == component) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.job, job) || other.job == job) &&
            (identical(other.time, time) || other.time == time) &&
            (identical(other.comment, comment) || other.comment == comment) &&
            (identical(other.endDateJob, endDateJob) ||
                other.endDateJob == endDateJob) &&
            (identical(other.endDateOrder, endDateOrder) ||
                other.endDateOrder == endDateOrder) &&
            (identical(other.finishDate, finishDate) ||
                other.finishDate == finishDate) &&
            (identical(other.finished, finished) ||
                other.finished == finished) &&
            (identical(other.daysLeft, daysLeft) ||
                other.daysLeft == daysLeft) &&
            (identical(other.outDate, outDate) || other.outDate == outDate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      area,
      repairNumber,
      name,
      equipment,
      component,
      type,
      job,
      time,
      comment,
      endDateJob,
      endDateOrder,
      finishDate,
      finished,
      daysLeft,
      outDate);

  /// Create a copy of ReportModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ReportModelImplCopyWith<_$ReportModelImpl> get copyWith =>
      __$$ReportModelImplCopyWithImpl<_$ReportModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ReportModelImplToJson(
      this,
    );
  }
}

abstract class _ReportModel extends ReportModel {
  const factory _ReportModel(
      {required final String area,
      required final String repairNumber,
      required final String name,
      required final String equipment,
      required final String component,
      required final String type,
      required final String job,
      required final String time,
      required final String comment,
      required final String endDateJob,
      required final String endDateOrder,
      required final String finishDate,
      required final String finished,
      required final String daysLeft,
      required final String outDate}) = _$ReportModelImpl;
  const _ReportModel._() : super._();

  factory _ReportModel.fromJson(Map<String, dynamic> json) =
      _$ReportModelImpl.fromJson;

  @override
  String get area;
  @override
  String get repairNumber;
  @override
  String get name;
  @override
  String get equipment;
  @override
  String get component;
  @override
  String get type;
  @override
  String get job;
  @override
  String get time;
  @override
  String get comment;
  @override
  String get endDateJob;
  @override
  String get endDateOrder;
  @override
  String get finishDate;
  @override
  String get finished;
  @override
  String get daysLeft;
  @override
  String get outDate;

  /// Create a copy of ReportModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ReportModelImplCopyWith<_$ReportModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ReportListModel _$ReportListModelFromJson(Map<String, dynamic> json) {
  return _ReportListModel.fromJson(json);
}

/// @nodoc
mixin _$ReportListModel {
  List<ReportModel> get data => throw _privateConstructorUsedError;

  /// Serializes this ReportListModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ReportListModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ReportListModelCopyWith<ReportListModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ReportListModelCopyWith<$Res> {
  factory $ReportListModelCopyWith(
          ReportListModel value, $Res Function(ReportListModel) then) =
      _$ReportListModelCopyWithImpl<$Res, ReportListModel>;
  @useResult
  $Res call({List<ReportModel> data});
}

/// @nodoc
class _$ReportListModelCopyWithImpl<$Res, $Val extends ReportListModel>
    implements $ReportListModelCopyWith<$Res> {
  _$ReportListModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ReportListModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
  }) {
    return _then(_value.copyWith(
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<ReportModel>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ReportListModelImplCopyWith<$Res>
    implements $ReportListModelCopyWith<$Res> {
  factory _$$ReportListModelImplCopyWith(_$ReportListModelImpl value,
          $Res Function(_$ReportListModelImpl) then) =
      __$$ReportListModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<ReportModel> data});
}

/// @nodoc
class __$$ReportListModelImplCopyWithImpl<$Res>
    extends _$ReportListModelCopyWithImpl<$Res, _$ReportListModelImpl>
    implements _$$ReportListModelImplCopyWith<$Res> {
  __$$ReportListModelImplCopyWithImpl(
      _$ReportListModelImpl _value, $Res Function(_$ReportListModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ReportListModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
  }) {
    return _then(_$ReportListModelImpl(
      null == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as List<ReportModel>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ReportListModelImpl
    with DiagnosticableTreeMixin
    implements _ReportListModel {
  _$ReportListModelImpl(final List<ReportModel> data) : _data = data;

  factory _$ReportListModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ReportListModelImplFromJson(json);

  final List<ReportModel> _data;
  @override
  List<ReportModel> get data {
    if (_data is EqualUnmodifiableListView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_data);
  }

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ReportListModel(data: $data)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'ReportListModel'))
      ..add(DiagnosticsProperty('data', data));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ReportListModelImpl &&
            const DeepCollectionEquality().equals(other._data, _data));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_data));

  /// Create a copy of ReportListModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ReportListModelImplCopyWith<_$ReportListModelImpl> get copyWith =>
      __$$ReportListModelImplCopyWithImpl<_$ReportListModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ReportListModelImplToJson(
      this,
    );
  }
}

abstract class _ReportListModel implements ReportListModel {
  factory _ReportListModel(final List<ReportModel> data) =
      _$ReportListModelImpl;

  factory _ReportListModel.fromJson(Map<String, dynamic> json) =
      _$ReportListModelImpl.fromJson;

  @override
  List<ReportModel> get data;

  /// Create a copy of ReportListModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ReportListModelImplCopyWith<_$ReportListModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
