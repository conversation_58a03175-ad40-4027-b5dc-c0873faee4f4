import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter/foundation.dart';

part 'area_stats_model.freezed.dart';
part 'area_stats_model.g.dart';

@freezed
class AreaStatsModel with _$AreaStatsModel {
  const AreaStatsModel._();
  @JsonSerializable(explicitToJson: true, includeIfNull: false)
  const factory AreaStatsModel({
    required int id,
    required int count,
  }) = _AreaStatsModel;

  factory AreaStatsModel.fromJson(Map<String, Object?> json) =>
      _$AreaStatsModelFromJson(json);
}
