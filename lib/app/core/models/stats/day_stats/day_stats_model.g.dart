// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'day_stats_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$DayStatsModelImpl _$$DayStatsModelImplFromJson(Map<String, dynamic> json) =>
    _$DayStatsModelImpl(
      date: json['date'] as String,
      workCount: (json['workCount'] as num?)?.toInt(),
      workHours: (json['workHours'] as num?)?.toDouble(),
      workerHours: (json['workerHours'] as num?)?.toDouble(),
      workers: (json['workers'] as List<dynamic>?)
          ?.map((e) => WorkerStatsModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$DayStatsModelImplToJson(_$DayStatsModelImpl instance) =>
    <String, dynamic>{
      'date': instance.date,
      if (instance.workCount case final value?) 'workCount': value,
      if (instance.workHours case final value?) 'workHours': value,
      if (instance.workerHours case final value?) 'workerHours': value,
      if (instance.workers?.map((e) => e.toJson()).toList() case final value?)
        'workers': value,
    };
