import 'package:freezed_annotation/freezed_annotation.dart';

import '../worker_stats/worker_stats_model.dart';

part 'day_stats_model.freezed.dart';
part 'day_stats_model.g.dart';

@freezed
class DayStatsModel with _$DayStatsModel {
  const DayStatsModel._();
  @JsonSerializable(explicitToJson: true, includeIfNull: false)
  const factory DayStatsModel({
    required String date, // dd.MM.yyyy
    int? workCount,
    double? workHours,
    double? workerHours,
    List<WorkerStatsModel>? workers,
  }) = _DayStatsModel;

  factory DayStatsModel.fromJson(Map<String, Object?> json) =>
      _$DayStatsModelFromJson(json);
}
