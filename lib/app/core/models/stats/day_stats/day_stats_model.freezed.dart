// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'day_stats_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

DayStatsModel _$DayStatsModelFromJson(Map<String, dynamic> json) {
  return _DayStatsModel.fromJson(json);
}

/// @nodoc
mixin _$DayStatsModel {
  String get date => throw _privateConstructorUsedError; // dd.MM.yyyy
  int? get workCount => throw _privateConstructorUsedError;
  double? get workHours => throw _privateConstructorUsedError;
  double? get workerHours => throw _privateConstructorUsedError;
  List<WorkerStatsModel>? get workers => throw _privateConstructorUsedError;

  /// Serializes this DayStatsModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DayStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DayStatsModelCopyWith<DayStatsModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DayStatsModelCopyWith<$Res> {
  factory $DayStatsModelCopyWith(
          DayStatsModel value, $Res Function(DayStatsModel) then) =
      _$DayStatsModelCopyWithImpl<$Res, DayStatsModel>;
  @useResult
  $Res call(
      {String date,
      int? workCount,
      double? workHours,
      double? workerHours,
      List<WorkerStatsModel>? workers});
}

/// @nodoc
class _$DayStatsModelCopyWithImpl<$Res, $Val extends DayStatsModel>
    implements $DayStatsModelCopyWith<$Res> {
  _$DayStatsModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DayStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? date = null,
    Object? workCount = freezed,
    Object? workHours = freezed,
    Object? workerHours = freezed,
    Object? workers = freezed,
  }) {
    return _then(_value.copyWith(
      date: null == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as String,
      workCount: freezed == workCount
          ? _value.workCount
          : workCount // ignore: cast_nullable_to_non_nullable
              as int?,
      workHours: freezed == workHours
          ? _value.workHours
          : workHours // ignore: cast_nullable_to_non_nullable
              as double?,
      workerHours: freezed == workerHours
          ? _value.workerHours
          : workerHours // ignore: cast_nullable_to_non_nullable
              as double?,
      workers: freezed == workers
          ? _value.workers
          : workers // ignore: cast_nullable_to_non_nullable
              as List<WorkerStatsModel>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DayStatsModelImplCopyWith<$Res>
    implements $DayStatsModelCopyWith<$Res> {
  factory _$$DayStatsModelImplCopyWith(
          _$DayStatsModelImpl value, $Res Function(_$DayStatsModelImpl) then) =
      __$$DayStatsModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String date,
      int? workCount,
      double? workHours,
      double? workerHours,
      List<WorkerStatsModel>? workers});
}

/// @nodoc
class __$$DayStatsModelImplCopyWithImpl<$Res>
    extends _$DayStatsModelCopyWithImpl<$Res, _$DayStatsModelImpl>
    implements _$$DayStatsModelImplCopyWith<$Res> {
  __$$DayStatsModelImplCopyWithImpl(
      _$DayStatsModelImpl _value, $Res Function(_$DayStatsModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of DayStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? date = null,
    Object? workCount = freezed,
    Object? workHours = freezed,
    Object? workerHours = freezed,
    Object? workers = freezed,
  }) {
    return _then(_$DayStatsModelImpl(
      date: null == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as String,
      workCount: freezed == workCount
          ? _value.workCount
          : workCount // ignore: cast_nullable_to_non_nullable
              as int?,
      workHours: freezed == workHours
          ? _value.workHours
          : workHours // ignore: cast_nullable_to_non_nullable
              as double?,
      workerHours: freezed == workerHours
          ? _value.workerHours
          : workerHours // ignore: cast_nullable_to_non_nullable
              as double?,
      workers: freezed == workers
          ? _value._workers
          : workers // ignore: cast_nullable_to_non_nullable
              as List<WorkerStatsModel>?,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class _$DayStatsModelImpl extends _DayStatsModel {
  const _$DayStatsModelImpl(
      {required this.date,
      this.workCount,
      this.workHours,
      this.workerHours,
      final List<WorkerStatsModel>? workers})
      : _workers = workers,
        super._();

  factory _$DayStatsModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$DayStatsModelImplFromJson(json);

  @override
  final String date;
// dd.MM.yyyy
  @override
  final int? workCount;
  @override
  final double? workHours;
  @override
  final double? workerHours;
  final List<WorkerStatsModel>? _workers;
  @override
  List<WorkerStatsModel>? get workers {
    final value = _workers;
    if (value == null) return null;
    if (_workers is EqualUnmodifiableListView) return _workers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'DayStatsModel(date: $date, workCount: $workCount, workHours: $workHours, workerHours: $workerHours, workers: $workers)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DayStatsModelImpl &&
            (identical(other.date, date) || other.date == date) &&
            (identical(other.workCount, workCount) ||
                other.workCount == workCount) &&
            (identical(other.workHours, workHours) ||
                other.workHours == workHours) &&
            (identical(other.workerHours, workerHours) ||
                other.workerHours == workerHours) &&
            const DeepCollectionEquality().equals(other._workers, _workers));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, date, workCount, workHours,
      workerHours, const DeepCollectionEquality().hash(_workers));

  /// Create a copy of DayStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DayStatsModelImplCopyWith<_$DayStatsModelImpl> get copyWith =>
      __$$DayStatsModelImplCopyWithImpl<_$DayStatsModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DayStatsModelImplToJson(
      this,
    );
  }
}

abstract class _DayStatsModel extends DayStatsModel {
  const factory _DayStatsModel(
      {required final String date,
      final int? workCount,
      final double? workHours,
      final double? workerHours,
      final List<WorkerStatsModel>? workers}) = _$DayStatsModelImpl;
  const _DayStatsModel._() : super._();

  factory _DayStatsModel.fromJson(Map<String, dynamic> json) =
      _$DayStatsModelImpl.fromJson;

  @override
  String get date; // dd.MM.yyyy
  @override
  int? get workCount;
  @override
  double? get workHours;
  @override
  double? get workerHours;
  @override
  List<WorkerStatsModel>? get workers;

  /// Create a copy of DayStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DayStatsModelImplCopyWith<_$DayStatsModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
