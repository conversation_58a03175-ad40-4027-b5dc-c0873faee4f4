// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'worker_stats_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

WorkerStatsModel _$WorkerStatsModelFromJson(Map<String, dynamic> json) {
  return _WorkerStatsModel.fromJson(json);
}

/// @nodoc
mixin _$WorkerStatsModel {
  String get workerId => throw _privateConstructorUsedError;
  int? get workCount => throw _privateConstructorUsedError;
  double? get workerHours => throw _privateConstructorUsedError;
  List<StatsWorkModel>? get works => throw _privateConstructorUsedError;

  /// Serializes this WorkerStatsModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of WorkerStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WorkerStatsModelCopyWith<WorkerStatsModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WorkerStatsModelCopyWith<$Res> {
  factory $WorkerStatsModelCopyWith(
          WorkerStatsModel value, $Res Function(WorkerStatsModel) then) =
      _$WorkerStatsModelCopyWithImpl<$Res, WorkerStatsModel>;
  @useResult
  $Res call(
      {String workerId,
      int? workCount,
      double? workerHours,
      List<StatsWorkModel>? works});
}

/// @nodoc
class _$WorkerStatsModelCopyWithImpl<$Res, $Val extends WorkerStatsModel>
    implements $WorkerStatsModelCopyWith<$Res> {
  _$WorkerStatsModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WorkerStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? workerId = null,
    Object? workCount = freezed,
    Object? workerHours = freezed,
    Object? works = freezed,
  }) {
    return _then(_value.copyWith(
      workerId: null == workerId
          ? _value.workerId
          : workerId // ignore: cast_nullable_to_non_nullable
              as String,
      workCount: freezed == workCount
          ? _value.workCount
          : workCount // ignore: cast_nullable_to_non_nullable
              as int?,
      workerHours: freezed == workerHours
          ? _value.workerHours
          : workerHours // ignore: cast_nullable_to_non_nullable
              as double?,
      works: freezed == works
          ? _value.works
          : works // ignore: cast_nullable_to_non_nullable
              as List<StatsWorkModel>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WorkerStatsModelImplCopyWith<$Res>
    implements $WorkerStatsModelCopyWith<$Res> {
  factory _$$WorkerStatsModelImplCopyWith(_$WorkerStatsModelImpl value,
          $Res Function(_$WorkerStatsModelImpl) then) =
      __$$WorkerStatsModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String workerId,
      int? workCount,
      double? workerHours,
      List<StatsWorkModel>? works});
}

/// @nodoc
class __$$WorkerStatsModelImplCopyWithImpl<$Res>
    extends _$WorkerStatsModelCopyWithImpl<$Res, _$WorkerStatsModelImpl>
    implements _$$WorkerStatsModelImplCopyWith<$Res> {
  __$$WorkerStatsModelImplCopyWithImpl(_$WorkerStatsModelImpl _value,
      $Res Function(_$WorkerStatsModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of WorkerStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? workerId = null,
    Object? workCount = freezed,
    Object? workerHours = freezed,
    Object? works = freezed,
  }) {
    return _then(_$WorkerStatsModelImpl(
      workerId: null == workerId
          ? _value.workerId
          : workerId // ignore: cast_nullable_to_non_nullable
              as String,
      workCount: freezed == workCount
          ? _value.workCount
          : workCount // ignore: cast_nullable_to_non_nullable
              as int?,
      workerHours: freezed == workerHours
          ? _value.workerHours
          : workerHours // ignore: cast_nullable_to_non_nullable
              as double?,
      works: freezed == works
          ? _value._works
          : works // ignore: cast_nullable_to_non_nullable
              as List<StatsWorkModel>?,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class _$WorkerStatsModelImpl extends _WorkerStatsModel {
  const _$WorkerStatsModelImpl(
      {required this.workerId,
      this.workCount,
      this.workerHours,
      final List<StatsWorkModel>? works})
      : _works = works,
        super._();

  factory _$WorkerStatsModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$WorkerStatsModelImplFromJson(json);

  @override
  final String workerId;
  @override
  final int? workCount;
  @override
  final double? workerHours;
  final List<StatsWorkModel>? _works;
  @override
  List<StatsWorkModel>? get works {
    final value = _works;
    if (value == null) return null;
    if (_works is EqualUnmodifiableListView) return _works;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'WorkerStatsModel(workerId: $workerId, workCount: $workCount, workerHours: $workerHours, works: $works)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WorkerStatsModelImpl &&
            (identical(other.workerId, workerId) ||
                other.workerId == workerId) &&
            (identical(other.workCount, workCount) ||
                other.workCount == workCount) &&
            (identical(other.workerHours, workerHours) ||
                other.workerHours == workerHours) &&
            const DeepCollectionEquality().equals(other._works, _works));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, workerId, workCount, workerHours,
      const DeepCollectionEquality().hash(_works));

  /// Create a copy of WorkerStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WorkerStatsModelImplCopyWith<_$WorkerStatsModelImpl> get copyWith =>
      __$$WorkerStatsModelImplCopyWithImpl<_$WorkerStatsModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WorkerStatsModelImplToJson(
      this,
    );
  }
}

abstract class _WorkerStatsModel extends WorkerStatsModel {
  const factory _WorkerStatsModel(
      {required final String workerId,
      final int? workCount,
      final double? workerHours,
      final List<StatsWorkModel>? works}) = _$WorkerStatsModelImpl;
  const _WorkerStatsModel._() : super._();

  factory _WorkerStatsModel.fromJson(Map<String, dynamic> json) =
      _$WorkerStatsModelImpl.fromJson;

  @override
  String get workerId;
  @override
  int? get workCount;
  @override
  double? get workerHours;
  @override
  List<StatsWorkModel>? get works;

  /// Create a copy of WorkerStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WorkerStatsModelImplCopyWith<_$WorkerStatsModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
