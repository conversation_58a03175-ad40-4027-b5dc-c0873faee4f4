import 'package:freezed_annotation/freezed_annotation.dart';

import '../../Stats_outage/stats_work_model.dart';

part 'worker_stats_model.freezed.dart';
part 'worker_stats_model.g.dart';

@freezed
class WorkerStatsModel with _$WorkerStatsModel {
  const WorkerStatsModel._();
  @JsonSerializable(explicitToJson: true, includeIfNull: false)
  const factory WorkerStatsModel({
    required String workerId,
    int? workCount,
    double? workerHours,
    List<StatsWorkModel>? works,
  }) = _WorkerStatsModel;

  factory WorkerStatsModel.fromJson(Map<String, Object?> json) =>
      _$WorkerStatsModelFromJson(json);
}
