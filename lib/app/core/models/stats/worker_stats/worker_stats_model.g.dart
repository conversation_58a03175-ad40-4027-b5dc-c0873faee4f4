// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'worker_stats_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$WorkerStatsModelImpl _$$WorkerStatsModelImplFromJson(
        Map<String, dynamic> json) =>
    _$WorkerStatsModelImpl(
      workerId: json['workerId'] as String,
      workCount: (json['workCount'] as num?)?.toInt(),
      workerHours: (json['workerHours'] as num?)?.toDouble(),
      works: (json['works'] as List<dynamic>?)
          ?.map((e) => StatsWorkModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$WorkerStatsModelImplToJson(
        _$WorkerStatsModelImpl instance) =>
    <String, dynamic>{
      'workerId': instance.workerId,
      if (instance.workCount case final value?) 'workCount': value,
      if (instance.workerHours case final value?) 'workerHours': value,
      if (instance.works?.map((e) => e.toJson()).toList() case final value?)
        'works': value,
    };
