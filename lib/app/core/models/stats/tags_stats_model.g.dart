// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tags_stats_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$TagsStatsModelImpl _$$TagsStatsModelImplFromJson(Map<String, dynamic> json) =>
    _$TagsStatsModelImpl(
      totalItems: (json['totalItems'] as num?)?.toInt(),
      stats: (json['stats'] as List<dynamic>?)
          ?.map((e) => TagsStatModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$TagsStatsModelImplToJson(
        _$TagsStatsModelImpl instance) =>
    <String, dynamic>{
      if (instance.totalItems case final value?) 'totalItems': value,
      if (instance.stats case final value?) 'stats': value,
    };

_$TagsStatModelImpl _$$TagsStatModelImplFromJson(Map<String, dynamic> json) =>
    _$TagsStatModelImpl(
      tags: (json['tags'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$ItemTagEnumMap, e))
          .toList(),
      count: (json['count'] as num?)?.toInt(),
      percentage: (json['percentage'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$TagsStatModelImplToJson(_$TagsStatModelImpl instance) =>
    <String, dynamic>{
      if (instance.tags?.map((e) => _$ItemTagEnumMap[e]!).toList()
          case final value?)
        'tags': value,
      if (instance.count case final value?) 'count': value,
      if (instance.percentage case final value?) 'percentage': value,
    };

const _$ItemTagEnumMap = {
  ItemTag.itemDefault: 'default',
  ItemTag.urgent: 'urgent',
  ItemTag.important: 'important',
  ItemTag.onHold: 'on_hold',
  ItemTag.warranty: 'warranty',
};

_$GetTagsStatsModelImpl _$$GetTagsStatsModelImplFromJson(
        Map<String, dynamic> json) =>
    _$GetTagsStatsModelImpl(
      inWork: json['inWork'] as bool?,
      branch: (json['branch'] as num?)?.toInt(),
      selectedTag: $enumDecodeNullable(_$ItemTagEnumMap, json['selectedTag']),
    );

Map<String, dynamic> _$$GetTagsStatsModelImplToJson(
        _$GetTagsStatsModelImpl instance) =>
    <String, dynamic>{
      if (instance.inWork case final value?) 'inWork': value,
      if (instance.branch case final value?) 'branch': value,
      if (_$ItemTagEnumMap[instance.selectedTag] case final value?)
        'selectedTag': value,
    };

_$UpdateTagsInItemModelImpl _$$UpdateTagsInItemModelImplFromJson(
        Map<String, dynamic> json) =>
    _$UpdateTagsInItemModelImpl(
      mainID: json['mainID'] as String?,
      tags: (json['tags'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$ItemTagEnumMap, e))
          .toList(),
    );

Map<String, dynamic> _$$UpdateTagsInItemModelImplToJson(
        _$UpdateTagsInItemModelImpl instance) =>
    <String, dynamic>{
      if (instance.mainID case final value?) 'mainID': value,
      if (instance.tags?.map((e) => _$ItemTagEnumMap[e]!).toList()
          case final value?)
        'tags': value,
    };
