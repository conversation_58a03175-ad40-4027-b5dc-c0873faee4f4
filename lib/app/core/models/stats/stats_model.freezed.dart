// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'stats_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

StatsModel _$StatsModelFromJson(Map<String, dynamic> json) {
  return _StatsModel.fromJson(json);
}

/// @nodoc
mixin _$StatsModel {
  int get inWorkCount => throw _privateConstructorUsedError;
  int get archiveCount => throw _privateConstructorUsedError;
  int get archiveInYearCount => throw _privateConstructorUsedError;
  int? get countComponents => throw _privateConstructorUsedError;
  double get allHours => throw _privateConstructorUsedError; // work + outage
  double get allOutAgeHours => throw _privateConstructorUsedError;
  int get recentShipment =>
      throw _privateConstructorUsedError; // количество позиции которые в скором времени нужно будет отгрузить
  int get overDeadlineCount => throw _privateConstructorUsedError;
  double get overDeadlineAvr => throw _privateConstructorUsedError;
  int get overDeadlineSum => throw _privateConstructorUsedError;
  List<BranchStatsModel> get branchStats => throw _privateConstructorUsedError;
  List<AreaStatsModel> get areaStats => throw _privateConstructorUsedError;

  /// Serializes this StatsModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of StatsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $StatsModelCopyWith<StatsModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StatsModelCopyWith<$Res> {
  factory $StatsModelCopyWith(
          StatsModel value, $Res Function(StatsModel) then) =
      _$StatsModelCopyWithImpl<$Res, StatsModel>;
  @useResult
  $Res call(
      {int inWorkCount,
      int archiveCount,
      int archiveInYearCount,
      int? countComponents,
      double allHours,
      double allOutAgeHours,
      int recentShipment,
      int overDeadlineCount,
      double overDeadlineAvr,
      int overDeadlineSum,
      List<BranchStatsModel> branchStats,
      List<AreaStatsModel> areaStats});
}

/// @nodoc
class _$StatsModelCopyWithImpl<$Res, $Val extends StatsModel>
    implements $StatsModelCopyWith<$Res> {
  _$StatsModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of StatsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? inWorkCount = null,
    Object? archiveCount = null,
    Object? archiveInYearCount = null,
    Object? countComponents = freezed,
    Object? allHours = null,
    Object? allOutAgeHours = null,
    Object? recentShipment = null,
    Object? overDeadlineCount = null,
    Object? overDeadlineAvr = null,
    Object? overDeadlineSum = null,
    Object? branchStats = null,
    Object? areaStats = null,
  }) {
    return _then(_value.copyWith(
      inWorkCount: null == inWorkCount
          ? _value.inWorkCount
          : inWorkCount // ignore: cast_nullable_to_non_nullable
              as int,
      archiveCount: null == archiveCount
          ? _value.archiveCount
          : archiveCount // ignore: cast_nullable_to_non_nullable
              as int,
      archiveInYearCount: null == archiveInYearCount
          ? _value.archiveInYearCount
          : archiveInYearCount // ignore: cast_nullable_to_non_nullable
              as int,
      countComponents: freezed == countComponents
          ? _value.countComponents
          : countComponents // ignore: cast_nullable_to_non_nullable
              as int?,
      allHours: null == allHours
          ? _value.allHours
          : allHours // ignore: cast_nullable_to_non_nullable
              as double,
      allOutAgeHours: null == allOutAgeHours
          ? _value.allOutAgeHours
          : allOutAgeHours // ignore: cast_nullable_to_non_nullable
              as double,
      recentShipment: null == recentShipment
          ? _value.recentShipment
          : recentShipment // ignore: cast_nullable_to_non_nullable
              as int,
      overDeadlineCount: null == overDeadlineCount
          ? _value.overDeadlineCount
          : overDeadlineCount // ignore: cast_nullable_to_non_nullable
              as int,
      overDeadlineAvr: null == overDeadlineAvr
          ? _value.overDeadlineAvr
          : overDeadlineAvr // ignore: cast_nullable_to_non_nullable
              as double,
      overDeadlineSum: null == overDeadlineSum
          ? _value.overDeadlineSum
          : overDeadlineSum // ignore: cast_nullable_to_non_nullable
              as int,
      branchStats: null == branchStats
          ? _value.branchStats
          : branchStats // ignore: cast_nullable_to_non_nullable
              as List<BranchStatsModel>,
      areaStats: null == areaStats
          ? _value.areaStats
          : areaStats // ignore: cast_nullable_to_non_nullable
              as List<AreaStatsModel>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$StatsModelImplCopyWith<$Res>
    implements $StatsModelCopyWith<$Res> {
  factory _$$StatsModelImplCopyWith(
          _$StatsModelImpl value, $Res Function(_$StatsModelImpl) then) =
      __$$StatsModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int inWorkCount,
      int archiveCount,
      int archiveInYearCount,
      int? countComponents,
      double allHours,
      double allOutAgeHours,
      int recentShipment,
      int overDeadlineCount,
      double overDeadlineAvr,
      int overDeadlineSum,
      List<BranchStatsModel> branchStats,
      List<AreaStatsModel> areaStats});
}

/// @nodoc
class __$$StatsModelImplCopyWithImpl<$Res>
    extends _$StatsModelCopyWithImpl<$Res, _$StatsModelImpl>
    implements _$$StatsModelImplCopyWith<$Res> {
  __$$StatsModelImplCopyWithImpl(
      _$StatsModelImpl _value, $Res Function(_$StatsModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of StatsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? inWorkCount = null,
    Object? archiveCount = null,
    Object? archiveInYearCount = null,
    Object? countComponents = freezed,
    Object? allHours = null,
    Object? allOutAgeHours = null,
    Object? recentShipment = null,
    Object? overDeadlineCount = null,
    Object? overDeadlineAvr = null,
    Object? overDeadlineSum = null,
    Object? branchStats = null,
    Object? areaStats = null,
  }) {
    return _then(_$StatsModelImpl(
      inWorkCount: null == inWorkCount
          ? _value.inWorkCount
          : inWorkCount // ignore: cast_nullable_to_non_nullable
              as int,
      archiveCount: null == archiveCount
          ? _value.archiveCount
          : archiveCount // ignore: cast_nullable_to_non_nullable
              as int,
      archiveInYearCount: null == archiveInYearCount
          ? _value.archiveInYearCount
          : archiveInYearCount // ignore: cast_nullable_to_non_nullable
              as int,
      countComponents: freezed == countComponents
          ? _value.countComponents
          : countComponents // ignore: cast_nullable_to_non_nullable
              as int?,
      allHours: null == allHours
          ? _value.allHours
          : allHours // ignore: cast_nullable_to_non_nullable
              as double,
      allOutAgeHours: null == allOutAgeHours
          ? _value.allOutAgeHours
          : allOutAgeHours // ignore: cast_nullable_to_non_nullable
              as double,
      recentShipment: null == recentShipment
          ? _value.recentShipment
          : recentShipment // ignore: cast_nullable_to_non_nullable
              as int,
      overDeadlineCount: null == overDeadlineCount
          ? _value.overDeadlineCount
          : overDeadlineCount // ignore: cast_nullable_to_non_nullable
              as int,
      overDeadlineAvr: null == overDeadlineAvr
          ? _value.overDeadlineAvr
          : overDeadlineAvr // ignore: cast_nullable_to_non_nullable
              as double,
      overDeadlineSum: null == overDeadlineSum
          ? _value.overDeadlineSum
          : overDeadlineSum // ignore: cast_nullable_to_non_nullable
              as int,
      branchStats: null == branchStats
          ? _value._branchStats
          : branchStats // ignore: cast_nullable_to_non_nullable
              as List<BranchStatsModel>,
      areaStats: null == areaStats
          ? _value._areaStats
          : areaStats // ignore: cast_nullable_to_non_nullable
              as List<AreaStatsModel>,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class _$StatsModelImpl extends _StatsModel with DiagnosticableTreeMixin {
  const _$StatsModelImpl(
      {required this.inWorkCount,
      required this.archiveCount,
      required this.archiveInYearCount,
      this.countComponents,
      required this.allHours,
      required this.allOutAgeHours,
      required this.recentShipment,
      required this.overDeadlineCount,
      required this.overDeadlineAvr,
      required this.overDeadlineSum,
      required final List<BranchStatsModel> branchStats,
      required final List<AreaStatsModel> areaStats})
      : _branchStats = branchStats,
        _areaStats = areaStats,
        super._();

  factory _$StatsModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$StatsModelImplFromJson(json);

  @override
  final int inWorkCount;
  @override
  final int archiveCount;
  @override
  final int archiveInYearCount;
  @override
  final int? countComponents;
  @override
  final double allHours;
// work + outage
  @override
  final double allOutAgeHours;
  @override
  final int recentShipment;
// количество позиции которые в скором времени нужно будет отгрузить
  @override
  final int overDeadlineCount;
  @override
  final double overDeadlineAvr;
  @override
  final int overDeadlineSum;
  final List<BranchStatsModel> _branchStats;
  @override
  List<BranchStatsModel> get branchStats {
    if (_branchStats is EqualUnmodifiableListView) return _branchStats;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_branchStats);
  }

  final List<AreaStatsModel> _areaStats;
  @override
  List<AreaStatsModel> get areaStats {
    if (_areaStats is EqualUnmodifiableListView) return _areaStats;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_areaStats);
  }

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'StatsModel(inWorkCount: $inWorkCount, archiveCount: $archiveCount, archiveInYearCount: $archiveInYearCount, countComponents: $countComponents, allHours: $allHours, allOutAgeHours: $allOutAgeHours, recentShipment: $recentShipment, overDeadlineCount: $overDeadlineCount, overDeadlineAvr: $overDeadlineAvr, overDeadlineSum: $overDeadlineSum, branchStats: $branchStats, areaStats: $areaStats)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'StatsModel'))
      ..add(DiagnosticsProperty('inWorkCount', inWorkCount))
      ..add(DiagnosticsProperty('archiveCount', archiveCount))
      ..add(DiagnosticsProperty('archiveInYearCount', archiveInYearCount))
      ..add(DiagnosticsProperty('countComponents', countComponents))
      ..add(DiagnosticsProperty('allHours', allHours))
      ..add(DiagnosticsProperty('allOutAgeHours', allOutAgeHours))
      ..add(DiagnosticsProperty('recentShipment', recentShipment))
      ..add(DiagnosticsProperty('overDeadlineCount', overDeadlineCount))
      ..add(DiagnosticsProperty('overDeadlineAvr', overDeadlineAvr))
      ..add(DiagnosticsProperty('overDeadlineSum', overDeadlineSum))
      ..add(DiagnosticsProperty('branchStats', branchStats))
      ..add(DiagnosticsProperty('areaStats', areaStats));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StatsModelImpl &&
            (identical(other.inWorkCount, inWorkCount) ||
                other.inWorkCount == inWorkCount) &&
            (identical(other.archiveCount, archiveCount) ||
                other.archiveCount == archiveCount) &&
            (identical(other.archiveInYearCount, archiveInYearCount) ||
                other.archiveInYearCount == archiveInYearCount) &&
            (identical(other.countComponents, countComponents) ||
                other.countComponents == countComponents) &&
            (identical(other.allHours, allHours) ||
                other.allHours == allHours) &&
            (identical(other.allOutAgeHours, allOutAgeHours) ||
                other.allOutAgeHours == allOutAgeHours) &&
            (identical(other.recentShipment, recentShipment) ||
                other.recentShipment == recentShipment) &&
            (identical(other.overDeadlineCount, overDeadlineCount) ||
                other.overDeadlineCount == overDeadlineCount) &&
            (identical(other.overDeadlineAvr, overDeadlineAvr) ||
                other.overDeadlineAvr == overDeadlineAvr) &&
            (identical(other.overDeadlineSum, overDeadlineSum) ||
                other.overDeadlineSum == overDeadlineSum) &&
            const DeepCollectionEquality()
                .equals(other._branchStats, _branchStats) &&
            const DeepCollectionEquality()
                .equals(other._areaStats, _areaStats));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      inWorkCount,
      archiveCount,
      archiveInYearCount,
      countComponents,
      allHours,
      allOutAgeHours,
      recentShipment,
      overDeadlineCount,
      overDeadlineAvr,
      overDeadlineSum,
      const DeepCollectionEquality().hash(_branchStats),
      const DeepCollectionEquality().hash(_areaStats));

  /// Create a copy of StatsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StatsModelImplCopyWith<_$StatsModelImpl> get copyWith =>
      __$$StatsModelImplCopyWithImpl<_$StatsModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$StatsModelImplToJson(
      this,
    );
  }
}

abstract class _StatsModel extends StatsModel {
  const factory _StatsModel(
      {required final int inWorkCount,
      required final int archiveCount,
      required final int archiveInYearCount,
      final int? countComponents,
      required final double allHours,
      required final double allOutAgeHours,
      required final int recentShipment,
      required final int overDeadlineCount,
      required final double overDeadlineAvr,
      required final int overDeadlineSum,
      required final List<BranchStatsModel> branchStats,
      required final List<AreaStatsModel> areaStats}) = _$StatsModelImpl;
  const _StatsModel._() : super._();

  factory _StatsModel.fromJson(Map<String, dynamic> json) =
      _$StatsModelImpl.fromJson;

  @override
  int get inWorkCount;
  @override
  int get archiveCount;
  @override
  int get archiveInYearCount;
  @override
  int? get countComponents;
  @override
  double get allHours; // work + outage
  @override
  double get allOutAgeHours;
  @override
  int get recentShipment; // количество позиции которые в скором времени нужно будет отгрузить
  @override
  int get overDeadlineCount;
  @override
  double get overDeadlineAvr;
  @override
  int get overDeadlineSum;
  @override
  List<BranchStatsModel> get branchStats;
  @override
  List<AreaStatsModel> get areaStats;

  /// Create a copy of StatsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StatsModelImplCopyWith<_$StatsModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$ScreenStatsModel {
  StatsModel? get stats => throw _privateConstructorUsedError;
  TagsStatsModel? get tagsStats => throw _privateConstructorUsedError;

  /// Create a copy of ScreenStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ScreenStatsModelCopyWith<ScreenStatsModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ScreenStatsModelCopyWith<$Res> {
  factory $ScreenStatsModelCopyWith(
          ScreenStatsModel value, $Res Function(ScreenStatsModel) then) =
      _$ScreenStatsModelCopyWithImpl<$Res, ScreenStatsModel>;
  @useResult
  $Res call({StatsModel? stats, TagsStatsModel? tagsStats});

  $StatsModelCopyWith<$Res>? get stats;
  $TagsStatsModelCopyWith<$Res>? get tagsStats;
}

/// @nodoc
class _$ScreenStatsModelCopyWithImpl<$Res, $Val extends ScreenStatsModel>
    implements $ScreenStatsModelCopyWith<$Res> {
  _$ScreenStatsModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ScreenStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? stats = freezed,
    Object? tagsStats = freezed,
  }) {
    return _then(_value.copyWith(
      stats: freezed == stats
          ? _value.stats
          : stats // ignore: cast_nullable_to_non_nullable
              as StatsModel?,
      tagsStats: freezed == tagsStats
          ? _value.tagsStats
          : tagsStats // ignore: cast_nullable_to_non_nullable
              as TagsStatsModel?,
    ) as $Val);
  }

  /// Create a copy of ScreenStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $StatsModelCopyWith<$Res>? get stats {
    if (_value.stats == null) {
      return null;
    }

    return $StatsModelCopyWith<$Res>(_value.stats!, (value) {
      return _then(_value.copyWith(stats: value) as $Val);
    });
  }

  /// Create a copy of ScreenStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TagsStatsModelCopyWith<$Res>? get tagsStats {
    if (_value.tagsStats == null) {
      return null;
    }

    return $TagsStatsModelCopyWith<$Res>(_value.tagsStats!, (value) {
      return _then(_value.copyWith(tagsStats: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ScreenStatsModelImplCopyWith<$Res>
    implements $ScreenStatsModelCopyWith<$Res> {
  factory _$$ScreenStatsModelImplCopyWith(_$ScreenStatsModelImpl value,
          $Res Function(_$ScreenStatsModelImpl) then) =
      __$$ScreenStatsModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({StatsModel? stats, TagsStatsModel? tagsStats});

  @override
  $StatsModelCopyWith<$Res>? get stats;
  @override
  $TagsStatsModelCopyWith<$Res>? get tagsStats;
}

/// @nodoc
class __$$ScreenStatsModelImplCopyWithImpl<$Res>
    extends _$ScreenStatsModelCopyWithImpl<$Res, _$ScreenStatsModelImpl>
    implements _$$ScreenStatsModelImplCopyWith<$Res> {
  __$$ScreenStatsModelImplCopyWithImpl(_$ScreenStatsModelImpl _value,
      $Res Function(_$ScreenStatsModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ScreenStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? stats = freezed,
    Object? tagsStats = freezed,
  }) {
    return _then(_$ScreenStatsModelImpl(
      stats: freezed == stats
          ? _value.stats
          : stats // ignore: cast_nullable_to_non_nullable
              as StatsModel?,
      tagsStats: freezed == tagsStats
          ? _value.tagsStats
          : tagsStats // ignore: cast_nullable_to_non_nullable
              as TagsStatsModel?,
    ));
  }
}

/// @nodoc

class _$ScreenStatsModelImpl
    with DiagnosticableTreeMixin
    implements _ScreenStatsModel {
  const _$ScreenStatsModelImpl({this.stats, this.tagsStats});

  @override
  final StatsModel? stats;
  @override
  final TagsStatsModel? tagsStats;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ScreenStatsModel(stats: $stats, tagsStats: $tagsStats)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'ScreenStatsModel'))
      ..add(DiagnosticsProperty('stats', stats))
      ..add(DiagnosticsProperty('tagsStats', tagsStats));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ScreenStatsModelImpl &&
            (identical(other.stats, stats) || other.stats == stats) &&
            (identical(other.tagsStats, tagsStats) ||
                other.tagsStats == tagsStats));
  }

  @override
  int get hashCode => Object.hash(runtimeType, stats, tagsStats);

  /// Create a copy of ScreenStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ScreenStatsModelImplCopyWith<_$ScreenStatsModelImpl> get copyWith =>
      __$$ScreenStatsModelImplCopyWithImpl<_$ScreenStatsModelImpl>(
          this, _$identity);
}

abstract class _ScreenStatsModel implements ScreenStatsModel {
  const factory _ScreenStatsModel(
      {final StatsModel? stats,
      final TagsStatsModel? tagsStats}) = _$ScreenStatsModelImpl;

  @override
  StatsModel? get stats;
  @override
  TagsStatsModel? get tagsStats;

  /// Create a copy of ScreenStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ScreenStatsModelImplCopyWith<_$ScreenStatsModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$TagsScreenStatsModel {
  bool? get inWork => throw _privateConstructorUsedError;
  int? get branchId => throw _privateConstructorUsedError;
  TagsStatsModel? get firstInitial => throw _privateConstructorUsedError;
  ItemTag get selectedItemTag => throw _privateConstructorUsedError;
  TagsStatsModel? get secondWithFilters => throw _privateConstructorUsedError;

  /// Create a copy of TagsScreenStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TagsScreenStatsModelCopyWith<TagsScreenStatsModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TagsScreenStatsModelCopyWith<$Res> {
  factory $TagsScreenStatsModelCopyWith(TagsScreenStatsModel value,
          $Res Function(TagsScreenStatsModel) then) =
      _$TagsScreenStatsModelCopyWithImpl<$Res, TagsScreenStatsModel>;
  @useResult
  $Res call(
      {bool? inWork,
      int? branchId,
      TagsStatsModel? firstInitial,
      ItemTag selectedItemTag,
      TagsStatsModel? secondWithFilters});

  $TagsStatsModelCopyWith<$Res>? get firstInitial;
  $TagsStatsModelCopyWith<$Res>? get secondWithFilters;
}

/// @nodoc
class _$TagsScreenStatsModelCopyWithImpl<$Res,
        $Val extends TagsScreenStatsModel>
    implements $TagsScreenStatsModelCopyWith<$Res> {
  _$TagsScreenStatsModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TagsScreenStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? inWork = freezed,
    Object? branchId = freezed,
    Object? firstInitial = freezed,
    Object? selectedItemTag = null,
    Object? secondWithFilters = freezed,
  }) {
    return _then(_value.copyWith(
      inWork: freezed == inWork
          ? _value.inWork
          : inWork // ignore: cast_nullable_to_non_nullable
              as bool?,
      branchId: freezed == branchId
          ? _value.branchId
          : branchId // ignore: cast_nullable_to_non_nullable
              as int?,
      firstInitial: freezed == firstInitial
          ? _value.firstInitial
          : firstInitial // ignore: cast_nullable_to_non_nullable
              as TagsStatsModel?,
      selectedItemTag: null == selectedItemTag
          ? _value.selectedItemTag
          : selectedItemTag // ignore: cast_nullable_to_non_nullable
              as ItemTag,
      secondWithFilters: freezed == secondWithFilters
          ? _value.secondWithFilters
          : secondWithFilters // ignore: cast_nullable_to_non_nullable
              as TagsStatsModel?,
    ) as $Val);
  }

  /// Create a copy of TagsScreenStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TagsStatsModelCopyWith<$Res>? get firstInitial {
    if (_value.firstInitial == null) {
      return null;
    }

    return $TagsStatsModelCopyWith<$Res>(_value.firstInitial!, (value) {
      return _then(_value.copyWith(firstInitial: value) as $Val);
    });
  }

  /// Create a copy of TagsScreenStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TagsStatsModelCopyWith<$Res>? get secondWithFilters {
    if (_value.secondWithFilters == null) {
      return null;
    }

    return $TagsStatsModelCopyWith<$Res>(_value.secondWithFilters!, (value) {
      return _then(_value.copyWith(secondWithFilters: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$TagsScreenStatsModelImplCopyWith<$Res>
    implements $TagsScreenStatsModelCopyWith<$Res> {
  factory _$$TagsScreenStatsModelImplCopyWith(_$TagsScreenStatsModelImpl value,
          $Res Function(_$TagsScreenStatsModelImpl) then) =
      __$$TagsScreenStatsModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool? inWork,
      int? branchId,
      TagsStatsModel? firstInitial,
      ItemTag selectedItemTag,
      TagsStatsModel? secondWithFilters});

  @override
  $TagsStatsModelCopyWith<$Res>? get firstInitial;
  @override
  $TagsStatsModelCopyWith<$Res>? get secondWithFilters;
}

/// @nodoc
class __$$TagsScreenStatsModelImplCopyWithImpl<$Res>
    extends _$TagsScreenStatsModelCopyWithImpl<$Res, _$TagsScreenStatsModelImpl>
    implements _$$TagsScreenStatsModelImplCopyWith<$Res> {
  __$$TagsScreenStatsModelImplCopyWithImpl(_$TagsScreenStatsModelImpl _value,
      $Res Function(_$TagsScreenStatsModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of TagsScreenStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? inWork = freezed,
    Object? branchId = freezed,
    Object? firstInitial = freezed,
    Object? selectedItemTag = null,
    Object? secondWithFilters = freezed,
  }) {
    return _then(_$TagsScreenStatsModelImpl(
      inWork: freezed == inWork
          ? _value.inWork
          : inWork // ignore: cast_nullable_to_non_nullable
              as bool?,
      branchId: freezed == branchId
          ? _value.branchId
          : branchId // ignore: cast_nullable_to_non_nullable
              as int?,
      firstInitial: freezed == firstInitial
          ? _value.firstInitial
          : firstInitial // ignore: cast_nullable_to_non_nullable
              as TagsStatsModel?,
      selectedItemTag: null == selectedItemTag
          ? _value.selectedItemTag
          : selectedItemTag // ignore: cast_nullable_to_non_nullable
              as ItemTag,
      secondWithFilters: freezed == secondWithFilters
          ? _value.secondWithFilters
          : secondWithFilters // ignore: cast_nullable_to_non_nullable
              as TagsStatsModel?,
    ));
  }
}

/// @nodoc

class _$TagsScreenStatsModelImpl
    with DiagnosticableTreeMixin
    implements _TagsScreenStatsModel {
  const _$TagsScreenStatsModelImpl(
      {this.inWork,
      this.branchId,
      this.firstInitial,
      this.selectedItemTag = ItemTag.itemDefault,
      this.secondWithFilters});

  @override
  final bool? inWork;
  @override
  final int? branchId;
  @override
  final TagsStatsModel? firstInitial;
  @override
  @JsonKey()
  final ItemTag selectedItemTag;
  @override
  final TagsStatsModel? secondWithFilters;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'TagsScreenStatsModel(inWork: $inWork, branchId: $branchId, firstInitial: $firstInitial, selectedItemTag: $selectedItemTag, secondWithFilters: $secondWithFilters)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'TagsScreenStatsModel'))
      ..add(DiagnosticsProperty('inWork', inWork))
      ..add(DiagnosticsProperty('branchId', branchId))
      ..add(DiagnosticsProperty('firstInitial', firstInitial))
      ..add(DiagnosticsProperty('selectedItemTag', selectedItemTag))
      ..add(DiagnosticsProperty('secondWithFilters', secondWithFilters));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TagsScreenStatsModelImpl &&
            (identical(other.inWork, inWork) || other.inWork == inWork) &&
            (identical(other.branchId, branchId) ||
                other.branchId == branchId) &&
            (identical(other.firstInitial, firstInitial) ||
                other.firstInitial == firstInitial) &&
            (identical(other.selectedItemTag, selectedItemTag) ||
                other.selectedItemTag == selectedItemTag) &&
            (identical(other.secondWithFilters, secondWithFilters) ||
                other.secondWithFilters == secondWithFilters));
  }

  @override
  int get hashCode => Object.hash(runtimeType, inWork, branchId, firstInitial,
      selectedItemTag, secondWithFilters);

  /// Create a copy of TagsScreenStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TagsScreenStatsModelImplCopyWith<_$TagsScreenStatsModelImpl>
      get copyWith =>
          __$$TagsScreenStatsModelImplCopyWithImpl<_$TagsScreenStatsModelImpl>(
              this, _$identity);
}

abstract class _TagsScreenStatsModel implements TagsScreenStatsModel {
  const factory _TagsScreenStatsModel(
      {final bool? inWork,
      final int? branchId,
      final TagsStatsModel? firstInitial,
      final ItemTag selectedItemTag,
      final TagsStatsModel? secondWithFilters}) = _$TagsScreenStatsModelImpl;

  @override
  bool? get inWork;
  @override
  int? get branchId;
  @override
  TagsStatsModel? get firstInitial;
  @override
  ItemTag get selectedItemTag;
  @override
  TagsStatsModel? get secondWithFilters;

  /// Create a copy of TagsScreenStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TagsScreenStatsModelImplCopyWith<_$TagsScreenStatsModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}
