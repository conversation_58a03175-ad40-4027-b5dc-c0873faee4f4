// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'branch_stats_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

BranchStatsModel _$BranchStatsModelFromJson(Map<String, dynamic> json) {
  return _BranchStatsModel.fromJson(json);
}

/// @nodoc
mixin _$BranchStatsModel {
  int get id => throw _privateConstructorUsedError;
  int get count => throw _privateConstructorUsedError;

  /// Serializes this BranchStatsModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of BranchStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BranchStatsModelCopyWith<BranchStatsModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BranchStatsModelCopyWith<$Res> {
  factory $BranchStatsModelCopyWith(
          BranchStatsModel value, $Res Function(BranchStatsModel) then) =
      _$BranchStatsModelCopyWithImpl<$Res, BranchStatsModel>;
  @useResult
  $Res call({int id, int count});
}

/// @nodoc
class _$BranchStatsModelCopyWithImpl<$Res, $Val extends BranchStatsModel>
    implements $BranchStatsModelCopyWith<$Res> {
  _$BranchStatsModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BranchStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? count = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      count: null == count
          ? _value.count
          : count // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BranchStatsModelImplCopyWith<$Res>
    implements $BranchStatsModelCopyWith<$Res> {
  factory _$$BranchStatsModelImplCopyWith(_$BranchStatsModelImpl value,
          $Res Function(_$BranchStatsModelImpl) then) =
      __$$BranchStatsModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int id, int count});
}

/// @nodoc
class __$$BranchStatsModelImplCopyWithImpl<$Res>
    extends _$BranchStatsModelCopyWithImpl<$Res, _$BranchStatsModelImpl>
    implements _$$BranchStatsModelImplCopyWith<$Res> {
  __$$BranchStatsModelImplCopyWithImpl(_$BranchStatsModelImpl _value,
      $Res Function(_$BranchStatsModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of BranchStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? count = null,
  }) {
    return _then(_$BranchStatsModelImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      count: null == count
          ? _value.count
          : count // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class _$BranchStatsModelImpl extends _BranchStatsModel
    with DiagnosticableTreeMixin {
  const _$BranchStatsModelImpl({required this.id, required this.count})
      : super._();

  factory _$BranchStatsModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$BranchStatsModelImplFromJson(json);

  @override
  final int id;
  @override
  final int count;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'BranchStatsModel(id: $id, count: $count)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'BranchStatsModel'))
      ..add(DiagnosticsProperty('id', id))
      ..add(DiagnosticsProperty('count', count));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BranchStatsModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.count, count) || other.count == count));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, count);

  /// Create a copy of BranchStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BranchStatsModelImplCopyWith<_$BranchStatsModelImpl> get copyWith =>
      __$$BranchStatsModelImplCopyWithImpl<_$BranchStatsModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BranchStatsModelImplToJson(
      this,
    );
  }
}

abstract class _BranchStatsModel extends BranchStatsModel {
  const factory _BranchStatsModel(
      {required final int id,
      required final int count}) = _$BranchStatsModelImpl;
  const _BranchStatsModel._() : super._();

  factory _BranchStatsModel.fromJson(Map<String, dynamic> json) =
      _$BranchStatsModelImpl.fromJson;

  @override
  int get id;
  @override
  int get count;

  /// Create a copy of BranchStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BranchStatsModelImplCopyWith<_$BranchStatsModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
