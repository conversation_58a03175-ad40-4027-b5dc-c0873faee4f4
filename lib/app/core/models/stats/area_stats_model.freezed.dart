// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'area_stats_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AreaStatsModel _$AreaStatsModelFromJson(Map<String, dynamic> json) {
  return _AreaStatsModel.fromJson(json);
}

/// @nodoc
mixin _$AreaStatsModel {
  int get id => throw _privateConstructorUsedError;
  int get count => throw _privateConstructorUsedError;

  /// Serializes this AreaStatsModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AreaStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AreaStatsModelCopyWith<AreaStatsModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AreaStatsModelCopyWith<$Res> {
  factory $AreaStatsModelCopyWith(
          AreaStatsModel value, $Res Function(AreaStatsModel) then) =
      _$AreaStatsModelCopyWithImpl<$Res, AreaStatsModel>;
  @useResult
  $Res call({int id, int count});
}

/// @nodoc
class _$AreaStatsModelCopyWithImpl<$Res, $Val extends AreaStatsModel>
    implements $AreaStatsModelCopyWith<$Res> {
  _$AreaStatsModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AreaStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? count = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      count: null == count
          ? _value.count
          : count // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AreaStatsModelImplCopyWith<$Res>
    implements $AreaStatsModelCopyWith<$Res> {
  factory _$$AreaStatsModelImplCopyWith(_$AreaStatsModelImpl value,
          $Res Function(_$AreaStatsModelImpl) then) =
      __$$AreaStatsModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int id, int count});
}

/// @nodoc
class __$$AreaStatsModelImplCopyWithImpl<$Res>
    extends _$AreaStatsModelCopyWithImpl<$Res, _$AreaStatsModelImpl>
    implements _$$AreaStatsModelImplCopyWith<$Res> {
  __$$AreaStatsModelImplCopyWithImpl(
      _$AreaStatsModelImpl _value, $Res Function(_$AreaStatsModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of AreaStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? count = null,
  }) {
    return _then(_$AreaStatsModelImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      count: null == count
          ? _value.count
          : count // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class _$AreaStatsModelImpl extends _AreaStatsModel
    with DiagnosticableTreeMixin {
  const _$AreaStatsModelImpl({required this.id, required this.count})
      : super._();

  factory _$AreaStatsModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$AreaStatsModelImplFromJson(json);

  @override
  final int id;
  @override
  final int count;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AreaStatsModel(id: $id, count: $count)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'AreaStatsModel'))
      ..add(DiagnosticsProperty('id', id))
      ..add(DiagnosticsProperty('count', count));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AreaStatsModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.count, count) || other.count == count));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, count);

  /// Create a copy of AreaStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AreaStatsModelImplCopyWith<_$AreaStatsModelImpl> get copyWith =>
      __$$AreaStatsModelImplCopyWithImpl<_$AreaStatsModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AreaStatsModelImplToJson(
      this,
    );
  }
}

abstract class _AreaStatsModel extends AreaStatsModel {
  const factory _AreaStatsModel(
      {required final int id, required final int count}) = _$AreaStatsModelImpl;
  const _AreaStatsModel._() : super._();

  factory _AreaStatsModel.fromJson(Map<String, dynamic> json) =
      _$AreaStatsModelImpl.fromJson;

  @override
  int get id;
  @override
  int get count;

  /// Create a copy of AreaStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AreaStatsModelImplCopyWith<_$AreaStatsModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
