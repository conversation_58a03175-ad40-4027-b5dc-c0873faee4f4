import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:uer_flutter/app/core/models/stats/tags_stats_model.dart';

import 'area_stats_model.dart';
import 'branch_stats_model.dart';

part 'stats_model.freezed.dart';
part 'stats_model.g.dart';

@freezed
class StatsModel with _$StatsModel {
  const StatsModel._();
  @JsonSerializable(explicitToJson: true, includeIfNull: false)
  const factory StatsModel({
    required int inWorkCount,
    required int archiveCount,
    required int archiveInYearCount,
    int? countComponents,
    required double allHours, // work + outage
    required double allOutAgeHours,
    required int
        recentShipment, // количество позиции которые в скором времени нужно будет отгрузить
    required int overDeadlineCount,
    required double overDeadlineAvr,
    required int overDeadlineSum,
    required List<BranchStatsModel> branchStats,
    required List<AreaStatsModel> areaStats,
  }) = _StatsModel;

  Map<String, String> getStatsOverDeadLine() {
    return {
      "Просроченные": "$overDeadlineCount",
      "Просрочка средняя": "${(overDeadlineAvr).toStringAsFixed(0)} дней"
    };
  }

  Map<String, String> getStatsRecentDeadLine() {
    return {
      "4 недели до отгрузки": "$recentShipment",
    };
  }

  Map<String, String> getStatsArchive() {
    return {
      "Архив": "$archiveCount",
    };
  }

  Map<String, String> getStatsArchiveYear() {
    return {
      "Архив за год": "$archiveInYearCount",
    };
  }

  Map<String, String> getStatsInWork() {
    return {
      "Всего в работе": "$inWorkCount",
    };
  }

  Map<String, String> getStatsAllHoursAndOutAge() {
    return {
      "Затрачено дней": (allHours / 8).toStringAsFixed(0),
      "из них простоев": (allOutAgeHours / 8).toStringAsFixed(0),
    };
  }

  factory StatsModel.fromJson(Map<String, Object?> json) =>
      _$StatsModelFromJson(json);
}

@freezed
class ScreenStatsModel with _$ScreenStatsModel {
  const factory ScreenStatsModel({
    StatsModel? stats,
    TagsStatsModel? tagsStats,
  }) = _ScreenStatsModel;
}

@freezed
class TagsScreenStatsModel with _$TagsScreenStatsModel {
  const factory TagsScreenStatsModel({
    bool? inWork,
    int? branchId,
    TagsStatsModel? firstInitial,
    @Default(ItemTag.itemDefault) ItemTag selectedItemTag,
    TagsStatsModel? secondWithFilters,
  }) = _TagsScreenStatsModel;
}
