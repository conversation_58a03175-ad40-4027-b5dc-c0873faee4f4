// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'tags_stats_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

TagsStatsModel _$TagsStatsModelFromJson(Map<String, dynamic> json) {
  return _TagsStatsModel.fromJson(json);
}

/// @nodoc
mixin _$TagsStatsModel {
  int? get totalItems => throw _privateConstructorUsedError;
  List<TagsStatModel>? get stats => throw _privateConstructorUsedError;

  /// Serializes this TagsStatsModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TagsStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TagsStatsModelCopyWith<TagsStatsModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TagsStatsModelCopyWith<$Res> {
  factory $TagsStatsModelCopyWith(
          TagsStatsModel value, $Res Function(TagsStatsModel) then) =
      _$TagsStatsModelCopyWithImpl<$Res, TagsStatsModel>;
  @useResult
  $Res call({int? totalItems, List<TagsStatModel>? stats});
}

/// @nodoc
class _$TagsStatsModelCopyWithImpl<$Res, $Val extends TagsStatsModel>
    implements $TagsStatsModelCopyWith<$Res> {
  _$TagsStatsModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TagsStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalItems = freezed,
    Object? stats = freezed,
  }) {
    return _then(_value.copyWith(
      totalItems: freezed == totalItems
          ? _value.totalItems
          : totalItems // ignore: cast_nullable_to_non_nullable
              as int?,
      stats: freezed == stats
          ? _value.stats
          : stats // ignore: cast_nullable_to_non_nullable
              as List<TagsStatModel>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TagsStatsModelImplCopyWith<$Res>
    implements $TagsStatsModelCopyWith<$Res> {
  factory _$$TagsStatsModelImplCopyWith(_$TagsStatsModelImpl value,
          $Res Function(_$TagsStatsModelImpl) then) =
      __$$TagsStatsModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? totalItems, List<TagsStatModel>? stats});
}

/// @nodoc
class __$$TagsStatsModelImplCopyWithImpl<$Res>
    extends _$TagsStatsModelCopyWithImpl<$Res, _$TagsStatsModelImpl>
    implements _$$TagsStatsModelImplCopyWith<$Res> {
  __$$TagsStatsModelImplCopyWithImpl(
      _$TagsStatsModelImpl _value, $Res Function(_$TagsStatsModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of TagsStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalItems = freezed,
    Object? stats = freezed,
  }) {
    return _then(_$TagsStatsModelImpl(
      totalItems: freezed == totalItems
          ? _value.totalItems
          : totalItems // ignore: cast_nullable_to_non_nullable
              as int?,
      stats: freezed == stats
          ? _value._stats
          : stats // ignore: cast_nullable_to_non_nullable
              as List<TagsStatModel>?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$TagsStatsModelImpl implements _TagsStatsModel {
  const _$TagsStatsModelImpl(
      {this.totalItems, final List<TagsStatModel>? stats})
      : _stats = stats;

  factory _$TagsStatsModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$TagsStatsModelImplFromJson(json);

  @override
  final int? totalItems;
  final List<TagsStatModel>? _stats;
  @override
  List<TagsStatModel>? get stats {
    final value = _stats;
    if (value == null) return null;
    if (_stats is EqualUnmodifiableListView) return _stats;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'TagsStatsModel(totalItems: $totalItems, stats: $stats)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TagsStatsModelImpl &&
            (identical(other.totalItems, totalItems) ||
                other.totalItems == totalItems) &&
            const DeepCollectionEquality().equals(other._stats, _stats));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, totalItems, const DeepCollectionEquality().hash(_stats));

  /// Create a copy of TagsStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TagsStatsModelImplCopyWith<_$TagsStatsModelImpl> get copyWith =>
      __$$TagsStatsModelImplCopyWithImpl<_$TagsStatsModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TagsStatsModelImplToJson(
      this,
    );
  }
}

abstract class _TagsStatsModel implements TagsStatsModel {
  const factory _TagsStatsModel(
      {final int? totalItems,
      final List<TagsStatModel>? stats}) = _$TagsStatsModelImpl;

  factory _TagsStatsModel.fromJson(Map<String, dynamic> json) =
      _$TagsStatsModelImpl.fromJson;

  @override
  int? get totalItems;
  @override
  List<TagsStatModel>? get stats;

  /// Create a copy of TagsStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TagsStatsModelImplCopyWith<_$TagsStatsModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

TagsStatModel _$TagsStatModelFromJson(Map<String, dynamic> json) {
  return _TagsStatModel.fromJson(json);
}

/// @nodoc
mixin _$TagsStatModel {
  List<ItemTag>? get tags => throw _privateConstructorUsedError;
  int? get count => throw _privateConstructorUsedError;
  double? get percentage => throw _privateConstructorUsedError;

  /// Serializes this TagsStatModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TagsStatModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TagsStatModelCopyWith<TagsStatModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TagsStatModelCopyWith<$Res> {
  factory $TagsStatModelCopyWith(
          TagsStatModel value, $Res Function(TagsStatModel) then) =
      _$TagsStatModelCopyWithImpl<$Res, TagsStatModel>;
  @useResult
  $Res call({List<ItemTag>? tags, int? count, double? percentage});
}

/// @nodoc
class _$TagsStatModelCopyWithImpl<$Res, $Val extends TagsStatModel>
    implements $TagsStatModelCopyWith<$Res> {
  _$TagsStatModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TagsStatModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tags = freezed,
    Object? count = freezed,
    Object? percentage = freezed,
  }) {
    return _then(_value.copyWith(
      tags: freezed == tags
          ? _value.tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<ItemTag>?,
      count: freezed == count
          ? _value.count
          : count // ignore: cast_nullable_to_non_nullable
              as int?,
      percentage: freezed == percentage
          ? _value.percentage
          : percentage // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TagsStatModelImplCopyWith<$Res>
    implements $TagsStatModelCopyWith<$Res> {
  factory _$$TagsStatModelImplCopyWith(
          _$TagsStatModelImpl value, $Res Function(_$TagsStatModelImpl) then) =
      __$$TagsStatModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<ItemTag>? tags, int? count, double? percentage});
}

/// @nodoc
class __$$TagsStatModelImplCopyWithImpl<$Res>
    extends _$TagsStatModelCopyWithImpl<$Res, _$TagsStatModelImpl>
    implements _$$TagsStatModelImplCopyWith<$Res> {
  __$$TagsStatModelImplCopyWithImpl(
      _$TagsStatModelImpl _value, $Res Function(_$TagsStatModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of TagsStatModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tags = freezed,
    Object? count = freezed,
    Object? percentage = freezed,
  }) {
    return _then(_$TagsStatModelImpl(
      tags: freezed == tags
          ? _value._tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<ItemTag>?,
      count: freezed == count
          ? _value.count
          : count // ignore: cast_nullable_to_non_nullable
              as int?,
      percentage: freezed == percentage
          ? _value.percentage
          : percentage // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$TagsStatModelImpl implements _TagsStatModel {
  const _$TagsStatModelImpl(
      {final List<ItemTag>? tags, this.count, this.percentage})
      : _tags = tags;

  factory _$TagsStatModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$TagsStatModelImplFromJson(json);

  final List<ItemTag>? _tags;
  @override
  List<ItemTag>? get tags {
    final value = _tags;
    if (value == null) return null;
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? count;
  @override
  final double? percentage;

  @override
  String toString() {
    return 'TagsStatModel(tags: $tags, count: $count, percentage: $percentage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TagsStatModelImpl &&
            const DeepCollectionEquality().equals(other._tags, _tags) &&
            (identical(other.count, count) || other.count == count) &&
            (identical(other.percentage, percentage) ||
                other.percentage == percentage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_tags), count, percentage);

  /// Create a copy of TagsStatModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TagsStatModelImplCopyWith<_$TagsStatModelImpl> get copyWith =>
      __$$TagsStatModelImplCopyWithImpl<_$TagsStatModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TagsStatModelImplToJson(
      this,
    );
  }
}

abstract class _TagsStatModel implements TagsStatModel {
  const factory _TagsStatModel(
      {final List<ItemTag>? tags,
      final int? count,
      final double? percentage}) = _$TagsStatModelImpl;

  factory _TagsStatModel.fromJson(Map<String, dynamic> json) =
      _$TagsStatModelImpl.fromJson;

  @override
  List<ItemTag>? get tags;
  @override
  int? get count;
  @override
  double? get percentage;

  /// Create a copy of TagsStatModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TagsStatModelImplCopyWith<_$TagsStatModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

GetTagsStatsModel _$GetTagsStatsModelFromJson(Map<String, dynamic> json) {
  return _GetTagsStatsModel.fromJson(json);
}

/// @nodoc
mixin _$GetTagsStatsModel {
  bool? get inWork => throw _privateConstructorUsedError;
  int? get branch => throw _privateConstructorUsedError;
  ItemTag? get selectedTag => throw _privateConstructorUsedError;

  /// Serializes this GetTagsStatsModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of GetTagsStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $GetTagsStatsModelCopyWith<GetTagsStatsModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GetTagsStatsModelCopyWith<$Res> {
  factory $GetTagsStatsModelCopyWith(
          GetTagsStatsModel value, $Res Function(GetTagsStatsModel) then) =
      _$GetTagsStatsModelCopyWithImpl<$Res, GetTagsStatsModel>;
  @useResult
  $Res call({bool? inWork, int? branch, ItemTag? selectedTag});
}

/// @nodoc
class _$GetTagsStatsModelCopyWithImpl<$Res, $Val extends GetTagsStatsModel>
    implements $GetTagsStatsModelCopyWith<$Res> {
  _$GetTagsStatsModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of GetTagsStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? inWork = freezed,
    Object? branch = freezed,
    Object? selectedTag = freezed,
  }) {
    return _then(_value.copyWith(
      inWork: freezed == inWork
          ? _value.inWork
          : inWork // ignore: cast_nullable_to_non_nullable
              as bool?,
      branch: freezed == branch
          ? _value.branch
          : branch // ignore: cast_nullable_to_non_nullable
              as int?,
      selectedTag: freezed == selectedTag
          ? _value.selectedTag
          : selectedTag // ignore: cast_nullable_to_non_nullable
              as ItemTag?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GetTagsStatsModelImplCopyWith<$Res>
    implements $GetTagsStatsModelCopyWith<$Res> {
  factory _$$GetTagsStatsModelImplCopyWith(_$GetTagsStatsModelImpl value,
          $Res Function(_$GetTagsStatsModelImpl) then) =
      __$$GetTagsStatsModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool? inWork, int? branch, ItemTag? selectedTag});
}

/// @nodoc
class __$$GetTagsStatsModelImplCopyWithImpl<$Res>
    extends _$GetTagsStatsModelCopyWithImpl<$Res, _$GetTagsStatsModelImpl>
    implements _$$GetTagsStatsModelImplCopyWith<$Res> {
  __$$GetTagsStatsModelImplCopyWithImpl(_$GetTagsStatsModelImpl _value,
      $Res Function(_$GetTagsStatsModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of GetTagsStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? inWork = freezed,
    Object? branch = freezed,
    Object? selectedTag = freezed,
  }) {
    return _then(_$GetTagsStatsModelImpl(
      inWork: freezed == inWork
          ? _value.inWork
          : inWork // ignore: cast_nullable_to_non_nullable
              as bool?,
      branch: freezed == branch
          ? _value.branch
          : branch // ignore: cast_nullable_to_non_nullable
              as int?,
      selectedTag: freezed == selectedTag
          ? _value.selectedTag
          : selectedTag // ignore: cast_nullable_to_non_nullable
              as ItemTag?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$GetTagsStatsModelImpl implements _GetTagsStatsModel {
  const _$GetTagsStatsModelImpl({this.inWork, this.branch, this.selectedTag});

  factory _$GetTagsStatsModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$GetTagsStatsModelImplFromJson(json);

  @override
  final bool? inWork;
  @override
  final int? branch;
  @override
  final ItemTag? selectedTag;

  @override
  String toString() {
    return 'GetTagsStatsModel(inWork: $inWork, branch: $branch, selectedTag: $selectedTag)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetTagsStatsModelImpl &&
            (identical(other.inWork, inWork) || other.inWork == inWork) &&
            (identical(other.branch, branch) || other.branch == branch) &&
            (identical(other.selectedTag, selectedTag) ||
                other.selectedTag == selectedTag));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, inWork, branch, selectedTag);

  /// Create a copy of GetTagsStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$GetTagsStatsModelImplCopyWith<_$GetTagsStatsModelImpl> get copyWith =>
      __$$GetTagsStatsModelImplCopyWithImpl<_$GetTagsStatsModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GetTagsStatsModelImplToJson(
      this,
    );
  }
}

abstract class _GetTagsStatsModel implements GetTagsStatsModel {
  const factory _GetTagsStatsModel(
      {final bool? inWork,
      final int? branch,
      final ItemTag? selectedTag}) = _$GetTagsStatsModelImpl;

  factory _GetTagsStatsModel.fromJson(Map<String, dynamic> json) =
      _$GetTagsStatsModelImpl.fromJson;

  @override
  bool? get inWork;
  @override
  int? get branch;
  @override
  ItemTag? get selectedTag;

  /// Create a copy of GetTagsStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$GetTagsStatsModelImplCopyWith<_$GetTagsStatsModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

UpdateTagsInItemModel _$UpdateTagsInItemModelFromJson(
    Map<String, dynamic> json) {
  return _UpdateTagsInItemModel.fromJson(json);
}

/// @nodoc
mixin _$UpdateTagsInItemModel {
  String? get mainID => throw _privateConstructorUsedError;
  List<ItemTag>? get tags => throw _privateConstructorUsedError;

  /// Serializes this UpdateTagsInItemModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UpdateTagsInItemModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UpdateTagsInItemModelCopyWith<UpdateTagsInItemModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UpdateTagsInItemModelCopyWith<$Res> {
  factory $UpdateTagsInItemModelCopyWith(UpdateTagsInItemModel value,
          $Res Function(UpdateTagsInItemModel) then) =
      _$UpdateTagsInItemModelCopyWithImpl<$Res, UpdateTagsInItemModel>;
  @useResult
  $Res call({String? mainID, List<ItemTag>? tags});
}

/// @nodoc
class _$UpdateTagsInItemModelCopyWithImpl<$Res,
        $Val extends UpdateTagsInItemModel>
    implements $UpdateTagsInItemModelCopyWith<$Res> {
  _$UpdateTagsInItemModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UpdateTagsInItemModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mainID = freezed,
    Object? tags = freezed,
  }) {
    return _then(_value.copyWith(
      mainID: freezed == mainID
          ? _value.mainID
          : mainID // ignore: cast_nullable_to_non_nullable
              as String?,
      tags: freezed == tags
          ? _value.tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<ItemTag>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UpdateTagsInItemModelImplCopyWith<$Res>
    implements $UpdateTagsInItemModelCopyWith<$Res> {
  factory _$$UpdateTagsInItemModelImplCopyWith(
          _$UpdateTagsInItemModelImpl value,
          $Res Function(_$UpdateTagsInItemModelImpl) then) =
      __$$UpdateTagsInItemModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? mainID, List<ItemTag>? tags});
}

/// @nodoc
class __$$UpdateTagsInItemModelImplCopyWithImpl<$Res>
    extends _$UpdateTagsInItemModelCopyWithImpl<$Res,
        _$UpdateTagsInItemModelImpl>
    implements _$$UpdateTagsInItemModelImplCopyWith<$Res> {
  __$$UpdateTagsInItemModelImplCopyWithImpl(_$UpdateTagsInItemModelImpl _value,
      $Res Function(_$UpdateTagsInItemModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of UpdateTagsInItemModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mainID = freezed,
    Object? tags = freezed,
  }) {
    return _then(_$UpdateTagsInItemModelImpl(
      mainID: freezed == mainID
          ? _value.mainID
          : mainID // ignore: cast_nullable_to_non_nullable
              as String?,
      tags: freezed == tags
          ? _value._tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<ItemTag>?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$UpdateTagsInItemModelImpl implements _UpdateTagsInItemModel {
  const _$UpdateTagsInItemModelImpl({this.mainID, final List<ItemTag>? tags})
      : _tags = tags;

  factory _$UpdateTagsInItemModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$UpdateTagsInItemModelImplFromJson(json);

  @override
  final String? mainID;
  final List<ItemTag>? _tags;
  @override
  List<ItemTag>? get tags {
    final value = _tags;
    if (value == null) return null;
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'UpdateTagsInItemModel(mainID: $mainID, tags: $tags)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateTagsInItemModelImpl &&
            (identical(other.mainID, mainID) || other.mainID == mainID) &&
            const DeepCollectionEquality().equals(other._tags, _tags));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, mainID, const DeepCollectionEquality().hash(_tags));

  /// Create a copy of UpdateTagsInItemModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateTagsInItemModelImplCopyWith<_$UpdateTagsInItemModelImpl>
      get copyWith => __$$UpdateTagsInItemModelImplCopyWithImpl<
          _$UpdateTagsInItemModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UpdateTagsInItemModelImplToJson(
      this,
    );
  }
}

abstract class _UpdateTagsInItemModel implements UpdateTagsInItemModel {
  const factory _UpdateTagsInItemModel(
      {final String? mainID,
      final List<ItemTag>? tags}) = _$UpdateTagsInItemModelImpl;

  factory _UpdateTagsInItemModel.fromJson(Map<String, dynamic> json) =
      _$UpdateTagsInItemModelImpl.fromJson;

  @override
  String? get mainID;
  @override
  List<ItemTag>? get tags;

  /// Create a copy of UpdateTagsInItemModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateTagsInItemModelImplCopyWith<_$UpdateTagsInItemModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}
