import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:uer_flutter/app/styles/colors.dart';

part 'tags_stats_model.freezed.dart';
part 'tags_stats_model.g.dart';

@freezed
class TagsStatsModel with _$TagsStatsModel {
  @JsonSerializable(includeIfNull: false)
  const factory TagsStatsModel({
    int? totalItems,
    List<TagsStatModel>? stats,
  }) = _TagsStatsModel;

  factory TagsStatsModel.fromJson(Map<String, Object?> json) =>
      _$TagsStatsModelFromJson(json);
}

// @freezed
// class TagsStatsListModel with _$TagsStatsListModel {
//   const factory TagsStatsListModel({
//     List<TagsStatsModel>? data,
//   }) = _TagsStatsListModel;
//   factory TagsStatsListModel.fromJson(Map<String, Object?> json) =>
//       _$TagsStatsListModelFromJson(json);
// }

@freezed
class TagsStatModel with _$TagsStatModel {
  @JsonSerializable(includeIfNull: false)
  const factory TagsStatModel({
    List<ItemTag>? tags,
    int? count,
    double? percentage,
  }) = _TagsStatModel;

  factory TagsStatModel.fromJson(Map<String, Object?> json) =>
      _$TagsStatModelFromJson(json);
}

enum ItemTag {
  @JsonValue('default')
  itemDefault,
  urgent,
  important,
  @JsonValue('on_hold')
  onHold,
  warranty;

  String getName() {
    switch (this) {
      case ItemTag.itemDefault:
        return 'Обычный';
      case ItemTag.urgent:
        return 'Срочный';
      case ItemTag.important:
        return 'Важный';
      case ItemTag.onHold:
        return 'Приостановленный';
      case ItemTag.warranty:
        return 'Гарантийный';
    }
  }

  Color getColor() {
    switch (this) {
      case ItemTag.itemDefault:
        return AppLightColors.lightGray;
      case ItemTag.urgent:
        return AppLightColors.blue;
      case ItemTag.important:
        return AppLightColors.warning;
      case ItemTag.onHold:
        return AppLightColors.error;
      case ItemTag.warranty:
        return AppLightColors.success;
      default:
        return Colors.black;
    }
  }
}

@freezed
class GetTagsStatsModel with _$GetTagsStatsModel {
  @JsonSerializable(includeIfNull: false)
  const factory GetTagsStatsModel({
    bool? inWork,
    int? branch,
    ItemTag? selectedTag,
  }) = _GetTagsStatsModel;

  factory GetTagsStatsModel.fromJson(Map<String, Object?> json) =>
      _$GetTagsStatsModelFromJson(json);
}

@freezed
class UpdateTagsInItemModel with _$UpdateTagsInItemModel {
  @JsonSerializable(includeIfNull: false)
  const factory UpdateTagsInItemModel({
    String? mainID,
    List<ItemTag>? tags,
  }) = _UpdateTagsInItemModel;

  factory UpdateTagsInItemModel.fromJson(Map<String, Object?> json) =>
      _$UpdateTagsInItemModelFromJson(json);
}
