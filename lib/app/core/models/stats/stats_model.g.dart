// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'stats_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$StatsModelImpl _$$StatsModelImplFromJson(Map<String, dynamic> json) =>
    _$StatsModelImpl(
      inWorkCount: (json['inWorkCount'] as num).toInt(),
      archiveCount: (json['archiveCount'] as num).toInt(),
      archiveInYearCount: (json['archiveInYearCount'] as num).toInt(),
      countComponents: (json['countComponents'] as num?)?.toInt(),
      allHours: (json['allHours'] as num).toDouble(),
      allOutAgeHours: (json['allOutAgeHours'] as num).toDouble(),
      recentShipment: (json['recentShipment'] as num).toInt(),
      overDeadlineCount: (json['overDeadlineCount'] as num).toInt(),
      overDeadlineAvr: (json['overDeadlineAvr'] as num).toDouble(),
      overDeadlineSum: (json['overDeadlineSum'] as num).toInt(),
      branchStats: (json['branchStats'] as List<dynamic>)
          .map((e) => BranchStatsModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      areaStats: (json['areaStats'] as List<dynamic>)
          .map((e) => AreaStatsModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$StatsModelImplToJson(_$StatsModelImpl instance) =>
    <String, dynamic>{
      'inWorkCount': instance.inWorkCount,
      'archiveCount': instance.archiveCount,
      'archiveInYearCount': instance.archiveInYearCount,
      if (instance.countComponents case final value?) 'countComponents': value,
      'allHours': instance.allHours,
      'allOutAgeHours': instance.allOutAgeHours,
      'recentShipment': instance.recentShipment,
      'overDeadlineCount': instance.overDeadlineCount,
      'overDeadlineAvr': instance.overDeadlineAvr,
      'overDeadlineSum': instance.overDeadlineSum,
      'branchStats': instance.branchStats.map((e) => e.toJson()).toList(),
      'areaStats': instance.areaStats.map((e) => e.toJson()).toList(),
    };
