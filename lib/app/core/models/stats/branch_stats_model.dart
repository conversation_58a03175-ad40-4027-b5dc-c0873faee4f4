import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter/foundation.dart';

part 'branch_stats_model.freezed.dart';
part 'branch_stats_model.g.dart';

@freezed
class BranchStatsModel with _$BranchStatsModel {
  const BranchStatsModel._();
  @JsonSerializable(explicitToJson: true, includeIfNull: false)
  const factory BranchStatsModel({
    required int id,
    required int count,
  }) = _BranchStatsModel;

  factory BranchStatsModel.fromJson(Map<String, Object?> json) =>
      _$BranchStatsModelFromJson(json);
}
