import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

//import 'package:uer_flutter/models/worker/worker_model.dart';

part 'task_ssz_info_model.freezed.dart';
part 'task_ssz_info_model.g.dart';

@freezed
class TaskSSZInfoModel with _$TaskSSZInfoModel {
  const TaskSSZInfoModel._();
  @JsonSerializable(explicitToJson: true, includeIfNull: false)
  const factory TaskSSZInfoModel({
    required String repairNumber,
    required String mainID,
    String? equipment,
    required bool notAdd,
    required int tasksCount,
    required int finishCount,
    required int inworkCount,
    required int pauseCount,
    //required List<WorkerModel> workers,
  }) = _TaskSSZInfoModel;

  factory TaskSSZInfoModel.fromJson(Map<String, Object?> json) =>
      _$TaskSSZInfoModelFromJson(json);
}

@freezed
class TaskSSZInfoListModel with _$TaskSSZInfoListModel {
  factory TaskSSZInfoListModel(List<TaskSSZInfoModel> data) =
      _TaskSSZInfoListModel;

  factory TaskSSZInfoListModel.fromJson(Map<String, dynamic> json) =>
      _$TaskSSZInfoListModelFromJson(json);
}
