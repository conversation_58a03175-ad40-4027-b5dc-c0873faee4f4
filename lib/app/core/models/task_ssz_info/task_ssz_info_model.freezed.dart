// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'task_ssz_info_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

TaskSSZInfoModel _$TaskSSZInfoModelFromJson(Map<String, dynamic> json) {
  return _TaskSSZInfoModel.fromJson(json);
}

/// @nodoc
mixin _$TaskSSZInfoModel {
  String get repairNumber => throw _privateConstructorUsedError;
  String get mainID => throw _privateConstructorUsedError;
  String? get equipment => throw _privateConstructorUsedError;
  bool get notAdd => throw _privateConstructorUsedError;
  int get tasksCount => throw _privateConstructorUsedError;
  int get finishCount => throw _privateConstructorUsedError;
  int get inworkCount => throw _privateConstructorUsedError;
  int get pauseCount => throw _privateConstructorUsedError;

  /// Serializes this TaskSSZInfoModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TaskSSZInfoModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TaskSSZInfoModelCopyWith<TaskSSZInfoModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TaskSSZInfoModelCopyWith<$Res> {
  factory $TaskSSZInfoModelCopyWith(
          TaskSSZInfoModel value, $Res Function(TaskSSZInfoModel) then) =
      _$TaskSSZInfoModelCopyWithImpl<$Res, TaskSSZInfoModel>;
  @useResult
  $Res call(
      {String repairNumber,
      String mainID,
      String? equipment,
      bool notAdd,
      int tasksCount,
      int finishCount,
      int inworkCount,
      int pauseCount});
}

/// @nodoc
class _$TaskSSZInfoModelCopyWithImpl<$Res, $Val extends TaskSSZInfoModel>
    implements $TaskSSZInfoModelCopyWith<$Res> {
  _$TaskSSZInfoModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TaskSSZInfoModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? repairNumber = null,
    Object? mainID = null,
    Object? equipment = freezed,
    Object? notAdd = null,
    Object? tasksCount = null,
    Object? finishCount = null,
    Object? inworkCount = null,
    Object? pauseCount = null,
  }) {
    return _then(_value.copyWith(
      repairNumber: null == repairNumber
          ? _value.repairNumber
          : repairNumber // ignore: cast_nullable_to_non_nullable
              as String,
      mainID: null == mainID
          ? _value.mainID
          : mainID // ignore: cast_nullable_to_non_nullable
              as String,
      equipment: freezed == equipment
          ? _value.equipment
          : equipment // ignore: cast_nullable_to_non_nullable
              as String?,
      notAdd: null == notAdd
          ? _value.notAdd
          : notAdd // ignore: cast_nullable_to_non_nullable
              as bool,
      tasksCount: null == tasksCount
          ? _value.tasksCount
          : tasksCount // ignore: cast_nullable_to_non_nullable
              as int,
      finishCount: null == finishCount
          ? _value.finishCount
          : finishCount // ignore: cast_nullable_to_non_nullable
              as int,
      inworkCount: null == inworkCount
          ? _value.inworkCount
          : inworkCount // ignore: cast_nullable_to_non_nullable
              as int,
      pauseCount: null == pauseCount
          ? _value.pauseCount
          : pauseCount // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TaskSSZInfoModelImplCopyWith<$Res>
    implements $TaskSSZInfoModelCopyWith<$Res> {
  factory _$$TaskSSZInfoModelImplCopyWith(_$TaskSSZInfoModelImpl value,
          $Res Function(_$TaskSSZInfoModelImpl) then) =
      __$$TaskSSZInfoModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String repairNumber,
      String mainID,
      String? equipment,
      bool notAdd,
      int tasksCount,
      int finishCount,
      int inworkCount,
      int pauseCount});
}

/// @nodoc
class __$$TaskSSZInfoModelImplCopyWithImpl<$Res>
    extends _$TaskSSZInfoModelCopyWithImpl<$Res, _$TaskSSZInfoModelImpl>
    implements _$$TaskSSZInfoModelImplCopyWith<$Res> {
  __$$TaskSSZInfoModelImplCopyWithImpl(_$TaskSSZInfoModelImpl _value,
      $Res Function(_$TaskSSZInfoModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of TaskSSZInfoModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? repairNumber = null,
    Object? mainID = null,
    Object? equipment = freezed,
    Object? notAdd = null,
    Object? tasksCount = null,
    Object? finishCount = null,
    Object? inworkCount = null,
    Object? pauseCount = null,
  }) {
    return _then(_$TaskSSZInfoModelImpl(
      repairNumber: null == repairNumber
          ? _value.repairNumber
          : repairNumber // ignore: cast_nullable_to_non_nullable
              as String,
      mainID: null == mainID
          ? _value.mainID
          : mainID // ignore: cast_nullable_to_non_nullable
              as String,
      equipment: freezed == equipment
          ? _value.equipment
          : equipment // ignore: cast_nullable_to_non_nullable
              as String?,
      notAdd: null == notAdd
          ? _value.notAdd
          : notAdd // ignore: cast_nullable_to_non_nullable
              as bool,
      tasksCount: null == tasksCount
          ? _value.tasksCount
          : tasksCount // ignore: cast_nullable_to_non_nullable
              as int,
      finishCount: null == finishCount
          ? _value.finishCount
          : finishCount // ignore: cast_nullable_to_non_nullable
              as int,
      inworkCount: null == inworkCount
          ? _value.inworkCount
          : inworkCount // ignore: cast_nullable_to_non_nullable
              as int,
      pauseCount: null == pauseCount
          ? _value.pauseCount
          : pauseCount // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class _$TaskSSZInfoModelImpl extends _TaskSSZInfoModel
    with DiagnosticableTreeMixin {
  const _$TaskSSZInfoModelImpl(
      {required this.repairNumber,
      required this.mainID,
      this.equipment,
      required this.notAdd,
      required this.tasksCount,
      required this.finishCount,
      required this.inworkCount,
      required this.pauseCount})
      : super._();

  factory _$TaskSSZInfoModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$TaskSSZInfoModelImplFromJson(json);

  @override
  final String repairNumber;
  @override
  final String mainID;
  @override
  final String? equipment;
  @override
  final bool notAdd;
  @override
  final int tasksCount;
  @override
  final int finishCount;
  @override
  final int inworkCount;
  @override
  final int pauseCount;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'TaskSSZInfoModel(repairNumber: $repairNumber, mainID: $mainID, equipment: $equipment, notAdd: $notAdd, tasksCount: $tasksCount, finishCount: $finishCount, inworkCount: $inworkCount, pauseCount: $pauseCount)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'TaskSSZInfoModel'))
      ..add(DiagnosticsProperty('repairNumber', repairNumber))
      ..add(DiagnosticsProperty('mainID', mainID))
      ..add(DiagnosticsProperty('equipment', equipment))
      ..add(DiagnosticsProperty('notAdd', notAdd))
      ..add(DiagnosticsProperty('tasksCount', tasksCount))
      ..add(DiagnosticsProperty('finishCount', finishCount))
      ..add(DiagnosticsProperty('inworkCount', inworkCount))
      ..add(DiagnosticsProperty('pauseCount', pauseCount));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TaskSSZInfoModelImpl &&
            (identical(other.repairNumber, repairNumber) ||
                other.repairNumber == repairNumber) &&
            (identical(other.mainID, mainID) || other.mainID == mainID) &&
            (identical(other.equipment, equipment) ||
                other.equipment == equipment) &&
            (identical(other.notAdd, notAdd) || other.notAdd == notAdd) &&
            (identical(other.tasksCount, tasksCount) ||
                other.tasksCount == tasksCount) &&
            (identical(other.finishCount, finishCount) ||
                other.finishCount == finishCount) &&
            (identical(other.inworkCount, inworkCount) ||
                other.inworkCount == inworkCount) &&
            (identical(other.pauseCount, pauseCount) ||
                other.pauseCount == pauseCount));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, repairNumber, mainID, equipment,
      notAdd, tasksCount, finishCount, inworkCount, pauseCount);

  /// Create a copy of TaskSSZInfoModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TaskSSZInfoModelImplCopyWith<_$TaskSSZInfoModelImpl> get copyWith =>
      __$$TaskSSZInfoModelImplCopyWithImpl<_$TaskSSZInfoModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TaskSSZInfoModelImplToJson(
      this,
    );
  }
}

abstract class _TaskSSZInfoModel extends TaskSSZInfoModel {
  const factory _TaskSSZInfoModel(
      {required final String repairNumber,
      required final String mainID,
      final String? equipment,
      required final bool notAdd,
      required final int tasksCount,
      required final int finishCount,
      required final int inworkCount,
      required final int pauseCount}) = _$TaskSSZInfoModelImpl;
  const _TaskSSZInfoModel._() : super._();

  factory _TaskSSZInfoModel.fromJson(Map<String, dynamic> json) =
      _$TaskSSZInfoModelImpl.fromJson;

  @override
  String get repairNumber;
  @override
  String get mainID;
  @override
  String? get equipment;
  @override
  bool get notAdd;
  @override
  int get tasksCount;
  @override
  int get finishCount;
  @override
  int get inworkCount;
  @override
  int get pauseCount;

  /// Create a copy of TaskSSZInfoModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TaskSSZInfoModelImplCopyWith<_$TaskSSZInfoModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

TaskSSZInfoListModel _$TaskSSZInfoListModelFromJson(Map<String, dynamic> json) {
  return _TaskSSZInfoListModel.fromJson(json);
}

/// @nodoc
mixin _$TaskSSZInfoListModel {
  List<TaskSSZInfoModel> get data => throw _privateConstructorUsedError;

  /// Serializes this TaskSSZInfoListModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TaskSSZInfoListModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TaskSSZInfoListModelCopyWith<TaskSSZInfoListModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TaskSSZInfoListModelCopyWith<$Res> {
  factory $TaskSSZInfoListModelCopyWith(TaskSSZInfoListModel value,
          $Res Function(TaskSSZInfoListModel) then) =
      _$TaskSSZInfoListModelCopyWithImpl<$Res, TaskSSZInfoListModel>;
  @useResult
  $Res call({List<TaskSSZInfoModel> data});
}

/// @nodoc
class _$TaskSSZInfoListModelCopyWithImpl<$Res,
        $Val extends TaskSSZInfoListModel>
    implements $TaskSSZInfoListModelCopyWith<$Res> {
  _$TaskSSZInfoListModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TaskSSZInfoListModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
  }) {
    return _then(_value.copyWith(
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<TaskSSZInfoModel>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TaskSSZInfoListModelImplCopyWith<$Res>
    implements $TaskSSZInfoListModelCopyWith<$Res> {
  factory _$$TaskSSZInfoListModelImplCopyWith(_$TaskSSZInfoListModelImpl value,
          $Res Function(_$TaskSSZInfoListModelImpl) then) =
      __$$TaskSSZInfoListModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<TaskSSZInfoModel> data});
}

/// @nodoc
class __$$TaskSSZInfoListModelImplCopyWithImpl<$Res>
    extends _$TaskSSZInfoListModelCopyWithImpl<$Res, _$TaskSSZInfoListModelImpl>
    implements _$$TaskSSZInfoListModelImplCopyWith<$Res> {
  __$$TaskSSZInfoListModelImplCopyWithImpl(_$TaskSSZInfoListModelImpl _value,
      $Res Function(_$TaskSSZInfoListModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of TaskSSZInfoListModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
  }) {
    return _then(_$TaskSSZInfoListModelImpl(
      null == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as List<TaskSSZInfoModel>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TaskSSZInfoListModelImpl
    with DiagnosticableTreeMixin
    implements _TaskSSZInfoListModel {
  _$TaskSSZInfoListModelImpl(final List<TaskSSZInfoModel> data) : _data = data;

  factory _$TaskSSZInfoListModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$TaskSSZInfoListModelImplFromJson(json);

  final List<TaskSSZInfoModel> _data;
  @override
  List<TaskSSZInfoModel> get data {
    if (_data is EqualUnmodifiableListView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_data);
  }

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'TaskSSZInfoListModel(data: $data)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'TaskSSZInfoListModel'))
      ..add(DiagnosticsProperty('data', data));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TaskSSZInfoListModelImpl &&
            const DeepCollectionEquality().equals(other._data, _data));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_data));

  /// Create a copy of TaskSSZInfoListModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TaskSSZInfoListModelImplCopyWith<_$TaskSSZInfoListModelImpl>
      get copyWith =>
          __$$TaskSSZInfoListModelImplCopyWithImpl<_$TaskSSZInfoListModelImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TaskSSZInfoListModelImplToJson(
      this,
    );
  }
}

abstract class _TaskSSZInfoListModel implements TaskSSZInfoListModel {
  factory _TaskSSZInfoListModel(final List<TaskSSZInfoModel> data) =
      _$TaskSSZInfoListModelImpl;

  factory _TaskSSZInfoListModel.fromJson(Map<String, dynamic> json) =
      _$TaskSSZInfoListModelImpl.fromJson;

  @override
  List<TaskSSZInfoModel> get data;

  /// Create a copy of TaskSSZInfoListModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TaskSSZInfoListModelImplCopyWith<_$TaskSSZInfoListModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}
