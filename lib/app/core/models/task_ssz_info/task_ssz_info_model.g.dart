// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'task_ssz_info_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$TaskSSZInfoModelImpl _$$TaskSSZInfoModelImplFromJson(
        Map<String, dynamic> json) =>
    _$TaskSSZInfoModelImpl(
      repairNumber: json['repairNumber'] as String,
      mainID: json['mainID'] as String,
      equipment: json['equipment'] as String?,
      notAdd: json['notAdd'] as bool,
      tasksCount: (json['tasksCount'] as num).toInt(),
      finishCount: (json['finishCount'] as num).toInt(),
      inworkCount: (json['inworkCount'] as num).toInt(),
      pauseCount: (json['pauseCount'] as num).toInt(),
    );

Map<String, dynamic> _$$TaskSSZInfoModelImplToJson(
        _$TaskSSZInfoModelImpl instance) =>
    <String, dynamic>{
      'repairNumber': instance.repairNumber,
      'mainID': instance.mainID,
      if (instance.equipment case final value?) 'equipment': value,
      'notAdd': instance.notAdd,
      'tasksCount': instance.tasksCount,
      'finishCount': instance.finishCount,
      'inworkCount': instance.inworkCount,
      'pauseCount': instance.pauseCount,
    };

_$TaskSSZInfoListModelImpl _$$TaskSSZInfoListModelImplFromJson(
        Map<String, dynamic> json) =>
    _$TaskSSZInfoListModelImpl(
      (json['data'] as List<dynamic>)
          .map((e) => TaskSSZInfoModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$TaskSSZInfoListModelImplToJson(
        _$TaskSSZInfoListModelImpl instance) =>
    <String, dynamic>{
      'data': instance.data,
    };
