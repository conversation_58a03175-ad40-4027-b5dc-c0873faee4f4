import '../../../helpers/constants.dart';
import '../../../routes/models/common_items_complex_data.dart';
import '../search/search_model.dart';

enum CommonItemsScreens<String> {
  inWork,
  recentShipmentItems,
  archive,
  archiveInYear
}

extension CommonItemsScreensExtension on CommonItemsScreens {
  CommonItemsPageComplexData getData({int? branchID}) {
    switch (this) {
      case CommonItemsScreens.inWork:
        return CommonItemsPageComplexData(
            title: "Заказы в работе",
            searchModel: SearchModel(
                finished: false,
                newBool: false,
                branch: branchID,
                projection: PROJECTION_SEARCH_ITEM));
      case CommonItemsScreens.recentShipmentItems:
        return CommonItemsPageComplexData(
            title: "Скоро отгружать",
            searchModel: SearchModel(
                finished: false,
                newBool: false,
                branch: branchID,
                recentShipmentWeeks: 4,
                projection: PROJECTION_SEARCH_ITEM));
      case CommonItemsScreens.archive:
        return CommonItemsPageComplexData(
            title: "Архив",
            searchModel: SearchModel(
                finished: true,
                newBool: false,
                branch: branchID,
                projection: PROJECTION_SEARCH_ITEM));
      case CommonItemsScreens.archiveInYear:
        return CommonItemsPageComplexData(
            title: "Архив за год",
            searchModel: SearchModel(
                finished: true,
                newBool: false,
                branch: branchID,
                archiveInYear: true,
                projection: PROJECTION_SEARCH_ITEM));
      default:
        return CommonItemsPageComplexData(
            title: "Все заказы",
            searchModel: const SearchModel(
                finished: false,
                newBool: false,
                projection: PROJECTION_SEARCH_ITEM));
    }
  }
}
