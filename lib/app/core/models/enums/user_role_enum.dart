enum UserRole<String> { admin, manager, director, user, client, station, block }

extension UserRoleExtension on UserRole {
  String get desc {
    switch (this) {
      case UserRole.admin:
        return 'Админ';
      case UserRole.manager:
        return 'Управляющий';
      case UserRole.director:
        return 'Директор';
      case UserRole.user:
        return 'Пользователь';
      case UserRole.client:
        return 'Клиент';
      case UserRole.station:
        return 'Стационарный';
      case UserRole.block:
        return 'Заблокирован';
      default:
        return "";
    }
  }
}
