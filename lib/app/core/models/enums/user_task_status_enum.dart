import 'package:flutter/material.dart';

enum UserTaskStatusType<String> {
  created,
  inwork,
  finished,
}

extension UserTaskStatusTypeExtension on UserTaskStatusType {
  String get name {
    switch (this) {
      case UserTaskStatusType.inwork:
        return 'В работе';
      case UserTaskStatusType.created:
        return 'Создана';
      case UserTaskStatusType.finished:
        return 'Завершена';
      default:
        return "";
    }
  }

  // TODO: add method for get badge

  MaterialColor get colorBadge {
    switch (this) {
      case UserTaskStatusType.inwork:
        return Colors.orange;
      case UserTaskStatusType.created:
        return Colors.grey;
      case UserTaskStatusType.finished:
        return Colors.green;
      default:
        return Colors.grey;
    }
  }
}
