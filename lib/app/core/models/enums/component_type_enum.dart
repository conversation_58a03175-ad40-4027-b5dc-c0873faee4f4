enum ComponentType<String> {
  stator,
  rotor,
  inductor,
  anchor,
  slidingBearing,
  rollingBearing,
  transformer
}

extension ComponentTypeExtension on ComponentType {
  String get desc {
    switch (this) {
      case ComponentType.stator:
        return 'Статор';
      case ComponentType.rotor:
        return 'Ротор';
      case ComponentType.inductor:
        return 'Индуктор';
      case ComponentType.anchor:
        return 'Якорь';
      case ComponentType.slidingBearing:
        return 'Подшип. скольжения';
      case ComponentType.rollingBearing:
        return "Подшип. щиты";
      case ComponentType.transformer:
        return "Трансформатор";
      default:
        return "";
    }
  }
}
