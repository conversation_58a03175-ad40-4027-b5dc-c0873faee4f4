import 'package:flutter/material.dart';

enum TutorialEnum<String> {
  search,
  historyNotification,
  stat,
  compare,
  areas,
  filter,
  calendarSSZ,
  stateSSZ,
  notificationStatus,
  notificationSSZ,
  notificationZPR,
  notificationActive,
  notificationDowntime,
  photo,
  video,
  comments,
  itemScreen,
  componentScreen,
  addNewItem,
  startSSZ,
  stopSSZ,
  actionSSZ,
  quickStartSSZ,
  quickStopSSZ,
  quickActionSSZ,
}

extension TutorialEnumExtension on TutorialEnum {
  static String baseUrl = "https://dev.uer-ural.ru/files/guides/";

  String get fullUrl {
    return baseUrl + url;
  }

  String get name {
    switch (this) {
      case TutorialEnum.search:
        return 'Поиск заказа';
      case TutorialEnum.historyNotification:
        return 'История уведомлений';
      case TutorialEnum.stat:
        return 'Статистика';
      case TutorialEnum.compare:
        return 'Сравнение работ между участками';
      case TutorialEnum.areas:
        return 'Участки и компоненты';
      case TutorialEnum.filter:
        return 'Фильтры по филиалам и участкам';
      case TutorialEnum.calendarSSZ:
        return 'Календарь ССЗ';
      case TutorialEnum.stateSSZ:
        return 'Состояние ССЗ';
      case TutorialEnum.notificationStatus:
        return 'Уведомления по статусам';
      case TutorialEnum.notificationSSZ:
        return 'Уведомления по ССЗ';
      case TutorialEnum.notificationZPR:
        return 'Уведомления о новом ЗПР';
      case TutorialEnum.notificationActive:
        return 'Уведомления об активности';
      case TutorialEnum.notificationDowntime:
        return 'Уведомления о простое';
      case TutorialEnum.photo:
        return 'Просмотр и добавление фото';
      case TutorialEnum.video:
        return 'Просмотр видео испытаний';
      case TutorialEnum.comments:
        return 'Просмотр и добавление комментариев';
      case TutorialEnum.itemScreen:
        return 'Экран заказа';
      case TutorialEnum.componentScreen:
        return 'Экран компонента';
      case TutorialEnum.addNewItem:
        return 'Добавление заказа в систему';
      case TutorialEnum.startSSZ:
        return 'Сканирование и старт ССЗ';
      case TutorialEnum.stopSSZ:
        return 'Сканирование и завершение/приостановка ССЗ';
      case TutorialEnum.actionSSZ:
        return 'Экран выбора действия, добавление и изменение статусов';
      case TutorialEnum.quickStartSSZ:
        return 'Старт ССЗ без сканирования';
      case TutorialEnum.quickStopSSZ:
        return 'Завершение/приостановка ССЗ без сканирования';
      case TutorialEnum.quickActionSSZ:
        return 'Экран выбора действия, добавление и изменение статусов без сканирования';
      default:
        return "";
    }
  }

  IconData get icon {
    switch (this) {
      case TutorialEnum.search:
        return Icons.search;
      case TutorialEnum.historyNotification:
        return Icons.history;
      case TutorialEnum.stat:
        return Icons.bar_chart;
      case TutorialEnum.compare:
        return Icons.compare_arrows;
      case TutorialEnum.areas:
        return Icons.location_on;
      case TutorialEnum.filter:
        return Icons.filter_list;
      case TutorialEnum.calendarSSZ:
        return Icons.calendar_today;
      case TutorialEnum.stateSSZ:
        return Icons.list;
      case TutorialEnum.notificationStatus:
        return Icons.notifications;
      case TutorialEnum.notificationSSZ:
        return Icons.notifications;
      case TutorialEnum.notificationZPR:
        return Icons.notifications;
      case TutorialEnum.notificationActive:
        return Icons.notifications;
      case TutorialEnum.notificationDowntime:
        return Icons.notifications;
      case TutorialEnum.photo:
        return Icons.photo;
      case TutorialEnum.video:
        return Icons.video_collection;
      case TutorialEnum.comments:
        return Icons.comment;
      case TutorialEnum.itemScreen:
        return Icons.list;
      case TutorialEnum.componentScreen:
        return Icons.list;
      case TutorialEnum.addNewItem:
        return Icons.add;
      case TutorialEnum.startSSZ:
        return Icons.play_arrow;
      case TutorialEnum.stopSSZ:
        return Icons.stop;
      case TutorialEnum.actionSSZ:
        return Icons.list;
      case TutorialEnum.quickStartSSZ:
        return Icons.play_arrow;
      case TutorialEnum.quickStopSSZ:
        return Icons.stop;
      case TutorialEnum.quickActionSSZ:
        return Icons.list;
      default:
        return Icons.help;
    }
  }

  Color get color {
    switch (this) {
      case TutorialEnum.search:
        return Colors.blue;
      case TutorialEnum.historyNotification:
        return Colors.red;
      case TutorialEnum.stat:
        return Colors.green;
      case TutorialEnum.compare:
        return Colors.orange;
      case TutorialEnum.areas:
        return Colors.red;
      case TutorialEnum.filter:
        return Colors.purple;
      case TutorialEnum.calendarSSZ:
        return Colors.blue;
      case TutorialEnum.stateSSZ:
        return Colors.green;
      case TutorialEnum.notificationStatus:
        return Colors.red;
      case TutorialEnum.notificationSSZ:
        return Colors.orange;
      case TutorialEnum.notificationZPR:
        return Colors.blue;
      case TutorialEnum.notificationActive:
        return Colors.pink;
      case TutorialEnum.notificationDowntime:
        return Colors.deepPurple;
      case TutorialEnum.photo:
        return Colors.deepOrange;
      case TutorialEnum.video:
        return Colors.blueAccent;
      case TutorialEnum.comments:
        return Colors.greenAccent;
      case TutorialEnum.itemScreen:
        return Colors.cyan;
      case TutorialEnum.componentScreen:
        return Colors.brown;
      case TutorialEnum.addNewItem:
        return Colors.red;
      case TutorialEnum.startSSZ:
        return Colors.green;
      case TutorialEnum.stopSSZ:
        return Colors.orange;
      case TutorialEnum.actionSSZ:
        return Colors.teal;
      case TutorialEnum.quickStartSSZ:
        return Colors.green;
      case TutorialEnum.quickStopSSZ:
        return Colors.orange;
      case TutorialEnum.quickActionSSZ:
        return Colors.teal;
      default:
        return Colors.blue;
    }
  }

  String get url {
    switch (this) {
      case TutorialEnum.search:
        return "1_01_poisk.mp4";
      case TutorialEnum.historyNotification:
        return "1_02_history.mp4";
      case TutorialEnum.stat:
        return "1_03_stat.mp4";
      case TutorialEnum.compare:
        return "1_04_sravnenie.mp4";
      case TutorialEnum.areas:
        return "1_05_uchastki.mp4";
      case TutorialEnum.filter:
        return "2_01_filtry.mp4";
      case TutorialEnum.calendarSSZ:
        return "2_02_kalendar.mp4";
      case TutorialEnum.stateSSZ:
        return "2_03_sostoyanie.mp4";
      case TutorialEnum.notificationStatus:
        return "3_01_uvpostat.mp4";
      case TutorialEnum.notificationSSZ:
        return "3_02_uvpouch.mp4";
      case TutorialEnum.notificationZPR:
        return "3_03_uvozpr.mp4";
      case TutorialEnum.notificationActive:
        return "3_04_uvact.mp4";
      case TutorialEnum.notificationDowntime:
        return "3_05_uvprost.mp4";
      case TutorialEnum.photo:
        return "4_01_photo.mp4";
      case TutorialEnum.video:
        return "4_02_video.mp4";
      case TutorialEnum.comments:
        return "4_03_comments.mp4";
      case TutorialEnum.itemScreen:
        return "4_04_ekzak.mp4";
      case TutorialEnum.componentScreen:
        return "4_05_ekkomp.mp4";
      case TutorialEnum.addNewItem:
        return "5_01_dobzak.mp4";
      case TutorialEnum.startSSZ:
        return "6_01_scanstart.mp4";
      case TutorialEnum.stopSSZ:
        return "6_02_scanstop.mp4";
      case TutorialEnum.actionSSZ:
        return "6_03_ekvybdeist.mp4";
      case TutorialEnum.quickStartSSZ:
        return "7_01_startbs.mp4";
      case TutorialEnum.quickStopSSZ:
        return "7_02_stopbs.mp4";
      case TutorialEnum.quickActionSSZ:
        return "7_03_ekvybdeistbs.mp4";
      default:
        return "";
    }
  }

  static List<String> allCategories() {
    return [
      "Главный экран",
      "ССЗ",
      "Настройки",
      "Общее",
      "Приёмка",
      "Пользователь",
      "Директор филиала"
    ];
  }

  static List<TutorialEnum> getEnumsForCategory(String category) {
    return allCategories().contains(category)
        ? TutorialEnum.values
            .where((TutorialEnum e) => e.category == category)
            .toList()
        : [];
  }

  String get category {
    switch (this) {
      case TutorialEnum.search:
      case TutorialEnum.historyNotification:
      case TutorialEnum.stat:
      case TutorialEnum.compare:
      case TutorialEnum.areas:
        return "Главный экран";
      case TutorialEnum.filter:
      case TutorialEnum.calendarSSZ:
      case TutorialEnum.stateSSZ:
        return "ССЗ";
      case TutorialEnum.notificationStatus:
      case TutorialEnum.notificationSSZ:
      case TutorialEnum.notificationZPR:
      case TutorialEnum.notificationActive:
      case TutorialEnum.notificationDowntime:
        return "Настройки";
      case TutorialEnum.photo:
      case TutorialEnum.video:
      case TutorialEnum.comments:
      case TutorialEnum.itemScreen:
      case TutorialEnum.componentScreen:
        return "Общее";
      case TutorialEnum.addNewItem:
        return "Приёмка";
      case TutorialEnum.startSSZ:
      case TutorialEnum.stopSSZ:
      case TutorialEnum.actionSSZ:
        return "Пользователь";
      case TutorialEnum.quickStartSSZ:
      case TutorialEnum.quickStopSSZ:
      case TutorialEnum.quickActionSSZ:
        return "Директор филиала";
      default:
        return "";
    }
  }
}
