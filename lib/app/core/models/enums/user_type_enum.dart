enum UserType { distributor, controlMaster, mainEngineer, headArea, pdo, empty }

extension UserTypeExtension on UserType {
  String get desc {
    switch (this) {
      case UserType.distributor:
        return 'Распределитель работ';
      case UserType.controlMaster:
        return 'Контрольный мастер';
      case UserType.mainEngineer:
        return 'Главный инженер';
      case UserType.headArea:
        return 'Начальник участка';
      case UserType.pdo:
        return 'ПДО';
      case UserType.empty:
        return 'Не выбран';
      default:
        return "";
    }
  }
}
