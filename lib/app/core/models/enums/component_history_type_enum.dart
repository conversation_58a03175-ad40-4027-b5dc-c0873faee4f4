import 'package:flutter/material.dart';

enum ComponentHistoryType<String> {
  task,
  job,
  info,
}

extension ComponentHistoryTypeExtension on ComponentHistoryType {
  Icon get icon {
    switch (this) {
      case ComponentHistoryType.task:
        return const Icon(
          Icons.today,
          size: 18,
        );
      case ComponentHistoryType.job:
        return const Icon(
          Icons.handyman_rounded,
          size: 18,
        );
      case ComponentHistoryType.info:
        return const Icon(
          Icons.info_outlined,
          size: 18,
        );
    }
  }
}
