import 'package:flutter/material.dart';

enum HistoryStatusType<String> {
  inwork,
  finish,
  pause,
  info,
}

extension HistoryStatusTypeExtension on HistoryStatusType {
  String get desc {
    switch (this) {
      case HistoryStatusType.inwork:
        return 'Выполняется';
      case HistoryStatusType.finish:
        return 'Завершено';
      case HistoryStatusType.pause:
        return 'Приостановлена';
      case HistoryStatusType.info: // комментарий
        return '';
      default:
        return "";
    }
  }

  // MaterialColor get colorBadge {
  //   switch (this) {
  //     case HistoryStatusType.inwork:
  //       return Colors.orange;
  //     case HistoryStatusType.finish:
  //       return Colors.green;
  //     case HistoryStatusType.pause:
  //       return Colors.red;
  //     case HistoryStatusType.info: // комментарий
  //       return Colors.grey;
  //     default:
  //       return Colors.grey;
  //   }
  // }

  Icon get statusBadge {
    switch (this) {
      case HistoryStatusType.finish:
        return const Icon(
          Icons.check_circle_rounded,
          color: Colors.green,
          size: 14,
        );
      case HistoryStatusType.inwork:
        return const Icon(
          Icons.circle,
          color: Colors.orange,
          size: 14,
        );
      case HistoryStatusType.pause:
        return const Icon(
          Icons.circle,
          color: Colors.red,
          size: 14,
        );
      case HistoryStatusType.info:
        return const Icon(
          Icons.circle,
          color: Colors.grey,
          size: 14,
        );
      default:
        return const Icon(
          Icons.circle,
          color: Colors.grey,
          size: 14,
        );
    }
  }
}
