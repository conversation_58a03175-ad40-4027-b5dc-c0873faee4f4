enum SearchType<String> {
  repairNumber,
  client,
  equipment,
  mainID,
}

extension SearchTypeExtension on SearchType {
  String get name {
    switch (this) {
      case SearchType.repairNumber:
        return 'По рем. номеру';
      case SearchType.client:
        return 'По контрагенту';
      case SearchType.equipment:
        return 'По оборудованию';
      case SearchType.mainID:
        return 'По ИД заказа';
      default:
        return "";
    }
  }

  String get hint {
    switch (this) {
      case SearchType.repairNumber:
        return 'Введите ремонтный номер';
      case SearchType.client:
        return 'Введите контрагента';
      case SearchType.equipment:
        return 'Введите тип оборудования';
      case SearchType.mainID:
        return 'Введите ИД заказа';
      default:
        return "";
    }
  }
}
