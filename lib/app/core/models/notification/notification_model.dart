import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter/foundation.dart';

part 'notification_model.freezed.dart';
part 'notification_model.g.dart';

@freezed
class NotificationModel with _$NotificationModel {
  const NotificationModel._();
  @JsonSerializable(explicitToJson: true, includeIfNull: false)
  const factory NotificationModel({
    String? title,
    String? message,
    String? from,
    List<String>? owners,
    double? createdAt,
    double? updatedAt,
  }) = _NotificationModel;

  factory NotificationModel.fromJson(Map<String, Object?> json) =>
      _$NotificationModelFromJson(json);
}

@freezed
class NotificationListModel with _$NotificationListModel {
  factory NotificationListModel(List<NotificationModel> data) =
      _NotificationListModel;

  factory NotificationListModel.fromJson(Map<String, dynamic> json) =>
      _$NotificationListModelFromJson(json);
}
