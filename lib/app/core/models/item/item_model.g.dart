// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'item_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ItemModelImpl _$$ItemModelImplFromJson(Map<String, dynamic> json) =>
    _$ItemModelImpl(
      repairNumber: json['repairNumber'] as String?,
      mainID: json['mainID'] as String?,
      altIds:
          (json['altIds'] as List<dynamic>?)?.map((e) => e as String).toList(),
      altRN:
          (json['altRN'] as List<dynamic>?)?.map((e) => e as String).toList(),
      tags: (json['tags'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$ItemTagEnumMap, e))
          .toList(),
      major: json['major'] as bool?,
      components: (json['components'] as List<dynamic>?)
              ?.map((e) => ComponentModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      endDate: (json['endDate'] as num?)?.toDouble(),
      finished: json['finished'] as bool?,
      branch: (json['branch'] as num?)?.toInt(),
      finishDate: (json['finishDate'] as num?)?.toDouble(),
      finish1cDate: (json['finish1cDate'] as num?)?.toDouble(),
      photos: (json['photos'] as List<dynamic>?)
          ?.map((e) => PhotoModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      videosTest: (json['videosTest'] as List<dynamic>?)
          ?.map((e) => VideoTestModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      mark: json['mark'] as bool?,
      deleted: json['deleted'] as bool?,
      order: json['order'] == null
          ? null
          : OrderModel.fromJson(json['order'] as Map<String, dynamic>),
      newBool: json['new'] as bool?,
      assembled: json['assembled'] as bool?,
      createdAt: (json['createdAt'] as num?)?.toDouble(),
      updatedAt: (json['updatedAt'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$ItemModelImplToJson(_$ItemModelImpl instance) =>
    <String, dynamic>{
      if (instance.repairNumber case final value?) 'repairNumber': value,
      if (instance.mainID case final value?) 'mainID': value,
      if (instance.altIds case final value?) 'altIds': value,
      if (instance.altRN case final value?) 'altRN': value,
      if (instance.tags?.map((e) => _$ItemTagEnumMap[e]!).toList()
          case final value?)
        'tags': value,
      if (instance.major case final value?) 'major': value,
      'components': instance.components.map((e) => e.toJson()).toList(),
      if (instance.endDate case final value?) 'endDate': value,
      if (instance.finished case final value?) 'finished': value,
      if (instance.branch case final value?) 'branch': value,
      if (instance.finishDate case final value?) 'finishDate': value,
      if (instance.finish1cDate case final value?) 'finish1cDate': value,
      if (instance.photos?.map((e) => e.toJson()).toList() case final value?)
        'photos': value,
      if (instance.videosTest?.map((e) => e.toJson()).toList()
          case final value?)
        'videosTest': value,
      if (instance.mark case final value?) 'mark': value,
      if (instance.deleted case final value?) 'deleted': value,
      if (instance.order?.toJson() case final value?) 'order': value,
      if (instance.newBool case final value?) 'new': value,
      if (instance.assembled case final value?) 'assembled': value,
      if (instance.createdAt case final value?) 'createdAt': value,
      if (instance.updatedAt case final value?) 'updatedAt': value,
    };

const _$ItemTagEnumMap = {
  ItemTag.itemDefault: 'default',
  ItemTag.urgent: 'urgent',
  ItemTag.important: 'important',
  ItemTag.onHold: 'on_hold',
  ItemTag.warranty: 'warranty',
};

_$ItemListModelImpl _$$ItemListModelImplFromJson(Map<String, dynamic> json) =>
    _$ItemListModelImpl(
      (json['data'] as List<dynamic>)
          .map((e) => ItemModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$ItemListModelImplToJson(_$ItemListModelImpl instance) =>
    <String, dynamic>{
      'data': instance.data,
    };
