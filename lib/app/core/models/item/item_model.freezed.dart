// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'item_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ItemModel _$ItemModelFromJson(Map<String, dynamic> json) {
  return _ItemModel.fromJson(json);
}

/// @nodoc
mixin _$ItemModel {
  String? get repairNumber => throw _privateConstructorUsedError;
  String? get mainID => throw _privateConstructorUsedError;
  List<String>? get altIds => throw _privateConstructorUsedError;
  List<String>? get altRN => throw _privateConstructorUsedError;
  List<ItemTag>? get tags => throw _privateConstructorUsedError;
  bool? get major => throw _privateConstructorUsedError;
  List<ComponentModel> get components => throw _privateConstructorUsedError;
  double? get endDate => throw _privateConstructorUsedError;
  bool? get finished => throw _privateConstructorUsedError;
  int? get branch => throw _privateConstructorUsedError;
  double? get finishDate => throw _privateConstructorUsedError;
  double? get finish1cDate => throw _privateConstructorUsedError;
  List<PhotoModel>? get photos => throw _privateConstructorUsedError;
  List<VideoTestModel>? get videosTest => throw _privateConstructorUsedError;
  bool? get mark => throw _privateConstructorUsedError;
  bool? get deleted => throw _privateConstructorUsedError;
  OrderModel? get order => throw _privateConstructorUsedError;
  @JsonKey(name: 'new')
  bool? get newBool => throw _privateConstructorUsedError;
  bool? get assembled => throw _privateConstructorUsedError;
  double? get createdAt => throw _privateConstructorUsedError;
  double? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this ItemModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ItemModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ItemModelCopyWith<ItemModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ItemModelCopyWith<$Res> {
  factory $ItemModelCopyWith(ItemModel value, $Res Function(ItemModel) then) =
      _$ItemModelCopyWithImpl<$Res, ItemModel>;
  @useResult
  $Res call(
      {String? repairNumber,
      String? mainID,
      List<String>? altIds,
      List<String>? altRN,
      List<ItemTag>? tags,
      bool? major,
      List<ComponentModel> components,
      double? endDate,
      bool? finished,
      int? branch,
      double? finishDate,
      double? finish1cDate,
      List<PhotoModel>? photos,
      List<VideoTestModel>? videosTest,
      bool? mark,
      bool? deleted,
      OrderModel? order,
      @JsonKey(name: 'new') bool? newBool,
      bool? assembled,
      double? createdAt,
      double? updatedAt});

  $OrderModelCopyWith<$Res>? get order;
}

/// @nodoc
class _$ItemModelCopyWithImpl<$Res, $Val extends ItemModel>
    implements $ItemModelCopyWith<$Res> {
  _$ItemModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ItemModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? repairNumber = freezed,
    Object? mainID = freezed,
    Object? altIds = freezed,
    Object? altRN = freezed,
    Object? tags = freezed,
    Object? major = freezed,
    Object? components = null,
    Object? endDate = freezed,
    Object? finished = freezed,
    Object? branch = freezed,
    Object? finishDate = freezed,
    Object? finish1cDate = freezed,
    Object? photos = freezed,
    Object? videosTest = freezed,
    Object? mark = freezed,
    Object? deleted = freezed,
    Object? order = freezed,
    Object? newBool = freezed,
    Object? assembled = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_value.copyWith(
      repairNumber: freezed == repairNumber
          ? _value.repairNumber
          : repairNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      mainID: freezed == mainID
          ? _value.mainID
          : mainID // ignore: cast_nullable_to_non_nullable
              as String?,
      altIds: freezed == altIds
          ? _value.altIds
          : altIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      altRN: freezed == altRN
          ? _value.altRN
          : altRN // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      tags: freezed == tags
          ? _value.tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<ItemTag>?,
      major: freezed == major
          ? _value.major
          : major // ignore: cast_nullable_to_non_nullable
              as bool?,
      components: null == components
          ? _value.components
          : components // ignore: cast_nullable_to_non_nullable
              as List<ComponentModel>,
      endDate: freezed == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as double?,
      finished: freezed == finished
          ? _value.finished
          : finished // ignore: cast_nullable_to_non_nullable
              as bool?,
      branch: freezed == branch
          ? _value.branch
          : branch // ignore: cast_nullable_to_non_nullable
              as int?,
      finishDate: freezed == finishDate
          ? _value.finishDate
          : finishDate // ignore: cast_nullable_to_non_nullable
              as double?,
      finish1cDate: freezed == finish1cDate
          ? _value.finish1cDate
          : finish1cDate // ignore: cast_nullable_to_non_nullable
              as double?,
      photos: freezed == photos
          ? _value.photos
          : photos // ignore: cast_nullable_to_non_nullable
              as List<PhotoModel>?,
      videosTest: freezed == videosTest
          ? _value.videosTest
          : videosTest // ignore: cast_nullable_to_non_nullable
              as List<VideoTestModel>?,
      mark: freezed == mark
          ? _value.mark
          : mark // ignore: cast_nullable_to_non_nullable
              as bool?,
      deleted: freezed == deleted
          ? _value.deleted
          : deleted // ignore: cast_nullable_to_non_nullable
              as bool?,
      order: freezed == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as OrderModel?,
      newBool: freezed == newBool
          ? _value.newBool
          : newBool // ignore: cast_nullable_to_non_nullable
              as bool?,
      assembled: freezed == assembled
          ? _value.assembled
          : assembled // ignore: cast_nullable_to_non_nullable
              as bool?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as double?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }

  /// Create a copy of ItemModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $OrderModelCopyWith<$Res>? get order {
    if (_value.order == null) {
      return null;
    }

    return $OrderModelCopyWith<$Res>(_value.order!, (value) {
      return _then(_value.copyWith(order: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ItemModelImplCopyWith<$Res>
    implements $ItemModelCopyWith<$Res> {
  factory _$$ItemModelImplCopyWith(
          _$ItemModelImpl value, $Res Function(_$ItemModelImpl) then) =
      __$$ItemModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? repairNumber,
      String? mainID,
      List<String>? altIds,
      List<String>? altRN,
      List<ItemTag>? tags,
      bool? major,
      List<ComponentModel> components,
      double? endDate,
      bool? finished,
      int? branch,
      double? finishDate,
      double? finish1cDate,
      List<PhotoModel>? photos,
      List<VideoTestModel>? videosTest,
      bool? mark,
      bool? deleted,
      OrderModel? order,
      @JsonKey(name: 'new') bool? newBool,
      bool? assembled,
      double? createdAt,
      double? updatedAt});

  @override
  $OrderModelCopyWith<$Res>? get order;
}

/// @nodoc
class __$$ItemModelImplCopyWithImpl<$Res>
    extends _$ItemModelCopyWithImpl<$Res, _$ItemModelImpl>
    implements _$$ItemModelImplCopyWith<$Res> {
  __$$ItemModelImplCopyWithImpl(
      _$ItemModelImpl _value, $Res Function(_$ItemModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ItemModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? repairNumber = freezed,
    Object? mainID = freezed,
    Object? altIds = freezed,
    Object? altRN = freezed,
    Object? tags = freezed,
    Object? major = freezed,
    Object? components = null,
    Object? endDate = freezed,
    Object? finished = freezed,
    Object? branch = freezed,
    Object? finishDate = freezed,
    Object? finish1cDate = freezed,
    Object? photos = freezed,
    Object? videosTest = freezed,
    Object? mark = freezed,
    Object? deleted = freezed,
    Object? order = freezed,
    Object? newBool = freezed,
    Object? assembled = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_$ItemModelImpl(
      repairNumber: freezed == repairNumber
          ? _value.repairNumber
          : repairNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      mainID: freezed == mainID
          ? _value.mainID
          : mainID // ignore: cast_nullable_to_non_nullable
              as String?,
      altIds: freezed == altIds
          ? _value._altIds
          : altIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      altRN: freezed == altRN
          ? _value._altRN
          : altRN // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      tags: freezed == tags
          ? _value._tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<ItemTag>?,
      major: freezed == major
          ? _value.major
          : major // ignore: cast_nullable_to_non_nullable
              as bool?,
      components: null == components
          ? _value._components
          : components // ignore: cast_nullable_to_non_nullable
              as List<ComponentModel>,
      endDate: freezed == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as double?,
      finished: freezed == finished
          ? _value.finished
          : finished // ignore: cast_nullable_to_non_nullable
              as bool?,
      branch: freezed == branch
          ? _value.branch
          : branch // ignore: cast_nullable_to_non_nullable
              as int?,
      finishDate: freezed == finishDate
          ? _value.finishDate
          : finishDate // ignore: cast_nullable_to_non_nullable
              as double?,
      finish1cDate: freezed == finish1cDate
          ? _value.finish1cDate
          : finish1cDate // ignore: cast_nullable_to_non_nullable
              as double?,
      photos: freezed == photos
          ? _value._photos
          : photos // ignore: cast_nullable_to_non_nullable
              as List<PhotoModel>?,
      videosTest: freezed == videosTest
          ? _value._videosTest
          : videosTest // ignore: cast_nullable_to_non_nullable
              as List<VideoTestModel>?,
      mark: freezed == mark
          ? _value.mark
          : mark // ignore: cast_nullable_to_non_nullable
              as bool?,
      deleted: freezed == deleted
          ? _value.deleted
          : deleted // ignore: cast_nullable_to_non_nullable
              as bool?,
      order: freezed == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as OrderModel?,
      newBool: freezed == newBool
          ? _value.newBool
          : newBool // ignore: cast_nullable_to_non_nullable
              as bool?,
      assembled: freezed == assembled
          ? _value.assembled
          : assembled // ignore: cast_nullable_to_non_nullable
              as bool?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as double?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class _$ItemModelImpl extends _ItemModel with DiagnosticableTreeMixin {
  const _$ItemModelImpl(
      {this.repairNumber,
      this.mainID,
      final List<String>? altIds,
      final List<String>? altRN,
      final List<ItemTag>? tags,
      this.major,
      final List<ComponentModel> components = const [],
      this.endDate,
      this.finished,
      this.branch,
      this.finishDate,
      this.finish1cDate,
      final List<PhotoModel>? photos,
      final List<VideoTestModel>? videosTest,
      this.mark,
      this.deleted,
      this.order,
      @JsonKey(name: 'new') this.newBool,
      this.assembled,
      this.createdAt,
      this.updatedAt})
      : _altIds = altIds,
        _altRN = altRN,
        _tags = tags,
        _components = components,
        _photos = photos,
        _videosTest = videosTest,
        super._();

  factory _$ItemModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ItemModelImplFromJson(json);

  @override
  final String? repairNumber;
  @override
  final String? mainID;
  final List<String>? _altIds;
  @override
  List<String>? get altIds {
    final value = _altIds;
    if (value == null) return null;
    if (_altIds is EqualUnmodifiableListView) return _altIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<String>? _altRN;
  @override
  List<String>? get altRN {
    final value = _altRN;
    if (value == null) return null;
    if (_altRN is EqualUnmodifiableListView) return _altRN;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<ItemTag>? _tags;
  @override
  List<ItemTag>? get tags {
    final value = _tags;
    if (value == null) return null;
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final bool? major;
  final List<ComponentModel> _components;
  @override
  @JsonKey()
  List<ComponentModel> get components {
    if (_components is EqualUnmodifiableListView) return _components;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_components);
  }

  @override
  final double? endDate;
  @override
  final bool? finished;
  @override
  final int? branch;
  @override
  final double? finishDate;
  @override
  final double? finish1cDate;
  final List<PhotoModel>? _photos;
  @override
  List<PhotoModel>? get photos {
    final value = _photos;
    if (value == null) return null;
    if (_photos is EqualUnmodifiableListView) return _photos;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<VideoTestModel>? _videosTest;
  @override
  List<VideoTestModel>? get videosTest {
    final value = _videosTest;
    if (value == null) return null;
    if (_videosTest is EqualUnmodifiableListView) return _videosTest;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final bool? mark;
  @override
  final bool? deleted;
  @override
  final OrderModel? order;
  @override
  @JsonKey(name: 'new')
  final bool? newBool;
  @override
  final bool? assembled;
  @override
  final double? createdAt;
  @override
  final double? updatedAt;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ItemModel(repairNumber: $repairNumber, mainID: $mainID, altIds: $altIds, altRN: $altRN, tags: $tags, major: $major, components: $components, endDate: $endDate, finished: $finished, branch: $branch, finishDate: $finishDate, finish1cDate: $finish1cDate, photos: $photos, videosTest: $videosTest, mark: $mark, deleted: $deleted, order: $order, newBool: $newBool, assembled: $assembled, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'ItemModel'))
      ..add(DiagnosticsProperty('repairNumber', repairNumber))
      ..add(DiagnosticsProperty('mainID', mainID))
      ..add(DiagnosticsProperty('altIds', altIds))
      ..add(DiagnosticsProperty('altRN', altRN))
      ..add(DiagnosticsProperty('tags', tags))
      ..add(DiagnosticsProperty('major', major))
      ..add(DiagnosticsProperty('components', components))
      ..add(DiagnosticsProperty('endDate', endDate))
      ..add(DiagnosticsProperty('finished', finished))
      ..add(DiagnosticsProperty('branch', branch))
      ..add(DiagnosticsProperty('finishDate', finishDate))
      ..add(DiagnosticsProperty('finish1cDate', finish1cDate))
      ..add(DiagnosticsProperty('photos', photos))
      ..add(DiagnosticsProperty('videosTest', videosTest))
      ..add(DiagnosticsProperty('mark', mark))
      ..add(DiagnosticsProperty('deleted', deleted))
      ..add(DiagnosticsProperty('order', order))
      ..add(DiagnosticsProperty('newBool', newBool))
      ..add(DiagnosticsProperty('assembled', assembled))
      ..add(DiagnosticsProperty('createdAt', createdAt))
      ..add(DiagnosticsProperty('updatedAt', updatedAt));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ItemModelImpl &&
            (identical(other.repairNumber, repairNumber) ||
                other.repairNumber == repairNumber) &&
            (identical(other.mainID, mainID) || other.mainID == mainID) &&
            const DeepCollectionEquality().equals(other._altIds, _altIds) &&
            const DeepCollectionEquality().equals(other._altRN, _altRN) &&
            const DeepCollectionEquality().equals(other._tags, _tags) &&
            (identical(other.major, major) || other.major == major) &&
            const DeepCollectionEquality()
                .equals(other._components, _components) &&
            (identical(other.endDate, endDate) || other.endDate == endDate) &&
            (identical(other.finished, finished) ||
                other.finished == finished) &&
            (identical(other.branch, branch) || other.branch == branch) &&
            (identical(other.finishDate, finishDate) ||
                other.finishDate == finishDate) &&
            (identical(other.finish1cDate, finish1cDate) ||
                other.finish1cDate == finish1cDate) &&
            const DeepCollectionEquality().equals(other._photos, _photos) &&
            const DeepCollectionEquality()
                .equals(other._videosTest, _videosTest) &&
            (identical(other.mark, mark) || other.mark == mark) &&
            (identical(other.deleted, deleted) || other.deleted == deleted) &&
            (identical(other.order, order) || other.order == order) &&
            (identical(other.newBool, newBool) || other.newBool == newBool) &&
            (identical(other.assembled, assembled) ||
                other.assembled == assembled) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        repairNumber,
        mainID,
        const DeepCollectionEquality().hash(_altIds),
        const DeepCollectionEquality().hash(_altRN),
        const DeepCollectionEquality().hash(_tags),
        major,
        const DeepCollectionEquality().hash(_components),
        endDate,
        finished,
        branch,
        finishDate,
        finish1cDate,
        const DeepCollectionEquality().hash(_photos),
        const DeepCollectionEquality().hash(_videosTest),
        mark,
        deleted,
        order,
        newBool,
        assembled,
        createdAt,
        updatedAt
      ]);

  /// Create a copy of ItemModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ItemModelImplCopyWith<_$ItemModelImpl> get copyWith =>
      __$$ItemModelImplCopyWithImpl<_$ItemModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ItemModelImplToJson(
      this,
    );
  }
}

abstract class _ItemModel extends ItemModel {
  const factory _ItemModel(
      {final String? repairNumber,
      final String? mainID,
      final List<String>? altIds,
      final List<String>? altRN,
      final List<ItemTag>? tags,
      final bool? major,
      final List<ComponentModel> components,
      final double? endDate,
      final bool? finished,
      final int? branch,
      final double? finishDate,
      final double? finish1cDate,
      final List<PhotoModel>? photos,
      final List<VideoTestModel>? videosTest,
      final bool? mark,
      final bool? deleted,
      final OrderModel? order,
      @JsonKey(name: 'new') final bool? newBool,
      final bool? assembled,
      final double? createdAt,
      final double? updatedAt}) = _$ItemModelImpl;
  const _ItemModel._() : super._();

  factory _ItemModel.fromJson(Map<String, dynamic> json) =
      _$ItemModelImpl.fromJson;

  @override
  String? get repairNumber;
  @override
  String? get mainID;
  @override
  List<String>? get altIds;
  @override
  List<String>? get altRN;
  @override
  List<ItemTag>? get tags;
  @override
  bool? get major;
  @override
  List<ComponentModel> get components;
  @override
  double? get endDate;
  @override
  bool? get finished;
  @override
  int? get branch;
  @override
  double? get finishDate;
  @override
  double? get finish1cDate;
  @override
  List<PhotoModel>? get photos;
  @override
  List<VideoTestModel>? get videosTest;
  @override
  bool? get mark;
  @override
  bool? get deleted;
  @override
  OrderModel? get order;
  @override
  @JsonKey(name: 'new')
  bool? get newBool;
  @override
  bool? get assembled;
  @override
  double? get createdAt;
  @override
  double? get updatedAt;

  /// Create a copy of ItemModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ItemModelImplCopyWith<_$ItemModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ItemListModel _$ItemListModelFromJson(Map<String, dynamic> json) {
  return _ItemListModel.fromJson(json);
}

/// @nodoc
mixin _$ItemListModel {
  List<ItemModel> get data => throw _privateConstructorUsedError;

  /// Serializes this ItemListModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ItemListModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ItemListModelCopyWith<ItemListModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ItemListModelCopyWith<$Res> {
  factory $ItemListModelCopyWith(
          ItemListModel value, $Res Function(ItemListModel) then) =
      _$ItemListModelCopyWithImpl<$Res, ItemListModel>;
  @useResult
  $Res call({List<ItemModel> data});
}

/// @nodoc
class _$ItemListModelCopyWithImpl<$Res, $Val extends ItemListModel>
    implements $ItemListModelCopyWith<$Res> {
  _$ItemListModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ItemListModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
  }) {
    return _then(_value.copyWith(
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<ItemModel>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ItemListModelImplCopyWith<$Res>
    implements $ItemListModelCopyWith<$Res> {
  factory _$$ItemListModelImplCopyWith(
          _$ItemListModelImpl value, $Res Function(_$ItemListModelImpl) then) =
      __$$ItemListModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<ItemModel> data});
}

/// @nodoc
class __$$ItemListModelImplCopyWithImpl<$Res>
    extends _$ItemListModelCopyWithImpl<$Res, _$ItemListModelImpl>
    implements _$$ItemListModelImplCopyWith<$Res> {
  __$$ItemListModelImplCopyWithImpl(
      _$ItemListModelImpl _value, $Res Function(_$ItemListModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ItemListModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
  }) {
    return _then(_$ItemListModelImpl(
      null == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as List<ItemModel>,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$ItemListModelImpl
    with DiagnosticableTreeMixin
    implements _ItemListModel {
  _$ItemListModelImpl(final List<ItemModel> data) : _data = data;

  factory _$ItemListModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ItemListModelImplFromJson(json);

  final List<ItemModel> _data;
  @override
  List<ItemModel> get data {
    if (_data is EqualUnmodifiableListView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_data);
  }

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ItemListModel(data: $data)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'ItemListModel'))
      ..add(DiagnosticsProperty('data', data));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ItemListModelImpl &&
            const DeepCollectionEquality().equals(other._data, _data));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_data));

  /// Create a copy of ItemListModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ItemListModelImplCopyWith<_$ItemListModelImpl> get copyWith =>
      __$$ItemListModelImplCopyWithImpl<_$ItemListModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ItemListModelImplToJson(
      this,
    );
  }
}

abstract class _ItemListModel implements ItemListModel {
  factory _ItemListModel(final List<ItemModel> data) = _$ItemListModelImpl;

  factory _ItemListModel.fromJson(Map<String, dynamic> json) =
      _$ItemListModelImpl.fromJson;

  @override
  List<ItemModel> get data;

  /// Create a copy of ItemListModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ItemListModelImplCopyWith<_$ItemListModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
