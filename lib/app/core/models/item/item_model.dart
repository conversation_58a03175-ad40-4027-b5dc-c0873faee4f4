import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:uer_flutter/app/core/models/stats/tags_stats_model.dart';
import 'package:uer_flutter/app/helpers/date_extension.dart';

import '../../../helpers/KeyValueObject.dart';
import '../component/component_model.dart';
import '../enums/component_type_enum.dart';
import '../order/order_model.dart';
import '../photo/photo_model.dart';
import '../video_test/video_test_model.dart';

part 'item_model.freezed.dart';
part 'item_model.g.dart';

// comment
@freezed
class ItemModel with _$ItemModel {
  const ItemModel._();
  @JsonSerializable(explicitToJson: true, includeIfNull: false)
  const factory ItemModel({
    String? repairNumber,
    String? mainID,
    List<String>? altIds,
    List<String>? altRN,
    List<ItemTag>? tags,
    bool? major,
    @Default([]) List<ComponentModel> components,
    double? endDate,
    bool? finished,
    int? branch,
    double? finishDate,
    double? finish1cDate,
    List<PhotoModel>? photos,
    List<VideoTestModel>? videosTest,
    bool? mark,
    bool? deleted,
    OrderModel? order,
    @JsonKey(name: 'new') bool? newBool,
    bool? assembled,
    double? createdAt,
    double? updatedAt,
  }) = _ItemModel;

  factory ItemModel.fromJson(Map<String, Object?> json) =>
      _$ItemModelFromJson(json);

  String repairNumberFormat() {
    return "№ $repairNumber";
  }

  ComponentModel? getComponentForID(String identifier) {
    for (var component in components) {
      if (component.identifier == identifier) {
        return component;
      }
    }

    return null;
  }

  bool dcMachine() {
    var name = order?.name?.toLowerCase();

    if (name?.contains("постоян") == true) {
      return true;
    }

    return false;
  }

  bool transform() {
    var name = order?.name?.toLowerCase();

    if (name?.contains("трансфор") == true) {
      return true;
    }

    return false;
  }

  String getComponentsDesc() {
    var str = "";

    for (var component in components) {
      if (str.isNotEmpty) {
        str += " | ";
      }

      switch (component.type) {
        case ComponentType.stator:
          str += "C";
          break;
        case ComponentType.rotor:
          str += "Р";
          break;
        case ComponentType.rollingBearing:
          str += "ПК";
          break;
        case ComponentType.slidingBearing:
          str += "ПС";
          break;
        case ComponentType.transformer:
          str += "ТР";
          break;
        case ComponentType.inductor:
          str += "ИН";
          break;
        case ComponentType.anchor:
          str += "ЯК";
          break;
        default:
      }
    }

    return str;
  }

  bool hasInductorOrStator() {
    for (var comp in components) {
      if (comp.type == ComponentType.stator ||
          comp.type == ComponentType.inductor) {
        return true;
      }
    }

    return false;
  }

  bool checkAllComponentsOnSameArea() {
    var area = components.first.currentArea;

    for (var component in components) {
      if (component.currentArea != area) {
        return false;
      }
    }

    return true;
  }

  bool allJobsAndTaskFinished() {
    for (var comp in components) {
      var hist = comp.getActiveHistory();

      if (hist.isNotEmpty) {
        return false;
      }
    }

    return true;
  }

  String getStartDate() {
    // дата приёмки

    if (components.isEmpty) {
      return "---";
    }

    var component = components.first;
    var time = component.newHistory?.first.createdAt ?? 0;

    if (time == 0) {
      return "---";
    }

    return DateTime.fromMillisecondsSinceEpoch(time.toInt() * 1000)
        .dateShortString();
  }

  String getFinishDate() {
    // дата завершения работ

    if (finishDate == null) {
      return "----";
    }

    return DateTime.fromMillisecondsSinceEpoch(finishDate!.toInt() * 1000)
        .dateShortString();
  }

  String getEndContractDate() {
    // дата завершения по договору
    return DateTime.fromMillisecondsSinceEpoch((endDate?.toInt() ?? 1) * 1000)
        .dateShortString();
  }

  DateTime getEndContactDateTime() {
    // дата завершения по договору
    return DateTime.fromMillisecondsSinceEpoch((endDate?.toInt() ?? 1) * 1000);
  }

  List<KeyValueObject> getInfoItem(bool full) {
    List<KeyValueObject> arr = [];

    var altIdsStr = altIds?.join(", ");
    var altRNStr = altRN?.join(", ");

    if (full == false) {
      arr.add(KeyValueObject("Статус ЗПР 1С", order?.statusOrder ?? "----"));
      arr.add(KeyValueObject("Менеджер", order?.manager ?? "----"));
      arr.add(KeyValueObject("Номенклатура:", order?.name ?? "----"));
      arr.add(KeyValueObject("Оборудование:", order?.equipment ?? "----"));
      arr.add(KeyValueObject("Контрагент:", order?.client?.name ?? "----"));
      // arr.add(KeyValueObject("Капитальный ремонт:", major ? "Да" : "Нет"));
      arr.add(KeyValueObject("Дата приёмки:", getStartDate()));
      arr.add(
          KeyValueObject("Дата завершения по договору:", getEndContractDate()));
      arr.add(KeyValueObject(
          "Состояние:", assembled == true ? "Собран" : "Разобран"));
      arr.add(KeyValueObject("Дата создания ЗПР:", order?.date ?? "----"));
    }

    if (full == true) {
      arr.add(KeyValueObject("Статус ЗПР 1С", order?.statusOrder ?? "----"));
      arr.add(KeyValueObject("ИД ЗПР 1С", mainID ?? "----"));

      if (altIdsStr?.isNotEmpty == true) {
        arr.add(KeyValueObject("ИД слитых ЗПР", altIdsStr ?? "----"));
      }

      if (altRNStr?.isNotEmpty == true) {
        arr.add(KeyValueObject("Рем.номера слитых ЗПР", altRNStr ?? "----"));
      }

      arr.add(KeyValueObject("Менеджер", order?.manager ?? "----"));
      arr.add(KeyValueObject("Номенклатура:", order?.name ?? "----"));
      arr.add(KeyValueObject("Оборудование:", order?.equipment ?? "----"));
      arr.add(KeyValueObject("Рем. номер:", order?.rn ?? "----"));
      arr.add(KeyValueObject("Рем. номер ОГК:", order?.rnOGK ?? "----"));
      arr.add(KeyValueObject(
          "Капитальный ремонт:", (major ?? false) ? "Да" : "Нет"));
      arr.add(KeyValueObject("Дата создания ЗПР:", order?.date ?? "----"));
      arr.add(KeyValueObject("Дата приёмки:", getStartDate()));
      arr.add(
          KeyValueObject("Дата завершения по договору:", getEndContractDate()));
      arr.add(KeyValueObject("Дата завершения работ:", getFinishDate()));
      arr.add(KeyValueObject(
          "Состояние:", assembled == true ? "Собран" : "Разобран"));
      arr.add(KeyValueObject("Филиал:", order?.branch?.name ?? "----"));
      arr.add(KeyValueObject("Контрагент:", order?.client?.name ?? "----"));
      arr.add(KeyValueObject("ИНН Контрагента:", order?.client?.inn ?? "----"));
      arr.add(KeyValueObject("Мощность, кВт:", order?.power ?? "----"));
      arr.add(KeyValueObject("Напряжение, В:", order?.voltage ?? "----"));
      arr.add(KeyValueObject("Обороты, об/мин:", order?.turnovers ?? "----"));
      arr.add(KeyValueObject("Вес, кг:", order?.weight ?? "----"));
      arr.add(KeyValueObject("НапряжениеВН, В:", order?.voltageVN ?? "----"));
      arr.add(KeyValueObject("НапряжениеНН, В:", order?.voltageNN ?? "----"));
      arr.add(KeyValueObject("НапряжениеСН, В", order?.voltageSN ?? "----"));
      arr.add(KeyValueObject("Ток, А:", order?.amperage ?? "----"));
    }

    return arr;
  }
}

@freezed
class ItemListModel with _$ItemListModel {
  @JsonSerializable(includeIfNull: false)
  factory ItemListModel(List<ItemModel> data) = _ItemListModel;

  factory ItemListModel.fromJson(Map<String, Object?> json) =>
      _$ItemListModelFromJson(json);
}
