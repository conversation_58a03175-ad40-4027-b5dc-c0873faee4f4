import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter/foundation.dart';

part 'stats_ssz_time_model.freezed.dart';
part 'stats_ssz_time_model.g.dart';

@freezed
class StatsSSZTimeModel with _$StatsSSZTimeModel {
  const StatsSSZTimeModel._();
  @JsonSerializable(explicitToJson: true, includeIfNull: false)
  const factory StatsSSZTimeModel({
    required String name,
    required int area,
    required double workerTime,
  }) = _StatsSSZTimeModel;

  factory StatsSSZTimeModel.fromJson(Map<String, Object?> json) =>
      _$StatsSSZTimeModelFromJson(json);
}
