import 'package:freezed_annotation/freezed_annotation.dart';

part 'stats_sum_works_time_model.freezed.dart';
part 'stats_sum_works_time_model.g.dart';

@freezed
class StatsSumWorkHoursByMonthModel with _$StatsSumWorkHoursByMonthModel {
  const StatsSumWorkHoursByMonthModel._();
  @JsonSerializable(explicitToJson: true, includeIfNull: false)
  const factory StatsSumWorkHoursByMonthModel({
    Map<String, AverageWithTypeModel>? monthlyAverages,
  }) = _StatsSumWorkHoursByMonthModel;

  factory StatsSumWorkHoursByMonthModel.fromJson(Map<String, Object?> json) =>
      _$StatsSumWorkHoursByMonthModelFromJson(json);
}

@freezed
class AverageWithTypeModel with _$AverageWithTypeModel {
  const factory AverageWithTypeModel({
    int? average,
    Map<String, double>? byType,
    int? count,
  }) = _AverageWithTypeModel;

  factory AverageWithTypeModel.fromJson(Map<String, Object?> json) =>
      _$AverageWithTypeModelFromJson(json);
}

// downtimes
@freezed
class AverageWithTypeDowntimesModel with _$AverageWithTypeDowntimesModel {
  const factory AverageWithTypeDowntimesModel({
    int? average,
    Map<String, DowntimeByType>? byType,
    int? count,
  }) = _AverageWithTypeDowntimesModel;

  factory AverageWithTypeDowntimesModel.fromJson(Map<String, Object?> json) =>
      _$AverageWithTypeDowntimesModelFromJson(json);
}

@freezed
class DowntimeByType with _$DowntimeByType {
  @JsonSerializable(includeIfNull: false)
  const factory DowntimeByType({
    double? average,
    int? count,
  }) = _DowntimeByType;

  factory DowntimeByType.fromJson(Map<String, Object?> json) =>
      _$DowntimeByTypeFromJson(json);
}

@freezed
class StatsAverageDowntimesByMonthModel
    with _$StatsAverageDowntimesByMonthModel {
  @JsonSerializable(includeIfNull: false)
  const factory StatsAverageDowntimesByMonthModel({
    Map<String, AverageWithTypeDowntimesModel>? monthlyAverages,
    double? yearlyAverage,
    double? prevYearAverage,
    double? difference,
    String? comparisonText,
    double? totalOutageTime,
    double? prevYearTotalOutageTime,
    int? totalOutageItems,
    int? prevYearOutageItems,
  }) = _StatsAverageDowntimesByMonthModel;

  factory StatsAverageDowntimesByMonthModel.fromJson(
          Map<String, Object?> json) =>
      _$StatsAverageDowntimesByMonthModelFromJson(json);
}

// @freezed
// class StatsSumWorkHoursByMonthListModel
//     with _$StatsSumWorkHoursByMonthListModel {
//   factory StatsSumWorkHoursByMonthListModel({
//     required List<StatsSumWorkHoursByMonthModel> data,
//   }) = _StatsSumWorkHoursByMonthListModel;

//   factory StatsSumWorkHoursByMonthListModel.fromJson(
//           Map<String, dynamic> json) =>
//       _$StatsSumWorkHoursByMonthListModelFromJson(json);
// }
