// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'stats_sum_works_time_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$StatsSumWorkHoursByMonthModelImpl
    _$$StatsSumWorkHoursByMonthModelImplFromJson(Map<String, dynamic> json) =>
        _$StatsSumWorkHoursByMonthModelImpl(
          monthlyAverages:
              (json['monthlyAverages'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(
                k, AverageWithTypeModel.fromJson(e as Map<String, dynamic>)),
          ),
        );

Map<String, dynamic> _$$StatsSumWorkHoursByMonthModelImplToJson(
        _$StatsSumWorkHoursByMonthModelImpl instance) =>
    <String, dynamic>{
      if (instance.monthlyAverages?.map((k, e) => MapEntry(k, e.toJson()))
          case final value?)
        'monthlyAverages': value,
    };

_$AverageWithTypeModelImpl _$$AverageWithTypeModelImplFromJson(
        Map<String, dynamic> json) =>
    _$AverageWithTypeModelImpl(
      average: (json['average'] as num?)?.toInt(),
      byType: (json['byType'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
      count: (json['count'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$AverageWithTypeModelImplToJson(
        _$AverageWithTypeModelImpl instance) =>
    <String, dynamic>{
      'average': instance.average,
      'byType': instance.byType,
      'count': instance.count,
    };

_$AverageWithTypeDowntimesModelImpl
    _$$AverageWithTypeDowntimesModelImplFromJson(Map<String, dynamic> json) =>
        _$AverageWithTypeDowntimesModelImpl(
          average: (json['average'] as num?)?.toInt(),
          byType: (json['byType'] as Map<String, dynamic>?)?.map(
            (k, e) =>
                MapEntry(k, DowntimeByType.fromJson(e as Map<String, dynamic>)),
          ),
          count: (json['count'] as num?)?.toInt(),
        );

Map<String, dynamic> _$$AverageWithTypeDowntimesModelImplToJson(
        _$AverageWithTypeDowntimesModelImpl instance) =>
    <String, dynamic>{
      'average': instance.average,
      'byType': instance.byType,
      'count': instance.count,
    };

_$DowntimeByTypeImpl _$$DowntimeByTypeImplFromJson(Map<String, dynamic> json) =>
    _$DowntimeByTypeImpl(
      average: (json['average'] as num?)?.toDouble(),
      count: (json['count'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$DowntimeByTypeImplToJson(
        _$DowntimeByTypeImpl instance) =>
    <String, dynamic>{
      if (instance.average case final value?) 'average': value,
      if (instance.count case final value?) 'count': value,
    };

_$StatsAverageDowntimesByMonthModelImpl
    _$$StatsAverageDowntimesByMonthModelImplFromJson(
            Map<String, dynamic> json) =>
        _$StatsAverageDowntimesByMonthModelImpl(
          monthlyAverages:
              (json['monthlyAverages'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(
                k,
                AverageWithTypeDowntimesModel.fromJson(
                    e as Map<String, dynamic>)),
          ),
          yearlyAverage: (json['yearlyAverage'] as num?)?.toDouble(),
          prevYearAverage: (json['prevYearAverage'] as num?)?.toDouble(),
          difference: (json['difference'] as num?)?.toDouble(),
          comparisonText: json['comparisonText'] as String?,
          totalOutageTime: (json['totalOutageTime'] as num?)?.toDouble(),
          prevYearTotalOutageTime:
              (json['prevYearTotalOutageTime'] as num?)?.toDouble(),
          totalOutageItems: (json['totalOutageItems'] as num?)?.toInt(),
          prevYearOutageItems: (json['prevYearOutageItems'] as num?)?.toInt(),
        );

Map<String, dynamic> _$$StatsAverageDowntimesByMonthModelImplToJson(
        _$StatsAverageDowntimesByMonthModelImpl instance) =>
    <String, dynamic>{
      if (instance.monthlyAverages case final value?) 'monthlyAverages': value,
      if (instance.yearlyAverage case final value?) 'yearlyAverage': value,
      if (instance.prevYearAverage case final value?) 'prevYearAverage': value,
      if (instance.difference case final value?) 'difference': value,
      if (instance.comparisonText case final value?) 'comparisonText': value,
      if (instance.totalOutageTime case final value?) 'totalOutageTime': value,
      if (instance.prevYearTotalOutageTime case final value?)
        'prevYearTotalOutageTime': value,
      if (instance.totalOutageItems case final value?)
        'totalOutageItems': value,
      if (instance.prevYearOutageItems case final value?)
        'prevYearOutageItems': value,
    };
