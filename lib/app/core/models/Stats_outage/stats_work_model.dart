import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter/foundation.dart';

part 'stats_work_model.freezed.dart';
part 'stats_work_model.g.dart';

@freezed
class StatsWorkModel with _$StatsWorkModel {
  const StatsWorkModel._();
  @JsonSerializable(explicitToJson: true, includeIfNull: false)
  const factory StatsWorkModel({
    required String name,
    required String componentName,
    int? area,
    String? dateStr,
    required double workTime,
    required double workerTime,
    required double outAgeTime,
    required double statusTime,
    required double percentWork,
    required double percentOutAge,
  }) = _StatsWorkModel;

  factory StatsWorkModel.fromJson(Map<String, Object?> json) =>
      _$StatsWorkModelFromJson(json);
}
