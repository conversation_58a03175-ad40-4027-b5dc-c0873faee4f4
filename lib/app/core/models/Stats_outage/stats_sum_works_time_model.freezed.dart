// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'stats_sum_works_time_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

StatsSumWorkHoursByMonthModel _$StatsSumWorkHoursByMonthModelFromJson(
    Map<String, dynamic> json) {
  return _StatsSumWorkHoursByMonthModel.fromJson(json);
}

/// @nodoc
mixin _$StatsSumWorkHoursByMonthModel {
  Map<String, AverageWithTypeModel>? get monthlyAverages =>
      throw _privateConstructorUsedError;

  /// Serializes this StatsSumWorkHoursByMonthModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of StatsSumWorkHoursByMonthModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $StatsSumWorkHoursByMonthModelCopyWith<StatsSumWorkHoursByMonthModel>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StatsSumWorkHoursByMonthModelCopyWith<$Res> {
  factory $StatsSumWorkHoursByMonthModelCopyWith(
          StatsSumWorkHoursByMonthModel value,
          $Res Function(StatsSumWorkHoursByMonthModel) then) =
      _$StatsSumWorkHoursByMonthModelCopyWithImpl<$Res,
          StatsSumWorkHoursByMonthModel>;
  @useResult
  $Res call({Map<String, AverageWithTypeModel>? monthlyAverages});
}

/// @nodoc
class _$StatsSumWorkHoursByMonthModelCopyWithImpl<$Res,
        $Val extends StatsSumWorkHoursByMonthModel>
    implements $StatsSumWorkHoursByMonthModelCopyWith<$Res> {
  _$StatsSumWorkHoursByMonthModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of StatsSumWorkHoursByMonthModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? monthlyAverages = freezed,
  }) {
    return _then(_value.copyWith(
      monthlyAverages: freezed == monthlyAverages
          ? _value.monthlyAverages
          : monthlyAverages // ignore: cast_nullable_to_non_nullable
              as Map<String, AverageWithTypeModel>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$StatsSumWorkHoursByMonthModelImplCopyWith<$Res>
    implements $StatsSumWorkHoursByMonthModelCopyWith<$Res> {
  factory _$$StatsSumWorkHoursByMonthModelImplCopyWith(
          _$StatsSumWorkHoursByMonthModelImpl value,
          $Res Function(_$StatsSumWorkHoursByMonthModelImpl) then) =
      __$$StatsSumWorkHoursByMonthModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({Map<String, AverageWithTypeModel>? monthlyAverages});
}

/// @nodoc
class __$$StatsSumWorkHoursByMonthModelImplCopyWithImpl<$Res>
    extends _$StatsSumWorkHoursByMonthModelCopyWithImpl<$Res,
        _$StatsSumWorkHoursByMonthModelImpl>
    implements _$$StatsSumWorkHoursByMonthModelImplCopyWith<$Res> {
  __$$StatsSumWorkHoursByMonthModelImplCopyWithImpl(
      _$StatsSumWorkHoursByMonthModelImpl _value,
      $Res Function(_$StatsSumWorkHoursByMonthModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of StatsSumWorkHoursByMonthModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? monthlyAverages = freezed,
  }) {
    return _then(_$StatsSumWorkHoursByMonthModelImpl(
      monthlyAverages: freezed == monthlyAverages
          ? _value._monthlyAverages
          : monthlyAverages // ignore: cast_nullable_to_non_nullable
              as Map<String, AverageWithTypeModel>?,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class _$StatsSumWorkHoursByMonthModelImpl
    extends _StatsSumWorkHoursByMonthModel {
  const _$StatsSumWorkHoursByMonthModelImpl(
      {final Map<String, AverageWithTypeModel>? monthlyAverages})
      : _monthlyAverages = monthlyAverages,
        super._();

  factory _$StatsSumWorkHoursByMonthModelImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$StatsSumWorkHoursByMonthModelImplFromJson(json);

  final Map<String, AverageWithTypeModel>? _monthlyAverages;
  @override
  Map<String, AverageWithTypeModel>? get monthlyAverages {
    final value = _monthlyAverages;
    if (value == null) return null;
    if (_monthlyAverages is EqualUnmodifiableMapView) return _monthlyAverages;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'StatsSumWorkHoursByMonthModel(monthlyAverages: $monthlyAverages)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StatsSumWorkHoursByMonthModelImpl &&
            const DeepCollectionEquality()
                .equals(other._monthlyAverages, _monthlyAverages));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_monthlyAverages));

  /// Create a copy of StatsSumWorkHoursByMonthModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StatsSumWorkHoursByMonthModelImplCopyWith<
          _$StatsSumWorkHoursByMonthModelImpl>
      get copyWith => __$$StatsSumWorkHoursByMonthModelImplCopyWithImpl<
          _$StatsSumWorkHoursByMonthModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$StatsSumWorkHoursByMonthModelImplToJson(
      this,
    );
  }
}

abstract class _StatsSumWorkHoursByMonthModel
    extends StatsSumWorkHoursByMonthModel {
  const factory _StatsSumWorkHoursByMonthModel(
          {final Map<String, AverageWithTypeModel>? monthlyAverages}) =
      _$StatsSumWorkHoursByMonthModelImpl;
  const _StatsSumWorkHoursByMonthModel._() : super._();

  factory _StatsSumWorkHoursByMonthModel.fromJson(Map<String, dynamic> json) =
      _$StatsSumWorkHoursByMonthModelImpl.fromJson;

  @override
  Map<String, AverageWithTypeModel>? get monthlyAverages;

  /// Create a copy of StatsSumWorkHoursByMonthModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StatsSumWorkHoursByMonthModelImplCopyWith<
          _$StatsSumWorkHoursByMonthModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}

AverageWithTypeModel _$AverageWithTypeModelFromJson(Map<String, dynamic> json) {
  return _AverageWithTypeModel.fromJson(json);
}

/// @nodoc
mixin _$AverageWithTypeModel {
  int? get average => throw _privateConstructorUsedError;
  Map<String, double>? get byType => throw _privateConstructorUsedError;
  int? get count => throw _privateConstructorUsedError;

  /// Serializes this AverageWithTypeModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AverageWithTypeModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AverageWithTypeModelCopyWith<AverageWithTypeModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AverageWithTypeModelCopyWith<$Res> {
  factory $AverageWithTypeModelCopyWith(AverageWithTypeModel value,
          $Res Function(AverageWithTypeModel) then) =
      _$AverageWithTypeModelCopyWithImpl<$Res, AverageWithTypeModel>;
  @useResult
  $Res call({int? average, Map<String, double>? byType, int? count});
}

/// @nodoc
class _$AverageWithTypeModelCopyWithImpl<$Res,
        $Val extends AverageWithTypeModel>
    implements $AverageWithTypeModelCopyWith<$Res> {
  _$AverageWithTypeModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AverageWithTypeModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? average = freezed,
    Object? byType = freezed,
    Object? count = freezed,
  }) {
    return _then(_value.copyWith(
      average: freezed == average
          ? _value.average
          : average // ignore: cast_nullable_to_non_nullable
              as int?,
      byType: freezed == byType
          ? _value.byType
          : byType // ignore: cast_nullable_to_non_nullable
              as Map<String, double>?,
      count: freezed == count
          ? _value.count
          : count // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AverageWithTypeModelImplCopyWith<$Res>
    implements $AverageWithTypeModelCopyWith<$Res> {
  factory _$$AverageWithTypeModelImplCopyWith(_$AverageWithTypeModelImpl value,
          $Res Function(_$AverageWithTypeModelImpl) then) =
      __$$AverageWithTypeModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? average, Map<String, double>? byType, int? count});
}

/// @nodoc
class __$$AverageWithTypeModelImplCopyWithImpl<$Res>
    extends _$AverageWithTypeModelCopyWithImpl<$Res, _$AverageWithTypeModelImpl>
    implements _$$AverageWithTypeModelImplCopyWith<$Res> {
  __$$AverageWithTypeModelImplCopyWithImpl(_$AverageWithTypeModelImpl _value,
      $Res Function(_$AverageWithTypeModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of AverageWithTypeModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? average = freezed,
    Object? byType = freezed,
    Object? count = freezed,
  }) {
    return _then(_$AverageWithTypeModelImpl(
      average: freezed == average
          ? _value.average
          : average // ignore: cast_nullable_to_non_nullable
              as int?,
      byType: freezed == byType
          ? _value._byType
          : byType // ignore: cast_nullable_to_non_nullable
              as Map<String, double>?,
      count: freezed == count
          ? _value.count
          : count // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AverageWithTypeModelImpl implements _AverageWithTypeModel {
  const _$AverageWithTypeModelImpl(
      {this.average, final Map<String, double>? byType, this.count})
      : _byType = byType;

  factory _$AverageWithTypeModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$AverageWithTypeModelImplFromJson(json);

  @override
  final int? average;
  final Map<String, double>? _byType;
  @override
  Map<String, double>? get byType {
    final value = _byType;
    if (value == null) return null;
    if (_byType is EqualUnmodifiableMapView) return _byType;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  final int? count;

  @override
  String toString() {
    return 'AverageWithTypeModel(average: $average, byType: $byType, count: $count)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AverageWithTypeModelImpl &&
            (identical(other.average, average) || other.average == average) &&
            const DeepCollectionEquality().equals(other._byType, _byType) &&
            (identical(other.count, count) || other.count == count));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, average,
      const DeepCollectionEquality().hash(_byType), count);

  /// Create a copy of AverageWithTypeModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AverageWithTypeModelImplCopyWith<_$AverageWithTypeModelImpl>
      get copyWith =>
          __$$AverageWithTypeModelImplCopyWithImpl<_$AverageWithTypeModelImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AverageWithTypeModelImplToJson(
      this,
    );
  }
}

abstract class _AverageWithTypeModel implements AverageWithTypeModel {
  const factory _AverageWithTypeModel(
      {final int? average,
      final Map<String, double>? byType,
      final int? count}) = _$AverageWithTypeModelImpl;

  factory _AverageWithTypeModel.fromJson(Map<String, dynamic> json) =
      _$AverageWithTypeModelImpl.fromJson;

  @override
  int? get average;
  @override
  Map<String, double>? get byType;
  @override
  int? get count;

  /// Create a copy of AverageWithTypeModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AverageWithTypeModelImplCopyWith<_$AverageWithTypeModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}

AverageWithTypeDowntimesModel _$AverageWithTypeDowntimesModelFromJson(
    Map<String, dynamic> json) {
  return _AverageWithTypeDowntimesModel.fromJson(json);
}

/// @nodoc
mixin _$AverageWithTypeDowntimesModel {
  int? get average => throw _privateConstructorUsedError;
  Map<String, DowntimeByType>? get byType => throw _privateConstructorUsedError;
  int? get count => throw _privateConstructorUsedError;

  /// Serializes this AverageWithTypeDowntimesModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AverageWithTypeDowntimesModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AverageWithTypeDowntimesModelCopyWith<AverageWithTypeDowntimesModel>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AverageWithTypeDowntimesModelCopyWith<$Res> {
  factory $AverageWithTypeDowntimesModelCopyWith(
          AverageWithTypeDowntimesModel value,
          $Res Function(AverageWithTypeDowntimesModel) then) =
      _$AverageWithTypeDowntimesModelCopyWithImpl<$Res,
          AverageWithTypeDowntimesModel>;
  @useResult
  $Res call({int? average, Map<String, DowntimeByType>? byType, int? count});
}

/// @nodoc
class _$AverageWithTypeDowntimesModelCopyWithImpl<$Res,
        $Val extends AverageWithTypeDowntimesModel>
    implements $AverageWithTypeDowntimesModelCopyWith<$Res> {
  _$AverageWithTypeDowntimesModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AverageWithTypeDowntimesModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? average = freezed,
    Object? byType = freezed,
    Object? count = freezed,
  }) {
    return _then(_value.copyWith(
      average: freezed == average
          ? _value.average
          : average // ignore: cast_nullable_to_non_nullable
              as int?,
      byType: freezed == byType
          ? _value.byType
          : byType // ignore: cast_nullable_to_non_nullable
              as Map<String, DowntimeByType>?,
      count: freezed == count
          ? _value.count
          : count // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AverageWithTypeDowntimesModelImplCopyWith<$Res>
    implements $AverageWithTypeDowntimesModelCopyWith<$Res> {
  factory _$$AverageWithTypeDowntimesModelImplCopyWith(
          _$AverageWithTypeDowntimesModelImpl value,
          $Res Function(_$AverageWithTypeDowntimesModelImpl) then) =
      __$$AverageWithTypeDowntimesModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? average, Map<String, DowntimeByType>? byType, int? count});
}

/// @nodoc
class __$$AverageWithTypeDowntimesModelImplCopyWithImpl<$Res>
    extends _$AverageWithTypeDowntimesModelCopyWithImpl<$Res,
        _$AverageWithTypeDowntimesModelImpl>
    implements _$$AverageWithTypeDowntimesModelImplCopyWith<$Res> {
  __$$AverageWithTypeDowntimesModelImplCopyWithImpl(
      _$AverageWithTypeDowntimesModelImpl _value,
      $Res Function(_$AverageWithTypeDowntimesModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of AverageWithTypeDowntimesModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? average = freezed,
    Object? byType = freezed,
    Object? count = freezed,
  }) {
    return _then(_$AverageWithTypeDowntimesModelImpl(
      average: freezed == average
          ? _value.average
          : average // ignore: cast_nullable_to_non_nullable
              as int?,
      byType: freezed == byType
          ? _value._byType
          : byType // ignore: cast_nullable_to_non_nullable
              as Map<String, DowntimeByType>?,
      count: freezed == count
          ? _value.count
          : count // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AverageWithTypeDowntimesModelImpl
    implements _AverageWithTypeDowntimesModel {
  const _$AverageWithTypeDowntimesModelImpl(
      {this.average, final Map<String, DowntimeByType>? byType, this.count})
      : _byType = byType;

  factory _$AverageWithTypeDowntimesModelImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$AverageWithTypeDowntimesModelImplFromJson(json);

  @override
  final int? average;
  final Map<String, DowntimeByType>? _byType;
  @override
  Map<String, DowntimeByType>? get byType {
    final value = _byType;
    if (value == null) return null;
    if (_byType is EqualUnmodifiableMapView) return _byType;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  final int? count;

  @override
  String toString() {
    return 'AverageWithTypeDowntimesModel(average: $average, byType: $byType, count: $count)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AverageWithTypeDowntimesModelImpl &&
            (identical(other.average, average) || other.average == average) &&
            const DeepCollectionEquality().equals(other._byType, _byType) &&
            (identical(other.count, count) || other.count == count));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, average,
      const DeepCollectionEquality().hash(_byType), count);

  /// Create a copy of AverageWithTypeDowntimesModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AverageWithTypeDowntimesModelImplCopyWith<
          _$AverageWithTypeDowntimesModelImpl>
      get copyWith => __$$AverageWithTypeDowntimesModelImplCopyWithImpl<
          _$AverageWithTypeDowntimesModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AverageWithTypeDowntimesModelImplToJson(
      this,
    );
  }
}

abstract class _AverageWithTypeDowntimesModel
    implements AverageWithTypeDowntimesModel {
  const factory _AverageWithTypeDowntimesModel(
      {final int? average,
      final Map<String, DowntimeByType>? byType,
      final int? count}) = _$AverageWithTypeDowntimesModelImpl;

  factory _AverageWithTypeDowntimesModel.fromJson(Map<String, dynamic> json) =
      _$AverageWithTypeDowntimesModelImpl.fromJson;

  @override
  int? get average;
  @override
  Map<String, DowntimeByType>? get byType;
  @override
  int? get count;

  /// Create a copy of AverageWithTypeDowntimesModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AverageWithTypeDowntimesModelImplCopyWith<
          _$AverageWithTypeDowntimesModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}

DowntimeByType _$DowntimeByTypeFromJson(Map<String, dynamic> json) {
  return _DowntimeByType.fromJson(json);
}

/// @nodoc
mixin _$DowntimeByType {
  double? get average => throw _privateConstructorUsedError;
  int? get count => throw _privateConstructorUsedError;

  /// Serializes this DowntimeByType to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DowntimeByType
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DowntimeByTypeCopyWith<DowntimeByType> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DowntimeByTypeCopyWith<$Res> {
  factory $DowntimeByTypeCopyWith(
          DowntimeByType value, $Res Function(DowntimeByType) then) =
      _$DowntimeByTypeCopyWithImpl<$Res, DowntimeByType>;
  @useResult
  $Res call({double? average, int? count});
}

/// @nodoc
class _$DowntimeByTypeCopyWithImpl<$Res, $Val extends DowntimeByType>
    implements $DowntimeByTypeCopyWith<$Res> {
  _$DowntimeByTypeCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DowntimeByType
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? average = freezed,
    Object? count = freezed,
  }) {
    return _then(_value.copyWith(
      average: freezed == average
          ? _value.average
          : average // ignore: cast_nullable_to_non_nullable
              as double?,
      count: freezed == count
          ? _value.count
          : count // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DowntimeByTypeImplCopyWith<$Res>
    implements $DowntimeByTypeCopyWith<$Res> {
  factory _$$DowntimeByTypeImplCopyWith(_$DowntimeByTypeImpl value,
          $Res Function(_$DowntimeByTypeImpl) then) =
      __$$DowntimeByTypeImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({double? average, int? count});
}

/// @nodoc
class __$$DowntimeByTypeImplCopyWithImpl<$Res>
    extends _$DowntimeByTypeCopyWithImpl<$Res, _$DowntimeByTypeImpl>
    implements _$$DowntimeByTypeImplCopyWith<$Res> {
  __$$DowntimeByTypeImplCopyWithImpl(
      _$DowntimeByTypeImpl _value, $Res Function(_$DowntimeByTypeImpl) _then)
      : super(_value, _then);

  /// Create a copy of DowntimeByType
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? average = freezed,
    Object? count = freezed,
  }) {
    return _then(_$DowntimeByTypeImpl(
      average: freezed == average
          ? _value.average
          : average // ignore: cast_nullable_to_non_nullable
              as double?,
      count: freezed == count
          ? _value.count
          : count // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$DowntimeByTypeImpl implements _DowntimeByType {
  const _$DowntimeByTypeImpl({this.average, this.count});

  factory _$DowntimeByTypeImpl.fromJson(Map<String, dynamic> json) =>
      _$$DowntimeByTypeImplFromJson(json);

  @override
  final double? average;
  @override
  final int? count;

  @override
  String toString() {
    return 'DowntimeByType(average: $average, count: $count)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DowntimeByTypeImpl &&
            (identical(other.average, average) || other.average == average) &&
            (identical(other.count, count) || other.count == count));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, average, count);

  /// Create a copy of DowntimeByType
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DowntimeByTypeImplCopyWith<_$DowntimeByTypeImpl> get copyWith =>
      __$$DowntimeByTypeImplCopyWithImpl<_$DowntimeByTypeImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DowntimeByTypeImplToJson(
      this,
    );
  }
}

abstract class _DowntimeByType implements DowntimeByType {
  const factory _DowntimeByType({final double? average, final int? count}) =
      _$DowntimeByTypeImpl;

  factory _DowntimeByType.fromJson(Map<String, dynamic> json) =
      _$DowntimeByTypeImpl.fromJson;

  @override
  double? get average;
  @override
  int? get count;

  /// Create a copy of DowntimeByType
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DowntimeByTypeImplCopyWith<_$DowntimeByTypeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

StatsAverageDowntimesByMonthModel _$StatsAverageDowntimesByMonthModelFromJson(
    Map<String, dynamic> json) {
  return _StatsAverageDowntimesByMonthModel.fromJson(json);
}

/// @nodoc
mixin _$StatsAverageDowntimesByMonthModel {
  Map<String, AverageWithTypeDowntimesModel>? get monthlyAverages =>
      throw _privateConstructorUsedError;
  double? get yearlyAverage => throw _privateConstructorUsedError;
  double? get prevYearAverage => throw _privateConstructorUsedError;
  double? get difference => throw _privateConstructorUsedError;
  String? get comparisonText => throw _privateConstructorUsedError;
  double? get totalOutageTime => throw _privateConstructorUsedError;
  double? get prevYearTotalOutageTime => throw _privateConstructorUsedError;
  int? get totalOutageItems => throw _privateConstructorUsedError;
  int? get prevYearOutageItems => throw _privateConstructorUsedError;

  /// Serializes this StatsAverageDowntimesByMonthModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of StatsAverageDowntimesByMonthModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $StatsAverageDowntimesByMonthModelCopyWith<StatsAverageDowntimesByMonthModel>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StatsAverageDowntimesByMonthModelCopyWith<$Res> {
  factory $StatsAverageDowntimesByMonthModelCopyWith(
          StatsAverageDowntimesByMonthModel value,
          $Res Function(StatsAverageDowntimesByMonthModel) then) =
      _$StatsAverageDowntimesByMonthModelCopyWithImpl<$Res,
          StatsAverageDowntimesByMonthModel>;
  @useResult
  $Res call(
      {Map<String, AverageWithTypeDowntimesModel>? monthlyAverages,
      double? yearlyAverage,
      double? prevYearAverage,
      double? difference,
      String? comparisonText,
      double? totalOutageTime,
      double? prevYearTotalOutageTime,
      int? totalOutageItems,
      int? prevYearOutageItems});
}

/// @nodoc
class _$StatsAverageDowntimesByMonthModelCopyWithImpl<$Res,
        $Val extends StatsAverageDowntimesByMonthModel>
    implements $StatsAverageDowntimesByMonthModelCopyWith<$Res> {
  _$StatsAverageDowntimesByMonthModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of StatsAverageDowntimesByMonthModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? monthlyAverages = freezed,
    Object? yearlyAverage = freezed,
    Object? prevYearAverage = freezed,
    Object? difference = freezed,
    Object? comparisonText = freezed,
    Object? totalOutageTime = freezed,
    Object? prevYearTotalOutageTime = freezed,
    Object? totalOutageItems = freezed,
    Object? prevYearOutageItems = freezed,
  }) {
    return _then(_value.copyWith(
      monthlyAverages: freezed == monthlyAverages
          ? _value.monthlyAverages
          : monthlyAverages // ignore: cast_nullable_to_non_nullable
              as Map<String, AverageWithTypeDowntimesModel>?,
      yearlyAverage: freezed == yearlyAverage
          ? _value.yearlyAverage
          : yearlyAverage // ignore: cast_nullable_to_non_nullable
              as double?,
      prevYearAverage: freezed == prevYearAverage
          ? _value.prevYearAverage
          : prevYearAverage // ignore: cast_nullable_to_non_nullable
              as double?,
      difference: freezed == difference
          ? _value.difference
          : difference // ignore: cast_nullable_to_non_nullable
              as double?,
      comparisonText: freezed == comparisonText
          ? _value.comparisonText
          : comparisonText // ignore: cast_nullable_to_non_nullable
              as String?,
      totalOutageTime: freezed == totalOutageTime
          ? _value.totalOutageTime
          : totalOutageTime // ignore: cast_nullable_to_non_nullable
              as double?,
      prevYearTotalOutageTime: freezed == prevYearTotalOutageTime
          ? _value.prevYearTotalOutageTime
          : prevYearTotalOutageTime // ignore: cast_nullable_to_non_nullable
              as double?,
      totalOutageItems: freezed == totalOutageItems
          ? _value.totalOutageItems
          : totalOutageItems // ignore: cast_nullable_to_non_nullable
              as int?,
      prevYearOutageItems: freezed == prevYearOutageItems
          ? _value.prevYearOutageItems
          : prevYearOutageItems // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$StatsAverageDowntimesByMonthModelImplCopyWith<$Res>
    implements $StatsAverageDowntimesByMonthModelCopyWith<$Res> {
  factory _$$StatsAverageDowntimesByMonthModelImplCopyWith(
          _$StatsAverageDowntimesByMonthModelImpl value,
          $Res Function(_$StatsAverageDowntimesByMonthModelImpl) then) =
      __$$StatsAverageDowntimesByMonthModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {Map<String, AverageWithTypeDowntimesModel>? monthlyAverages,
      double? yearlyAverage,
      double? prevYearAverage,
      double? difference,
      String? comparisonText,
      double? totalOutageTime,
      double? prevYearTotalOutageTime,
      int? totalOutageItems,
      int? prevYearOutageItems});
}

/// @nodoc
class __$$StatsAverageDowntimesByMonthModelImplCopyWithImpl<$Res>
    extends _$StatsAverageDowntimesByMonthModelCopyWithImpl<$Res,
        _$StatsAverageDowntimesByMonthModelImpl>
    implements _$$StatsAverageDowntimesByMonthModelImplCopyWith<$Res> {
  __$$StatsAverageDowntimesByMonthModelImplCopyWithImpl(
      _$StatsAverageDowntimesByMonthModelImpl _value,
      $Res Function(_$StatsAverageDowntimesByMonthModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of StatsAverageDowntimesByMonthModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? monthlyAverages = freezed,
    Object? yearlyAverage = freezed,
    Object? prevYearAverage = freezed,
    Object? difference = freezed,
    Object? comparisonText = freezed,
    Object? totalOutageTime = freezed,
    Object? prevYearTotalOutageTime = freezed,
    Object? totalOutageItems = freezed,
    Object? prevYearOutageItems = freezed,
  }) {
    return _then(_$StatsAverageDowntimesByMonthModelImpl(
      monthlyAverages: freezed == monthlyAverages
          ? _value._monthlyAverages
          : monthlyAverages // ignore: cast_nullable_to_non_nullable
              as Map<String, AverageWithTypeDowntimesModel>?,
      yearlyAverage: freezed == yearlyAverage
          ? _value.yearlyAverage
          : yearlyAverage // ignore: cast_nullable_to_non_nullable
              as double?,
      prevYearAverage: freezed == prevYearAverage
          ? _value.prevYearAverage
          : prevYearAverage // ignore: cast_nullable_to_non_nullable
              as double?,
      difference: freezed == difference
          ? _value.difference
          : difference // ignore: cast_nullable_to_non_nullable
              as double?,
      comparisonText: freezed == comparisonText
          ? _value.comparisonText
          : comparisonText // ignore: cast_nullable_to_non_nullable
              as String?,
      totalOutageTime: freezed == totalOutageTime
          ? _value.totalOutageTime
          : totalOutageTime // ignore: cast_nullable_to_non_nullable
              as double?,
      prevYearTotalOutageTime: freezed == prevYearTotalOutageTime
          ? _value.prevYearTotalOutageTime
          : prevYearTotalOutageTime // ignore: cast_nullable_to_non_nullable
              as double?,
      totalOutageItems: freezed == totalOutageItems
          ? _value.totalOutageItems
          : totalOutageItems // ignore: cast_nullable_to_non_nullable
              as int?,
      prevYearOutageItems: freezed == prevYearOutageItems
          ? _value.prevYearOutageItems
          : prevYearOutageItems // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$StatsAverageDowntimesByMonthModelImpl
    implements _StatsAverageDowntimesByMonthModel {
  const _$StatsAverageDowntimesByMonthModelImpl(
      {final Map<String, AverageWithTypeDowntimesModel>? monthlyAverages,
      this.yearlyAverage,
      this.prevYearAverage,
      this.difference,
      this.comparisonText,
      this.totalOutageTime,
      this.prevYearTotalOutageTime,
      this.totalOutageItems,
      this.prevYearOutageItems})
      : _monthlyAverages = monthlyAverages;

  factory _$StatsAverageDowntimesByMonthModelImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$StatsAverageDowntimesByMonthModelImplFromJson(json);

  final Map<String, AverageWithTypeDowntimesModel>? _monthlyAverages;
  @override
  Map<String, AverageWithTypeDowntimesModel>? get monthlyAverages {
    final value = _monthlyAverages;
    if (value == null) return null;
    if (_monthlyAverages is EqualUnmodifiableMapView) return _monthlyAverages;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  final double? yearlyAverage;
  @override
  final double? prevYearAverage;
  @override
  final double? difference;
  @override
  final String? comparisonText;
  @override
  final double? totalOutageTime;
  @override
  final double? prevYearTotalOutageTime;
  @override
  final int? totalOutageItems;
  @override
  final int? prevYearOutageItems;

  @override
  String toString() {
    return 'StatsAverageDowntimesByMonthModel(monthlyAverages: $monthlyAverages, yearlyAverage: $yearlyAverage, prevYearAverage: $prevYearAverage, difference: $difference, comparisonText: $comparisonText, totalOutageTime: $totalOutageTime, prevYearTotalOutageTime: $prevYearTotalOutageTime, totalOutageItems: $totalOutageItems, prevYearOutageItems: $prevYearOutageItems)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StatsAverageDowntimesByMonthModelImpl &&
            const DeepCollectionEquality()
                .equals(other._monthlyAverages, _monthlyAverages) &&
            (identical(other.yearlyAverage, yearlyAverage) ||
                other.yearlyAverage == yearlyAverage) &&
            (identical(other.prevYearAverage, prevYearAverage) ||
                other.prevYearAverage == prevYearAverage) &&
            (identical(other.difference, difference) ||
                other.difference == difference) &&
            (identical(other.comparisonText, comparisonText) ||
                other.comparisonText == comparisonText) &&
            (identical(other.totalOutageTime, totalOutageTime) ||
                other.totalOutageTime == totalOutageTime) &&
            (identical(
                    other.prevYearTotalOutageTime, prevYearTotalOutageTime) ||
                other.prevYearTotalOutageTime == prevYearTotalOutageTime) &&
            (identical(other.totalOutageItems, totalOutageItems) ||
                other.totalOutageItems == totalOutageItems) &&
            (identical(other.prevYearOutageItems, prevYearOutageItems) ||
                other.prevYearOutageItems == prevYearOutageItems));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_monthlyAverages),
      yearlyAverage,
      prevYearAverage,
      difference,
      comparisonText,
      totalOutageTime,
      prevYearTotalOutageTime,
      totalOutageItems,
      prevYearOutageItems);

  /// Create a copy of StatsAverageDowntimesByMonthModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StatsAverageDowntimesByMonthModelImplCopyWith<
          _$StatsAverageDowntimesByMonthModelImpl>
      get copyWith => __$$StatsAverageDowntimesByMonthModelImplCopyWithImpl<
          _$StatsAverageDowntimesByMonthModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$StatsAverageDowntimesByMonthModelImplToJson(
      this,
    );
  }
}

abstract class _StatsAverageDowntimesByMonthModel
    implements StatsAverageDowntimesByMonthModel {
  const factory _StatsAverageDowntimesByMonthModel(
          {final Map<String, AverageWithTypeDowntimesModel>? monthlyAverages,
          final double? yearlyAverage,
          final double? prevYearAverage,
          final double? difference,
          final String? comparisonText,
          final double? totalOutageTime,
          final double? prevYearTotalOutageTime,
          final int? totalOutageItems,
          final int? prevYearOutageItems}) =
      _$StatsAverageDowntimesByMonthModelImpl;

  factory _StatsAverageDowntimesByMonthModel.fromJson(
          Map<String, dynamic> json) =
      _$StatsAverageDowntimesByMonthModelImpl.fromJson;

  @override
  Map<String, AverageWithTypeDowntimesModel>? get monthlyAverages;
  @override
  double? get yearlyAverage;
  @override
  double? get prevYearAverage;
  @override
  double? get difference;
  @override
  String? get comparisonText;
  @override
  double? get totalOutageTime;
  @override
  double? get prevYearTotalOutageTime;
  @override
  int? get totalOutageItems;
  @override
  int? get prevYearOutageItems;

  /// Create a copy of StatsAverageDowntimesByMonthModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StatsAverageDowntimesByMonthModelImplCopyWith<
          _$StatsAverageDowntimesByMonthModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}
