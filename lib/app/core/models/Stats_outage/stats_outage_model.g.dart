// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'stats_outage_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$StatsOutAgeModelImpl _$$StatsOutAgeModelImplFromJson(
        Map<String, dynamic> json) =>
    _$StatsOutAgeModelImpl(
      repairNumber: json['repairNumber'] as String,
      mainID: json['mainID'] as String?,
      outAgeHours: (json['outAgeHours'] as num).toDouble(),
      equipment: json['equipment'] as String?,
      archive: json['archive'] as bool?,
      branch: (json['branch'] as num?)?.toInt(),
      countWork: (json['countWork'] as num?)?.toInt(),
      countComponents: (json['countComponents'] as num?)?.toInt(),
      workHours: (json['workHours'] as num?)?.toDouble(),
      workerHours: (json['workerHours'] as num?)?.toDouble(),
      statusHours: (json['statusHours'] as num?)?.toDouble(),
      statorOutAgeHours: (json['statorOutAgeHours'] as num?)?.toDouble(),
      rotorOutAgeHours: (json['rotorOutAgeHours'] as num?)?.toDouble(),
      bearingOutAgeHours: (json['bearingOutAgeHours'] as num?)?.toDouble(),
      transformerOutAgeHours:
          (json['transformerOutAgeHours'] as num?)?.toDouble(),
      works: (json['works'] as List<dynamic>?)
          ?.map((e) => StatsWorkModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      sszTimes: (json['sszTimes'] as List<dynamic>?)
          ?.map((e) => StatsSSZTimeModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      dayStats: (json['dayStats'] as List<dynamic>?)
          ?.map((e) => DayStatsModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      timelines: (json['timelines'] as List<dynamic>?)
          ?.map((e) => TimelinesStatsModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$StatsOutAgeModelImplToJson(
        _$StatsOutAgeModelImpl instance) =>
    <String, dynamic>{
      'repairNumber': instance.repairNumber,
      if (instance.mainID case final value?) 'mainID': value,
      'outAgeHours': instance.outAgeHours,
      if (instance.equipment case final value?) 'equipment': value,
      if (instance.archive case final value?) 'archive': value,
      if (instance.branch case final value?) 'branch': value,
      if (instance.countWork case final value?) 'countWork': value,
      if (instance.countComponents case final value?) 'countComponents': value,
      if (instance.workHours case final value?) 'workHours': value,
      if (instance.workerHours case final value?) 'workerHours': value,
      if (instance.statusHours case final value?) 'statusHours': value,
      if (instance.statorOutAgeHours case final value?)
        'statorOutAgeHours': value,
      if (instance.rotorOutAgeHours case final value?)
        'rotorOutAgeHours': value,
      if (instance.bearingOutAgeHours case final value?)
        'bearingOutAgeHours': value,
      if (instance.transformerOutAgeHours case final value?)
        'transformerOutAgeHours': value,
      if (instance.works?.map((e) => e.toJson()).toList() case final value?)
        'works': value,
      if (instance.sszTimes?.map((e) => e.toJson()).toList() case final value?)
        'sszTimes': value,
      if (instance.dayStats?.map((e) => e.toJson()).toList() case final value?)
        'dayStats': value,
      if (instance.timelines?.map((e) => e.toJson()).toList() case final value?)
        'timelines': value,
    };

_$StatsOutAgeListModelImpl _$$StatsOutAgeListModelImplFromJson(
        Map<String, dynamic> json) =>
    _$StatsOutAgeListModelImpl(
      (json['data'] as List<dynamic>)
          .map((e) => StatsOutAgeModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$StatsOutAgeListModelImplToJson(
        _$StatsOutAgeListModelImpl instance) =>
    <String, dynamic>{
      'data': instance.data,
    };

_$TimelinesStatsModelImpl _$$TimelinesStatsModelImplFromJson(
        Map<String, dynamic> json) =>
    _$TimelinesStatsModelImpl(
      id: json['_id'] as String?,
      name: json['name'] as String?,
      componentType:
          $enumDecodeNullable(_$ComponentTypeEnumMap, json['componentType']),
      main: json['main'] as bool?,
      timeline: (json['timeline'] as List<dynamic>?)
          ?.map((e) => TimelineStatsModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$TimelinesStatsModelImplToJson(
        _$TimelinesStatsModelImpl instance) =>
    <String, dynamic>{
      if (instance.id case final value?) '_id': value,
      if (instance.name case final value?) 'name': value,
      if (_$ComponentTypeEnumMap[instance.componentType] case final value?)
        'componentType': value,
      if (instance.main case final value?) 'main': value,
      if (instance.timeline case final value?) 'timeline': value,
    };

const _$ComponentTypeEnumMap = {
  ComponentType.stator: 'stator',
  ComponentType.rotor: 'rotor',
  ComponentType.inductor: 'inductor',
  ComponentType.anchor: 'anchor',
  ComponentType.slidingBearing: 'slidingBearing',
  ComponentType.rollingBearing: 'rollingBearing',
  ComponentType.transformer: 'transformer',
};

_$TimelineStatsModelImpl _$$TimelineStatsModelImplFromJson(
        Map<String, dynamic> json) =>
    _$TimelineStatsModelImpl(
      names:
          (json['names'] as List<dynamic>?)?.map((e) => e as String).toList(),
      type: $enumDecodeNullable(_$WorkStatusEnumMap, json['type']),
      duration: (json['duration'] as num?)?.toInt(),
      startDate: (json['startDate'] as num?)?.toInt(),
      endDate: (json['endDate'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$TimelineStatsModelImplToJson(
        _$TimelineStatsModelImpl instance) =>
    <String, dynamic>{
      if (instance.names case final value?) 'names': value,
      if (_$WorkStatusEnumMap[instance.type] case final value?) 'type': value,
      if (instance.duration case final value?) 'duration': value,
      if (instance.startDate case final value?) 'startDate': value,
      if (instance.endDate case final value?) 'endDate': value,
    };

const _$WorkStatusEnumMap = {
  WorkStatus.work: 'work',
  WorkStatus.outage: 'outage',
};
