// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'stats_outage_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

StatsOutAgeModel _$StatsOutAgeModelFromJson(Map<String, dynamic> json) {
  return _StatsOutAgeModel.fromJson(json);
}

/// @nodoc
mixin _$StatsOutAgeModel {
  String get repairNumber => throw _privateConstructorUsedError;
  String? get mainID => throw _privateConstructorUsedError;
  double get outAgeHours => throw _privateConstructorUsedError;
  String? get equipment => throw _privateConstructorUsedError;
  bool? get archive => throw _privateConstructorUsedError;
  int? get branch => throw _privateConstructorUsedError;
  int? get countWork => throw _privateConstructorUsedError;
  int? get countComponents => throw _privateConstructorUsedError;
  double? get workHours => throw _privateConstructorUsedError;
  double? get workerHours => throw _privateConstructorUsedError;
  double? get statusHours => throw _privateConstructorUsedError;
  double? get statorOutAgeHours => throw _privateConstructorUsedError;
  double? get rotorOutAgeHours => throw _privateConstructorUsedError;
  double? get bearingOutAgeHours => throw _privateConstructorUsedError;
  double? get transformerOutAgeHours => throw _privateConstructorUsedError;
  List<StatsWorkModel>? get works => throw _privateConstructorUsedError;
  List<StatsSSZTimeModel>? get sszTimes => throw _privateConstructorUsedError;
  List<DayStatsModel>? get dayStats => throw _privateConstructorUsedError;
  List<TimelinesStatsModel>? get timelines =>
      throw _privateConstructorUsedError;

  /// Serializes this StatsOutAgeModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of StatsOutAgeModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $StatsOutAgeModelCopyWith<StatsOutAgeModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StatsOutAgeModelCopyWith<$Res> {
  factory $StatsOutAgeModelCopyWith(
          StatsOutAgeModel value, $Res Function(StatsOutAgeModel) then) =
      _$StatsOutAgeModelCopyWithImpl<$Res, StatsOutAgeModel>;
  @useResult
  $Res call(
      {String repairNumber,
      String? mainID,
      double outAgeHours,
      String? equipment,
      bool? archive,
      int? branch,
      int? countWork,
      int? countComponents,
      double? workHours,
      double? workerHours,
      double? statusHours,
      double? statorOutAgeHours,
      double? rotorOutAgeHours,
      double? bearingOutAgeHours,
      double? transformerOutAgeHours,
      List<StatsWorkModel>? works,
      List<StatsSSZTimeModel>? sszTimes,
      List<DayStatsModel>? dayStats,
      List<TimelinesStatsModel>? timelines});
}

/// @nodoc
class _$StatsOutAgeModelCopyWithImpl<$Res, $Val extends StatsOutAgeModel>
    implements $StatsOutAgeModelCopyWith<$Res> {
  _$StatsOutAgeModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of StatsOutAgeModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? repairNumber = null,
    Object? mainID = freezed,
    Object? outAgeHours = null,
    Object? equipment = freezed,
    Object? archive = freezed,
    Object? branch = freezed,
    Object? countWork = freezed,
    Object? countComponents = freezed,
    Object? workHours = freezed,
    Object? workerHours = freezed,
    Object? statusHours = freezed,
    Object? statorOutAgeHours = freezed,
    Object? rotorOutAgeHours = freezed,
    Object? bearingOutAgeHours = freezed,
    Object? transformerOutAgeHours = freezed,
    Object? works = freezed,
    Object? sszTimes = freezed,
    Object? dayStats = freezed,
    Object? timelines = freezed,
  }) {
    return _then(_value.copyWith(
      repairNumber: null == repairNumber
          ? _value.repairNumber
          : repairNumber // ignore: cast_nullable_to_non_nullable
              as String,
      mainID: freezed == mainID
          ? _value.mainID
          : mainID // ignore: cast_nullable_to_non_nullable
              as String?,
      outAgeHours: null == outAgeHours
          ? _value.outAgeHours
          : outAgeHours // ignore: cast_nullable_to_non_nullable
              as double,
      equipment: freezed == equipment
          ? _value.equipment
          : equipment // ignore: cast_nullable_to_non_nullable
              as String?,
      archive: freezed == archive
          ? _value.archive
          : archive // ignore: cast_nullable_to_non_nullable
              as bool?,
      branch: freezed == branch
          ? _value.branch
          : branch // ignore: cast_nullable_to_non_nullable
              as int?,
      countWork: freezed == countWork
          ? _value.countWork
          : countWork // ignore: cast_nullable_to_non_nullable
              as int?,
      countComponents: freezed == countComponents
          ? _value.countComponents
          : countComponents // ignore: cast_nullable_to_non_nullable
              as int?,
      workHours: freezed == workHours
          ? _value.workHours
          : workHours // ignore: cast_nullable_to_non_nullable
              as double?,
      workerHours: freezed == workerHours
          ? _value.workerHours
          : workerHours // ignore: cast_nullable_to_non_nullable
              as double?,
      statusHours: freezed == statusHours
          ? _value.statusHours
          : statusHours // ignore: cast_nullable_to_non_nullable
              as double?,
      statorOutAgeHours: freezed == statorOutAgeHours
          ? _value.statorOutAgeHours
          : statorOutAgeHours // ignore: cast_nullable_to_non_nullable
              as double?,
      rotorOutAgeHours: freezed == rotorOutAgeHours
          ? _value.rotorOutAgeHours
          : rotorOutAgeHours // ignore: cast_nullable_to_non_nullable
              as double?,
      bearingOutAgeHours: freezed == bearingOutAgeHours
          ? _value.bearingOutAgeHours
          : bearingOutAgeHours // ignore: cast_nullable_to_non_nullable
              as double?,
      transformerOutAgeHours: freezed == transformerOutAgeHours
          ? _value.transformerOutAgeHours
          : transformerOutAgeHours // ignore: cast_nullable_to_non_nullable
              as double?,
      works: freezed == works
          ? _value.works
          : works // ignore: cast_nullable_to_non_nullable
              as List<StatsWorkModel>?,
      sszTimes: freezed == sszTimes
          ? _value.sszTimes
          : sszTimes // ignore: cast_nullable_to_non_nullable
              as List<StatsSSZTimeModel>?,
      dayStats: freezed == dayStats
          ? _value.dayStats
          : dayStats // ignore: cast_nullable_to_non_nullable
              as List<DayStatsModel>?,
      timelines: freezed == timelines
          ? _value.timelines
          : timelines // ignore: cast_nullable_to_non_nullable
              as List<TimelinesStatsModel>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$StatsOutAgeModelImplCopyWith<$Res>
    implements $StatsOutAgeModelCopyWith<$Res> {
  factory _$$StatsOutAgeModelImplCopyWith(_$StatsOutAgeModelImpl value,
          $Res Function(_$StatsOutAgeModelImpl) then) =
      __$$StatsOutAgeModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String repairNumber,
      String? mainID,
      double outAgeHours,
      String? equipment,
      bool? archive,
      int? branch,
      int? countWork,
      int? countComponents,
      double? workHours,
      double? workerHours,
      double? statusHours,
      double? statorOutAgeHours,
      double? rotorOutAgeHours,
      double? bearingOutAgeHours,
      double? transformerOutAgeHours,
      List<StatsWorkModel>? works,
      List<StatsSSZTimeModel>? sszTimes,
      List<DayStatsModel>? dayStats,
      List<TimelinesStatsModel>? timelines});
}

/// @nodoc
class __$$StatsOutAgeModelImplCopyWithImpl<$Res>
    extends _$StatsOutAgeModelCopyWithImpl<$Res, _$StatsOutAgeModelImpl>
    implements _$$StatsOutAgeModelImplCopyWith<$Res> {
  __$$StatsOutAgeModelImplCopyWithImpl(_$StatsOutAgeModelImpl _value,
      $Res Function(_$StatsOutAgeModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of StatsOutAgeModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? repairNumber = null,
    Object? mainID = freezed,
    Object? outAgeHours = null,
    Object? equipment = freezed,
    Object? archive = freezed,
    Object? branch = freezed,
    Object? countWork = freezed,
    Object? countComponents = freezed,
    Object? workHours = freezed,
    Object? workerHours = freezed,
    Object? statusHours = freezed,
    Object? statorOutAgeHours = freezed,
    Object? rotorOutAgeHours = freezed,
    Object? bearingOutAgeHours = freezed,
    Object? transformerOutAgeHours = freezed,
    Object? works = freezed,
    Object? sszTimes = freezed,
    Object? dayStats = freezed,
    Object? timelines = freezed,
  }) {
    return _then(_$StatsOutAgeModelImpl(
      repairNumber: null == repairNumber
          ? _value.repairNumber
          : repairNumber // ignore: cast_nullable_to_non_nullable
              as String,
      mainID: freezed == mainID
          ? _value.mainID
          : mainID // ignore: cast_nullable_to_non_nullable
              as String?,
      outAgeHours: null == outAgeHours
          ? _value.outAgeHours
          : outAgeHours // ignore: cast_nullable_to_non_nullable
              as double,
      equipment: freezed == equipment
          ? _value.equipment
          : equipment // ignore: cast_nullable_to_non_nullable
              as String?,
      archive: freezed == archive
          ? _value.archive
          : archive // ignore: cast_nullable_to_non_nullable
              as bool?,
      branch: freezed == branch
          ? _value.branch
          : branch // ignore: cast_nullable_to_non_nullable
              as int?,
      countWork: freezed == countWork
          ? _value.countWork
          : countWork // ignore: cast_nullable_to_non_nullable
              as int?,
      countComponents: freezed == countComponents
          ? _value.countComponents
          : countComponents // ignore: cast_nullable_to_non_nullable
              as int?,
      workHours: freezed == workHours
          ? _value.workHours
          : workHours // ignore: cast_nullable_to_non_nullable
              as double?,
      workerHours: freezed == workerHours
          ? _value.workerHours
          : workerHours // ignore: cast_nullable_to_non_nullable
              as double?,
      statusHours: freezed == statusHours
          ? _value.statusHours
          : statusHours // ignore: cast_nullable_to_non_nullable
              as double?,
      statorOutAgeHours: freezed == statorOutAgeHours
          ? _value.statorOutAgeHours
          : statorOutAgeHours // ignore: cast_nullable_to_non_nullable
              as double?,
      rotorOutAgeHours: freezed == rotorOutAgeHours
          ? _value.rotorOutAgeHours
          : rotorOutAgeHours // ignore: cast_nullable_to_non_nullable
              as double?,
      bearingOutAgeHours: freezed == bearingOutAgeHours
          ? _value.bearingOutAgeHours
          : bearingOutAgeHours // ignore: cast_nullable_to_non_nullable
              as double?,
      transformerOutAgeHours: freezed == transformerOutAgeHours
          ? _value.transformerOutAgeHours
          : transformerOutAgeHours // ignore: cast_nullable_to_non_nullable
              as double?,
      works: freezed == works
          ? _value._works
          : works // ignore: cast_nullable_to_non_nullable
              as List<StatsWorkModel>?,
      sszTimes: freezed == sszTimes
          ? _value._sszTimes
          : sszTimes // ignore: cast_nullable_to_non_nullable
              as List<StatsSSZTimeModel>?,
      dayStats: freezed == dayStats
          ? _value._dayStats
          : dayStats // ignore: cast_nullable_to_non_nullable
              as List<DayStatsModel>?,
      timelines: freezed == timelines
          ? _value._timelines
          : timelines // ignore: cast_nullable_to_non_nullable
              as List<TimelinesStatsModel>?,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class _$StatsOutAgeModelImpl extends _StatsOutAgeModel
    with DiagnosticableTreeMixin {
  const _$StatsOutAgeModelImpl(
      {required this.repairNumber,
      this.mainID,
      required this.outAgeHours,
      this.equipment,
      this.archive,
      this.branch,
      this.countWork,
      this.countComponents,
      this.workHours,
      this.workerHours,
      this.statusHours,
      this.statorOutAgeHours,
      this.rotorOutAgeHours,
      this.bearingOutAgeHours,
      this.transformerOutAgeHours,
      final List<StatsWorkModel>? works,
      final List<StatsSSZTimeModel>? sszTimes,
      final List<DayStatsModel>? dayStats,
      final List<TimelinesStatsModel>? timelines})
      : _works = works,
        _sszTimes = sszTimes,
        _dayStats = dayStats,
        _timelines = timelines,
        super._();

  factory _$StatsOutAgeModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$StatsOutAgeModelImplFromJson(json);

  @override
  final String repairNumber;
  @override
  final String? mainID;
  @override
  final double outAgeHours;
  @override
  final String? equipment;
  @override
  final bool? archive;
  @override
  final int? branch;
  @override
  final int? countWork;
  @override
  final int? countComponents;
  @override
  final double? workHours;
  @override
  final double? workerHours;
  @override
  final double? statusHours;
  @override
  final double? statorOutAgeHours;
  @override
  final double? rotorOutAgeHours;
  @override
  final double? bearingOutAgeHours;
  @override
  final double? transformerOutAgeHours;
  final List<StatsWorkModel>? _works;
  @override
  List<StatsWorkModel>? get works {
    final value = _works;
    if (value == null) return null;
    if (_works is EqualUnmodifiableListView) return _works;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<StatsSSZTimeModel>? _sszTimes;
  @override
  List<StatsSSZTimeModel>? get sszTimes {
    final value = _sszTimes;
    if (value == null) return null;
    if (_sszTimes is EqualUnmodifiableListView) return _sszTimes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<DayStatsModel>? _dayStats;
  @override
  List<DayStatsModel>? get dayStats {
    final value = _dayStats;
    if (value == null) return null;
    if (_dayStats is EqualUnmodifiableListView) return _dayStats;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<TimelinesStatsModel>? _timelines;
  @override
  List<TimelinesStatsModel>? get timelines {
    final value = _timelines;
    if (value == null) return null;
    if (_timelines is EqualUnmodifiableListView) return _timelines;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'StatsOutAgeModel(repairNumber: $repairNumber, mainID: $mainID, outAgeHours: $outAgeHours, equipment: $equipment, archive: $archive, branch: $branch, countWork: $countWork, countComponents: $countComponents, workHours: $workHours, workerHours: $workerHours, statusHours: $statusHours, statorOutAgeHours: $statorOutAgeHours, rotorOutAgeHours: $rotorOutAgeHours, bearingOutAgeHours: $bearingOutAgeHours, transformerOutAgeHours: $transformerOutAgeHours, works: $works, sszTimes: $sszTimes, dayStats: $dayStats, timelines: $timelines)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'StatsOutAgeModel'))
      ..add(DiagnosticsProperty('repairNumber', repairNumber))
      ..add(DiagnosticsProperty('mainID', mainID))
      ..add(DiagnosticsProperty('outAgeHours', outAgeHours))
      ..add(DiagnosticsProperty('equipment', equipment))
      ..add(DiagnosticsProperty('archive', archive))
      ..add(DiagnosticsProperty('branch', branch))
      ..add(DiagnosticsProperty('countWork', countWork))
      ..add(DiagnosticsProperty('countComponents', countComponents))
      ..add(DiagnosticsProperty('workHours', workHours))
      ..add(DiagnosticsProperty('workerHours', workerHours))
      ..add(DiagnosticsProperty('statusHours', statusHours))
      ..add(DiagnosticsProperty('statorOutAgeHours', statorOutAgeHours))
      ..add(DiagnosticsProperty('rotorOutAgeHours', rotorOutAgeHours))
      ..add(DiagnosticsProperty('bearingOutAgeHours', bearingOutAgeHours))
      ..add(
          DiagnosticsProperty('transformerOutAgeHours', transformerOutAgeHours))
      ..add(DiagnosticsProperty('works', works))
      ..add(DiagnosticsProperty('sszTimes', sszTimes))
      ..add(DiagnosticsProperty('dayStats', dayStats))
      ..add(DiagnosticsProperty('timelines', timelines));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StatsOutAgeModelImpl &&
            (identical(other.repairNumber, repairNumber) ||
                other.repairNumber == repairNumber) &&
            (identical(other.mainID, mainID) || other.mainID == mainID) &&
            (identical(other.outAgeHours, outAgeHours) ||
                other.outAgeHours == outAgeHours) &&
            (identical(other.equipment, equipment) ||
                other.equipment == equipment) &&
            (identical(other.archive, archive) || other.archive == archive) &&
            (identical(other.branch, branch) || other.branch == branch) &&
            (identical(other.countWork, countWork) ||
                other.countWork == countWork) &&
            (identical(other.countComponents, countComponents) ||
                other.countComponents == countComponents) &&
            (identical(other.workHours, workHours) ||
                other.workHours == workHours) &&
            (identical(other.workerHours, workerHours) ||
                other.workerHours == workerHours) &&
            (identical(other.statusHours, statusHours) ||
                other.statusHours == statusHours) &&
            (identical(other.statorOutAgeHours, statorOutAgeHours) ||
                other.statorOutAgeHours == statorOutAgeHours) &&
            (identical(other.rotorOutAgeHours, rotorOutAgeHours) ||
                other.rotorOutAgeHours == rotorOutAgeHours) &&
            (identical(other.bearingOutAgeHours, bearingOutAgeHours) ||
                other.bearingOutAgeHours == bearingOutAgeHours) &&
            (identical(other.transformerOutAgeHours, transformerOutAgeHours) ||
                other.transformerOutAgeHours == transformerOutAgeHours) &&
            const DeepCollectionEquality().equals(other._works, _works) &&
            const DeepCollectionEquality().equals(other._sszTimes, _sszTimes) &&
            const DeepCollectionEquality().equals(other._dayStats, _dayStats) &&
            const DeepCollectionEquality()
                .equals(other._timelines, _timelines));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        repairNumber,
        mainID,
        outAgeHours,
        equipment,
        archive,
        branch,
        countWork,
        countComponents,
        workHours,
        workerHours,
        statusHours,
        statorOutAgeHours,
        rotorOutAgeHours,
        bearingOutAgeHours,
        transformerOutAgeHours,
        const DeepCollectionEquality().hash(_works),
        const DeepCollectionEquality().hash(_sszTimes),
        const DeepCollectionEquality().hash(_dayStats),
        const DeepCollectionEquality().hash(_timelines)
      ]);

  /// Create a copy of StatsOutAgeModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StatsOutAgeModelImplCopyWith<_$StatsOutAgeModelImpl> get copyWith =>
      __$$StatsOutAgeModelImplCopyWithImpl<_$StatsOutAgeModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$StatsOutAgeModelImplToJson(
      this,
    );
  }
}

abstract class _StatsOutAgeModel extends StatsOutAgeModel {
  const factory _StatsOutAgeModel(
      {required final String repairNumber,
      final String? mainID,
      required final double outAgeHours,
      final String? equipment,
      final bool? archive,
      final int? branch,
      final int? countWork,
      final int? countComponents,
      final double? workHours,
      final double? workerHours,
      final double? statusHours,
      final double? statorOutAgeHours,
      final double? rotorOutAgeHours,
      final double? bearingOutAgeHours,
      final double? transformerOutAgeHours,
      final List<StatsWorkModel>? works,
      final List<StatsSSZTimeModel>? sszTimes,
      final List<DayStatsModel>? dayStats,
      final List<TimelinesStatsModel>? timelines}) = _$StatsOutAgeModelImpl;
  const _StatsOutAgeModel._() : super._();

  factory _StatsOutAgeModel.fromJson(Map<String, dynamic> json) =
      _$StatsOutAgeModelImpl.fromJson;

  @override
  String get repairNumber;
  @override
  String? get mainID;
  @override
  double get outAgeHours;
  @override
  String? get equipment;
  @override
  bool? get archive;
  @override
  int? get branch;
  @override
  int? get countWork;
  @override
  int? get countComponents;
  @override
  double? get workHours;
  @override
  double? get workerHours;
  @override
  double? get statusHours;
  @override
  double? get statorOutAgeHours;
  @override
  double? get rotorOutAgeHours;
  @override
  double? get bearingOutAgeHours;
  @override
  double? get transformerOutAgeHours;
  @override
  List<StatsWorkModel>? get works;
  @override
  List<StatsSSZTimeModel>? get sszTimes;
  @override
  List<DayStatsModel>? get dayStats;
  @override
  List<TimelinesStatsModel>? get timelines;

  /// Create a copy of StatsOutAgeModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StatsOutAgeModelImplCopyWith<_$StatsOutAgeModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

StatsOutAgeListModel _$StatsOutAgeListModelFromJson(Map<String, dynamic> json) {
  return _StatsOutAgeListModel.fromJson(json);
}

/// @nodoc
mixin _$StatsOutAgeListModel {
  List<StatsOutAgeModel> get data => throw _privateConstructorUsedError;

  /// Serializes this StatsOutAgeListModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of StatsOutAgeListModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $StatsOutAgeListModelCopyWith<StatsOutAgeListModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StatsOutAgeListModelCopyWith<$Res> {
  factory $StatsOutAgeListModelCopyWith(StatsOutAgeListModel value,
          $Res Function(StatsOutAgeListModel) then) =
      _$StatsOutAgeListModelCopyWithImpl<$Res, StatsOutAgeListModel>;
  @useResult
  $Res call({List<StatsOutAgeModel> data});
}

/// @nodoc
class _$StatsOutAgeListModelCopyWithImpl<$Res,
        $Val extends StatsOutAgeListModel>
    implements $StatsOutAgeListModelCopyWith<$Res> {
  _$StatsOutAgeListModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of StatsOutAgeListModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
  }) {
    return _then(_value.copyWith(
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<StatsOutAgeModel>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$StatsOutAgeListModelImplCopyWith<$Res>
    implements $StatsOutAgeListModelCopyWith<$Res> {
  factory _$$StatsOutAgeListModelImplCopyWith(_$StatsOutAgeListModelImpl value,
          $Res Function(_$StatsOutAgeListModelImpl) then) =
      __$$StatsOutAgeListModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<StatsOutAgeModel> data});
}

/// @nodoc
class __$$StatsOutAgeListModelImplCopyWithImpl<$Res>
    extends _$StatsOutAgeListModelCopyWithImpl<$Res, _$StatsOutAgeListModelImpl>
    implements _$$StatsOutAgeListModelImplCopyWith<$Res> {
  __$$StatsOutAgeListModelImplCopyWithImpl(_$StatsOutAgeListModelImpl _value,
      $Res Function(_$StatsOutAgeListModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of StatsOutAgeListModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
  }) {
    return _then(_$StatsOutAgeListModelImpl(
      null == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as List<StatsOutAgeModel>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$StatsOutAgeListModelImpl
    with DiagnosticableTreeMixin
    implements _StatsOutAgeListModel {
  _$StatsOutAgeListModelImpl(final List<StatsOutAgeModel> data) : _data = data;

  factory _$StatsOutAgeListModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$StatsOutAgeListModelImplFromJson(json);

  final List<StatsOutAgeModel> _data;
  @override
  List<StatsOutAgeModel> get data {
    if (_data is EqualUnmodifiableListView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_data);
  }

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'StatsOutAgeListModel(data: $data)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'StatsOutAgeListModel'))
      ..add(DiagnosticsProperty('data', data));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StatsOutAgeListModelImpl &&
            const DeepCollectionEquality().equals(other._data, _data));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_data));

  /// Create a copy of StatsOutAgeListModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StatsOutAgeListModelImplCopyWith<_$StatsOutAgeListModelImpl>
      get copyWith =>
          __$$StatsOutAgeListModelImplCopyWithImpl<_$StatsOutAgeListModelImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$StatsOutAgeListModelImplToJson(
      this,
    );
  }
}

abstract class _StatsOutAgeListModel implements StatsOutAgeListModel {
  factory _StatsOutAgeListModel(final List<StatsOutAgeModel> data) =
      _$StatsOutAgeListModelImpl;

  factory _StatsOutAgeListModel.fromJson(Map<String, dynamic> json) =
      _$StatsOutAgeListModelImpl.fromJson;

  @override
  List<StatsOutAgeModel> get data;

  /// Create a copy of StatsOutAgeListModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StatsOutAgeListModelImplCopyWith<_$StatsOutAgeListModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}

TimelinesStatsModel _$TimelinesStatsModelFromJson(Map<String, dynamic> json) {
  return _TimelinesStatsModel.fromJson(json);
}

/// @nodoc
mixin _$TimelinesStatsModel {
  @JsonKey(name: '_id')
  String? get id => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  ComponentType<dynamic>? get componentType =>
      throw _privateConstructorUsedError;
  bool? get main => throw _privateConstructorUsedError;
  List<TimelineStatsModel>? get timeline => throw _privateConstructorUsedError;

  /// Serializes this TimelinesStatsModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TimelinesStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TimelinesStatsModelCopyWith<TimelinesStatsModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TimelinesStatsModelCopyWith<$Res> {
  factory $TimelinesStatsModelCopyWith(
          TimelinesStatsModel value, $Res Function(TimelinesStatsModel) then) =
      _$TimelinesStatsModelCopyWithImpl<$Res, TimelinesStatsModel>;
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String? id,
      String? name,
      ComponentType<dynamic>? componentType,
      bool? main,
      List<TimelineStatsModel>? timeline});
}

/// @nodoc
class _$TimelinesStatsModelCopyWithImpl<$Res, $Val extends TimelinesStatsModel>
    implements $TimelinesStatsModelCopyWith<$Res> {
  _$TimelinesStatsModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TimelinesStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? componentType = freezed,
    Object? main = freezed,
    Object? timeline = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      componentType: freezed == componentType
          ? _value.componentType
          : componentType // ignore: cast_nullable_to_non_nullable
              as ComponentType<dynamic>?,
      main: freezed == main
          ? _value.main
          : main // ignore: cast_nullable_to_non_nullable
              as bool?,
      timeline: freezed == timeline
          ? _value.timeline
          : timeline // ignore: cast_nullable_to_non_nullable
              as List<TimelineStatsModel>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TimelinesStatsModelImplCopyWith<$Res>
    implements $TimelinesStatsModelCopyWith<$Res> {
  factory _$$TimelinesStatsModelImplCopyWith(_$TimelinesStatsModelImpl value,
          $Res Function(_$TimelinesStatsModelImpl) then) =
      __$$TimelinesStatsModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String? id,
      String? name,
      ComponentType<dynamic>? componentType,
      bool? main,
      List<TimelineStatsModel>? timeline});
}

/// @nodoc
class __$$TimelinesStatsModelImplCopyWithImpl<$Res>
    extends _$TimelinesStatsModelCopyWithImpl<$Res, _$TimelinesStatsModelImpl>
    implements _$$TimelinesStatsModelImplCopyWith<$Res> {
  __$$TimelinesStatsModelImplCopyWithImpl(_$TimelinesStatsModelImpl _value,
      $Res Function(_$TimelinesStatsModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of TimelinesStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? componentType = freezed,
    Object? main = freezed,
    Object? timeline = freezed,
  }) {
    return _then(_$TimelinesStatsModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      componentType: freezed == componentType
          ? _value.componentType
          : componentType // ignore: cast_nullable_to_non_nullable
              as ComponentType<dynamic>?,
      main: freezed == main
          ? _value.main
          : main // ignore: cast_nullable_to_non_nullable
              as bool?,
      timeline: freezed == timeline
          ? _value._timeline
          : timeline // ignore: cast_nullable_to_non_nullable
              as List<TimelineStatsModel>?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$TimelinesStatsModelImpl
    with DiagnosticableTreeMixin
    implements _TimelinesStatsModel {
  const _$TimelinesStatsModelImpl(
      {@JsonKey(name: '_id') this.id,
      this.name,
      this.componentType,
      this.main,
      final List<TimelineStatsModel>? timeline})
      : _timeline = timeline;

  factory _$TimelinesStatsModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$TimelinesStatsModelImplFromJson(json);

  @override
  @JsonKey(name: '_id')
  final String? id;
  @override
  final String? name;
  @override
  final ComponentType<dynamic>? componentType;
  @override
  final bool? main;
  final List<TimelineStatsModel>? _timeline;
  @override
  List<TimelineStatsModel>? get timeline {
    final value = _timeline;
    if (value == null) return null;
    if (_timeline is EqualUnmodifiableListView) return _timeline;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'TimelinesStatsModel(id: $id, name: $name, componentType: $componentType, main: $main, timeline: $timeline)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'TimelinesStatsModel'))
      ..add(DiagnosticsProperty('id', id))
      ..add(DiagnosticsProperty('name', name))
      ..add(DiagnosticsProperty('componentType', componentType))
      ..add(DiagnosticsProperty('main', main))
      ..add(DiagnosticsProperty('timeline', timeline));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TimelinesStatsModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.componentType, componentType) ||
                other.componentType == componentType) &&
            (identical(other.main, main) || other.main == main) &&
            const DeepCollectionEquality().equals(other._timeline, _timeline));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, name, componentType, main,
      const DeepCollectionEquality().hash(_timeline));

  /// Create a copy of TimelinesStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TimelinesStatsModelImplCopyWith<_$TimelinesStatsModelImpl> get copyWith =>
      __$$TimelinesStatsModelImplCopyWithImpl<_$TimelinesStatsModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TimelinesStatsModelImplToJson(
      this,
    );
  }
}

abstract class _TimelinesStatsModel implements TimelinesStatsModel {
  const factory _TimelinesStatsModel(
      {@JsonKey(name: '_id') final String? id,
      final String? name,
      final ComponentType<dynamic>? componentType,
      final bool? main,
      final List<TimelineStatsModel>? timeline}) = _$TimelinesStatsModelImpl;

  factory _TimelinesStatsModel.fromJson(Map<String, dynamic> json) =
      _$TimelinesStatsModelImpl.fromJson;

  @override
  @JsonKey(name: '_id')
  String? get id;
  @override
  String? get name;
  @override
  ComponentType<dynamic>? get componentType;
  @override
  bool? get main;
  @override
  List<TimelineStatsModel>? get timeline;

  /// Create a copy of TimelinesStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TimelinesStatsModelImplCopyWith<_$TimelinesStatsModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

TimelineStatsModel _$TimelineStatsModelFromJson(Map<String, dynamic> json) {
  return _TimelineStatsModel.fromJson(json);
}

/// @nodoc
mixin _$TimelineStatsModel {
  List<String>? get names => throw _privateConstructorUsedError;
  WorkStatus? get type => throw _privateConstructorUsedError;
  int? get duration => throw _privateConstructorUsedError;
  int? get startDate => throw _privateConstructorUsedError;
  int? get endDate => throw _privateConstructorUsedError;

  /// Serializes this TimelineStatsModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TimelineStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TimelineStatsModelCopyWith<TimelineStatsModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TimelineStatsModelCopyWith<$Res> {
  factory $TimelineStatsModelCopyWith(
          TimelineStatsModel value, $Res Function(TimelineStatsModel) then) =
      _$TimelineStatsModelCopyWithImpl<$Res, TimelineStatsModel>;
  @useResult
  $Res call(
      {List<String>? names,
      WorkStatus? type,
      int? duration,
      int? startDate,
      int? endDate});
}

/// @nodoc
class _$TimelineStatsModelCopyWithImpl<$Res, $Val extends TimelineStatsModel>
    implements $TimelineStatsModelCopyWith<$Res> {
  _$TimelineStatsModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TimelineStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? names = freezed,
    Object? type = freezed,
    Object? duration = freezed,
    Object? startDate = freezed,
    Object? endDate = freezed,
  }) {
    return _then(_value.copyWith(
      names: freezed == names
          ? _value.names
          : names // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as WorkStatus?,
      duration: freezed == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as int?,
      startDate: freezed == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as int?,
      endDate: freezed == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TimelineStatsModelImplCopyWith<$Res>
    implements $TimelineStatsModelCopyWith<$Res> {
  factory _$$TimelineStatsModelImplCopyWith(_$TimelineStatsModelImpl value,
          $Res Function(_$TimelineStatsModelImpl) then) =
      __$$TimelineStatsModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<String>? names,
      WorkStatus? type,
      int? duration,
      int? startDate,
      int? endDate});
}

/// @nodoc
class __$$TimelineStatsModelImplCopyWithImpl<$Res>
    extends _$TimelineStatsModelCopyWithImpl<$Res, _$TimelineStatsModelImpl>
    implements _$$TimelineStatsModelImplCopyWith<$Res> {
  __$$TimelineStatsModelImplCopyWithImpl(_$TimelineStatsModelImpl _value,
      $Res Function(_$TimelineStatsModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of TimelineStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? names = freezed,
    Object? type = freezed,
    Object? duration = freezed,
    Object? startDate = freezed,
    Object? endDate = freezed,
  }) {
    return _then(_$TimelineStatsModelImpl(
      names: freezed == names
          ? _value._names
          : names // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as WorkStatus?,
      duration: freezed == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as int?,
      startDate: freezed == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as int?,
      endDate: freezed == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$TimelineStatsModelImpl
    with DiagnosticableTreeMixin
    implements _TimelineStatsModel {
  const _$TimelineStatsModelImpl(
      {final List<String>? names,
      this.type,
      this.duration,
      this.startDate,
      this.endDate})
      : _names = names;

  factory _$TimelineStatsModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$TimelineStatsModelImplFromJson(json);

  final List<String>? _names;
  @override
  List<String>? get names {
    final value = _names;
    if (value == null) return null;
    if (_names is EqualUnmodifiableListView) return _names;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final WorkStatus? type;
  @override
  final int? duration;
  @override
  final int? startDate;
  @override
  final int? endDate;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'TimelineStatsModel(names: $names, type: $type, duration: $duration, startDate: $startDate, endDate: $endDate)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'TimelineStatsModel'))
      ..add(DiagnosticsProperty('names', names))
      ..add(DiagnosticsProperty('type', type))
      ..add(DiagnosticsProperty('duration', duration))
      ..add(DiagnosticsProperty('startDate', startDate))
      ..add(DiagnosticsProperty('endDate', endDate));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TimelineStatsModelImpl &&
            const DeepCollectionEquality().equals(other._names, _names) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.duration, duration) ||
                other.duration == duration) &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_names),
      type,
      duration,
      startDate,
      endDate);

  /// Create a copy of TimelineStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TimelineStatsModelImplCopyWith<_$TimelineStatsModelImpl> get copyWith =>
      __$$TimelineStatsModelImplCopyWithImpl<_$TimelineStatsModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TimelineStatsModelImplToJson(
      this,
    );
  }
}

abstract class _TimelineStatsModel implements TimelineStatsModel {
  const factory _TimelineStatsModel(
      {final List<String>? names,
      final WorkStatus? type,
      final int? duration,
      final int? startDate,
      final int? endDate}) = _$TimelineStatsModelImpl;

  factory _TimelineStatsModel.fromJson(Map<String, dynamic> json) =
      _$TimelineStatsModelImpl.fromJson;

  @override
  List<String>? get names;
  @override
  WorkStatus? get type;
  @override
  int? get duration;
  @override
  int? get startDate;
  @override
  int? get endDate;

  /// Create a copy of TimelineStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TimelineStatsModelImplCopyWith<_$TimelineStatsModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
