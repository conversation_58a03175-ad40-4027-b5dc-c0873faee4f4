// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tasks_stats_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$TasksStatsModelImpl _$$TasksStatsModelImplFromJson(
        Map<String, dynamic> json) =>
    _$TasksStatsModelImpl(
      selectedStatTask: json['selectedStatTask'] == null
          ? null
          : TaskStatsModel.fromJson(
              json['selectedStatTask'] as Map<String, dynamic>),
      foundStatsTasks: (json['foundStatsTasks'] as List<dynamic>?)
          ?.map((e) => TaskStatsModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      foundTasks: (json['foundTasks'] as List<dynamic>?)
          ?.map((e) => FoundTaskModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$TasksStatsModelImplToJson(
        _$TasksStatsModelImpl instance) =>
    <String, dynamic>{
      if (instance.selectedStatTask case final value?)
        'selectedStatTask': value,
      if (instance.foundStatsTasks case final value?) 'foundStatsTasks': value,
      if (instance.foundTasks case final value?) 'foundTasks': value,
    };

_$TaskStatsModelImpl _$$TaskStatsModelImplFromJson(Map<String, dynamic> json) =>
    _$TaskStatsModelImpl(
      id: json['id'] as String?,
      name: json['name'] as String?,
    );

Map<String, dynamic> _$$TaskStatsModelImplToJson(
        _$TaskStatsModelImpl instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.name case final value?) 'name': value,
    };

_$FoundTaskModelImpl _$$FoundTaskModelImplFromJson(Map<String, dynamic> json) =>
    _$FoundTaskModelImpl(
      repairNumber: json['repairNumber'] as String?,
      equipmentName: json['equipmentName'] as String?,
      power: json['power'] as String?,
      turnovers: json['turnovers'] as String?,
      standardHours: (json['standardHours'] as num?)?.toDouble(),
      branch: json['branch'] as String?,
      mainID: json['mainID'] as String?,
    );

Map<String, dynamic> _$$FoundTaskModelImplToJson(
        _$FoundTaskModelImpl instance) =>
    <String, dynamic>{
      if (instance.repairNumber case final value?) 'repairNumber': value,
      if (instance.equipmentName case final value?) 'equipmentName': value,
      if (instance.power case final value?) 'power': value,
      if (instance.turnovers case final value?) 'turnovers': value,
      if (instance.standardHours case final value?) 'standardHours': value,
      if (instance.branch case final value?) 'branch': value,
      if (instance.mainID case final value?) 'mainID': value,
    };
