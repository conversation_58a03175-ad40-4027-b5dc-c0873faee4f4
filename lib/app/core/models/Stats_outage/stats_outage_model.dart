import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:uer_flutter/app/core/models/enums/component_type_enum.dart';

import '../../../helpers/KeyValueObject.dart';
import '../stats/day_stats/day_stats_model.dart';
import 'stats_ssz_time_model.dart';
import 'stats_work_model.dart';

part 'stats_outage_model.freezed.dart';
part 'stats_outage_model.g.dart';

@freezed
class StatsOutAgeModel with _$StatsOutAgeModel {
  const StatsOutAgeModel._();
  @JsonSerializable(explicitToJson: true, includeIfNull: false)
  const factory StatsOutAgeModel({
    required String repairNumber,
    String? mainID,
    required double outAgeHours,
    String? equipment,
    bool? archive,
    int? branch,
    int? countWork,
    int? countComponents,
    double? workHours,
    double? workerHours,
    double? statusHours,
    double? statorOutAgeHours,
    double? rotorOutAgeHours,
    double? bearingOutAgeHours,
    double? transformerOutAgeHours,
    List<StatsWorkModel>? works,
    List<StatsSSZTimeModel>? sszTimes,
    List<DayStatsModel>? dayStats,
    List<TimelinesStatsModel>? timelines,
  }) = _StatsOutAgeModel;

  List<KeyValueObject> getInfo() {
    List<KeyValueObject> arr = [];

    var commonHours = (workHours ?? 0) + outAgeHours;

    arr.add(KeyValueObject("Выполненых работ (ССЗ)", "${countWork ?? 0} шт"));
    arr.add(KeyValueObject("Выполненых работ, н/ч",
        "${(workerHours ?? 0).toStringAsFixed(2)} н/ч"));
    arr.add(KeyValueObject(
        "Общее календ. время, д", "${(commonHours / 8).toStringAsFixed(2)} д"));
    arr.add(KeyValueObject("Выполненых работ, д",
        "${((workHours ?? 0) / 8).toStringAsFixed(2)} д"));
    arr.add(KeyValueObject(
        "Статусы, д", "${((statusHours ?? 0) / 8).toStringAsFixed(2)} д"));
    arr.add(KeyValueObject(
        "Общее время простоя, д", "${(outAgeHours / 8).toStringAsFixed(2)} д"));
    arr.add(KeyValueObject("", ""));
    arr.add(KeyValueObject("Простой:", ""));
    //arr.add(KeyValueObject("‾‾‾‾‾‾‾‾", ""));

    if ((transformerOutAgeHours ?? 0) > 0) {
      arr.add(KeyValueObject("Трансформатор",
          "${((transformerOutAgeHours ?? 0) / 8).toStringAsFixed(2)} д"));
    } else {
      arr.add(KeyValueObject("Статор/Индуктор",
          "${((statorOutAgeHours ?? 0) / 8).toStringAsFixed(2)} д"));
      arr.add(KeyValueObject("Ротор/Якорь",
          "${((rotorOutAgeHours ?? 0) / 8).toStringAsFixed(2)} д"));
      arr.add(KeyValueObject("Подшипники",
          "${((bearingOutAgeHours ?? 0) / 8).toStringAsFixed(2)} д"));
    }

    return arr;
  }

  static List<KeyValueObject> getEmptyInfo() {
    List<KeyValueObject> arr = [];

    arr.add(KeyValueObject("Выполненых работ (ССЗ), шт", "---"));
    arr.add(KeyValueObject("Выполненых работ, н/ч", "---"));
    arr.add(KeyValueObject("Общее календ. время", "---"));
    arr.add(KeyValueObject("Выполненых работ, д", "---"));
    arr.add(KeyValueObject("Статусы, д", "---"));
    arr.add(KeyValueObject("Общее время простоя, д", "---"));
    arr.add(KeyValueObject("", ""));
    arr.add(KeyValueObject("Простой:", ""));
    //arr.add(KeyValueObject("‾‾‾‾‾‾‾‾", ""));

    arr.add(KeyValueObject("Статор/Индуктор", "---"));
    arr.add(KeyValueObject("Ротор/Якорь", "---"));
    arr.add(KeyValueObject("Подшипники", "---"));

    return arr;
  }

  factory StatsOutAgeModel.fromJson(Map<String, Object?> json) =>
      _$StatsOutAgeModelFromJson(json);
}

@freezed
class StatsOutAgeListModel with _$StatsOutAgeListModel {
  factory StatsOutAgeListModel(List<StatsOutAgeModel> data) =
      _StatsOutAgeListModel;

  factory StatsOutAgeListModel.fromJson(Map<String, dynamic> json) =>
      _$StatsOutAgeListModelFromJson(json);
}

@freezed
class TimelinesStatsModel with _$TimelinesStatsModel {
  @JsonSerializable(includeIfNull: false)
  const factory TimelinesStatsModel({
    @JsonKey(name: '_id') String? id,
    String? name,
    ComponentType? componentType,
    bool? main,
    List<TimelineStatsModel>? timeline,
  }) = _TimelinesStatsModel;

  factory TimelinesStatsModel.fromJson(Map<String, dynamic> json) =>
      _$TimelinesStatsModelFromJson(json);
}

@freezed
class TimelineStatsModel with _$TimelineStatsModel {
  @JsonSerializable(includeIfNull: false)
  const factory TimelineStatsModel({
    List<String>? names,
    WorkStatus? type,
    int? duration,
    int? startDate,
    int? endDate,
  }) = _TimelineStatsModel;

  factory TimelineStatsModel.fromJson(Map<String, dynamic> json) =>
      _$TimelineStatsModelFromJson(json);
}

enum WorkStatus {
  @JsonValue('work')
  work,
  @JsonValue('outage')
  outage;
}
