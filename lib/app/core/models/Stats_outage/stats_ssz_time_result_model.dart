import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter/foundation.dart';

part 'stats_ssz_time_result_model.freezed.dart';
part 'stats_ssz_time_result_model.g.dart';

@freezed
class StatsSSZTimeResultModel with _$StatsSSZTimeResultModel {
  const StatsSSZTimeResultModel._();
  @JsonSerializable(explicitToJson: true, includeIfNull: false)
  const factory StatsSSZTimeResultModel({
    required String name,
    required List<StatsSSZTimeBranchModel> timeBranchs,
  }) = _StatsSSZTimeResultModel;

  factory StatsSSZTimeResultModel.fromJson(Map<String, Object?> json) =>
      _$StatsSSZTimeResultModelFromJson(json);
}

@freezed
class StatsSSZTimeResultListModel with _$StatsSSZTimeResultListModel {
  factory StatsSSZTimeResultListModel(List<StatsSSZTimeResultModel> data) =
      _StatsSSZTimeResultListModel;

  factory StatsSSZTimeResultListModel.fromJson(Map<String, dynamic> json) =>
      _$StatsSSZTimeResultListModelFromJson(json);
}

@freezed
class StatsSSZTimeBranchModel with _$StatsSSZTimeBranchModel {
  const StatsSSZTimeBranchModel._();
  @JsonSerializable(explicitToJson: true, includeIfNull: false)
  const factory StatsSSZTimeBranchModel({
    required int branch,
    required double workerTime,
  }) = _StatsSSZTimeBranchModel;

  factory StatsSSZTimeBranchModel.fromJson(Map<String, Object?> json) =>
      _$StatsSSZTimeBranchModelFromJson(json);
}
