// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'tasks_stats_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

TasksStatsModel _$TasksStatsModelFromJson(Map<String, dynamic> json) {
  return _TasksStatsModel.fromJson(json);
}

/// @nodoc
mixin _$TasksStatsModel {
  TaskStatsModel? get selectedStatTask => throw _privateConstructorUsedError;
  List<TaskStatsModel>? get foundStatsTasks =>
      throw _privateConstructorUsedError;
  List<FoundTaskModel>? get foundTasks => throw _privateConstructorUsedError;

  /// Serializes this TasksStatsModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TasksStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TasksStatsModelCopyWith<TasksStatsModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TasksStatsModelCopyWith<$Res> {
  factory $TasksStatsModelCopyWith(
          TasksStatsModel value, $Res Function(TasksStatsModel) then) =
      _$TasksStatsModelCopyWithImpl<$Res, TasksStatsModel>;
  @useResult
  $Res call(
      {TaskStatsModel? selectedStatTask,
      List<TaskStatsModel>? foundStatsTasks,
      List<FoundTaskModel>? foundTasks});

  $TaskStatsModelCopyWith<$Res>? get selectedStatTask;
}

/// @nodoc
class _$TasksStatsModelCopyWithImpl<$Res, $Val extends TasksStatsModel>
    implements $TasksStatsModelCopyWith<$Res> {
  _$TasksStatsModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TasksStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedStatTask = freezed,
    Object? foundStatsTasks = freezed,
    Object? foundTasks = freezed,
  }) {
    return _then(_value.copyWith(
      selectedStatTask: freezed == selectedStatTask
          ? _value.selectedStatTask
          : selectedStatTask // ignore: cast_nullable_to_non_nullable
              as TaskStatsModel?,
      foundStatsTasks: freezed == foundStatsTasks
          ? _value.foundStatsTasks
          : foundStatsTasks // ignore: cast_nullable_to_non_nullable
              as List<TaskStatsModel>?,
      foundTasks: freezed == foundTasks
          ? _value.foundTasks
          : foundTasks // ignore: cast_nullable_to_non_nullable
              as List<FoundTaskModel>?,
    ) as $Val);
  }

  /// Create a copy of TasksStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TaskStatsModelCopyWith<$Res>? get selectedStatTask {
    if (_value.selectedStatTask == null) {
      return null;
    }

    return $TaskStatsModelCopyWith<$Res>(_value.selectedStatTask!, (value) {
      return _then(_value.copyWith(selectedStatTask: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$TasksStatsModelImplCopyWith<$Res>
    implements $TasksStatsModelCopyWith<$Res> {
  factory _$$TasksStatsModelImplCopyWith(_$TasksStatsModelImpl value,
          $Res Function(_$TasksStatsModelImpl) then) =
      __$$TasksStatsModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {TaskStatsModel? selectedStatTask,
      List<TaskStatsModel>? foundStatsTasks,
      List<FoundTaskModel>? foundTasks});

  @override
  $TaskStatsModelCopyWith<$Res>? get selectedStatTask;
}

/// @nodoc
class __$$TasksStatsModelImplCopyWithImpl<$Res>
    extends _$TasksStatsModelCopyWithImpl<$Res, _$TasksStatsModelImpl>
    implements _$$TasksStatsModelImplCopyWith<$Res> {
  __$$TasksStatsModelImplCopyWithImpl(
      _$TasksStatsModelImpl _value, $Res Function(_$TasksStatsModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of TasksStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedStatTask = freezed,
    Object? foundStatsTasks = freezed,
    Object? foundTasks = freezed,
  }) {
    return _then(_$TasksStatsModelImpl(
      selectedStatTask: freezed == selectedStatTask
          ? _value.selectedStatTask
          : selectedStatTask // ignore: cast_nullable_to_non_nullable
              as TaskStatsModel?,
      foundStatsTasks: freezed == foundStatsTasks
          ? _value._foundStatsTasks
          : foundStatsTasks // ignore: cast_nullable_to_non_nullable
              as List<TaskStatsModel>?,
      foundTasks: freezed == foundTasks
          ? _value._foundTasks
          : foundTasks // ignore: cast_nullable_to_non_nullable
              as List<FoundTaskModel>?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$TasksStatsModelImpl implements _TasksStatsModel {
  const _$TasksStatsModelImpl(
      {this.selectedStatTask,
      final List<TaskStatsModel>? foundStatsTasks,
      final List<FoundTaskModel>? foundTasks})
      : _foundStatsTasks = foundStatsTasks,
        _foundTasks = foundTasks;

  factory _$TasksStatsModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$TasksStatsModelImplFromJson(json);

  @override
  final TaskStatsModel? selectedStatTask;
  final List<TaskStatsModel>? _foundStatsTasks;
  @override
  List<TaskStatsModel>? get foundStatsTasks {
    final value = _foundStatsTasks;
    if (value == null) return null;
    if (_foundStatsTasks is EqualUnmodifiableListView) return _foundStatsTasks;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<FoundTaskModel>? _foundTasks;
  @override
  List<FoundTaskModel>? get foundTasks {
    final value = _foundTasks;
    if (value == null) return null;
    if (_foundTasks is EqualUnmodifiableListView) return _foundTasks;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'TasksStatsModel(selectedStatTask: $selectedStatTask, foundStatsTasks: $foundStatsTasks, foundTasks: $foundTasks)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TasksStatsModelImpl &&
            (identical(other.selectedStatTask, selectedStatTask) ||
                other.selectedStatTask == selectedStatTask) &&
            const DeepCollectionEquality()
                .equals(other._foundStatsTasks, _foundStatsTasks) &&
            const DeepCollectionEquality()
                .equals(other._foundTasks, _foundTasks));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      selectedStatTask,
      const DeepCollectionEquality().hash(_foundStatsTasks),
      const DeepCollectionEquality().hash(_foundTasks));

  /// Create a copy of TasksStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TasksStatsModelImplCopyWith<_$TasksStatsModelImpl> get copyWith =>
      __$$TasksStatsModelImplCopyWithImpl<_$TasksStatsModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TasksStatsModelImplToJson(
      this,
    );
  }
}

abstract class _TasksStatsModel implements TasksStatsModel {
  const factory _TasksStatsModel(
      {final TaskStatsModel? selectedStatTask,
      final List<TaskStatsModel>? foundStatsTasks,
      final List<FoundTaskModel>? foundTasks}) = _$TasksStatsModelImpl;

  factory _TasksStatsModel.fromJson(Map<String, dynamic> json) =
      _$TasksStatsModelImpl.fromJson;

  @override
  TaskStatsModel? get selectedStatTask;
  @override
  List<TaskStatsModel>? get foundStatsTasks;
  @override
  List<FoundTaskModel>? get foundTasks;

  /// Create a copy of TasksStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TasksStatsModelImplCopyWith<_$TasksStatsModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

TaskStatsModel _$TaskStatsModelFromJson(Map<String, dynamic> json) {
  return _TaskStatsModel.fromJson(json);
}

/// @nodoc
mixin _$TaskStatsModel {
  String? get id => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;

  /// Serializes this TaskStatsModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TaskStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TaskStatsModelCopyWith<TaskStatsModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TaskStatsModelCopyWith<$Res> {
  factory $TaskStatsModelCopyWith(
          TaskStatsModel value, $Res Function(TaskStatsModel) then) =
      _$TaskStatsModelCopyWithImpl<$Res, TaskStatsModel>;
  @useResult
  $Res call({String? id, String? name});
}

/// @nodoc
class _$TaskStatsModelCopyWithImpl<$Res, $Val extends TaskStatsModel>
    implements $TaskStatsModelCopyWith<$Res> {
  _$TaskStatsModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TaskStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TaskStatsModelImplCopyWith<$Res>
    implements $TaskStatsModelCopyWith<$Res> {
  factory _$$TaskStatsModelImplCopyWith(_$TaskStatsModelImpl value,
          $Res Function(_$TaskStatsModelImpl) then) =
      __$$TaskStatsModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? id, String? name});
}

/// @nodoc
class __$$TaskStatsModelImplCopyWithImpl<$Res>
    extends _$TaskStatsModelCopyWithImpl<$Res, _$TaskStatsModelImpl>
    implements _$$TaskStatsModelImplCopyWith<$Res> {
  __$$TaskStatsModelImplCopyWithImpl(
      _$TaskStatsModelImpl _value, $Res Function(_$TaskStatsModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of TaskStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
  }) {
    return _then(_$TaskStatsModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$TaskStatsModelImpl implements _TaskStatsModel {
  const _$TaskStatsModelImpl({this.id, this.name});

  factory _$TaskStatsModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$TaskStatsModelImplFromJson(json);

  @override
  final String? id;
  @override
  final String? name;

  @override
  String toString() {
    return 'TaskStatsModel(id: $id, name: $name)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TaskStatsModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, name);

  /// Create a copy of TaskStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TaskStatsModelImplCopyWith<_$TaskStatsModelImpl> get copyWith =>
      __$$TaskStatsModelImplCopyWithImpl<_$TaskStatsModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TaskStatsModelImplToJson(
      this,
    );
  }
}

abstract class _TaskStatsModel implements TaskStatsModel {
  const factory _TaskStatsModel({final String? id, final String? name}) =
      _$TaskStatsModelImpl;

  factory _TaskStatsModel.fromJson(Map<String, dynamic> json) =
      _$TaskStatsModelImpl.fromJson;

  @override
  String? get id;
  @override
  String? get name;

  /// Create a copy of TaskStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TaskStatsModelImplCopyWith<_$TaskStatsModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

FoundTaskModel _$FoundTaskModelFromJson(Map<String, dynamic> json) {
  return _FoundTaskModel.fromJson(json);
}

/// @nodoc
mixin _$FoundTaskModel {
  String? get repairNumber => throw _privateConstructorUsedError;
  String? get equipmentName => throw _privateConstructorUsedError;
  String? get power => throw _privateConstructorUsedError;
  String? get turnovers => throw _privateConstructorUsedError;
  double? get standardHours => throw _privateConstructorUsedError;
  String? get branch => throw _privateConstructorUsedError;
  String? get mainID => throw _privateConstructorUsedError;

  /// Serializes this FoundTaskModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of FoundTaskModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FoundTaskModelCopyWith<FoundTaskModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FoundTaskModelCopyWith<$Res> {
  factory $FoundTaskModelCopyWith(
          FoundTaskModel value, $Res Function(FoundTaskModel) then) =
      _$FoundTaskModelCopyWithImpl<$Res, FoundTaskModel>;
  @useResult
  $Res call(
      {String? repairNumber,
      String? equipmentName,
      String? power,
      String? turnovers,
      double? standardHours,
      String? branch,
      String? mainID});
}

/// @nodoc
class _$FoundTaskModelCopyWithImpl<$Res, $Val extends FoundTaskModel>
    implements $FoundTaskModelCopyWith<$Res> {
  _$FoundTaskModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FoundTaskModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? repairNumber = freezed,
    Object? equipmentName = freezed,
    Object? power = freezed,
    Object? turnovers = freezed,
    Object? standardHours = freezed,
    Object? branch = freezed,
    Object? mainID = freezed,
  }) {
    return _then(_value.copyWith(
      repairNumber: freezed == repairNumber
          ? _value.repairNumber
          : repairNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      equipmentName: freezed == equipmentName
          ? _value.equipmentName
          : equipmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      power: freezed == power
          ? _value.power
          : power // ignore: cast_nullable_to_non_nullable
              as String?,
      turnovers: freezed == turnovers
          ? _value.turnovers
          : turnovers // ignore: cast_nullable_to_non_nullable
              as String?,
      standardHours: freezed == standardHours
          ? _value.standardHours
          : standardHours // ignore: cast_nullable_to_non_nullable
              as double?,
      branch: freezed == branch
          ? _value.branch
          : branch // ignore: cast_nullable_to_non_nullable
              as String?,
      mainID: freezed == mainID
          ? _value.mainID
          : mainID // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FoundTaskModelImplCopyWith<$Res>
    implements $FoundTaskModelCopyWith<$Res> {
  factory _$$FoundTaskModelImplCopyWith(_$FoundTaskModelImpl value,
          $Res Function(_$FoundTaskModelImpl) then) =
      __$$FoundTaskModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? repairNumber,
      String? equipmentName,
      String? power,
      String? turnovers,
      double? standardHours,
      String? branch,
      String? mainID});
}

/// @nodoc
class __$$FoundTaskModelImplCopyWithImpl<$Res>
    extends _$FoundTaskModelCopyWithImpl<$Res, _$FoundTaskModelImpl>
    implements _$$FoundTaskModelImplCopyWith<$Res> {
  __$$FoundTaskModelImplCopyWithImpl(
      _$FoundTaskModelImpl _value, $Res Function(_$FoundTaskModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of FoundTaskModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? repairNumber = freezed,
    Object? equipmentName = freezed,
    Object? power = freezed,
    Object? turnovers = freezed,
    Object? standardHours = freezed,
    Object? branch = freezed,
    Object? mainID = freezed,
  }) {
    return _then(_$FoundTaskModelImpl(
      repairNumber: freezed == repairNumber
          ? _value.repairNumber
          : repairNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      equipmentName: freezed == equipmentName
          ? _value.equipmentName
          : equipmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      power: freezed == power
          ? _value.power
          : power // ignore: cast_nullable_to_non_nullable
              as String?,
      turnovers: freezed == turnovers
          ? _value.turnovers
          : turnovers // ignore: cast_nullable_to_non_nullable
              as String?,
      standardHours: freezed == standardHours
          ? _value.standardHours
          : standardHours // ignore: cast_nullable_to_non_nullable
              as double?,
      branch: freezed == branch
          ? _value.branch
          : branch // ignore: cast_nullable_to_non_nullable
              as String?,
      mainID: freezed == mainID
          ? _value.mainID
          : mainID // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$FoundTaskModelImpl implements _FoundTaskModel {
  const _$FoundTaskModelImpl(
      {this.repairNumber,
      this.equipmentName,
      this.power,
      this.turnovers,
      this.standardHours,
      this.branch,
      this.mainID});

  factory _$FoundTaskModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$FoundTaskModelImplFromJson(json);

  @override
  final String? repairNumber;
  @override
  final String? equipmentName;
  @override
  final String? power;
  @override
  final String? turnovers;
  @override
  final double? standardHours;
  @override
  final String? branch;
  @override
  final String? mainID;

  @override
  String toString() {
    return 'FoundTaskModel(repairNumber: $repairNumber, equipmentName: $equipmentName, power: $power, turnovers: $turnovers, standardHours: $standardHours, branch: $branch, mainID: $mainID)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FoundTaskModelImpl &&
            (identical(other.repairNumber, repairNumber) ||
                other.repairNumber == repairNumber) &&
            (identical(other.equipmentName, equipmentName) ||
                other.equipmentName == equipmentName) &&
            (identical(other.power, power) || other.power == power) &&
            (identical(other.turnovers, turnovers) ||
                other.turnovers == turnovers) &&
            (identical(other.standardHours, standardHours) ||
                other.standardHours == standardHours) &&
            (identical(other.branch, branch) || other.branch == branch) &&
            (identical(other.mainID, mainID) || other.mainID == mainID));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, repairNumber, equipmentName,
      power, turnovers, standardHours, branch, mainID);

  /// Create a copy of FoundTaskModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FoundTaskModelImplCopyWith<_$FoundTaskModelImpl> get copyWith =>
      __$$FoundTaskModelImplCopyWithImpl<_$FoundTaskModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FoundTaskModelImplToJson(
      this,
    );
  }
}

abstract class _FoundTaskModel implements FoundTaskModel {
  const factory _FoundTaskModel(
      {final String? repairNumber,
      final String? equipmentName,
      final String? power,
      final String? turnovers,
      final double? standardHours,
      final String? branch,
      final String? mainID}) = _$FoundTaskModelImpl;

  factory _FoundTaskModel.fromJson(Map<String, dynamic> json) =
      _$FoundTaskModelImpl.fromJson;

  @override
  String? get repairNumber;
  @override
  String? get equipmentName;
  @override
  String? get power;
  @override
  String? get turnovers;
  @override
  double? get standardHours;
  @override
  String? get branch;
  @override
  String? get mainID;

  /// Create a copy of FoundTaskModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FoundTaskModelImplCopyWith<_$FoundTaskModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
