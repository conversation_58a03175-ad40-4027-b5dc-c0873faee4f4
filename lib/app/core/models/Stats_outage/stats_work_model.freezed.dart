// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'stats_work_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

StatsWorkModel _$StatsWorkModelFromJson(Map<String, dynamic> json) {
  return _StatsWorkModel.fromJson(json);
}

/// @nodoc
mixin _$StatsWorkModel {
  String get name => throw _privateConstructorUsedError;
  String get componentName => throw _privateConstructorUsedError;
  int? get area => throw _privateConstructorUsedError;
  String? get dateStr => throw _privateConstructorUsedError;
  double get workTime => throw _privateConstructorUsedError;
  double get workerTime => throw _privateConstructorUsedError;
  double get outAgeTime => throw _privateConstructorUsedError;
  double get statusTime => throw _privateConstructorUsedError;
  double get percentWork => throw _privateConstructorUsedError;
  double get percentOutAge => throw _privateConstructorUsedError;

  /// Serializes this StatsWorkModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of StatsWorkModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $StatsWorkModelCopyWith<StatsWorkModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StatsWorkModelCopyWith<$Res> {
  factory $StatsWorkModelCopyWith(
          StatsWorkModel value, $Res Function(StatsWorkModel) then) =
      _$StatsWorkModelCopyWithImpl<$Res, StatsWorkModel>;
  @useResult
  $Res call(
      {String name,
      String componentName,
      int? area,
      String? dateStr,
      double workTime,
      double workerTime,
      double outAgeTime,
      double statusTime,
      double percentWork,
      double percentOutAge});
}

/// @nodoc
class _$StatsWorkModelCopyWithImpl<$Res, $Val extends StatsWorkModel>
    implements $StatsWorkModelCopyWith<$Res> {
  _$StatsWorkModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of StatsWorkModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? componentName = null,
    Object? area = freezed,
    Object? dateStr = freezed,
    Object? workTime = null,
    Object? workerTime = null,
    Object? outAgeTime = null,
    Object? statusTime = null,
    Object? percentWork = null,
    Object? percentOutAge = null,
  }) {
    return _then(_value.copyWith(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      componentName: null == componentName
          ? _value.componentName
          : componentName // ignore: cast_nullable_to_non_nullable
              as String,
      area: freezed == area
          ? _value.area
          : area // ignore: cast_nullable_to_non_nullable
              as int?,
      dateStr: freezed == dateStr
          ? _value.dateStr
          : dateStr // ignore: cast_nullable_to_non_nullable
              as String?,
      workTime: null == workTime
          ? _value.workTime
          : workTime // ignore: cast_nullable_to_non_nullable
              as double,
      workerTime: null == workerTime
          ? _value.workerTime
          : workerTime // ignore: cast_nullable_to_non_nullable
              as double,
      outAgeTime: null == outAgeTime
          ? _value.outAgeTime
          : outAgeTime // ignore: cast_nullable_to_non_nullable
              as double,
      statusTime: null == statusTime
          ? _value.statusTime
          : statusTime // ignore: cast_nullable_to_non_nullable
              as double,
      percentWork: null == percentWork
          ? _value.percentWork
          : percentWork // ignore: cast_nullable_to_non_nullable
              as double,
      percentOutAge: null == percentOutAge
          ? _value.percentOutAge
          : percentOutAge // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$StatsWorkModelImplCopyWith<$Res>
    implements $StatsWorkModelCopyWith<$Res> {
  factory _$$StatsWorkModelImplCopyWith(_$StatsWorkModelImpl value,
          $Res Function(_$StatsWorkModelImpl) then) =
      __$$StatsWorkModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String name,
      String componentName,
      int? area,
      String? dateStr,
      double workTime,
      double workerTime,
      double outAgeTime,
      double statusTime,
      double percentWork,
      double percentOutAge});
}

/// @nodoc
class __$$StatsWorkModelImplCopyWithImpl<$Res>
    extends _$StatsWorkModelCopyWithImpl<$Res, _$StatsWorkModelImpl>
    implements _$$StatsWorkModelImplCopyWith<$Res> {
  __$$StatsWorkModelImplCopyWithImpl(
      _$StatsWorkModelImpl _value, $Res Function(_$StatsWorkModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of StatsWorkModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? componentName = null,
    Object? area = freezed,
    Object? dateStr = freezed,
    Object? workTime = null,
    Object? workerTime = null,
    Object? outAgeTime = null,
    Object? statusTime = null,
    Object? percentWork = null,
    Object? percentOutAge = null,
  }) {
    return _then(_$StatsWorkModelImpl(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      componentName: null == componentName
          ? _value.componentName
          : componentName // ignore: cast_nullable_to_non_nullable
              as String,
      area: freezed == area
          ? _value.area
          : area // ignore: cast_nullable_to_non_nullable
              as int?,
      dateStr: freezed == dateStr
          ? _value.dateStr
          : dateStr // ignore: cast_nullable_to_non_nullable
              as String?,
      workTime: null == workTime
          ? _value.workTime
          : workTime // ignore: cast_nullable_to_non_nullable
              as double,
      workerTime: null == workerTime
          ? _value.workerTime
          : workerTime // ignore: cast_nullable_to_non_nullable
              as double,
      outAgeTime: null == outAgeTime
          ? _value.outAgeTime
          : outAgeTime // ignore: cast_nullable_to_non_nullable
              as double,
      statusTime: null == statusTime
          ? _value.statusTime
          : statusTime // ignore: cast_nullable_to_non_nullable
              as double,
      percentWork: null == percentWork
          ? _value.percentWork
          : percentWork // ignore: cast_nullable_to_non_nullable
              as double,
      percentOutAge: null == percentOutAge
          ? _value.percentOutAge
          : percentOutAge // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class _$StatsWorkModelImpl extends _StatsWorkModel
    with DiagnosticableTreeMixin {
  const _$StatsWorkModelImpl(
      {required this.name,
      required this.componentName,
      this.area,
      this.dateStr,
      required this.workTime,
      required this.workerTime,
      required this.outAgeTime,
      required this.statusTime,
      required this.percentWork,
      required this.percentOutAge})
      : super._();

  factory _$StatsWorkModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$StatsWorkModelImplFromJson(json);

  @override
  final String name;
  @override
  final String componentName;
  @override
  final int? area;
  @override
  final String? dateStr;
  @override
  final double workTime;
  @override
  final double workerTime;
  @override
  final double outAgeTime;
  @override
  final double statusTime;
  @override
  final double percentWork;
  @override
  final double percentOutAge;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'StatsWorkModel(name: $name, componentName: $componentName, area: $area, dateStr: $dateStr, workTime: $workTime, workerTime: $workerTime, outAgeTime: $outAgeTime, statusTime: $statusTime, percentWork: $percentWork, percentOutAge: $percentOutAge)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'StatsWorkModel'))
      ..add(DiagnosticsProperty('name', name))
      ..add(DiagnosticsProperty('componentName', componentName))
      ..add(DiagnosticsProperty('area', area))
      ..add(DiagnosticsProperty('dateStr', dateStr))
      ..add(DiagnosticsProperty('workTime', workTime))
      ..add(DiagnosticsProperty('workerTime', workerTime))
      ..add(DiagnosticsProperty('outAgeTime', outAgeTime))
      ..add(DiagnosticsProperty('statusTime', statusTime))
      ..add(DiagnosticsProperty('percentWork', percentWork))
      ..add(DiagnosticsProperty('percentOutAge', percentOutAge));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StatsWorkModelImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.componentName, componentName) ||
                other.componentName == componentName) &&
            (identical(other.area, area) || other.area == area) &&
            (identical(other.dateStr, dateStr) || other.dateStr == dateStr) &&
            (identical(other.workTime, workTime) ||
                other.workTime == workTime) &&
            (identical(other.workerTime, workerTime) ||
                other.workerTime == workerTime) &&
            (identical(other.outAgeTime, outAgeTime) ||
                other.outAgeTime == outAgeTime) &&
            (identical(other.statusTime, statusTime) ||
                other.statusTime == statusTime) &&
            (identical(other.percentWork, percentWork) ||
                other.percentWork == percentWork) &&
            (identical(other.percentOutAge, percentOutAge) ||
                other.percentOutAge == percentOutAge));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      name,
      componentName,
      area,
      dateStr,
      workTime,
      workerTime,
      outAgeTime,
      statusTime,
      percentWork,
      percentOutAge);

  /// Create a copy of StatsWorkModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StatsWorkModelImplCopyWith<_$StatsWorkModelImpl> get copyWith =>
      __$$StatsWorkModelImplCopyWithImpl<_$StatsWorkModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$StatsWorkModelImplToJson(
      this,
    );
  }
}

abstract class _StatsWorkModel extends StatsWorkModel {
  const factory _StatsWorkModel(
      {required final String name,
      required final String componentName,
      final int? area,
      final String? dateStr,
      required final double workTime,
      required final double workerTime,
      required final double outAgeTime,
      required final double statusTime,
      required final double percentWork,
      required final double percentOutAge}) = _$StatsWorkModelImpl;
  const _StatsWorkModel._() : super._();

  factory _StatsWorkModel.fromJson(Map<String, dynamic> json) =
      _$StatsWorkModelImpl.fromJson;

  @override
  String get name;
  @override
  String get componentName;
  @override
  int? get area;
  @override
  String? get dateStr;
  @override
  double get workTime;
  @override
  double get workerTime;
  @override
  double get outAgeTime;
  @override
  double get statusTime;
  @override
  double get percentWork;
  @override
  double get percentOutAge;

  /// Create a copy of StatsWorkModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StatsWorkModelImplCopyWith<_$StatsWorkModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
