// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'stats_ssz_time_result_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

StatsSSZTimeResultModel _$StatsSSZTimeResultModelFromJson(
    Map<String, dynamic> json) {
  return _StatsSSZTimeResultModel.fromJson(json);
}

/// @nodoc
mixin _$StatsSSZTimeResultModel {
  String get name => throw _privateConstructorUsedError;
  List<StatsSSZTimeBranchModel> get timeBranchs =>
      throw _privateConstructorUsedError;

  /// Serializes this StatsSSZTimeResultModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of StatsSSZTimeResultModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $StatsSSZTimeResultModelCopyWith<StatsSSZTimeResultModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StatsSSZTimeResultModelCopyWith<$Res> {
  factory $StatsSSZTimeResultModelCopyWith(StatsSSZTimeResultModel value,
          $Res Function(StatsSSZTimeResultModel) then) =
      _$StatsSSZTimeResultModelCopyWithImpl<$Res, StatsSSZTimeResultModel>;
  @useResult
  $Res call({String name, List<StatsSSZTimeBranchModel> timeBranchs});
}

/// @nodoc
class _$StatsSSZTimeResultModelCopyWithImpl<$Res,
        $Val extends StatsSSZTimeResultModel>
    implements $StatsSSZTimeResultModelCopyWith<$Res> {
  _$StatsSSZTimeResultModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of StatsSSZTimeResultModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? timeBranchs = null,
  }) {
    return _then(_value.copyWith(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      timeBranchs: null == timeBranchs
          ? _value.timeBranchs
          : timeBranchs // ignore: cast_nullable_to_non_nullable
              as List<StatsSSZTimeBranchModel>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$StatsSSZTimeResultModelImplCopyWith<$Res>
    implements $StatsSSZTimeResultModelCopyWith<$Res> {
  factory _$$StatsSSZTimeResultModelImplCopyWith(
          _$StatsSSZTimeResultModelImpl value,
          $Res Function(_$StatsSSZTimeResultModelImpl) then) =
      __$$StatsSSZTimeResultModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String name, List<StatsSSZTimeBranchModel> timeBranchs});
}

/// @nodoc
class __$$StatsSSZTimeResultModelImplCopyWithImpl<$Res>
    extends _$StatsSSZTimeResultModelCopyWithImpl<$Res,
        _$StatsSSZTimeResultModelImpl>
    implements _$$StatsSSZTimeResultModelImplCopyWith<$Res> {
  __$$StatsSSZTimeResultModelImplCopyWithImpl(
      _$StatsSSZTimeResultModelImpl _value,
      $Res Function(_$StatsSSZTimeResultModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of StatsSSZTimeResultModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? timeBranchs = null,
  }) {
    return _then(_$StatsSSZTimeResultModelImpl(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      timeBranchs: null == timeBranchs
          ? _value._timeBranchs
          : timeBranchs // ignore: cast_nullable_to_non_nullable
              as List<StatsSSZTimeBranchModel>,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class _$StatsSSZTimeResultModelImpl extends _StatsSSZTimeResultModel
    with DiagnosticableTreeMixin {
  const _$StatsSSZTimeResultModelImpl(
      {required this.name,
      required final List<StatsSSZTimeBranchModel> timeBranchs})
      : _timeBranchs = timeBranchs,
        super._();

  factory _$StatsSSZTimeResultModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$StatsSSZTimeResultModelImplFromJson(json);

  @override
  final String name;
  final List<StatsSSZTimeBranchModel> _timeBranchs;
  @override
  List<StatsSSZTimeBranchModel> get timeBranchs {
    if (_timeBranchs is EqualUnmodifiableListView) return _timeBranchs;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_timeBranchs);
  }

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'StatsSSZTimeResultModel(name: $name, timeBranchs: $timeBranchs)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'StatsSSZTimeResultModel'))
      ..add(DiagnosticsProperty('name', name))
      ..add(DiagnosticsProperty('timeBranchs', timeBranchs));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StatsSSZTimeResultModelImpl &&
            (identical(other.name, name) || other.name == name) &&
            const DeepCollectionEquality()
                .equals(other._timeBranchs, _timeBranchs));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, name, const DeepCollectionEquality().hash(_timeBranchs));

  /// Create a copy of StatsSSZTimeResultModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StatsSSZTimeResultModelImplCopyWith<_$StatsSSZTimeResultModelImpl>
      get copyWith => __$$StatsSSZTimeResultModelImplCopyWithImpl<
          _$StatsSSZTimeResultModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$StatsSSZTimeResultModelImplToJson(
      this,
    );
  }
}

abstract class _StatsSSZTimeResultModel extends StatsSSZTimeResultModel {
  const factory _StatsSSZTimeResultModel(
          {required final String name,
          required final List<StatsSSZTimeBranchModel> timeBranchs}) =
      _$StatsSSZTimeResultModelImpl;
  const _StatsSSZTimeResultModel._() : super._();

  factory _StatsSSZTimeResultModel.fromJson(Map<String, dynamic> json) =
      _$StatsSSZTimeResultModelImpl.fromJson;

  @override
  String get name;
  @override
  List<StatsSSZTimeBranchModel> get timeBranchs;

  /// Create a copy of StatsSSZTimeResultModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StatsSSZTimeResultModelImplCopyWith<_$StatsSSZTimeResultModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}

StatsSSZTimeResultListModel _$StatsSSZTimeResultListModelFromJson(
    Map<String, dynamic> json) {
  return _StatsSSZTimeResultListModel.fromJson(json);
}

/// @nodoc
mixin _$StatsSSZTimeResultListModel {
  List<StatsSSZTimeResultModel> get data => throw _privateConstructorUsedError;

  /// Serializes this StatsSSZTimeResultListModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of StatsSSZTimeResultListModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $StatsSSZTimeResultListModelCopyWith<StatsSSZTimeResultListModel>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StatsSSZTimeResultListModelCopyWith<$Res> {
  factory $StatsSSZTimeResultListModelCopyWith(
          StatsSSZTimeResultListModel value,
          $Res Function(StatsSSZTimeResultListModel) then) =
      _$StatsSSZTimeResultListModelCopyWithImpl<$Res,
          StatsSSZTimeResultListModel>;
  @useResult
  $Res call({List<StatsSSZTimeResultModel> data});
}

/// @nodoc
class _$StatsSSZTimeResultListModelCopyWithImpl<$Res,
        $Val extends StatsSSZTimeResultListModel>
    implements $StatsSSZTimeResultListModelCopyWith<$Res> {
  _$StatsSSZTimeResultListModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of StatsSSZTimeResultListModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
  }) {
    return _then(_value.copyWith(
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<StatsSSZTimeResultModel>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$StatsSSZTimeResultListModelImplCopyWith<$Res>
    implements $StatsSSZTimeResultListModelCopyWith<$Res> {
  factory _$$StatsSSZTimeResultListModelImplCopyWith(
          _$StatsSSZTimeResultListModelImpl value,
          $Res Function(_$StatsSSZTimeResultListModelImpl) then) =
      __$$StatsSSZTimeResultListModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<StatsSSZTimeResultModel> data});
}

/// @nodoc
class __$$StatsSSZTimeResultListModelImplCopyWithImpl<$Res>
    extends _$StatsSSZTimeResultListModelCopyWithImpl<$Res,
        _$StatsSSZTimeResultListModelImpl>
    implements _$$StatsSSZTimeResultListModelImplCopyWith<$Res> {
  __$$StatsSSZTimeResultListModelImplCopyWithImpl(
      _$StatsSSZTimeResultListModelImpl _value,
      $Res Function(_$StatsSSZTimeResultListModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of StatsSSZTimeResultListModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
  }) {
    return _then(_$StatsSSZTimeResultListModelImpl(
      null == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as List<StatsSSZTimeResultModel>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$StatsSSZTimeResultListModelImpl
    with DiagnosticableTreeMixin
    implements _StatsSSZTimeResultListModel {
  _$StatsSSZTimeResultListModelImpl(final List<StatsSSZTimeResultModel> data)
      : _data = data;

  factory _$StatsSSZTimeResultListModelImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$StatsSSZTimeResultListModelImplFromJson(json);

  final List<StatsSSZTimeResultModel> _data;
  @override
  List<StatsSSZTimeResultModel> get data {
    if (_data is EqualUnmodifiableListView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_data);
  }

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'StatsSSZTimeResultListModel(data: $data)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'StatsSSZTimeResultListModel'))
      ..add(DiagnosticsProperty('data', data));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StatsSSZTimeResultListModelImpl &&
            const DeepCollectionEquality().equals(other._data, _data));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_data));

  /// Create a copy of StatsSSZTimeResultListModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StatsSSZTimeResultListModelImplCopyWith<_$StatsSSZTimeResultListModelImpl>
      get copyWith => __$$StatsSSZTimeResultListModelImplCopyWithImpl<
          _$StatsSSZTimeResultListModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$StatsSSZTimeResultListModelImplToJson(
      this,
    );
  }
}

abstract class _StatsSSZTimeResultListModel
    implements StatsSSZTimeResultListModel {
  factory _StatsSSZTimeResultListModel(
          final List<StatsSSZTimeResultModel> data) =
      _$StatsSSZTimeResultListModelImpl;

  factory _StatsSSZTimeResultListModel.fromJson(Map<String, dynamic> json) =
      _$StatsSSZTimeResultListModelImpl.fromJson;

  @override
  List<StatsSSZTimeResultModel> get data;

  /// Create a copy of StatsSSZTimeResultListModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StatsSSZTimeResultListModelImplCopyWith<_$StatsSSZTimeResultListModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}

StatsSSZTimeBranchModel _$StatsSSZTimeBranchModelFromJson(
    Map<String, dynamic> json) {
  return _StatsSSZTimeBranchModel.fromJson(json);
}

/// @nodoc
mixin _$StatsSSZTimeBranchModel {
  int get branch => throw _privateConstructorUsedError;
  double get workerTime => throw _privateConstructorUsedError;

  /// Serializes this StatsSSZTimeBranchModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of StatsSSZTimeBranchModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $StatsSSZTimeBranchModelCopyWith<StatsSSZTimeBranchModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StatsSSZTimeBranchModelCopyWith<$Res> {
  factory $StatsSSZTimeBranchModelCopyWith(StatsSSZTimeBranchModel value,
          $Res Function(StatsSSZTimeBranchModel) then) =
      _$StatsSSZTimeBranchModelCopyWithImpl<$Res, StatsSSZTimeBranchModel>;
  @useResult
  $Res call({int branch, double workerTime});
}

/// @nodoc
class _$StatsSSZTimeBranchModelCopyWithImpl<$Res,
        $Val extends StatsSSZTimeBranchModel>
    implements $StatsSSZTimeBranchModelCopyWith<$Res> {
  _$StatsSSZTimeBranchModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of StatsSSZTimeBranchModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? branch = null,
    Object? workerTime = null,
  }) {
    return _then(_value.copyWith(
      branch: null == branch
          ? _value.branch
          : branch // ignore: cast_nullable_to_non_nullable
              as int,
      workerTime: null == workerTime
          ? _value.workerTime
          : workerTime // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$StatsSSZTimeBranchModelImplCopyWith<$Res>
    implements $StatsSSZTimeBranchModelCopyWith<$Res> {
  factory _$$StatsSSZTimeBranchModelImplCopyWith(
          _$StatsSSZTimeBranchModelImpl value,
          $Res Function(_$StatsSSZTimeBranchModelImpl) then) =
      __$$StatsSSZTimeBranchModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int branch, double workerTime});
}

/// @nodoc
class __$$StatsSSZTimeBranchModelImplCopyWithImpl<$Res>
    extends _$StatsSSZTimeBranchModelCopyWithImpl<$Res,
        _$StatsSSZTimeBranchModelImpl>
    implements _$$StatsSSZTimeBranchModelImplCopyWith<$Res> {
  __$$StatsSSZTimeBranchModelImplCopyWithImpl(
      _$StatsSSZTimeBranchModelImpl _value,
      $Res Function(_$StatsSSZTimeBranchModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of StatsSSZTimeBranchModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? branch = null,
    Object? workerTime = null,
  }) {
    return _then(_$StatsSSZTimeBranchModelImpl(
      branch: null == branch
          ? _value.branch
          : branch // ignore: cast_nullable_to_non_nullable
              as int,
      workerTime: null == workerTime
          ? _value.workerTime
          : workerTime // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class _$StatsSSZTimeBranchModelImpl extends _StatsSSZTimeBranchModel
    with DiagnosticableTreeMixin {
  const _$StatsSSZTimeBranchModelImpl(
      {required this.branch, required this.workerTime})
      : super._();

  factory _$StatsSSZTimeBranchModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$StatsSSZTimeBranchModelImplFromJson(json);

  @override
  final int branch;
  @override
  final double workerTime;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'StatsSSZTimeBranchModel(branch: $branch, workerTime: $workerTime)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'StatsSSZTimeBranchModel'))
      ..add(DiagnosticsProperty('branch', branch))
      ..add(DiagnosticsProperty('workerTime', workerTime));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StatsSSZTimeBranchModelImpl &&
            (identical(other.branch, branch) || other.branch == branch) &&
            (identical(other.workerTime, workerTime) ||
                other.workerTime == workerTime));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, branch, workerTime);

  /// Create a copy of StatsSSZTimeBranchModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StatsSSZTimeBranchModelImplCopyWith<_$StatsSSZTimeBranchModelImpl>
      get copyWith => __$$StatsSSZTimeBranchModelImplCopyWithImpl<
          _$StatsSSZTimeBranchModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$StatsSSZTimeBranchModelImplToJson(
      this,
    );
  }
}

abstract class _StatsSSZTimeBranchModel extends StatsSSZTimeBranchModel {
  const factory _StatsSSZTimeBranchModel(
      {required final int branch,
      required final double workerTime}) = _$StatsSSZTimeBranchModelImpl;
  const _StatsSSZTimeBranchModel._() : super._();

  factory _StatsSSZTimeBranchModel.fromJson(Map<String, dynamic> json) =
      _$StatsSSZTimeBranchModelImpl.fromJson;

  @override
  int get branch;
  @override
  double get workerTime;

  /// Create a copy of StatsSSZTimeBranchModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StatsSSZTimeBranchModelImplCopyWith<_$StatsSSZTimeBranchModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}
