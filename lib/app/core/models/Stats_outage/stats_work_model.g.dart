// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'stats_work_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$StatsWorkModelImpl _$$StatsWorkModelImplFromJson(Map<String, dynamic> json) =>
    _$StatsWorkModelImpl(
      name: json['name'] as String,
      componentName: json['componentName'] as String,
      area: (json['area'] as num?)?.toInt(),
      dateStr: json['dateStr'] as String?,
      workTime: (json['workTime'] as num).toDouble(),
      workerTime: (json['workerTime'] as num).toDouble(),
      outAgeTime: (json['outAgeTime'] as num).toDouble(),
      statusTime: (json['statusTime'] as num).toDouble(),
      percentWork: (json['percentWork'] as num).toDouble(),
      percentOutAge: (json['percentOutAge'] as num).toDouble(),
    );

Map<String, dynamic> _$$StatsWorkModelImplToJson(
        _$StatsWorkModelImpl instance) =>
    <String, dynamic>{
      'name': instance.name,
      'componentName': instance.componentName,
      if (instance.area case final value?) 'area': value,
      if (instance.dateStr case final value?) 'dateStr': value,
      'workTime': instance.workTime,
      'workerTime': instance.workerTime,
      'outAgeTime': instance.outAgeTime,
      'statusTime': instance.statusTime,
      'percentWork': instance.percentWork,
      'percentOutAge': instance.percentOutAge,
    };
