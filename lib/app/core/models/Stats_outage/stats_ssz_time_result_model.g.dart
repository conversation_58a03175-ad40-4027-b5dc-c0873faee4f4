// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'stats_ssz_time_result_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$StatsSSZTimeResultModelImpl _$$StatsSSZTimeResultModelImplFromJson(
        Map<String, dynamic> json) =>
    _$StatsSSZTimeResultModelImpl(
      name: json['name'] as String,
      timeBranchs: (json['timeBranchs'] as List<dynamic>)
          .map((e) =>
              StatsSSZTimeBranchModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$StatsSSZTimeResultModelImplToJson(
        _$StatsSSZTimeResultModelImpl instance) =>
    <String, dynamic>{
      'name': instance.name,
      'timeBranchs': instance.timeBranchs.map((e) => e.toJson()).toList(),
    };

_$StatsSSZTimeResultListModelImpl _$$StatsSSZTimeResultListModelImplFromJson(
        Map<String, dynamic> json) =>
    _$StatsSSZTimeResultListModelImpl(
      (json['data'] as List<dynamic>)
          .map((e) =>
              StatsSSZTimeResultModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$StatsSSZTimeResultListModelImplToJson(
        _$StatsSSZTimeResultListModelImpl instance) =>
    <String, dynamic>{
      'data': instance.data,
    };

_$StatsSSZTimeBranchModelImpl _$$StatsSSZTimeBranchModelImplFromJson(
        Map<String, dynamic> json) =>
    _$StatsSSZTimeBranchModelImpl(
      branch: (json['branch'] as num).toInt(),
      workerTime: (json['workerTime'] as num).toDouble(),
    );

Map<String, dynamic> _$$StatsSSZTimeBranchModelImplToJson(
        _$StatsSSZTimeBranchModelImpl instance) =>
    <String, dynamic>{
      'branch': instance.branch,
      'workerTime': instance.workerTime,
    };
