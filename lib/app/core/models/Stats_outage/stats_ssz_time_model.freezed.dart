// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'stats_ssz_time_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

StatsSSZTimeModel _$StatsSSZTimeModelFromJson(Map<String, dynamic> json) {
  return _StatsSSZTimeModel.fromJson(json);
}

/// @nodoc
mixin _$StatsSSZTimeModel {
  String get name => throw _privateConstructorUsedError;
  int get area => throw _privateConstructorUsedError;
  double get workerTime => throw _privateConstructorUsedError;

  /// Serializes this StatsSSZTimeModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of StatsSSZTimeModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $StatsSSZTimeModelCopyWith<StatsSSZTimeModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StatsSSZTimeModelCopyWith<$Res> {
  factory $StatsSSZTimeModelCopyWith(
          StatsSSZTimeModel value, $Res Function(StatsSSZTimeModel) then) =
      _$StatsSSZTimeModelCopyWithImpl<$Res, StatsSSZTimeModel>;
  @useResult
  $Res call({String name, int area, double workerTime});
}

/// @nodoc
class _$StatsSSZTimeModelCopyWithImpl<$Res, $Val extends StatsSSZTimeModel>
    implements $StatsSSZTimeModelCopyWith<$Res> {
  _$StatsSSZTimeModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of StatsSSZTimeModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? area = null,
    Object? workerTime = null,
  }) {
    return _then(_value.copyWith(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      area: null == area
          ? _value.area
          : area // ignore: cast_nullable_to_non_nullable
              as int,
      workerTime: null == workerTime
          ? _value.workerTime
          : workerTime // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$StatsSSZTimeModelImplCopyWith<$Res>
    implements $StatsSSZTimeModelCopyWith<$Res> {
  factory _$$StatsSSZTimeModelImplCopyWith(_$StatsSSZTimeModelImpl value,
          $Res Function(_$StatsSSZTimeModelImpl) then) =
      __$$StatsSSZTimeModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String name, int area, double workerTime});
}

/// @nodoc
class __$$StatsSSZTimeModelImplCopyWithImpl<$Res>
    extends _$StatsSSZTimeModelCopyWithImpl<$Res, _$StatsSSZTimeModelImpl>
    implements _$$StatsSSZTimeModelImplCopyWith<$Res> {
  __$$StatsSSZTimeModelImplCopyWithImpl(_$StatsSSZTimeModelImpl _value,
      $Res Function(_$StatsSSZTimeModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of StatsSSZTimeModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? area = null,
    Object? workerTime = null,
  }) {
    return _then(_$StatsSSZTimeModelImpl(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      area: null == area
          ? _value.area
          : area // ignore: cast_nullable_to_non_nullable
              as int,
      workerTime: null == workerTime
          ? _value.workerTime
          : workerTime // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class _$StatsSSZTimeModelImpl extends _StatsSSZTimeModel
    with DiagnosticableTreeMixin {
  const _$StatsSSZTimeModelImpl(
      {required this.name, required this.area, required this.workerTime})
      : super._();

  factory _$StatsSSZTimeModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$StatsSSZTimeModelImplFromJson(json);

  @override
  final String name;
  @override
  final int area;
  @override
  final double workerTime;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'StatsSSZTimeModel(name: $name, area: $area, workerTime: $workerTime)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'StatsSSZTimeModel'))
      ..add(DiagnosticsProperty('name', name))
      ..add(DiagnosticsProperty('area', area))
      ..add(DiagnosticsProperty('workerTime', workerTime));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StatsSSZTimeModelImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.area, area) || other.area == area) &&
            (identical(other.workerTime, workerTime) ||
                other.workerTime == workerTime));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, name, area, workerTime);

  /// Create a copy of StatsSSZTimeModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StatsSSZTimeModelImplCopyWith<_$StatsSSZTimeModelImpl> get copyWith =>
      __$$StatsSSZTimeModelImplCopyWithImpl<_$StatsSSZTimeModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$StatsSSZTimeModelImplToJson(
      this,
    );
  }
}

abstract class _StatsSSZTimeModel extends StatsSSZTimeModel {
  const factory _StatsSSZTimeModel(
      {required final String name,
      required final int area,
      required final double workerTime}) = _$StatsSSZTimeModelImpl;
  const _StatsSSZTimeModel._() : super._();

  factory _StatsSSZTimeModel.fromJson(Map<String, dynamic> json) =
      _$StatsSSZTimeModelImpl.fromJson;

  @override
  String get name;
  @override
  int get area;
  @override
  double get workerTime;

  /// Create a copy of StatsSSZTimeModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StatsSSZTimeModelImplCopyWith<_$StatsSSZTimeModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
