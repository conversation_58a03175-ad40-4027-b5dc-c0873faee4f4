import 'package:freezed_annotation/freezed_annotation.dart';

part 'tasks_stats_model.freezed.dart';
part 'tasks_stats_model.g.dart';

@freezed
class TasksStatsModel with _$TasksStatsModel {
  @JsonSerializable(includeIfNull: false)
  const factory TasksStatsModel({
    TaskStatsModel? selectedStatTask,
    List<TaskStatsModel>? foundStatsTasks,
    List<FoundTaskModel>? foundTasks,
  }) = _TasksStatsModel;

  factory TasksStatsModel.fromJson(Map<String, Object?> json) =>
      _$TasksStatsModelFromJson(json);
}

@freezed
class TaskStatsModel with _$TaskStatsModel {
  @JsonSerializable(includeIfNull: false)
  const factory TaskStatsModel({
    String? id,
    String? name,
  }) = _TaskStatsModel;

  factory TaskStatsModel.fromJson(Map<String, Object?> json) =>
      _$TaskStatsModelFromJson(json);
}

@freezed
class FoundTaskModel with _$FoundTaskModel {
  @JsonSerializable(includeIfNull: false)
  const factory FoundTaskModel({
    String? repairNumber,
    String? equipmentName,
    String? power,
    String? turnovers,
    double? standardHours,
    String? branch,
    String? mainID,
  }) = _FoundTaskModel;

  factory FoundTaskModel.fromJson(Map<String, Object?> json) =>
      _$FoundTaskModelFromJson(json);
}
