import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:uer_flutter/app/core/models/stats/tags_stats_model.dart';

import '../../../core/models/enums/component_type_enum.dart';
import '../../../core/models/item/item_model.dart';

part 'receiving_page_model.freezed.dart';
//part 'receiving_page_model.g.dart';

@freezed
class ReceivingPageModel with _$ReceivingPageModel {
  const ReceivingPageModel._();
  //@JsonSerializable(explicitToJson: true, includeIfNull: false)
  const factory ReceivingPageModel({
    required ItemModel item,
    required int currentTypeIndex, // тип двигатель или трансформатор
    required bool rotorEnable, // ротор / якорь
    required bool statorEnable, // статор / индуктор
    required bool assembledValue, // в сборке или нет
    required bool majorRepair, // капитальный ремонт
    required List<ItemTag> tags, // tags

    required int bearingTypeIndex, // тип подшипников
    String? comment, // коммент

    required List<Uint8List> selectedPhotos,
  }) = _ReceivingPageModel;

  bool transform() {
    if (currentTypeIndex == 2) {
      return true;
    }
    return false;
  }

  ComponentType? getBearingType() {
    switch (bearingTypeIndex) {
      case 1:
        return null;
      case 2:
        return ComponentType.slidingBearing;
      case 3:
        return ComponentType.rollingBearing;
      default:
        return null;
    }
  }

  // factory ReceivingPageModel.fromJson(Map<String, Object?> json) =>
  //     _$ReceivingPageModelFromJson(json);
}
