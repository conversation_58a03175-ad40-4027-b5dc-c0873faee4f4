// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'receiving_page_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ReceivingPageModel {
  ItemModel get item => throw _privateConstructorUsedError;
  int get currentTypeIndex =>
      throw _privateConstructorUsedError; // тип двигатель или трансформатор
  bool get rotorEnable => throw _privateConstructorUsedError; // ротор / якорь
  bool get statorEnable =>
      throw _privateConstructorUsedError; // статор / индуктор
  bool get assembledValue =>
      throw _privateConstructorUsedError; // в сборке или нет
  bool get majorRepair =>
      throw _privateConstructorUsedError; // капитальный ремонт
  List<ItemTag> get tags => throw _privateConstructorUsedError; // tags
  int get bearingTypeIndex =>
      throw _privateConstructorUsedError; // тип подшипников
  String? get comment => throw _privateConstructorUsedError; // коммент
  List<Uint8List> get selectedPhotos => throw _privateConstructorUsedError;

  /// Create a copy of ReceivingPageModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ReceivingPageModelCopyWith<ReceivingPageModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ReceivingPageModelCopyWith<$Res> {
  factory $ReceivingPageModelCopyWith(
          ReceivingPageModel value, $Res Function(ReceivingPageModel) then) =
      _$ReceivingPageModelCopyWithImpl<$Res, ReceivingPageModel>;
  @useResult
  $Res call(
      {ItemModel item,
      int currentTypeIndex,
      bool rotorEnable,
      bool statorEnable,
      bool assembledValue,
      bool majorRepair,
      List<ItemTag> tags,
      int bearingTypeIndex,
      String? comment,
      List<Uint8List> selectedPhotos});

  $ItemModelCopyWith<$Res> get item;
}

/// @nodoc
class _$ReceivingPageModelCopyWithImpl<$Res, $Val extends ReceivingPageModel>
    implements $ReceivingPageModelCopyWith<$Res> {
  _$ReceivingPageModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ReceivingPageModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? item = null,
    Object? currentTypeIndex = null,
    Object? rotorEnable = null,
    Object? statorEnable = null,
    Object? assembledValue = null,
    Object? majorRepair = null,
    Object? tags = null,
    Object? bearingTypeIndex = null,
    Object? comment = freezed,
    Object? selectedPhotos = null,
  }) {
    return _then(_value.copyWith(
      item: null == item
          ? _value.item
          : item // ignore: cast_nullable_to_non_nullable
              as ItemModel,
      currentTypeIndex: null == currentTypeIndex
          ? _value.currentTypeIndex
          : currentTypeIndex // ignore: cast_nullable_to_non_nullable
              as int,
      rotorEnable: null == rotorEnable
          ? _value.rotorEnable
          : rotorEnable // ignore: cast_nullable_to_non_nullable
              as bool,
      statorEnable: null == statorEnable
          ? _value.statorEnable
          : statorEnable // ignore: cast_nullable_to_non_nullable
              as bool,
      assembledValue: null == assembledValue
          ? _value.assembledValue
          : assembledValue // ignore: cast_nullable_to_non_nullable
              as bool,
      majorRepair: null == majorRepair
          ? _value.majorRepair
          : majorRepair // ignore: cast_nullable_to_non_nullable
              as bool,
      tags: null == tags
          ? _value.tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<ItemTag>,
      bearingTypeIndex: null == bearingTypeIndex
          ? _value.bearingTypeIndex
          : bearingTypeIndex // ignore: cast_nullable_to_non_nullable
              as int,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
      selectedPhotos: null == selectedPhotos
          ? _value.selectedPhotos
          : selectedPhotos // ignore: cast_nullable_to_non_nullable
              as List<Uint8List>,
    ) as $Val);
  }

  /// Create a copy of ReceivingPageModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ItemModelCopyWith<$Res> get item {
    return $ItemModelCopyWith<$Res>(_value.item, (value) {
      return _then(_value.copyWith(item: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ReceivingPageModelImplCopyWith<$Res>
    implements $ReceivingPageModelCopyWith<$Res> {
  factory _$$ReceivingPageModelImplCopyWith(_$ReceivingPageModelImpl value,
          $Res Function(_$ReceivingPageModelImpl) then) =
      __$$ReceivingPageModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {ItemModel item,
      int currentTypeIndex,
      bool rotorEnable,
      bool statorEnable,
      bool assembledValue,
      bool majorRepair,
      List<ItemTag> tags,
      int bearingTypeIndex,
      String? comment,
      List<Uint8List> selectedPhotos});

  @override
  $ItemModelCopyWith<$Res> get item;
}

/// @nodoc
class __$$ReceivingPageModelImplCopyWithImpl<$Res>
    extends _$ReceivingPageModelCopyWithImpl<$Res, _$ReceivingPageModelImpl>
    implements _$$ReceivingPageModelImplCopyWith<$Res> {
  __$$ReceivingPageModelImplCopyWithImpl(_$ReceivingPageModelImpl _value,
      $Res Function(_$ReceivingPageModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ReceivingPageModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? item = null,
    Object? currentTypeIndex = null,
    Object? rotorEnable = null,
    Object? statorEnable = null,
    Object? assembledValue = null,
    Object? majorRepair = null,
    Object? tags = null,
    Object? bearingTypeIndex = null,
    Object? comment = freezed,
    Object? selectedPhotos = null,
  }) {
    return _then(_$ReceivingPageModelImpl(
      item: null == item
          ? _value.item
          : item // ignore: cast_nullable_to_non_nullable
              as ItemModel,
      currentTypeIndex: null == currentTypeIndex
          ? _value.currentTypeIndex
          : currentTypeIndex // ignore: cast_nullable_to_non_nullable
              as int,
      rotorEnable: null == rotorEnable
          ? _value.rotorEnable
          : rotorEnable // ignore: cast_nullable_to_non_nullable
              as bool,
      statorEnable: null == statorEnable
          ? _value.statorEnable
          : statorEnable // ignore: cast_nullable_to_non_nullable
              as bool,
      assembledValue: null == assembledValue
          ? _value.assembledValue
          : assembledValue // ignore: cast_nullable_to_non_nullable
              as bool,
      majorRepair: null == majorRepair
          ? _value.majorRepair
          : majorRepair // ignore: cast_nullable_to_non_nullable
              as bool,
      tags: null == tags
          ? _value._tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<ItemTag>,
      bearingTypeIndex: null == bearingTypeIndex
          ? _value.bearingTypeIndex
          : bearingTypeIndex // ignore: cast_nullable_to_non_nullable
              as int,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
      selectedPhotos: null == selectedPhotos
          ? _value._selectedPhotos
          : selectedPhotos // ignore: cast_nullable_to_non_nullable
              as List<Uint8List>,
    ));
  }
}

/// @nodoc

class _$ReceivingPageModelImpl extends _ReceivingPageModel
    with DiagnosticableTreeMixin {
  const _$ReceivingPageModelImpl(
      {required this.item,
      required this.currentTypeIndex,
      required this.rotorEnable,
      required this.statorEnable,
      required this.assembledValue,
      required this.majorRepair,
      required final List<ItemTag> tags,
      required this.bearingTypeIndex,
      this.comment,
      required final List<Uint8List> selectedPhotos})
      : _tags = tags,
        _selectedPhotos = selectedPhotos,
        super._();

  @override
  final ItemModel item;
  @override
  final int currentTypeIndex;
// тип двигатель или трансформатор
  @override
  final bool rotorEnable;
// ротор / якорь
  @override
  final bool statorEnable;
// статор / индуктор
  @override
  final bool assembledValue;
// в сборке или нет
  @override
  final bool majorRepair;
// капитальный ремонт
  final List<ItemTag> _tags;
// капитальный ремонт
  @override
  List<ItemTag> get tags {
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_tags);
  }

// tags
  @override
  final int bearingTypeIndex;
// тип подшипников
  @override
  final String? comment;
// коммент
  final List<Uint8List> _selectedPhotos;
// коммент
  @override
  List<Uint8List> get selectedPhotos {
    if (_selectedPhotos is EqualUnmodifiableListView) return _selectedPhotos;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_selectedPhotos);
  }

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ReceivingPageModel(item: $item, currentTypeIndex: $currentTypeIndex, rotorEnable: $rotorEnable, statorEnable: $statorEnable, assembledValue: $assembledValue, majorRepair: $majorRepair, tags: $tags, bearingTypeIndex: $bearingTypeIndex, comment: $comment, selectedPhotos: $selectedPhotos)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'ReceivingPageModel'))
      ..add(DiagnosticsProperty('item', item))
      ..add(DiagnosticsProperty('currentTypeIndex', currentTypeIndex))
      ..add(DiagnosticsProperty('rotorEnable', rotorEnable))
      ..add(DiagnosticsProperty('statorEnable', statorEnable))
      ..add(DiagnosticsProperty('assembledValue', assembledValue))
      ..add(DiagnosticsProperty('majorRepair', majorRepair))
      ..add(DiagnosticsProperty('tags', tags))
      ..add(DiagnosticsProperty('bearingTypeIndex', bearingTypeIndex))
      ..add(DiagnosticsProperty('comment', comment))
      ..add(DiagnosticsProperty('selectedPhotos', selectedPhotos));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ReceivingPageModelImpl &&
            (identical(other.item, item) || other.item == item) &&
            (identical(other.currentTypeIndex, currentTypeIndex) ||
                other.currentTypeIndex == currentTypeIndex) &&
            (identical(other.rotorEnable, rotorEnable) ||
                other.rotorEnable == rotorEnable) &&
            (identical(other.statorEnable, statorEnable) ||
                other.statorEnable == statorEnable) &&
            (identical(other.assembledValue, assembledValue) ||
                other.assembledValue == assembledValue) &&
            (identical(other.majorRepair, majorRepair) ||
                other.majorRepair == majorRepair) &&
            const DeepCollectionEquality().equals(other._tags, _tags) &&
            (identical(other.bearingTypeIndex, bearingTypeIndex) ||
                other.bearingTypeIndex == bearingTypeIndex) &&
            (identical(other.comment, comment) || other.comment == comment) &&
            const DeepCollectionEquality()
                .equals(other._selectedPhotos, _selectedPhotos));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      item,
      currentTypeIndex,
      rotorEnable,
      statorEnable,
      assembledValue,
      majorRepair,
      const DeepCollectionEquality().hash(_tags),
      bearingTypeIndex,
      comment,
      const DeepCollectionEquality().hash(_selectedPhotos));

  /// Create a copy of ReceivingPageModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ReceivingPageModelImplCopyWith<_$ReceivingPageModelImpl> get copyWith =>
      __$$ReceivingPageModelImplCopyWithImpl<_$ReceivingPageModelImpl>(
          this, _$identity);
}

abstract class _ReceivingPageModel extends ReceivingPageModel {
  const factory _ReceivingPageModel(
          {required final ItemModel item,
          required final int currentTypeIndex,
          required final bool rotorEnable,
          required final bool statorEnable,
          required final bool assembledValue,
          required final bool majorRepair,
          required final List<ItemTag> tags,
          required final int bearingTypeIndex,
          final String? comment,
          required final List<Uint8List> selectedPhotos}) =
      _$ReceivingPageModelImpl;
  const _ReceivingPageModel._() : super._();

  @override
  ItemModel get item;
  @override
  int get currentTypeIndex; // тип двигатель или трансформатор
  @override
  bool get rotorEnable; // ротор / якорь
  @override
  bool get statorEnable; // статор / индуктор
  @override
  bool get assembledValue; // в сборке или нет
  @override
  bool get majorRepair; // капитальный ремонт
  @override
  List<ItemTag> get tags; // tags
  @override
  int get bearingTypeIndex; // тип подшипников
  @override
  String? get comment; // коммент
  @override
  List<Uint8List> get selectedPhotos;

  /// Create a copy of ReceivingPageModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ReceivingPageModelImplCopyWith<_$ReceivingPageModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
