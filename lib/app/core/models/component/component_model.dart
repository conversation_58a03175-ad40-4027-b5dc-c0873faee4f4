import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:uer_flutter/app/core/models/stats/tags_stats_model.dart';
import 'package:uer_flutter/app/helpers/date_extension.dart';

import '../component_history/component_history_model.dart';
import '../enums/component_type_enum.dart';
import '../enums/history_status_enum.dart';

part 'component_model.freezed.dart';
part 'component_model.g.dart';

@freezed
class ComponentModel with _$ComponentModel {
  const ComponentModel._();
  @JsonSerializable(explicitToJson: true, includeIfNull: false)
  const factory ComponentModel({
    required String identifier,
    required ComponentType type,
    required int currentArea,
    List<int>? accessArea,
    List<ComponentHistoryModel>? newHistory,
    List<ItemTag>? tags,
    double? createdAt,
    double? updatedAt,
  }) = _ComponentModel;

  ComponentStatusModel? lastStatus() {
    var lastHistory = newHistory?.last;
    var lastStatus = lastHistory?.statuses.last;
    return lastStatus;
  }

  String lastUpdate() {
    var status = lastStatus();
    var updateTime = status?.updatedAt;

    if (updateTime != null) {
      return DateTime.fromMillisecondsSinceEpoch(updateTime.toInt() * 1000)
          .timeAgo();
    } else {
      return DateTime.now().timeAgo();
    }
  }

  List<ComponentHistoryModel> getActiveHistory() {
    if (newHistory == null) {
      return [];
    }

    List<ComponentHistoryModel> arrHistory = [];

    for (var hist in newHistory!) {
      var lastStatus = hist.statuses.last.status;

      if (lastStatus == HistoryStatusType.pause ||
          lastStatus == HistoryStatusType.inwork) {
        arrHistory.add(hist);
      }
    }

    return arrHistory;
  }

  factory ComponentModel.fromJson(Map<String, Object?> json) =>
      _$ComponentModelFromJson(json);
}
