// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'component_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ComponentModelImpl _$$ComponentModelImplFromJson(Map<String, dynamic> json) =>
    _$ComponentModelImpl(
      identifier: json['identifier'] as String,
      type: $enumDecode(_$ComponentTypeEnumMap, json['type']),
      currentArea: (json['currentArea'] as num).toInt(),
      accessArea: (json['accessArea'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
      newHistory: (json['newHistory'] as List<dynamic>?)
          ?.map(
              (e) => ComponentHistoryModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      tags: (json['tags'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$ItemTagEnumMap, e))
          .toList(),
      createdAt: (json['createdAt'] as num?)?.toDouble(),
      updatedAt: (json['updatedAt'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$ComponentModelImplToJson(
        _$ComponentModelImpl instance) =>
    <String, dynamic>{
      'identifier': instance.identifier,
      'type': _$ComponentTypeEnumMap[instance.type]!,
      'currentArea': instance.currentArea,
      if (instance.accessArea case final value?) 'accessArea': value,
      if (instance.newHistory?.map((e) => e.toJson()).toList()
          case final value?)
        'newHistory': value,
      if (instance.tags?.map((e) => _$ItemTagEnumMap[e]!).toList()
          case final value?)
        'tags': value,
      if (instance.createdAt case final value?) 'createdAt': value,
      if (instance.updatedAt case final value?) 'updatedAt': value,
    };

const _$ComponentTypeEnumMap = {
  ComponentType.stator: 'stator',
  ComponentType.rotor: 'rotor',
  ComponentType.inductor: 'inductor',
  ComponentType.anchor: 'anchor',
  ComponentType.slidingBearing: 'slidingBearing',
  ComponentType.rollingBearing: 'rollingBearing',
  ComponentType.transformer: 'transformer',
};

const _$ItemTagEnumMap = {
  ItemTag.itemDefault: 'default',
  ItemTag.urgent: 'urgent',
  ItemTag.important: 'important',
  ItemTag.onHold: 'on_hold',
  ItemTag.warranty: 'warranty',
};
