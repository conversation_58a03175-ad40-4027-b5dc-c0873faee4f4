// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'component_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ComponentModel _$ComponentModelFromJson(Map<String, dynamic> json) {
  return _ComponentModel.fromJson(json);
}

/// @nodoc
mixin _$ComponentModel {
  String get identifier => throw _privateConstructorUsedError;
  ComponentType<dynamic> get type => throw _privateConstructorUsedError;
  int get currentArea => throw _privateConstructorUsedError;
  List<int>? get accessArea => throw _privateConstructorUsedError;
  List<ComponentHistoryModel>? get newHistory =>
      throw _privateConstructorUsedError;
  List<ItemTag>? get tags => throw _privateConstructorUsedError;
  double? get createdAt => throw _privateConstructorUsedError;
  double? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this ComponentModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ComponentModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ComponentModelCopyWith<ComponentModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ComponentModelCopyWith<$Res> {
  factory $ComponentModelCopyWith(
          ComponentModel value, $Res Function(ComponentModel) then) =
      _$ComponentModelCopyWithImpl<$Res, ComponentModel>;
  @useResult
  $Res call(
      {String identifier,
      ComponentType<dynamic> type,
      int currentArea,
      List<int>? accessArea,
      List<ComponentHistoryModel>? newHistory,
      List<ItemTag>? tags,
      double? createdAt,
      double? updatedAt});
}

/// @nodoc
class _$ComponentModelCopyWithImpl<$Res, $Val extends ComponentModel>
    implements $ComponentModelCopyWith<$Res> {
  _$ComponentModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ComponentModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? identifier = null,
    Object? type = null,
    Object? currentArea = null,
    Object? accessArea = freezed,
    Object? newHistory = freezed,
    Object? tags = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_value.copyWith(
      identifier: null == identifier
          ? _value.identifier
          : identifier // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as ComponentType<dynamic>,
      currentArea: null == currentArea
          ? _value.currentArea
          : currentArea // ignore: cast_nullable_to_non_nullable
              as int,
      accessArea: freezed == accessArea
          ? _value.accessArea
          : accessArea // ignore: cast_nullable_to_non_nullable
              as List<int>?,
      newHistory: freezed == newHistory
          ? _value.newHistory
          : newHistory // ignore: cast_nullable_to_non_nullable
              as List<ComponentHistoryModel>?,
      tags: freezed == tags
          ? _value.tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<ItemTag>?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as double?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ComponentModelImplCopyWith<$Res>
    implements $ComponentModelCopyWith<$Res> {
  factory _$$ComponentModelImplCopyWith(_$ComponentModelImpl value,
          $Res Function(_$ComponentModelImpl) then) =
      __$$ComponentModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String identifier,
      ComponentType<dynamic> type,
      int currentArea,
      List<int>? accessArea,
      List<ComponentHistoryModel>? newHistory,
      List<ItemTag>? tags,
      double? createdAt,
      double? updatedAt});
}

/// @nodoc
class __$$ComponentModelImplCopyWithImpl<$Res>
    extends _$ComponentModelCopyWithImpl<$Res, _$ComponentModelImpl>
    implements _$$ComponentModelImplCopyWith<$Res> {
  __$$ComponentModelImplCopyWithImpl(
      _$ComponentModelImpl _value, $Res Function(_$ComponentModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ComponentModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? identifier = null,
    Object? type = null,
    Object? currentArea = null,
    Object? accessArea = freezed,
    Object? newHistory = freezed,
    Object? tags = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_$ComponentModelImpl(
      identifier: null == identifier
          ? _value.identifier
          : identifier // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as ComponentType<dynamic>,
      currentArea: null == currentArea
          ? _value.currentArea
          : currentArea // ignore: cast_nullable_to_non_nullable
              as int,
      accessArea: freezed == accessArea
          ? _value._accessArea
          : accessArea // ignore: cast_nullable_to_non_nullable
              as List<int>?,
      newHistory: freezed == newHistory
          ? _value._newHistory
          : newHistory // ignore: cast_nullable_to_non_nullable
              as List<ComponentHistoryModel>?,
      tags: freezed == tags
          ? _value._tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<ItemTag>?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as double?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class _$ComponentModelImpl extends _ComponentModel
    with DiagnosticableTreeMixin {
  const _$ComponentModelImpl(
      {required this.identifier,
      required this.type,
      required this.currentArea,
      final List<int>? accessArea,
      final List<ComponentHistoryModel>? newHistory,
      final List<ItemTag>? tags,
      this.createdAt,
      this.updatedAt})
      : _accessArea = accessArea,
        _newHistory = newHistory,
        _tags = tags,
        super._();

  factory _$ComponentModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ComponentModelImplFromJson(json);

  @override
  final String identifier;
  @override
  final ComponentType<dynamic> type;
  @override
  final int currentArea;
  final List<int>? _accessArea;
  @override
  List<int>? get accessArea {
    final value = _accessArea;
    if (value == null) return null;
    if (_accessArea is EqualUnmodifiableListView) return _accessArea;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<ComponentHistoryModel>? _newHistory;
  @override
  List<ComponentHistoryModel>? get newHistory {
    final value = _newHistory;
    if (value == null) return null;
    if (_newHistory is EqualUnmodifiableListView) return _newHistory;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<ItemTag>? _tags;
  @override
  List<ItemTag>? get tags {
    final value = _tags;
    if (value == null) return null;
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final double? createdAt;
  @override
  final double? updatedAt;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ComponentModel(identifier: $identifier, type: $type, currentArea: $currentArea, accessArea: $accessArea, newHistory: $newHistory, tags: $tags, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'ComponentModel'))
      ..add(DiagnosticsProperty('identifier', identifier))
      ..add(DiagnosticsProperty('type', type))
      ..add(DiagnosticsProperty('currentArea', currentArea))
      ..add(DiagnosticsProperty('accessArea', accessArea))
      ..add(DiagnosticsProperty('newHistory', newHistory))
      ..add(DiagnosticsProperty('tags', tags))
      ..add(DiagnosticsProperty('createdAt', createdAt))
      ..add(DiagnosticsProperty('updatedAt', updatedAt));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ComponentModelImpl &&
            (identical(other.identifier, identifier) ||
                other.identifier == identifier) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.currentArea, currentArea) ||
                other.currentArea == currentArea) &&
            const DeepCollectionEquality()
                .equals(other._accessArea, _accessArea) &&
            const DeepCollectionEquality()
                .equals(other._newHistory, _newHistory) &&
            const DeepCollectionEquality().equals(other._tags, _tags) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      identifier,
      type,
      currentArea,
      const DeepCollectionEquality().hash(_accessArea),
      const DeepCollectionEquality().hash(_newHistory),
      const DeepCollectionEquality().hash(_tags),
      createdAt,
      updatedAt);

  /// Create a copy of ComponentModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ComponentModelImplCopyWith<_$ComponentModelImpl> get copyWith =>
      __$$ComponentModelImplCopyWithImpl<_$ComponentModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ComponentModelImplToJson(
      this,
    );
  }
}

abstract class _ComponentModel extends ComponentModel {
  const factory _ComponentModel(
      {required final String identifier,
      required final ComponentType<dynamic> type,
      required final int currentArea,
      final List<int>? accessArea,
      final List<ComponentHistoryModel>? newHistory,
      final List<ItemTag>? tags,
      final double? createdAt,
      final double? updatedAt}) = _$ComponentModelImpl;
  const _ComponentModel._() : super._();

  factory _ComponentModel.fromJson(Map<String, dynamic> json) =
      _$ComponentModelImpl.fromJson;

  @override
  String get identifier;
  @override
  ComponentType<dynamic> get type;
  @override
  int get currentArea;
  @override
  List<int>? get accessArea;
  @override
  List<ComponentHistoryModel>? get newHistory;
  @override
  List<ItemTag>? get tags;
  @override
  double? get createdAt;
  @override
  double? get updatedAt;

  /// Create a copy of ComponentModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ComponentModelImplCopyWith<_$ComponentModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
