// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

UserModel _$UserModelFromJson(Map<String, dynamic> json) {
  return _UserModel.fromJson(json);
}

/// @nodoc
mixin _$UserModel {
  @JsonKey(name: '_id')
  String? get id => throw _privateConstructorUsedError; // _id
  String? get name => throw _privateConstructorUsedError;
  String get login => throw _privateConstructorUsedError;
  String? get password => throw _privateConstructorUsedError;
  UserRole<dynamic> get role => throw _privateConstructorUsedError;
  UserType? get userType => throw _privateConstructorUsedError;
  int? get area => throw _privateConstructorUsedError;
  int? get branch => throw _privateConstructorUsedError;
  List<String>? get subscribe => throw _privateConstructorUsedError;
  List<String>? get subscribeSSZ => throw _privateConstructorUsedError;
  bool? get subscribeZPR => throw _privateConstructorUsedError;
  List<String>? get subscribeItems => throw _privateConstructorUsedError;
  List<String>? get subscribeOutage => throw _privateConstructorUsedError;
  bool? get simpleJobAdd => throw _privateConstructorUsedError;
  double? get createdAt => throw _privateConstructorUsedError;
  double? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this UserModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UserModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserModelCopyWith<UserModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserModelCopyWith<$Res> {
  factory $UserModelCopyWith(UserModel value, $Res Function(UserModel) then) =
      _$UserModelCopyWithImpl<$Res, UserModel>;
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String? id,
      String? name,
      String login,
      String? password,
      UserRole<dynamic> role,
      UserType? userType,
      int? area,
      int? branch,
      List<String>? subscribe,
      List<String>? subscribeSSZ,
      bool? subscribeZPR,
      List<String>? subscribeItems,
      List<String>? subscribeOutage,
      bool? simpleJobAdd,
      double? createdAt,
      double? updatedAt});
}

/// @nodoc
class _$UserModelCopyWithImpl<$Res, $Val extends UserModel>
    implements $UserModelCopyWith<$Res> {
  _$UserModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? login = null,
    Object? password = freezed,
    Object? role = null,
    Object? userType = freezed,
    Object? area = freezed,
    Object? branch = freezed,
    Object? subscribe = freezed,
    Object? subscribeSSZ = freezed,
    Object? subscribeZPR = freezed,
    Object? subscribeItems = freezed,
    Object? subscribeOutage = freezed,
    Object? simpleJobAdd = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      login: null == login
          ? _value.login
          : login // ignore: cast_nullable_to_non_nullable
              as String,
      password: freezed == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String?,
      role: null == role
          ? _value.role
          : role // ignore: cast_nullable_to_non_nullable
              as UserRole<dynamic>,
      userType: freezed == userType
          ? _value.userType
          : userType // ignore: cast_nullable_to_non_nullable
              as UserType?,
      area: freezed == area
          ? _value.area
          : area // ignore: cast_nullable_to_non_nullable
              as int?,
      branch: freezed == branch
          ? _value.branch
          : branch // ignore: cast_nullable_to_non_nullable
              as int?,
      subscribe: freezed == subscribe
          ? _value.subscribe
          : subscribe // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      subscribeSSZ: freezed == subscribeSSZ
          ? _value.subscribeSSZ
          : subscribeSSZ // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      subscribeZPR: freezed == subscribeZPR
          ? _value.subscribeZPR
          : subscribeZPR // ignore: cast_nullable_to_non_nullable
              as bool?,
      subscribeItems: freezed == subscribeItems
          ? _value.subscribeItems
          : subscribeItems // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      subscribeOutage: freezed == subscribeOutage
          ? _value.subscribeOutage
          : subscribeOutage // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      simpleJobAdd: freezed == simpleJobAdd
          ? _value.simpleJobAdd
          : simpleJobAdd // ignore: cast_nullable_to_non_nullable
              as bool?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as double?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UserModelImplCopyWith<$Res>
    implements $UserModelCopyWith<$Res> {
  factory _$$UserModelImplCopyWith(
          _$UserModelImpl value, $Res Function(_$UserModelImpl) then) =
      __$$UserModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String? id,
      String? name,
      String login,
      String? password,
      UserRole<dynamic> role,
      UserType? userType,
      int? area,
      int? branch,
      List<String>? subscribe,
      List<String>? subscribeSSZ,
      bool? subscribeZPR,
      List<String>? subscribeItems,
      List<String>? subscribeOutage,
      bool? simpleJobAdd,
      double? createdAt,
      double? updatedAt});
}

/// @nodoc
class __$$UserModelImplCopyWithImpl<$Res>
    extends _$UserModelCopyWithImpl<$Res, _$UserModelImpl>
    implements _$$UserModelImplCopyWith<$Res> {
  __$$UserModelImplCopyWithImpl(
      _$UserModelImpl _value, $Res Function(_$UserModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? login = null,
    Object? password = freezed,
    Object? role = null,
    Object? userType = freezed,
    Object? area = freezed,
    Object? branch = freezed,
    Object? subscribe = freezed,
    Object? subscribeSSZ = freezed,
    Object? subscribeZPR = freezed,
    Object? subscribeItems = freezed,
    Object? subscribeOutage = freezed,
    Object? simpleJobAdd = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_$UserModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      login: null == login
          ? _value.login
          : login // ignore: cast_nullable_to_non_nullable
              as String,
      password: freezed == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String?,
      role: null == role
          ? _value.role
          : role // ignore: cast_nullable_to_non_nullable
              as UserRole<dynamic>,
      userType: freezed == userType
          ? _value.userType
          : userType // ignore: cast_nullable_to_non_nullable
              as UserType?,
      area: freezed == area
          ? _value.area
          : area // ignore: cast_nullable_to_non_nullable
              as int?,
      branch: freezed == branch
          ? _value.branch
          : branch // ignore: cast_nullable_to_non_nullable
              as int?,
      subscribe: freezed == subscribe
          ? _value._subscribe
          : subscribe // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      subscribeSSZ: freezed == subscribeSSZ
          ? _value._subscribeSSZ
          : subscribeSSZ // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      subscribeZPR: freezed == subscribeZPR
          ? _value.subscribeZPR
          : subscribeZPR // ignore: cast_nullable_to_non_nullable
              as bool?,
      subscribeItems: freezed == subscribeItems
          ? _value._subscribeItems
          : subscribeItems // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      subscribeOutage: freezed == subscribeOutage
          ? _value._subscribeOutage
          : subscribeOutage // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      simpleJobAdd: freezed == simpleJobAdd
          ? _value.simpleJobAdd
          : simpleJobAdd // ignore: cast_nullable_to_non_nullable
              as bool?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as double?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class _$UserModelImpl extends _UserModel with DiagnosticableTreeMixin {
  const _$UserModelImpl(
      {@JsonKey(name: '_id') this.id,
      this.name,
      required this.login,
      this.password,
      this.role = UserRole.user,
      this.userType,
      this.area,
      this.branch,
      final List<String>? subscribe,
      final List<String>? subscribeSSZ,
      this.subscribeZPR,
      final List<String>? subscribeItems,
      final List<String>? subscribeOutage,
      this.simpleJobAdd,
      this.createdAt,
      this.updatedAt})
      : _subscribe = subscribe,
        _subscribeSSZ = subscribeSSZ,
        _subscribeItems = subscribeItems,
        _subscribeOutage = subscribeOutage,
        super._();

  factory _$UserModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserModelImplFromJson(json);

  @override
  @JsonKey(name: '_id')
  final String? id;
// _id
  @override
  final String? name;
  @override
  final String login;
  @override
  final String? password;
  @override
  @JsonKey()
  final UserRole<dynamic> role;
  @override
  final UserType? userType;
  @override
  final int? area;
  @override
  final int? branch;
  final List<String>? _subscribe;
  @override
  List<String>? get subscribe {
    final value = _subscribe;
    if (value == null) return null;
    if (_subscribe is EqualUnmodifiableListView) return _subscribe;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<String>? _subscribeSSZ;
  @override
  List<String>? get subscribeSSZ {
    final value = _subscribeSSZ;
    if (value == null) return null;
    if (_subscribeSSZ is EqualUnmodifiableListView) return _subscribeSSZ;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final bool? subscribeZPR;
  final List<String>? _subscribeItems;
  @override
  List<String>? get subscribeItems {
    final value = _subscribeItems;
    if (value == null) return null;
    if (_subscribeItems is EqualUnmodifiableListView) return _subscribeItems;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<String>? _subscribeOutage;
  @override
  List<String>? get subscribeOutage {
    final value = _subscribeOutage;
    if (value == null) return null;
    if (_subscribeOutage is EqualUnmodifiableListView) return _subscribeOutage;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final bool? simpleJobAdd;
  @override
  final double? createdAt;
  @override
  final double? updatedAt;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'UserModel(id: $id, name: $name, login: $login, password: $password, role: $role, userType: $userType, area: $area, branch: $branch, subscribe: $subscribe, subscribeSSZ: $subscribeSSZ, subscribeZPR: $subscribeZPR, subscribeItems: $subscribeItems, subscribeOutage: $subscribeOutage, simpleJobAdd: $simpleJobAdd, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'UserModel'))
      ..add(DiagnosticsProperty('id', id))
      ..add(DiagnosticsProperty('name', name))
      ..add(DiagnosticsProperty('login', login))
      ..add(DiagnosticsProperty('password', password))
      ..add(DiagnosticsProperty('role', role))
      ..add(DiagnosticsProperty('userType', userType))
      ..add(DiagnosticsProperty('area', area))
      ..add(DiagnosticsProperty('branch', branch))
      ..add(DiagnosticsProperty('subscribe', subscribe))
      ..add(DiagnosticsProperty('subscribeSSZ', subscribeSSZ))
      ..add(DiagnosticsProperty('subscribeZPR', subscribeZPR))
      ..add(DiagnosticsProperty('subscribeItems', subscribeItems))
      ..add(DiagnosticsProperty('subscribeOutage', subscribeOutage))
      ..add(DiagnosticsProperty('simpleJobAdd', simpleJobAdd))
      ..add(DiagnosticsProperty('createdAt', createdAt))
      ..add(DiagnosticsProperty('updatedAt', updatedAt));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.login, login) || other.login == login) &&
            (identical(other.password, password) ||
                other.password == password) &&
            (identical(other.role, role) || other.role == role) &&
            (identical(other.userType, userType) ||
                other.userType == userType) &&
            (identical(other.area, area) || other.area == area) &&
            (identical(other.branch, branch) || other.branch == branch) &&
            const DeepCollectionEquality()
                .equals(other._subscribe, _subscribe) &&
            const DeepCollectionEquality()
                .equals(other._subscribeSSZ, _subscribeSSZ) &&
            (identical(other.subscribeZPR, subscribeZPR) ||
                other.subscribeZPR == subscribeZPR) &&
            const DeepCollectionEquality()
                .equals(other._subscribeItems, _subscribeItems) &&
            const DeepCollectionEquality()
                .equals(other._subscribeOutage, _subscribeOutage) &&
            (identical(other.simpleJobAdd, simpleJobAdd) ||
                other.simpleJobAdd == simpleJobAdd) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      login,
      password,
      role,
      userType,
      area,
      branch,
      const DeepCollectionEquality().hash(_subscribe),
      const DeepCollectionEquality().hash(_subscribeSSZ),
      subscribeZPR,
      const DeepCollectionEquality().hash(_subscribeItems),
      const DeepCollectionEquality().hash(_subscribeOutage),
      simpleJobAdd,
      createdAt,
      updatedAt);

  /// Create a copy of UserModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserModelImplCopyWith<_$UserModelImpl> get copyWith =>
      __$$UserModelImplCopyWithImpl<_$UserModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserModelImplToJson(
      this,
    );
  }
}

abstract class _UserModel extends UserModel {
  const factory _UserModel(
      {@JsonKey(name: '_id') final String? id,
      final String? name,
      required final String login,
      final String? password,
      final UserRole<dynamic> role,
      final UserType? userType,
      final int? area,
      final int? branch,
      final List<String>? subscribe,
      final List<String>? subscribeSSZ,
      final bool? subscribeZPR,
      final List<String>? subscribeItems,
      final List<String>? subscribeOutage,
      final bool? simpleJobAdd,
      final double? createdAt,
      final double? updatedAt}) = _$UserModelImpl;
  const _UserModel._() : super._();

  factory _UserModel.fromJson(Map<String, dynamic> json) =
      _$UserModelImpl.fromJson;

  @override
  @JsonKey(name: '_id')
  String? get id; // _id
  @override
  String? get name;
  @override
  String get login;
  @override
  String? get password;
  @override
  UserRole<dynamic> get role;
  @override
  UserType? get userType;
  @override
  int? get area;
  @override
  int? get branch;
  @override
  List<String>? get subscribe;
  @override
  List<String>? get subscribeSSZ;
  @override
  bool? get subscribeZPR;
  @override
  List<String>? get subscribeItems;
  @override
  List<String>? get subscribeOutage;
  @override
  bool? get simpleJobAdd;
  @override
  double? get createdAt;
  @override
  double? get updatedAt;

  /// Create a copy of UserModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserModelImplCopyWith<_$UserModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

UserListModel _$UserListModelFromJson(Map<String, dynamic> json) {
  return _UserListModel.fromJson(json);
}

/// @nodoc
mixin _$UserListModel {
  List<UserModel> get data => throw _privateConstructorUsedError;

  /// Serializes this UserListModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UserListModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserListModelCopyWith<UserListModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserListModelCopyWith<$Res> {
  factory $UserListModelCopyWith(
          UserListModel value, $Res Function(UserListModel) then) =
      _$UserListModelCopyWithImpl<$Res, UserListModel>;
  @useResult
  $Res call({List<UserModel> data});
}

/// @nodoc
class _$UserListModelCopyWithImpl<$Res, $Val extends UserListModel>
    implements $UserListModelCopyWith<$Res> {
  _$UserListModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserListModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
  }) {
    return _then(_value.copyWith(
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<UserModel>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UserListModelImplCopyWith<$Res>
    implements $UserListModelCopyWith<$Res> {
  factory _$$UserListModelImplCopyWith(
          _$UserListModelImpl value, $Res Function(_$UserListModelImpl) then) =
      __$$UserListModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<UserModel> data});
}

/// @nodoc
class __$$UserListModelImplCopyWithImpl<$Res>
    extends _$UserListModelCopyWithImpl<$Res, _$UserListModelImpl>
    implements _$$UserListModelImplCopyWith<$Res> {
  __$$UserListModelImplCopyWithImpl(
      _$UserListModelImpl _value, $Res Function(_$UserListModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserListModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
  }) {
    return _then(_$UserListModelImpl(
      null == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as List<UserModel>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserListModelImpl
    with DiagnosticableTreeMixin
    implements _UserListModel {
  _$UserListModelImpl(final List<UserModel> data) : _data = data;

  factory _$UserListModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserListModelImplFromJson(json);

  final List<UserModel> _data;
  @override
  List<UserModel> get data {
    if (_data is EqualUnmodifiableListView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_data);
  }

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'UserListModel(data: $data)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'UserListModel'))
      ..add(DiagnosticsProperty('data', data));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserListModelImpl &&
            const DeepCollectionEquality().equals(other._data, _data));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_data));

  /// Create a copy of UserListModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserListModelImplCopyWith<_$UserListModelImpl> get copyWith =>
      __$$UserListModelImplCopyWithImpl<_$UserListModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserListModelImplToJson(
      this,
    );
  }
}

abstract class _UserListModel implements UserListModel {
  factory _UserListModel(final List<UserModel> data) = _$UserListModelImpl;

  factory _UserListModel.fromJson(Map<String, dynamic> json) =
      _$UserListModelImpl.fromJson;

  @override
  List<UserModel> get data;

  /// Create a copy of UserListModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserListModelImplCopyWith<_$UserListModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
