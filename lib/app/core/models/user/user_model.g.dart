// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$UserModelImpl _$$UserModelImplFromJson(Map<String, dynamic> json) =>
    _$UserModelImpl(
      id: json['_id'] as String?,
      name: json['name'] as String?,
      login: json['login'] as String,
      password: json['password'] as String?,
      role:
          $enumDecodeNullable(_$UserRoleEnumMap, json['role']) ?? UserRole.user,
      userType: $enumDecodeNullable(_$UserTypeEnumMap, json['userType']),
      area: (json['area'] as num?)?.toInt(),
      branch: (json['branch'] as num?)?.toInt(),
      subscribe: (json['subscribe'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      subscribeSSZ: (json['subscribeSSZ'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      subscribeZPR: json['subscribeZPR'] as bool?,
      subscribeItems: (json['subscribeItems'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      subscribeOutage: (json['subscribeOutage'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      simpleJobAdd: json['simpleJobAdd'] as bool?,
      createdAt: (json['createdAt'] as num?)?.toDouble(),
      updatedAt: (json['updatedAt'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$UserModelImplToJson(_$UserModelImpl instance) =>
    <String, dynamic>{
      if (instance.id case final value?) '_id': value,
      if (instance.name case final value?) 'name': value,
      'login': instance.login,
      if (instance.password case final value?) 'password': value,
      'role': _$UserRoleEnumMap[instance.role]!,
      if (_$UserTypeEnumMap[instance.userType] case final value?)
        'userType': value,
      if (instance.area case final value?) 'area': value,
      if (instance.branch case final value?) 'branch': value,
      if (instance.subscribe case final value?) 'subscribe': value,
      if (instance.subscribeSSZ case final value?) 'subscribeSSZ': value,
      if (instance.subscribeZPR case final value?) 'subscribeZPR': value,
      if (instance.subscribeItems case final value?) 'subscribeItems': value,
      if (instance.subscribeOutage case final value?) 'subscribeOutage': value,
      if (instance.simpleJobAdd case final value?) 'simpleJobAdd': value,
      if (instance.createdAt case final value?) 'createdAt': value,
      if (instance.updatedAt case final value?) 'updatedAt': value,
    };

const _$UserRoleEnumMap = {
  UserRole.admin: 'admin',
  UserRole.manager: 'manager',
  UserRole.director: 'director',
  UserRole.user: 'user',
  UserRole.client: 'client',
  UserRole.station: 'station',
  UserRole.block: 'block',
};

const _$UserTypeEnumMap = {
  UserType.distributor: 'distributor',
  UserType.controlMaster: 'controlMaster',
  UserType.mainEngineer: 'mainEngineer',
  UserType.headArea: 'headArea',
  UserType.pdo: 'pdo',
  UserType.empty: 'empty',
};

_$UserListModelImpl _$$UserListModelImplFromJson(Map<String, dynamic> json) =>
    _$UserListModelImpl(
      (json['data'] as List<dynamic>)
          .map((e) => UserModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$UserListModelImplToJson(_$UserListModelImpl instance) =>
    <String, dynamic>{
      'data': instance.data,
    };
