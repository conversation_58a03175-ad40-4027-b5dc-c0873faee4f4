import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter/foundation.dart';
import '../enums/user_role_enum.dart';
import '../enums/user_type_enum.dart';

part 'user_model.freezed.dart';
part 'user_model.g.dart';

@freezed
class UserModel with _$UserModel {
  const UserModel._();
  @JsonSerializable(explicitToJson: true, includeIfNull: false)
  const factory UserModel({
    @JsonKey(name: '_id') String? id, // _id
    String? name,
    required String login,
    String? password,
    @Default(UserRole.user) UserRole role,
    UserType? userType,
    int? area,
    int? branch,
    List<String>? subscribe,
    List<String>? subscribeSSZ,
    bool? subscribeZPR,
    List<String>? subscribeItems,
    List<String>? subscribeOutage,
    bool? simpleJobAdd,
    double? createdAt,
    double? updatedAt,
  }) = _UserModel;

  bool checkSubscribeSSZ(String subscribeSSZ) {
    if (this.subscribeSSZ == null) {
      return false;
    }

    for (var i = 0; i < this.subscribeSSZ!.length; i++) {
      var subscribe = this.subscribeSSZ![i];

      if (subscribe == subscribeSSZ) {
        return true;
      }
    }

    return false;
  }

  bool checkSubscribeEvent(int areaID, int statusID) {
    if (subscribe == null) {
      return false;
    }

    var subscribeID = "${areaID}_$statusID";

    for (var i = 0; i < subscribe!.length; i++) {
      var subscribeValue = subscribe![i];

      if (subscribeValue == subscribeID) {
        return true;
      }
    }

    return false;
  }

  factory UserModel.fromJson(Map<String, Object?> json) =>
      _$UserModelFromJson(json);
}

@freezed
class UserListModel with _$UserListModel {
  factory UserListModel(List<UserModel> data) = _UserListModel;

  factory UserListModel.fromJson(Map<String, dynamic> json) =>
      _$UserListModelFromJson(json);
}
