// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'component_history_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ComponentHistoryModel _$ComponentHistoryModelFromJson(
    Map<String, dynamic> json) {
  return _ComponentHistoryModel.fromJson(json);
}

/// @nodoc
mixin _$ComponentHistoryModel {
  String get name => throw _privateConstructorUsedError;
  ComponentHistoryType<dynamic> get type => throw _privateConstructorUsedError;
  List<ComponentStatusModel> get statuses => throw _privateConstructorUsedError;
  List<WorkerModel>? get workers => throw _privateConstructorUsedError;
  int? get area => throw _privateConstructorUsedError; // area id
  int? get job =>
      throw _privateConstructorUsedError; // job id (local job,status in app)
  String? get task =>
      throw _privateConstructorUsedError; // task job id (job in 1c)
  String? get taskID => throw _privateConstructorUsedError; // id ssz
  String? get identifier =>
      throw _privateConstructorUsedError; // generated uniq id
  String? get histMainID => throw _privateConstructorUsedError;
  double? get createdAt => throw _privateConstructorUsedError;
  double? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this ComponentHistoryModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ComponentHistoryModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ComponentHistoryModelCopyWith<ComponentHistoryModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ComponentHistoryModelCopyWith<$Res> {
  factory $ComponentHistoryModelCopyWith(ComponentHistoryModel value,
          $Res Function(ComponentHistoryModel) then) =
      _$ComponentHistoryModelCopyWithImpl<$Res, ComponentHistoryModel>;
  @useResult
  $Res call(
      {String name,
      ComponentHistoryType<dynamic> type,
      List<ComponentStatusModel> statuses,
      List<WorkerModel>? workers,
      int? area,
      int? job,
      String? task,
      String? taskID,
      String? identifier,
      String? histMainID,
      double? createdAt,
      double? updatedAt});
}

/// @nodoc
class _$ComponentHistoryModelCopyWithImpl<$Res,
        $Val extends ComponentHistoryModel>
    implements $ComponentHistoryModelCopyWith<$Res> {
  _$ComponentHistoryModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ComponentHistoryModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? type = null,
    Object? statuses = null,
    Object? workers = freezed,
    Object? area = freezed,
    Object? job = freezed,
    Object? task = freezed,
    Object? taskID = freezed,
    Object? identifier = freezed,
    Object? histMainID = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_value.copyWith(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as ComponentHistoryType<dynamic>,
      statuses: null == statuses
          ? _value.statuses
          : statuses // ignore: cast_nullable_to_non_nullable
              as List<ComponentStatusModel>,
      workers: freezed == workers
          ? _value.workers
          : workers // ignore: cast_nullable_to_non_nullable
              as List<WorkerModel>?,
      area: freezed == area
          ? _value.area
          : area // ignore: cast_nullable_to_non_nullable
              as int?,
      job: freezed == job
          ? _value.job
          : job // ignore: cast_nullable_to_non_nullable
              as int?,
      task: freezed == task
          ? _value.task
          : task // ignore: cast_nullable_to_non_nullable
              as String?,
      taskID: freezed == taskID
          ? _value.taskID
          : taskID // ignore: cast_nullable_to_non_nullable
              as String?,
      identifier: freezed == identifier
          ? _value.identifier
          : identifier // ignore: cast_nullable_to_non_nullable
              as String?,
      histMainID: freezed == histMainID
          ? _value.histMainID
          : histMainID // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as double?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ComponentHistoryModelImplCopyWith<$Res>
    implements $ComponentHistoryModelCopyWith<$Res> {
  factory _$$ComponentHistoryModelImplCopyWith(
          _$ComponentHistoryModelImpl value,
          $Res Function(_$ComponentHistoryModelImpl) then) =
      __$$ComponentHistoryModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String name,
      ComponentHistoryType<dynamic> type,
      List<ComponentStatusModel> statuses,
      List<WorkerModel>? workers,
      int? area,
      int? job,
      String? task,
      String? taskID,
      String? identifier,
      String? histMainID,
      double? createdAt,
      double? updatedAt});
}

/// @nodoc
class __$$ComponentHistoryModelImplCopyWithImpl<$Res>
    extends _$ComponentHistoryModelCopyWithImpl<$Res,
        _$ComponentHistoryModelImpl>
    implements _$$ComponentHistoryModelImplCopyWith<$Res> {
  __$$ComponentHistoryModelImplCopyWithImpl(_$ComponentHistoryModelImpl _value,
      $Res Function(_$ComponentHistoryModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ComponentHistoryModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? type = null,
    Object? statuses = null,
    Object? workers = freezed,
    Object? area = freezed,
    Object? job = freezed,
    Object? task = freezed,
    Object? taskID = freezed,
    Object? identifier = freezed,
    Object? histMainID = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_$ComponentHistoryModelImpl(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as ComponentHistoryType<dynamic>,
      statuses: null == statuses
          ? _value._statuses
          : statuses // ignore: cast_nullable_to_non_nullable
              as List<ComponentStatusModel>,
      workers: freezed == workers
          ? _value._workers
          : workers // ignore: cast_nullable_to_non_nullable
              as List<WorkerModel>?,
      area: freezed == area
          ? _value.area
          : area // ignore: cast_nullable_to_non_nullable
              as int?,
      job: freezed == job
          ? _value.job
          : job // ignore: cast_nullable_to_non_nullable
              as int?,
      task: freezed == task
          ? _value.task
          : task // ignore: cast_nullable_to_non_nullable
              as String?,
      taskID: freezed == taskID
          ? _value.taskID
          : taskID // ignore: cast_nullable_to_non_nullable
              as String?,
      identifier: freezed == identifier
          ? _value.identifier
          : identifier // ignore: cast_nullable_to_non_nullable
              as String?,
      histMainID: freezed == histMainID
          ? _value.histMainID
          : histMainID // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as double?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class _$ComponentHistoryModelImpl extends _ComponentHistoryModel
    with DiagnosticableTreeMixin {
  const _$ComponentHistoryModelImpl(
      {required this.name,
      required this.type,
      required final List<ComponentStatusModel> statuses,
      final List<WorkerModel>? workers,
      this.area,
      this.job,
      this.task,
      this.taskID,
      this.identifier,
      this.histMainID,
      this.createdAt,
      this.updatedAt})
      : _statuses = statuses,
        _workers = workers,
        super._();

  factory _$ComponentHistoryModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ComponentHistoryModelImplFromJson(json);

  @override
  final String name;
  @override
  final ComponentHistoryType<dynamic> type;
  final List<ComponentStatusModel> _statuses;
  @override
  List<ComponentStatusModel> get statuses {
    if (_statuses is EqualUnmodifiableListView) return _statuses;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_statuses);
  }

  final List<WorkerModel>? _workers;
  @override
  List<WorkerModel>? get workers {
    final value = _workers;
    if (value == null) return null;
    if (_workers is EqualUnmodifiableListView) return _workers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? area;
// area id
  @override
  final int? job;
// job id (local job,status in app)
  @override
  final String? task;
// task job id (job in 1c)
  @override
  final String? taskID;
// id ssz
  @override
  final String? identifier;
// generated uniq id
  @override
  final String? histMainID;
  @override
  final double? createdAt;
  @override
  final double? updatedAt;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ComponentHistoryModel(name: $name, type: $type, statuses: $statuses, workers: $workers, area: $area, job: $job, task: $task, taskID: $taskID, identifier: $identifier, histMainID: $histMainID, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'ComponentHistoryModel'))
      ..add(DiagnosticsProperty('name', name))
      ..add(DiagnosticsProperty('type', type))
      ..add(DiagnosticsProperty('statuses', statuses))
      ..add(DiagnosticsProperty('workers', workers))
      ..add(DiagnosticsProperty('area', area))
      ..add(DiagnosticsProperty('job', job))
      ..add(DiagnosticsProperty('task', task))
      ..add(DiagnosticsProperty('taskID', taskID))
      ..add(DiagnosticsProperty('identifier', identifier))
      ..add(DiagnosticsProperty('histMainID', histMainID))
      ..add(DiagnosticsProperty('createdAt', createdAt))
      ..add(DiagnosticsProperty('updatedAt', updatedAt));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ComponentHistoryModelImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.type, type) || other.type == type) &&
            const DeepCollectionEquality().equals(other._statuses, _statuses) &&
            const DeepCollectionEquality().equals(other._workers, _workers) &&
            (identical(other.area, area) || other.area == area) &&
            (identical(other.job, job) || other.job == job) &&
            (identical(other.task, task) || other.task == task) &&
            (identical(other.taskID, taskID) || other.taskID == taskID) &&
            (identical(other.identifier, identifier) ||
                other.identifier == identifier) &&
            (identical(other.histMainID, histMainID) ||
                other.histMainID == histMainID) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      name,
      type,
      const DeepCollectionEquality().hash(_statuses),
      const DeepCollectionEquality().hash(_workers),
      area,
      job,
      task,
      taskID,
      identifier,
      histMainID,
      createdAt,
      updatedAt);

  /// Create a copy of ComponentHistoryModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ComponentHistoryModelImplCopyWith<_$ComponentHistoryModelImpl>
      get copyWith => __$$ComponentHistoryModelImplCopyWithImpl<
          _$ComponentHistoryModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ComponentHistoryModelImplToJson(
      this,
    );
  }
}

abstract class _ComponentHistoryModel extends ComponentHistoryModel {
  const factory _ComponentHistoryModel(
      {required final String name,
      required final ComponentHistoryType<dynamic> type,
      required final List<ComponentStatusModel> statuses,
      final List<WorkerModel>? workers,
      final int? area,
      final int? job,
      final String? task,
      final String? taskID,
      final String? identifier,
      final String? histMainID,
      final double? createdAt,
      final double? updatedAt}) = _$ComponentHistoryModelImpl;
  const _ComponentHistoryModel._() : super._();

  factory _ComponentHistoryModel.fromJson(Map<String, dynamic> json) =
      _$ComponentHistoryModelImpl.fromJson;

  @override
  String get name;
  @override
  ComponentHistoryType<dynamic> get type;
  @override
  List<ComponentStatusModel> get statuses;
  @override
  List<WorkerModel>? get workers;
  @override
  int? get area; // area id
  @override
  int? get job; // job id (local job,status in app)
  @override
  String? get task; // task job id (job in 1c)
  @override
  String? get taskID; // id ssz
  @override
  String? get identifier; // generated uniq id
  @override
  String? get histMainID;
  @override
  double? get createdAt;
  @override
  double? get updatedAt;

  /// Create a copy of ComponentHistoryModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ComponentHistoryModelImplCopyWith<_$ComponentHistoryModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}

ComponentStatusModel _$ComponentStatusModelFromJson(Map<String, dynamic> json) {
  return _ComponentStatusModel.fromJson(json);
}

/// @nodoc
mixin _$ComponentStatusModel {
  String get owner => throw _privateConstructorUsedError;
  HistoryStatusType<dynamic> get status => throw _privateConstructorUsedError;
  String? get comment => throw _privateConstructorUsedError;
  double? get createdAt => throw _privateConstructorUsedError;
  double? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this ComponentStatusModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ComponentStatusModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ComponentStatusModelCopyWith<ComponentStatusModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ComponentStatusModelCopyWith<$Res> {
  factory $ComponentStatusModelCopyWith(ComponentStatusModel value,
          $Res Function(ComponentStatusModel) then) =
      _$ComponentStatusModelCopyWithImpl<$Res, ComponentStatusModel>;
  @useResult
  $Res call(
      {String owner,
      HistoryStatusType<dynamic> status,
      String? comment,
      double? createdAt,
      double? updatedAt});
}

/// @nodoc
class _$ComponentStatusModelCopyWithImpl<$Res,
        $Val extends ComponentStatusModel>
    implements $ComponentStatusModelCopyWith<$Res> {
  _$ComponentStatusModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ComponentStatusModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? owner = null,
    Object? status = null,
    Object? comment = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_value.copyWith(
      owner: null == owner
          ? _value.owner
          : owner // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as HistoryStatusType<dynamic>,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as double?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ComponentStatusModelImplCopyWith<$Res>
    implements $ComponentStatusModelCopyWith<$Res> {
  factory _$$ComponentStatusModelImplCopyWith(_$ComponentStatusModelImpl value,
          $Res Function(_$ComponentStatusModelImpl) then) =
      __$$ComponentStatusModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String owner,
      HistoryStatusType<dynamic> status,
      String? comment,
      double? createdAt,
      double? updatedAt});
}

/// @nodoc
class __$$ComponentStatusModelImplCopyWithImpl<$Res>
    extends _$ComponentStatusModelCopyWithImpl<$Res, _$ComponentStatusModelImpl>
    implements _$$ComponentStatusModelImplCopyWith<$Res> {
  __$$ComponentStatusModelImplCopyWithImpl(_$ComponentStatusModelImpl _value,
      $Res Function(_$ComponentStatusModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ComponentStatusModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? owner = null,
    Object? status = null,
    Object? comment = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_$ComponentStatusModelImpl(
      owner: null == owner
          ? _value.owner
          : owner // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as HistoryStatusType<dynamic>,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as double?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class _$ComponentStatusModelImpl extends _ComponentStatusModel
    with DiagnosticableTreeMixin {
  const _$ComponentStatusModelImpl(
      {required this.owner,
      required this.status,
      this.comment,
      this.createdAt,
      this.updatedAt})
      : super._();

  factory _$ComponentStatusModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ComponentStatusModelImplFromJson(json);

  @override
  final String owner;
  @override
  final HistoryStatusType<dynamic> status;
  @override
  final String? comment;
  @override
  final double? createdAt;
  @override
  final double? updatedAt;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ComponentStatusModel(owner: $owner, status: $status, comment: $comment, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'ComponentStatusModel'))
      ..add(DiagnosticsProperty('owner', owner))
      ..add(DiagnosticsProperty('status', status))
      ..add(DiagnosticsProperty('comment', comment))
      ..add(DiagnosticsProperty('createdAt', createdAt))
      ..add(DiagnosticsProperty('updatedAt', updatedAt));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ComponentStatusModelImpl &&
            (identical(other.owner, owner) || other.owner == owner) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.comment, comment) || other.comment == comment) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, owner, status, comment, createdAt, updatedAt);

  /// Create a copy of ComponentStatusModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ComponentStatusModelImplCopyWith<_$ComponentStatusModelImpl>
      get copyWith =>
          __$$ComponentStatusModelImplCopyWithImpl<_$ComponentStatusModelImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ComponentStatusModelImplToJson(
      this,
    );
  }
}

abstract class _ComponentStatusModel extends ComponentStatusModel {
  const factory _ComponentStatusModel(
      {required final String owner,
      required final HistoryStatusType<dynamic> status,
      final String? comment,
      final double? createdAt,
      final double? updatedAt}) = _$ComponentStatusModelImpl;
  const _ComponentStatusModel._() : super._();

  factory _ComponentStatusModel.fromJson(Map<String, dynamic> json) =
      _$ComponentStatusModelImpl.fromJson;

  @override
  String get owner;
  @override
  HistoryStatusType<dynamic> get status;
  @override
  String? get comment;
  @override
  double? get createdAt;
  @override
  double? get updatedAt;

  /// Create a copy of ComponentStatusModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ComponentStatusModelImplCopyWith<_$ComponentStatusModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}
