// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'component_history_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ComponentHistoryModelImpl _$$ComponentHistoryModelImplFromJson(
        Map<String, dynamic> json) =>
    _$ComponentHistoryModelImpl(
      name: json['name'] as String,
      type: $enumDecode(_$ComponentHistoryTypeEnumMap, json['type']),
      statuses: (json['statuses'] as List<dynamic>)
          .map((e) => ComponentStatusModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      workers: (json['workers'] as List<dynamic>?)
          ?.map((e) => WorkerModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      area: (json['area'] as num?)?.toInt(),
      job: (json['job'] as num?)?.toInt(),
      task: json['task'] as String?,
      taskID: json['taskID'] as String?,
      identifier: json['identifier'] as String?,
      histMainID: json['histMainID'] as String?,
      createdAt: (json['createdAt'] as num?)?.toDouble(),
      updatedAt: (json['updatedAt'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$ComponentHistoryModelImplToJson(
        _$ComponentHistoryModelImpl instance) =>
    <String, dynamic>{
      'name': instance.name,
      'type': _$ComponentHistoryTypeEnumMap[instance.type]!,
      'statuses': instance.statuses.map((e) => e.toJson()).toList(),
      if (instance.workers?.map((e) => e.toJson()).toList() case final value?)
        'workers': value,
      if (instance.area case final value?) 'area': value,
      if (instance.job case final value?) 'job': value,
      if (instance.task case final value?) 'task': value,
      if (instance.taskID case final value?) 'taskID': value,
      if (instance.identifier case final value?) 'identifier': value,
      if (instance.histMainID case final value?) 'histMainID': value,
      if (instance.createdAt case final value?) 'createdAt': value,
      if (instance.updatedAt case final value?) 'updatedAt': value,
    };

const _$ComponentHistoryTypeEnumMap = {
  ComponentHistoryType.task: 'task',
  ComponentHistoryType.job: 'job',
  ComponentHistoryType.info: 'info',
};

_$ComponentStatusModelImpl _$$ComponentStatusModelImplFromJson(
        Map<String, dynamic> json) =>
    _$ComponentStatusModelImpl(
      owner: json['owner'] as String,
      status: $enumDecode(_$HistoryStatusTypeEnumMap, json['status']),
      comment: json['comment'] as String?,
      createdAt: (json['createdAt'] as num?)?.toDouble(),
      updatedAt: (json['updatedAt'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$ComponentStatusModelImplToJson(
        _$ComponentStatusModelImpl instance) =>
    <String, dynamic>{
      'owner': instance.owner,
      'status': _$HistoryStatusTypeEnumMap[instance.status]!,
      if (instance.comment case final value?) 'comment': value,
      if (instance.createdAt case final value?) 'createdAt': value,
      if (instance.updatedAt case final value?) 'updatedAt': value,
    };

const _$HistoryStatusTypeEnumMap = {
  HistoryStatusType.inwork: 'inwork',
  HistoryStatusType.finish: 'finish',
  HistoryStatusType.pause: 'pause',
  HistoryStatusType.info: 'info',
};
