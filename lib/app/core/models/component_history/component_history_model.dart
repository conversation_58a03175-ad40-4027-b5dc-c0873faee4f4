import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter/foundation.dart';
import 'package:uer_flutter/app/core/services/utils.dart';
import '../area/area_model.dart';
import '../status/status_model.dart';
import '../worker/worker_model.dart';

import '../enums/component_history_type_enum.dart';
import '../enums/history_status_enum.dart';

part 'component_history_model.freezed.dart';
part 'component_history_model.g.dart';

@freezed
class ComponentHistoryModel with _$ComponentHistoryModel {
  const ComponentHistoryModel._();
  @JsonSerializable(explicitToJson: true, includeIfNull: false)
  const factory ComponentHistoryModel({
    required String name,
    required ComponentHistoryType type,
    required List<ComponentStatusModel> statuses,
    List<WorkerModel>? workers,
    int? area, // area id
    int? job, // job id (local job,status in app)
    String? task, // task job id (job in 1c)
    String? taskID, // id ssz
    String? identifier, // generated uniq id
    String? histMainID,
    double? createdAt,
    double? updatedAt,
    // если в двигателе объединено несколько ЗПР, то этот параметр поможет определить к какому ЗПР относится работа (ССЗ)
  }) = _ComponentHistoryModel;

  factory ComponentHistoryModel.fromJson(Map<String, Object?> json) =>
      _$ComponentHistoryModelFromJson(json);

  String getAreaName(AreaModel? area) {
    if (area != null) {
      return area.name;
    }

    var split = name.split(" | ");

    if (split.isNotEmpty) {
      return split.first;
    }

    return "";
  }

  String getStatusName(StatusModel? status) {
    if (status != null) {
      return status.name;
    }
    var split = name.split(" | ");

    if (split.length > 1) {
      return split.last;
    }

    return "";
  }

  String calendarTimeForAllStatuses() {
    var firstStatus = statuses.first;
    var lastStatus = statuses.last;

    var firstDate = firstStatus.createdAt ?? 0;

    if (lastStatus.status == HistoryStatusType.finish) {
      var lastDate = lastStatus.createdAt ?? 0;

      var delta = lastDate - firstDate;

      return timeFromUnix(delta);
    } else {
      var nowDate = DateTime.now().millisecondsSinceEpoch / 1000;

      var delta = nowDate - firstDate;

      return timeFromUnix(delta);
    }
  }
}

@freezed
class ComponentStatusModel with _$ComponentStatusModel {
  const ComponentStatusModel._();
  @JsonSerializable(explicitToJson: true, includeIfNull: false)
  const factory ComponentStatusModel({
    required String owner,
    required HistoryStatusType status,
    String? comment,
    double? createdAt,
    double? updatedAt,
  }) = _ComponentStatusModel;

  factory ComponentStatusModel.fromJson(Map<String, Object?> json) =>
      _$ComponentStatusModelFromJson(json);
}
