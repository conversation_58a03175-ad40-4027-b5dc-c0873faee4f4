// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'order_client_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

OrderClientModel _$OrderClientModelFromJson(Map<String, dynamic> json) {
  return _OrderClientModel.fromJson(json);
}

/// @nodoc
mixin _$OrderClientModel {
  String get idClient => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String? get inn => throw _privateConstructorUsedError;

  /// Serializes this OrderClientModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of OrderClientModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OrderClientModelCopyWith<OrderClientModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrderClientModelCopyWith<$Res> {
  factory $OrderClientModelCopyWith(
          OrderClientModel value, $Res Function(OrderClientModel) then) =
      _$OrderClientModelCopyWithImpl<$Res, OrderClientModel>;
  @useResult
  $Res call({String idClient, String name, String? inn});
}

/// @nodoc
class _$OrderClientModelCopyWithImpl<$Res, $Val extends OrderClientModel>
    implements $OrderClientModelCopyWith<$Res> {
  _$OrderClientModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OrderClientModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? idClient = null,
    Object? name = null,
    Object? inn = freezed,
  }) {
    return _then(_value.copyWith(
      idClient: null == idClient
          ? _value.idClient
          : idClient // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      inn: freezed == inn
          ? _value.inn
          : inn // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OrderClientModelImplCopyWith<$Res>
    implements $OrderClientModelCopyWith<$Res> {
  factory _$$OrderClientModelImplCopyWith(_$OrderClientModelImpl value,
          $Res Function(_$OrderClientModelImpl) then) =
      __$$OrderClientModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String idClient, String name, String? inn});
}

/// @nodoc
class __$$OrderClientModelImplCopyWithImpl<$Res>
    extends _$OrderClientModelCopyWithImpl<$Res, _$OrderClientModelImpl>
    implements _$$OrderClientModelImplCopyWith<$Res> {
  __$$OrderClientModelImplCopyWithImpl(_$OrderClientModelImpl _value,
      $Res Function(_$OrderClientModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrderClientModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? idClient = null,
    Object? name = null,
    Object? inn = freezed,
  }) {
    return _then(_$OrderClientModelImpl(
      idClient: null == idClient
          ? _value.idClient
          : idClient // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      inn: freezed == inn
          ? _value.inn
          : inn // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class _$OrderClientModelImpl extends _OrderClientModel
    with DiagnosticableTreeMixin {
  const _$OrderClientModelImpl(
      {required this.idClient, required this.name, this.inn})
      : super._();

  factory _$OrderClientModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$OrderClientModelImplFromJson(json);

  @override
  final String idClient;
  @override
  final String name;
  @override
  final String? inn;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'OrderClientModel(idClient: $idClient, name: $name, inn: $inn)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'OrderClientModel'))
      ..add(DiagnosticsProperty('idClient', idClient))
      ..add(DiagnosticsProperty('name', name))
      ..add(DiagnosticsProperty('inn', inn));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrderClientModelImpl &&
            (identical(other.idClient, idClient) ||
                other.idClient == idClient) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.inn, inn) || other.inn == inn));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, idClient, name, inn);

  /// Create a copy of OrderClientModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OrderClientModelImplCopyWith<_$OrderClientModelImpl> get copyWith =>
      __$$OrderClientModelImplCopyWithImpl<_$OrderClientModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OrderClientModelImplToJson(
      this,
    );
  }
}

abstract class _OrderClientModel extends OrderClientModel {
  const factory _OrderClientModel(
      {required final String idClient,
      required final String name,
      final String? inn}) = _$OrderClientModelImpl;
  const _OrderClientModel._() : super._();

  factory _OrderClientModel.fromJson(Map<String, dynamic> json) =
      _$OrderClientModelImpl.fromJson;

  @override
  String get idClient;
  @override
  String get name;
  @override
  String? get inn;

  /// Create a copy of OrderClientModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OrderClientModelImplCopyWith<_$OrderClientModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
