import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter/foundation.dart';

part 'order_branch_model.freezed.dart';
part 'order_branch_model.g.dart';

@freezed
class OrderBranchModel with _$OrderBranchModel {
  const OrderBranchModel._();
  @JsonSerializable(explicitToJson: true, includeIfNull: false)
  const factory OrderBranchModel({
    required String idBranch,
    required String name,
  }) = _OrderBranchModel;

  factory OrderBranchModel.fromJson(Map<String, Object?> json) =>
      _$OrderBranchModelFromJson(json);
}
