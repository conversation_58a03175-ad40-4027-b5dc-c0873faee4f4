// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'order_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

OrderModel _$OrderModelFromJson(Map<String, dynamic> json) {
  return _OrderModel.fromJson(json);
}

/// @nodoc
mixin _$OrderModel {
  @JsonKey(name: '_id')
  String? get identifier => throw _privateConstructorUsedError;
  String? get id => throw _privateConstructorUsedError;
  String? get number => throw _privateConstructorUsedError;
  String? get date => throw _privateConstructorUsedError;
  String? get finishDate => throw _privateConstructorUsedError;
  String? get statusOrder => throw _privateConstructorUsedError;
  String? get manager => throw _privateConstructorUsedError;
  OrderBranchModel? get branch => throw _privateConstructorUsedError;
  OrderClientModel? get client => throw _privateConstructorUsedError;
  String? get rn => throw _privateConstructorUsedError;
  String? get rnOGK => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  String? get equipment => throw _privateConstructorUsedError;
  String? get power => throw _privateConstructorUsedError;
  String? get voltage => throw _privateConstructorUsedError;
  String? get turnovers => throw _privateConstructorUsedError;
  String? get weight => throw _privateConstructorUsedError;
  String? get voltageVN => throw _privateConstructorUsedError;
  String? get voltageNN => throw _privateConstructorUsedError;
  String? get voltageSN => throw _privateConstructorUsedError;
  String? get amperage => throw _privateConstructorUsedError;
  String? get version => throw _privateConstructorUsedError;
  String? get updateDate =>
      throw _privateConstructorUsedError; //Uint64? updateDateNumbers,
  String? get comment => throw _privateConstructorUsedError;
  double? get createdAt => throw _privateConstructorUsedError;
  double? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this OrderModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of OrderModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OrderModelCopyWith<OrderModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrderModelCopyWith<$Res> {
  factory $OrderModelCopyWith(
          OrderModel value, $Res Function(OrderModel) then) =
      _$OrderModelCopyWithImpl<$Res, OrderModel>;
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String? identifier,
      String? id,
      String? number,
      String? date,
      String? finishDate,
      String? statusOrder,
      String? manager,
      OrderBranchModel? branch,
      OrderClientModel? client,
      String? rn,
      String? rnOGK,
      String? name,
      String? equipment,
      String? power,
      String? voltage,
      String? turnovers,
      String? weight,
      String? voltageVN,
      String? voltageNN,
      String? voltageSN,
      String? amperage,
      String? version,
      String? updateDate,
      String? comment,
      double? createdAt,
      double? updatedAt});

  $OrderBranchModelCopyWith<$Res>? get branch;
  $OrderClientModelCopyWith<$Res>? get client;
}

/// @nodoc
class _$OrderModelCopyWithImpl<$Res, $Val extends OrderModel>
    implements $OrderModelCopyWith<$Res> {
  _$OrderModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OrderModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? identifier = freezed,
    Object? id = freezed,
    Object? number = freezed,
    Object? date = freezed,
    Object? finishDate = freezed,
    Object? statusOrder = freezed,
    Object? manager = freezed,
    Object? branch = freezed,
    Object? client = freezed,
    Object? rn = freezed,
    Object? rnOGK = freezed,
    Object? name = freezed,
    Object? equipment = freezed,
    Object? power = freezed,
    Object? voltage = freezed,
    Object? turnovers = freezed,
    Object? weight = freezed,
    Object? voltageVN = freezed,
    Object? voltageNN = freezed,
    Object? voltageSN = freezed,
    Object? amperage = freezed,
    Object? version = freezed,
    Object? updateDate = freezed,
    Object? comment = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_value.copyWith(
      identifier: freezed == identifier
          ? _value.identifier
          : identifier // ignore: cast_nullable_to_non_nullable
              as String?,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      number: freezed == number
          ? _value.number
          : number // ignore: cast_nullable_to_non_nullable
              as String?,
      date: freezed == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as String?,
      finishDate: freezed == finishDate
          ? _value.finishDate
          : finishDate // ignore: cast_nullable_to_non_nullable
              as String?,
      statusOrder: freezed == statusOrder
          ? _value.statusOrder
          : statusOrder // ignore: cast_nullable_to_non_nullable
              as String?,
      manager: freezed == manager
          ? _value.manager
          : manager // ignore: cast_nullable_to_non_nullable
              as String?,
      branch: freezed == branch
          ? _value.branch
          : branch // ignore: cast_nullable_to_non_nullable
              as OrderBranchModel?,
      client: freezed == client
          ? _value.client
          : client // ignore: cast_nullable_to_non_nullable
              as OrderClientModel?,
      rn: freezed == rn
          ? _value.rn
          : rn // ignore: cast_nullable_to_non_nullable
              as String?,
      rnOGK: freezed == rnOGK
          ? _value.rnOGK
          : rnOGK // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      equipment: freezed == equipment
          ? _value.equipment
          : equipment // ignore: cast_nullable_to_non_nullable
              as String?,
      power: freezed == power
          ? _value.power
          : power // ignore: cast_nullable_to_non_nullable
              as String?,
      voltage: freezed == voltage
          ? _value.voltage
          : voltage // ignore: cast_nullable_to_non_nullable
              as String?,
      turnovers: freezed == turnovers
          ? _value.turnovers
          : turnovers // ignore: cast_nullable_to_non_nullable
              as String?,
      weight: freezed == weight
          ? _value.weight
          : weight // ignore: cast_nullable_to_non_nullable
              as String?,
      voltageVN: freezed == voltageVN
          ? _value.voltageVN
          : voltageVN // ignore: cast_nullable_to_non_nullable
              as String?,
      voltageNN: freezed == voltageNN
          ? _value.voltageNN
          : voltageNN // ignore: cast_nullable_to_non_nullable
              as String?,
      voltageSN: freezed == voltageSN
          ? _value.voltageSN
          : voltageSN // ignore: cast_nullable_to_non_nullable
              as String?,
      amperage: freezed == amperage
          ? _value.amperage
          : amperage // ignore: cast_nullable_to_non_nullable
              as String?,
      version: freezed == version
          ? _value.version
          : version // ignore: cast_nullable_to_non_nullable
              as String?,
      updateDate: freezed == updateDate
          ? _value.updateDate
          : updateDate // ignore: cast_nullable_to_non_nullable
              as String?,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as double?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }

  /// Create a copy of OrderModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $OrderBranchModelCopyWith<$Res>? get branch {
    if (_value.branch == null) {
      return null;
    }

    return $OrderBranchModelCopyWith<$Res>(_value.branch!, (value) {
      return _then(_value.copyWith(branch: value) as $Val);
    });
  }

  /// Create a copy of OrderModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $OrderClientModelCopyWith<$Res>? get client {
    if (_value.client == null) {
      return null;
    }

    return $OrderClientModelCopyWith<$Res>(_value.client!, (value) {
      return _then(_value.copyWith(client: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$OrderModelImplCopyWith<$Res>
    implements $OrderModelCopyWith<$Res> {
  factory _$$OrderModelImplCopyWith(
          _$OrderModelImpl value, $Res Function(_$OrderModelImpl) then) =
      __$$OrderModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String? identifier,
      String? id,
      String? number,
      String? date,
      String? finishDate,
      String? statusOrder,
      String? manager,
      OrderBranchModel? branch,
      OrderClientModel? client,
      String? rn,
      String? rnOGK,
      String? name,
      String? equipment,
      String? power,
      String? voltage,
      String? turnovers,
      String? weight,
      String? voltageVN,
      String? voltageNN,
      String? voltageSN,
      String? amperage,
      String? version,
      String? updateDate,
      String? comment,
      double? createdAt,
      double? updatedAt});

  @override
  $OrderBranchModelCopyWith<$Res>? get branch;
  @override
  $OrderClientModelCopyWith<$Res>? get client;
}

/// @nodoc
class __$$OrderModelImplCopyWithImpl<$Res>
    extends _$OrderModelCopyWithImpl<$Res, _$OrderModelImpl>
    implements _$$OrderModelImplCopyWith<$Res> {
  __$$OrderModelImplCopyWithImpl(
      _$OrderModelImpl _value, $Res Function(_$OrderModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrderModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? identifier = freezed,
    Object? id = freezed,
    Object? number = freezed,
    Object? date = freezed,
    Object? finishDate = freezed,
    Object? statusOrder = freezed,
    Object? manager = freezed,
    Object? branch = freezed,
    Object? client = freezed,
    Object? rn = freezed,
    Object? rnOGK = freezed,
    Object? name = freezed,
    Object? equipment = freezed,
    Object? power = freezed,
    Object? voltage = freezed,
    Object? turnovers = freezed,
    Object? weight = freezed,
    Object? voltageVN = freezed,
    Object? voltageNN = freezed,
    Object? voltageSN = freezed,
    Object? amperage = freezed,
    Object? version = freezed,
    Object? updateDate = freezed,
    Object? comment = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_$OrderModelImpl(
      identifier: freezed == identifier
          ? _value.identifier
          : identifier // ignore: cast_nullable_to_non_nullable
              as String?,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      number: freezed == number
          ? _value.number
          : number // ignore: cast_nullable_to_non_nullable
              as String?,
      date: freezed == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as String?,
      finishDate: freezed == finishDate
          ? _value.finishDate
          : finishDate // ignore: cast_nullable_to_non_nullable
              as String?,
      statusOrder: freezed == statusOrder
          ? _value.statusOrder
          : statusOrder // ignore: cast_nullable_to_non_nullable
              as String?,
      manager: freezed == manager
          ? _value.manager
          : manager // ignore: cast_nullable_to_non_nullable
              as String?,
      branch: freezed == branch
          ? _value.branch
          : branch // ignore: cast_nullable_to_non_nullable
              as OrderBranchModel?,
      client: freezed == client
          ? _value.client
          : client // ignore: cast_nullable_to_non_nullable
              as OrderClientModel?,
      rn: freezed == rn
          ? _value.rn
          : rn // ignore: cast_nullable_to_non_nullable
              as String?,
      rnOGK: freezed == rnOGK
          ? _value.rnOGK
          : rnOGK // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      equipment: freezed == equipment
          ? _value.equipment
          : equipment // ignore: cast_nullable_to_non_nullable
              as String?,
      power: freezed == power
          ? _value.power
          : power // ignore: cast_nullable_to_non_nullable
              as String?,
      voltage: freezed == voltage
          ? _value.voltage
          : voltage // ignore: cast_nullable_to_non_nullable
              as String?,
      turnovers: freezed == turnovers
          ? _value.turnovers
          : turnovers // ignore: cast_nullable_to_non_nullable
              as String?,
      weight: freezed == weight
          ? _value.weight
          : weight // ignore: cast_nullable_to_non_nullable
              as String?,
      voltageVN: freezed == voltageVN
          ? _value.voltageVN
          : voltageVN // ignore: cast_nullable_to_non_nullable
              as String?,
      voltageNN: freezed == voltageNN
          ? _value.voltageNN
          : voltageNN // ignore: cast_nullable_to_non_nullable
              as String?,
      voltageSN: freezed == voltageSN
          ? _value.voltageSN
          : voltageSN // ignore: cast_nullable_to_non_nullable
              as String?,
      amperage: freezed == amperage
          ? _value.amperage
          : amperage // ignore: cast_nullable_to_non_nullable
              as String?,
      version: freezed == version
          ? _value.version
          : version // ignore: cast_nullable_to_non_nullable
              as String?,
      updateDate: freezed == updateDate
          ? _value.updateDate
          : updateDate // ignore: cast_nullable_to_non_nullable
              as String?,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as double?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class _$OrderModelImpl extends _OrderModel with DiagnosticableTreeMixin {
  const _$OrderModelImpl(
      {@JsonKey(name: '_id') this.identifier,
      this.id,
      this.number,
      this.date,
      this.finishDate,
      this.statusOrder,
      this.manager,
      this.branch,
      this.client,
      this.rn,
      this.rnOGK,
      this.name,
      this.equipment,
      this.power,
      this.voltage,
      this.turnovers,
      this.weight,
      this.voltageVN,
      this.voltageNN,
      this.voltageSN,
      this.amperage,
      this.version,
      this.updateDate,
      this.comment,
      this.createdAt,
      this.updatedAt})
      : super._();

  factory _$OrderModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$OrderModelImplFromJson(json);

  @override
  @JsonKey(name: '_id')
  final String? identifier;
  @override
  final String? id;
  @override
  final String? number;
  @override
  final String? date;
  @override
  final String? finishDate;
  @override
  final String? statusOrder;
  @override
  final String? manager;
  @override
  final OrderBranchModel? branch;
  @override
  final OrderClientModel? client;
  @override
  final String? rn;
  @override
  final String? rnOGK;
  @override
  final String? name;
  @override
  final String? equipment;
  @override
  final String? power;
  @override
  final String? voltage;
  @override
  final String? turnovers;
  @override
  final String? weight;
  @override
  final String? voltageVN;
  @override
  final String? voltageNN;
  @override
  final String? voltageSN;
  @override
  final String? amperage;
  @override
  final String? version;
  @override
  final String? updateDate;
//Uint64? updateDateNumbers,
  @override
  final String? comment;
  @override
  final double? createdAt;
  @override
  final double? updatedAt;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'OrderModel(identifier: $identifier, id: $id, number: $number, date: $date, finishDate: $finishDate, statusOrder: $statusOrder, manager: $manager, branch: $branch, client: $client, rn: $rn, rnOGK: $rnOGK, name: $name, equipment: $equipment, power: $power, voltage: $voltage, turnovers: $turnovers, weight: $weight, voltageVN: $voltageVN, voltageNN: $voltageNN, voltageSN: $voltageSN, amperage: $amperage, version: $version, updateDate: $updateDate, comment: $comment, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'OrderModel'))
      ..add(DiagnosticsProperty('identifier', identifier))
      ..add(DiagnosticsProperty('id', id))
      ..add(DiagnosticsProperty('number', number))
      ..add(DiagnosticsProperty('date', date))
      ..add(DiagnosticsProperty('finishDate', finishDate))
      ..add(DiagnosticsProperty('statusOrder', statusOrder))
      ..add(DiagnosticsProperty('manager', manager))
      ..add(DiagnosticsProperty('branch', branch))
      ..add(DiagnosticsProperty('client', client))
      ..add(DiagnosticsProperty('rn', rn))
      ..add(DiagnosticsProperty('rnOGK', rnOGK))
      ..add(DiagnosticsProperty('name', name))
      ..add(DiagnosticsProperty('equipment', equipment))
      ..add(DiagnosticsProperty('power', power))
      ..add(DiagnosticsProperty('voltage', voltage))
      ..add(DiagnosticsProperty('turnovers', turnovers))
      ..add(DiagnosticsProperty('weight', weight))
      ..add(DiagnosticsProperty('voltageVN', voltageVN))
      ..add(DiagnosticsProperty('voltageNN', voltageNN))
      ..add(DiagnosticsProperty('voltageSN', voltageSN))
      ..add(DiagnosticsProperty('amperage', amperage))
      ..add(DiagnosticsProperty('version', version))
      ..add(DiagnosticsProperty('updateDate', updateDate))
      ..add(DiagnosticsProperty('comment', comment))
      ..add(DiagnosticsProperty('createdAt', createdAt))
      ..add(DiagnosticsProperty('updatedAt', updatedAt));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrderModelImpl &&
            (identical(other.identifier, identifier) ||
                other.identifier == identifier) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.number, number) || other.number == number) &&
            (identical(other.date, date) || other.date == date) &&
            (identical(other.finishDate, finishDate) ||
                other.finishDate == finishDate) &&
            (identical(other.statusOrder, statusOrder) ||
                other.statusOrder == statusOrder) &&
            (identical(other.manager, manager) || other.manager == manager) &&
            (identical(other.branch, branch) || other.branch == branch) &&
            (identical(other.client, client) || other.client == client) &&
            (identical(other.rn, rn) || other.rn == rn) &&
            (identical(other.rnOGK, rnOGK) || other.rnOGK == rnOGK) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.equipment, equipment) ||
                other.equipment == equipment) &&
            (identical(other.power, power) || other.power == power) &&
            (identical(other.voltage, voltage) || other.voltage == voltage) &&
            (identical(other.turnovers, turnovers) ||
                other.turnovers == turnovers) &&
            (identical(other.weight, weight) || other.weight == weight) &&
            (identical(other.voltageVN, voltageVN) ||
                other.voltageVN == voltageVN) &&
            (identical(other.voltageNN, voltageNN) ||
                other.voltageNN == voltageNN) &&
            (identical(other.voltageSN, voltageSN) ||
                other.voltageSN == voltageSN) &&
            (identical(other.amperage, amperage) ||
                other.amperage == amperage) &&
            (identical(other.version, version) || other.version == version) &&
            (identical(other.updateDate, updateDate) ||
                other.updateDate == updateDate) &&
            (identical(other.comment, comment) || other.comment == comment) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        identifier,
        id,
        number,
        date,
        finishDate,
        statusOrder,
        manager,
        branch,
        client,
        rn,
        rnOGK,
        name,
        equipment,
        power,
        voltage,
        turnovers,
        weight,
        voltageVN,
        voltageNN,
        voltageSN,
        amperage,
        version,
        updateDate,
        comment,
        createdAt,
        updatedAt
      ]);

  /// Create a copy of OrderModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OrderModelImplCopyWith<_$OrderModelImpl> get copyWith =>
      __$$OrderModelImplCopyWithImpl<_$OrderModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OrderModelImplToJson(
      this,
    );
  }
}

abstract class _OrderModel extends OrderModel {
  const factory _OrderModel(
      {@JsonKey(name: '_id') final String? identifier,
      final String? id,
      final String? number,
      final String? date,
      final String? finishDate,
      final String? statusOrder,
      final String? manager,
      final OrderBranchModel? branch,
      final OrderClientModel? client,
      final String? rn,
      final String? rnOGK,
      final String? name,
      final String? equipment,
      final String? power,
      final String? voltage,
      final String? turnovers,
      final String? weight,
      final String? voltageVN,
      final String? voltageNN,
      final String? voltageSN,
      final String? amperage,
      final String? version,
      final String? updateDate,
      final String? comment,
      final double? createdAt,
      final double? updatedAt}) = _$OrderModelImpl;
  const _OrderModel._() : super._();

  factory _OrderModel.fromJson(Map<String, dynamic> json) =
      _$OrderModelImpl.fromJson;

  @override
  @JsonKey(name: '_id')
  String? get identifier;
  @override
  String? get id;
  @override
  String? get number;
  @override
  String? get date;
  @override
  String? get finishDate;
  @override
  String? get statusOrder;
  @override
  String? get manager;
  @override
  OrderBranchModel? get branch;
  @override
  OrderClientModel? get client;
  @override
  String? get rn;
  @override
  String? get rnOGK;
  @override
  String? get name;
  @override
  String? get equipment;
  @override
  String? get power;
  @override
  String? get voltage;
  @override
  String? get turnovers;
  @override
  String? get weight;
  @override
  String? get voltageVN;
  @override
  String? get voltageNN;
  @override
  String? get voltageSN;
  @override
  String? get amperage;
  @override
  String? get version;
  @override
  String? get updateDate; //Uint64? updateDateNumbers,
  @override
  String? get comment;
  @override
  double? get createdAt;
  @override
  double? get updatedAt;

  /// Create a copy of OrderModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OrderModelImplCopyWith<_$OrderModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
