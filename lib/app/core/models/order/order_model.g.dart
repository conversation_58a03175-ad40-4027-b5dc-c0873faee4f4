// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$OrderModelImpl _$$OrderModelImplFromJson(Map<String, dynamic> json) =>
    _$OrderModelImpl(
      identifier: json['_id'] as String?,
      id: json['id'] as String?,
      number: json['number'] as String?,
      date: json['date'] as String?,
      finishDate: json['finishDate'] as String?,
      statusOrder: json['statusOrder'] as String?,
      manager: json['manager'] as String?,
      branch: json['branch'] == null
          ? null
          : OrderBranchModel.fromJson(json['branch'] as Map<String, dynamic>),
      client: json['client'] == null
          ? null
          : OrderClientModel.fromJson(json['client'] as Map<String, dynamic>),
      rn: json['rn'] as String?,
      rnOGK: json['rnOGK'] as String?,
      name: json['name'] as String?,
      equipment: json['equipment'] as String?,
      power: json['power'] as String?,
      voltage: json['voltage'] as String?,
      turnovers: json['turnovers'] as String?,
      weight: json['weight'] as String?,
      voltageVN: json['voltageVN'] as String?,
      voltageNN: json['voltageNN'] as String?,
      voltageSN: json['voltageSN'] as String?,
      amperage: json['amperage'] as String?,
      version: json['version'] as String?,
      updateDate: json['updateDate'] as String?,
      comment: json['comment'] as String?,
      createdAt: (json['createdAt'] as num?)?.toDouble(),
      updatedAt: (json['updatedAt'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$OrderModelImplToJson(_$OrderModelImpl instance) =>
    <String, dynamic>{
      if (instance.identifier case final value?) '_id': value,
      if (instance.id case final value?) 'id': value,
      if (instance.number case final value?) 'number': value,
      if (instance.date case final value?) 'date': value,
      if (instance.finishDate case final value?) 'finishDate': value,
      if (instance.statusOrder case final value?) 'statusOrder': value,
      if (instance.manager case final value?) 'manager': value,
      if (instance.branch?.toJson() case final value?) 'branch': value,
      if (instance.client?.toJson() case final value?) 'client': value,
      if (instance.rn case final value?) 'rn': value,
      if (instance.rnOGK case final value?) 'rnOGK': value,
      if (instance.name case final value?) 'name': value,
      if (instance.equipment case final value?) 'equipment': value,
      if (instance.power case final value?) 'power': value,
      if (instance.voltage case final value?) 'voltage': value,
      if (instance.turnovers case final value?) 'turnovers': value,
      if (instance.weight case final value?) 'weight': value,
      if (instance.voltageVN case final value?) 'voltageVN': value,
      if (instance.voltageNN case final value?) 'voltageNN': value,
      if (instance.voltageSN case final value?) 'voltageSN': value,
      if (instance.amperage case final value?) 'amperage': value,
      if (instance.version case final value?) 'version': value,
      if (instance.updateDate case final value?) 'updateDate': value,
      if (instance.comment case final value?) 'comment': value,
      if (instance.createdAt case final value?) 'createdAt': value,
      if (instance.updatedAt case final value?) 'updatedAt': value,
    };
