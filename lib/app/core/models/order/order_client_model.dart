import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter/foundation.dart';

part 'order_client_model.freezed.dart';
part 'order_client_model.g.dart';

@freezed
class OrderClientModel with _$OrderClientModel {
  const OrderClientModel._();
  @JsonSerializable(explicitToJson: true, includeIfNull: false)
  const factory OrderClientModel({
    required String idClient,
    required String name,
    String? inn,
  }) = _OrderClientModel;

  factory OrderClientModel.fromJson(Map<String, Object?> json) =>
      _$OrderClientModelFromJson(json);
}
