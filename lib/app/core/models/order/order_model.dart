import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter/foundation.dart';

import 'order_branch_model.dart';
import 'order_client_model.dart';

part 'order_model.freezed.dart';
part 'order_model.g.dart';

@freezed
class OrderModel with _$OrderModel {
  const OrderModel._();
  @JsonSerializable(explicitToJson: true, includeIfNull: false)
  const factory OrderModel({
    @JsonKey(name: '_id') String? identifier,
    String? id,
    String? number,
    String? date,
    String? finishDate,
    String? statusOrder,
    String? manager,
    OrderBranchModel? branch,
    OrderClientModel? client,
    String? rn,
    String? rnOGK,
    String? name,
    String? equipment,
    String? power,
    String? voltage,
    String? turnovers,
    String? weight,
    String? voltageVN,
    String? voltageNN,
    String? voltageSN,
    String? amperage,
    String? version,
    String? updateDate,
    //Uint64? updateDateNumbers,
    String? comment,
    double? createdAt,
    double? updatedAt,
  }) = _OrderModel;

  factory OrderModel.fromJson(Map<String, Object?> json) =>
      _$OrderModelFromJson(json);
}
