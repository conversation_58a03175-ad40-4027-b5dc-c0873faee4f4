// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'order_branch_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

OrderBranchModel _$OrderBranchModelFromJson(Map<String, dynamic> json) {
  return _OrderBranchModel.fromJson(json);
}

/// @nodoc
mixin _$OrderBranchModel {
  String get idBranch => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;

  /// Serializes this OrderBranchModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of OrderBranchModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OrderBranchModelCopyWith<OrderBranchModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrderBranchModelCopyWith<$Res> {
  factory $OrderBranchModelCopyWith(
          OrderBranchModel value, $Res Function(OrderBranchModel) then) =
      _$OrderBranchModelCopyWithImpl<$Res, OrderBranchModel>;
  @useResult
  $Res call({String idBranch, String name});
}

/// @nodoc
class _$OrderBranchModelCopyWithImpl<$Res, $Val extends OrderBranchModel>
    implements $OrderBranchModelCopyWith<$Res> {
  _$OrderBranchModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OrderBranchModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? idBranch = null,
    Object? name = null,
  }) {
    return _then(_value.copyWith(
      idBranch: null == idBranch
          ? _value.idBranch
          : idBranch // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OrderBranchModelImplCopyWith<$Res>
    implements $OrderBranchModelCopyWith<$Res> {
  factory _$$OrderBranchModelImplCopyWith(_$OrderBranchModelImpl value,
          $Res Function(_$OrderBranchModelImpl) then) =
      __$$OrderBranchModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String idBranch, String name});
}

/// @nodoc
class __$$OrderBranchModelImplCopyWithImpl<$Res>
    extends _$OrderBranchModelCopyWithImpl<$Res, _$OrderBranchModelImpl>
    implements _$$OrderBranchModelImplCopyWith<$Res> {
  __$$OrderBranchModelImplCopyWithImpl(_$OrderBranchModelImpl _value,
      $Res Function(_$OrderBranchModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrderBranchModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? idBranch = null,
    Object? name = null,
  }) {
    return _then(_$OrderBranchModelImpl(
      idBranch: null == idBranch
          ? _value.idBranch
          : idBranch // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class _$OrderBranchModelImpl extends _OrderBranchModel
    with DiagnosticableTreeMixin {
  const _$OrderBranchModelImpl({required this.idBranch, required this.name})
      : super._();

  factory _$OrderBranchModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$OrderBranchModelImplFromJson(json);

  @override
  final String idBranch;
  @override
  final String name;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'OrderBranchModel(idBranch: $idBranch, name: $name)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'OrderBranchModel'))
      ..add(DiagnosticsProperty('idBranch', idBranch))
      ..add(DiagnosticsProperty('name', name));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrderBranchModelImpl &&
            (identical(other.idBranch, idBranch) ||
                other.idBranch == idBranch) &&
            (identical(other.name, name) || other.name == name));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, idBranch, name);

  /// Create a copy of OrderBranchModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OrderBranchModelImplCopyWith<_$OrderBranchModelImpl> get copyWith =>
      __$$OrderBranchModelImplCopyWithImpl<_$OrderBranchModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OrderBranchModelImplToJson(
      this,
    );
  }
}

abstract class _OrderBranchModel extends OrderBranchModel {
  const factory _OrderBranchModel(
      {required final String idBranch,
      required final String name}) = _$OrderBranchModelImpl;
  const _OrderBranchModel._() : super._();

  factory _OrderBranchModel.fromJson(Map<String, dynamic> json) =
      _$OrderBranchModelImpl.fromJson;

  @override
  String get idBranch;
  @override
  String get name;

  /// Create a copy of OrderBranchModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OrderBranchModelImplCopyWith<_$OrderBranchModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
