// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'branch_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$BranchModelImpl _$$BranchModelImplFromJson(Map<String, dynamic> json) =>
    _$BranchModelImpl(
      identifier: (json['identifier'] as num).toInt(),
      name: json['name'] as String,
      shortName: json['shortName'] as String,
      idBranch: json['idBranch'] as String,
    );

Map<String, dynamic> _$$BranchModelImplToJson(_$BranchModelImpl instance) =>
    <String, dynamic>{
      'identifier': instance.identifier,
      'name': instance.name,
      'shortName': instance.shortName,
      'idBranch': instance.idBranch,
    };

_$BranchListModelImpl _$$BranchListModelImplFromJson(
        Map<String, dynamic> json) =>
    _$BranchListModelImpl(
      (json['data'] as List<dynamic>)
          .map((e) => BranchModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$BranchListModelImplToJson(
        _$BranchListModelImpl instance) =>
    <String, dynamic>{
      'data': instance.data,
    };
