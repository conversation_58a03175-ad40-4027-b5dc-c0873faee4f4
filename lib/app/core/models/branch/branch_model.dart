import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter/foundation.dart';

part 'branch_model.freezed.dart';
part 'branch_model.g.dart';

@freezed
class BranchModel with _$BranchModel {
  const BranchModel._();
  @JsonSerializable(explicitToJson: true, includeIfNull: false)
  const factory BranchModel({
    required int identifier,
    required String name,
    required String shortName,
    required String idBranch,
  }) = _BranchModel;

  factory BranchModel.fromJson(Map<String, Object?> json) =>
      _$BranchModelFromJson(json);
}

@freezed
class BranchListModel with _$BranchListModel {
  factory BranchListModel(List<BranchModel> data) = _BranchListModel;

  factory BranchListModel.fromJson(Map<String, dynamic> json) =>
      _$BranchListModelFromJson(json);
}
