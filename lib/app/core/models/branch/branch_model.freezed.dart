// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'branch_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

BranchModel _$BranchModelFromJson(Map<String, dynamic> json) {
  return _BranchModel.fromJson(json);
}

/// @nodoc
mixin _$BranchModel {
  int get identifier => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get shortName => throw _privateConstructorUsedError;
  String get idBranch => throw _privateConstructorUsedError;

  /// Serializes this BranchModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of BranchModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BranchModelCopyWith<BranchModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BranchModelCopyWith<$Res> {
  factory $BranchModelCopyWith(
          BranchModel value, $Res Function(BranchModel) then) =
      _$BranchModelCopyWithImpl<$Res, BranchModel>;
  @useResult
  $Res call({int identifier, String name, String shortName, String idBranch});
}

/// @nodoc
class _$BranchModelCopyWithImpl<$Res, $Val extends BranchModel>
    implements $BranchModelCopyWith<$Res> {
  _$BranchModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BranchModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? identifier = null,
    Object? name = null,
    Object? shortName = null,
    Object? idBranch = null,
  }) {
    return _then(_value.copyWith(
      identifier: null == identifier
          ? _value.identifier
          : identifier // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      shortName: null == shortName
          ? _value.shortName
          : shortName // ignore: cast_nullable_to_non_nullable
              as String,
      idBranch: null == idBranch
          ? _value.idBranch
          : idBranch // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BranchModelImplCopyWith<$Res>
    implements $BranchModelCopyWith<$Res> {
  factory _$$BranchModelImplCopyWith(
          _$BranchModelImpl value, $Res Function(_$BranchModelImpl) then) =
      __$$BranchModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int identifier, String name, String shortName, String idBranch});
}

/// @nodoc
class __$$BranchModelImplCopyWithImpl<$Res>
    extends _$BranchModelCopyWithImpl<$Res, _$BranchModelImpl>
    implements _$$BranchModelImplCopyWith<$Res> {
  __$$BranchModelImplCopyWithImpl(
      _$BranchModelImpl _value, $Res Function(_$BranchModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of BranchModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? identifier = null,
    Object? name = null,
    Object? shortName = null,
    Object? idBranch = null,
  }) {
    return _then(_$BranchModelImpl(
      identifier: null == identifier
          ? _value.identifier
          : identifier // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      shortName: null == shortName
          ? _value.shortName
          : shortName // ignore: cast_nullable_to_non_nullable
              as String,
      idBranch: null == idBranch
          ? _value.idBranch
          : idBranch // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class _$BranchModelImpl extends _BranchModel with DiagnosticableTreeMixin {
  const _$BranchModelImpl(
      {required this.identifier,
      required this.name,
      required this.shortName,
      required this.idBranch})
      : super._();

  factory _$BranchModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$BranchModelImplFromJson(json);

  @override
  final int identifier;
  @override
  final String name;
  @override
  final String shortName;
  @override
  final String idBranch;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'BranchModel(identifier: $identifier, name: $name, shortName: $shortName, idBranch: $idBranch)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'BranchModel'))
      ..add(DiagnosticsProperty('identifier', identifier))
      ..add(DiagnosticsProperty('name', name))
      ..add(DiagnosticsProperty('shortName', shortName))
      ..add(DiagnosticsProperty('idBranch', idBranch));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BranchModelImpl &&
            (identical(other.identifier, identifier) ||
                other.identifier == identifier) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.shortName, shortName) ||
                other.shortName == shortName) &&
            (identical(other.idBranch, idBranch) ||
                other.idBranch == idBranch));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, identifier, name, shortName, idBranch);

  /// Create a copy of BranchModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BranchModelImplCopyWith<_$BranchModelImpl> get copyWith =>
      __$$BranchModelImplCopyWithImpl<_$BranchModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BranchModelImplToJson(
      this,
    );
  }
}

abstract class _BranchModel extends BranchModel {
  const factory _BranchModel(
      {required final int identifier,
      required final String name,
      required final String shortName,
      required final String idBranch}) = _$BranchModelImpl;
  const _BranchModel._() : super._();

  factory _BranchModel.fromJson(Map<String, dynamic> json) =
      _$BranchModelImpl.fromJson;

  @override
  int get identifier;
  @override
  String get name;
  @override
  String get shortName;
  @override
  String get idBranch;

  /// Create a copy of BranchModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BranchModelImplCopyWith<_$BranchModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

BranchListModel _$BranchListModelFromJson(Map<String, dynamic> json) {
  return _BranchListModel.fromJson(json);
}

/// @nodoc
mixin _$BranchListModel {
  List<BranchModel> get data => throw _privateConstructorUsedError;

  /// Serializes this BranchListModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of BranchListModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BranchListModelCopyWith<BranchListModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BranchListModelCopyWith<$Res> {
  factory $BranchListModelCopyWith(
          BranchListModel value, $Res Function(BranchListModel) then) =
      _$BranchListModelCopyWithImpl<$Res, BranchListModel>;
  @useResult
  $Res call({List<BranchModel> data});
}

/// @nodoc
class _$BranchListModelCopyWithImpl<$Res, $Val extends BranchListModel>
    implements $BranchListModelCopyWith<$Res> {
  _$BranchListModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BranchListModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
  }) {
    return _then(_value.copyWith(
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<BranchModel>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BranchListModelImplCopyWith<$Res>
    implements $BranchListModelCopyWith<$Res> {
  factory _$$BranchListModelImplCopyWith(_$BranchListModelImpl value,
          $Res Function(_$BranchListModelImpl) then) =
      __$$BranchListModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<BranchModel> data});
}

/// @nodoc
class __$$BranchListModelImplCopyWithImpl<$Res>
    extends _$BranchListModelCopyWithImpl<$Res, _$BranchListModelImpl>
    implements _$$BranchListModelImplCopyWith<$Res> {
  __$$BranchListModelImplCopyWithImpl(
      _$BranchListModelImpl _value, $Res Function(_$BranchListModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of BranchListModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
  }) {
    return _then(_$BranchListModelImpl(
      null == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as List<BranchModel>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BranchListModelImpl
    with DiagnosticableTreeMixin
    implements _BranchListModel {
  _$BranchListModelImpl(final List<BranchModel> data) : _data = data;

  factory _$BranchListModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$BranchListModelImplFromJson(json);

  final List<BranchModel> _data;
  @override
  List<BranchModel> get data {
    if (_data is EqualUnmodifiableListView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_data);
  }

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'BranchListModel(data: $data)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'BranchListModel'))
      ..add(DiagnosticsProperty('data', data));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BranchListModelImpl &&
            const DeepCollectionEquality().equals(other._data, _data));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_data));

  /// Create a copy of BranchListModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BranchListModelImplCopyWith<_$BranchListModelImpl> get copyWith =>
      __$$BranchListModelImplCopyWithImpl<_$BranchListModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BranchListModelImplToJson(
      this,
    );
  }
}

abstract class _BranchListModel implements BranchListModel {
  factory _BranchListModel(final List<BranchModel> data) =
      _$BranchListModelImpl;

  factory _BranchListModel.fromJson(Map<String, dynamic> json) =
      _$BranchListModelImpl.fromJson;

  @override
  List<BranchModel> get data;

  /// Create a copy of BranchListModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BranchListModelImplCopyWith<_$BranchListModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
