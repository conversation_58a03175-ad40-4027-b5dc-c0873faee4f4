import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:uer_flutter/app/helpers/string_helper.dart';

import '../../services/api/custom_client.dart';

part 'photo_model.freezed.dart';
part 'photo_model.g.dart';

@freezed
class PhotoModel with _$PhotoModel {
  const PhotoModel._();
  @JsonSerializable(explicitToJson: true, includeIfNull: false)
  const factory PhotoModel({
    @JsonKey(name: '_id') String? id,
    required String url,
    required String owner,
    int? area,
    int? job,
    String? comment,
    double? createdAt,
    double? updatedAt,
  }) = _PhotoModel;

  String getImgUrl() {
    return "${CustomClient.serverAddress}/$url";
  }

  String getName() {
    var split = url.split(".");
    var name = split.first;

    if (name.isEmpty) {
      return getRandomString(12);
    }

    if (name.contains("/")) {
      name = name.replaceAll('/', '_');
    }

    return name;
  }

  factory PhotoModel.fromJson(Map<String, Object?> json) =>
      _$PhotoModelFromJson(json);
}
