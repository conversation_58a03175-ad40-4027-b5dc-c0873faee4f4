// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'video_test_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$VideoTestModelImpl _$$VideoTestModelImplFromJson(Map<String, dynamic> json) =>
    _$VideoTestModelImpl(
      url: json['url'] as String,
      name: json['name'] as String,
      lastEdit: (json['lastEdit'] as num?)?.toDouble(),
      createdAt: (json['createdAt'] as num?)?.toDouble(),
      updatedAt: (json['updatedAt'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$VideoTestModelImplToJson(
        _$VideoTestModelImpl instance) =>
    <String, dynamic>{
      'url': instance.url,
      'name': instance.name,
      if (instance.lastEdit case final value?) 'lastEdit': value,
      if (instance.createdAt case final value?) 'createdAt': value,
      if (instance.updatedAt case final value?) 'updatedAt': value,
    };
