// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'video_test_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

VideoTestModel _$VideoTestModelFromJson(Map<String, dynamic> json) {
  return _VideoTestModel.fromJson(json);
}

/// @nodoc
mixin _$VideoTestModel {
  String get url => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  double? get lastEdit => throw _privateConstructorUsedError;
  double? get createdAt => throw _privateConstructorUsedError;
  double? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this VideoTestModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of VideoTestModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $VideoTestModelCopyWith<VideoTestModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VideoTestModelCopyWith<$Res> {
  factory $VideoTestModelCopyWith(
          VideoTestModel value, $Res Function(VideoTestModel) then) =
      _$VideoTestModelCopyWithImpl<$Res, VideoTestModel>;
  @useResult
  $Res call(
      {String url,
      String name,
      double? lastEdit,
      double? createdAt,
      double? updatedAt});
}

/// @nodoc
class _$VideoTestModelCopyWithImpl<$Res, $Val extends VideoTestModel>
    implements $VideoTestModelCopyWith<$Res> {
  _$VideoTestModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of VideoTestModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? url = null,
    Object? name = null,
    Object? lastEdit = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_value.copyWith(
      url: null == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      lastEdit: freezed == lastEdit
          ? _value.lastEdit
          : lastEdit // ignore: cast_nullable_to_non_nullable
              as double?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as double?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$VideoTestModelImplCopyWith<$Res>
    implements $VideoTestModelCopyWith<$Res> {
  factory _$$VideoTestModelImplCopyWith(_$VideoTestModelImpl value,
          $Res Function(_$VideoTestModelImpl) then) =
      __$$VideoTestModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String url,
      String name,
      double? lastEdit,
      double? createdAt,
      double? updatedAt});
}

/// @nodoc
class __$$VideoTestModelImplCopyWithImpl<$Res>
    extends _$VideoTestModelCopyWithImpl<$Res, _$VideoTestModelImpl>
    implements _$$VideoTestModelImplCopyWith<$Res> {
  __$$VideoTestModelImplCopyWithImpl(
      _$VideoTestModelImpl _value, $Res Function(_$VideoTestModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of VideoTestModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? url = null,
    Object? name = null,
    Object? lastEdit = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_$VideoTestModelImpl(
      url: null == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      lastEdit: freezed == lastEdit
          ? _value.lastEdit
          : lastEdit // ignore: cast_nullable_to_non_nullable
              as double?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as double?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class _$VideoTestModelImpl extends _VideoTestModel
    with DiagnosticableTreeMixin {
  const _$VideoTestModelImpl(
      {required this.url,
      required this.name,
      this.lastEdit,
      this.createdAt,
      this.updatedAt})
      : super._();

  factory _$VideoTestModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$VideoTestModelImplFromJson(json);

  @override
  final String url;
  @override
  final String name;
  @override
  final double? lastEdit;
  @override
  final double? createdAt;
  @override
  final double? updatedAt;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'VideoTestModel(url: $url, name: $name, lastEdit: $lastEdit, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'VideoTestModel'))
      ..add(DiagnosticsProperty('url', url))
      ..add(DiagnosticsProperty('name', name))
      ..add(DiagnosticsProperty('lastEdit', lastEdit))
      ..add(DiagnosticsProperty('createdAt', createdAt))
      ..add(DiagnosticsProperty('updatedAt', updatedAt));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VideoTestModelImpl &&
            (identical(other.url, url) || other.url == url) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.lastEdit, lastEdit) ||
                other.lastEdit == lastEdit) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, url, name, lastEdit, createdAt, updatedAt);

  /// Create a copy of VideoTestModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VideoTestModelImplCopyWith<_$VideoTestModelImpl> get copyWith =>
      __$$VideoTestModelImplCopyWithImpl<_$VideoTestModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VideoTestModelImplToJson(
      this,
    );
  }
}

abstract class _VideoTestModel extends VideoTestModel {
  const factory _VideoTestModel(
      {required final String url,
      required final String name,
      final double? lastEdit,
      final double? createdAt,
      final double? updatedAt}) = _$VideoTestModelImpl;
  const _VideoTestModel._() : super._();

  factory _VideoTestModel.fromJson(Map<String, dynamic> json) =
      _$VideoTestModelImpl.fromJson;

  @override
  String get url;
  @override
  String get name;
  @override
  double? get lastEdit;
  @override
  double? get createdAt;
  @override
  double? get updatedAt;

  /// Create a copy of VideoTestModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VideoTestModelImplCopyWith<_$VideoTestModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
