import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter/foundation.dart';

part 'video_test_model.freezed.dart';
part 'video_test_model.g.dart';

@freezed
class VideoTestModel with _$VideoTestModel {
  const VideoTestModel._();
  @JsonSerializable(explicitToJson: true, includeIfNull: false)
  const factory VideoTestModel({
    required String url,
    required String name,
    double? lastEdit,
    double? createdAt,
    double? updatedAt,
  }) = _VideoTestModel;

  // String getImgUrl() {
  //   return "${CustomClient.serverAddress}/$url";
  // }

  // String getName() {
  //   var split = url.split(".");
  //   return split.first;
  // }

  factory VideoTestModel.fromJson(Map<String, Object?> json) =>
      _$VideoTestModelFromJson(json);
}
