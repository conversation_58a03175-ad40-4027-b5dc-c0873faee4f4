import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'search_model.freezed.dart';
part 'search_model.g.dart';

@freezed
class SearchModel with _$SearchModel {
  const SearchModel._();
  @JsonSerializable(explicitToJson: true, includeIfNull: false)
  const factory SearchModel({
    String? searchField,
    String? mainID,
    String? componentID,
    String? repairNumber,
    List<String>? mainIDArr,
    String? orderName,
    String? orderEquipment,
    String? orderClientName,
    int? area,
    int? branch,
    int? limit,
    int? offset,
    int? recentShipmentWeeks,
    bool? overDeadline,
    bool? finished,
    bool? archiveInYear,
    bool? createdAtSort,
    String? projection,
    @Json<PERSON>ey(name: "new") bool? newBool,
    String? searchClient,
    String? searchEquipment,
  }) = _SearchModel;

  bool showSearchResult() {
    if (searchField != null) {
      return true;
    }
    if (mainID != null) {
      return true;
    }
    if (searchClient != null) {
      return true;
    }
    if (searchEquipment != null) {
      return true;
    }

    return false;
  }

  factory SearchModel.fromJson(Map<String, Object?> json) =>
      _$SearchModelFromJson(json);
}
