// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'search_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SearchModelImpl _$$SearchModelImplFromJson(Map<String, dynamic> json) =>
    _$SearchModelImpl(
      searchField: json['searchField'] as String?,
      mainID: json['mainID'] as String?,
      componentID: json['componentID'] as String?,
      repairNumber: json['repairNumber'] as String?,
      mainIDArr: (json['mainIDArr'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      orderName: json['orderName'] as String?,
      orderEquipment: json['orderEquipment'] as String?,
      orderClientName: json['orderClientName'] as String?,
      area: (json['area'] as num?)?.toInt(),
      branch: (json['branch'] as num?)?.toInt(),
      limit: (json['limit'] as num?)?.toInt(),
      offset: (json['offset'] as num?)?.toInt(),
      recentShipmentWeeks: (json['recentShipmentWeeks'] as num?)?.toInt(),
      overDeadline: json['overDeadline'] as bool?,
      finished: json['finished'] as bool?,
      archiveInYear: json['archiveInYear'] as bool?,
      createdAtSort: json['createdAtSort'] as bool?,
      projection: json['projection'] as String?,
      newBool: json['new'] as bool?,
      searchClient: json['searchClient'] as String?,
      searchEquipment: json['searchEquipment'] as String?,
    );

Map<String, dynamic> _$$SearchModelImplToJson(_$SearchModelImpl instance) =>
    <String, dynamic>{
      if (instance.searchField case final value?) 'searchField': value,
      if (instance.mainID case final value?) 'mainID': value,
      if (instance.componentID case final value?) 'componentID': value,
      if (instance.repairNumber case final value?) 'repairNumber': value,
      if (instance.mainIDArr case final value?) 'mainIDArr': value,
      if (instance.orderName case final value?) 'orderName': value,
      if (instance.orderEquipment case final value?) 'orderEquipment': value,
      if (instance.orderClientName case final value?) 'orderClientName': value,
      if (instance.area case final value?) 'area': value,
      if (instance.branch case final value?) 'branch': value,
      if (instance.limit case final value?) 'limit': value,
      if (instance.offset case final value?) 'offset': value,
      if (instance.recentShipmentWeeks case final value?)
        'recentShipmentWeeks': value,
      if (instance.overDeadline case final value?) 'overDeadline': value,
      if (instance.finished case final value?) 'finished': value,
      if (instance.archiveInYear case final value?) 'archiveInYear': value,
      if (instance.createdAtSort case final value?) 'createdAtSort': value,
      if (instance.projection case final value?) 'projection': value,
      if (instance.newBool case final value?) 'new': value,
      if (instance.searchClient case final value?) 'searchClient': value,
      if (instance.searchEquipment case final value?) 'searchEquipment': value,
    };
