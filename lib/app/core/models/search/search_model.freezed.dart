// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'search_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SearchModel _$SearchModelFromJson(Map<String, dynamic> json) {
  return _SearchModel.fromJson(json);
}

/// @nodoc
mixin _$SearchModel {
  String? get searchField => throw _privateConstructorUsedError;
  String? get mainID => throw _privateConstructorUsedError;
  String? get componentID => throw _privateConstructorUsedError;
  String? get repairNumber => throw _privateConstructorUsedError;
  List<String>? get mainIDArr => throw _privateConstructorUsedError;
  String? get orderName => throw _privateConstructorUsedError;
  String? get orderEquipment => throw _privateConstructorUsedError;
  String? get orderClientName => throw _privateConstructorUsedError;
  int? get area => throw _privateConstructorUsedError;
  int? get branch => throw _privateConstructorUsedError;
  int? get limit => throw _privateConstructorUsedError;
  int? get offset => throw _privateConstructorUsedError;
  int? get recentShipmentWeeks => throw _privateConstructorUsedError;
  bool? get overDeadline => throw _privateConstructorUsedError;
  bool? get finished => throw _privateConstructorUsedError;
  bool? get archiveInYear => throw _privateConstructorUsedError;
  bool? get createdAtSort => throw _privateConstructorUsedError;
  String? get projection => throw _privateConstructorUsedError;
  @JsonKey(name: "new")
  bool? get newBool => throw _privateConstructorUsedError;
  String? get searchClient => throw _privateConstructorUsedError;
  String? get searchEquipment => throw _privateConstructorUsedError;

  /// Serializes this SearchModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SearchModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SearchModelCopyWith<SearchModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SearchModelCopyWith<$Res> {
  factory $SearchModelCopyWith(
          SearchModel value, $Res Function(SearchModel) then) =
      _$SearchModelCopyWithImpl<$Res, SearchModel>;
  @useResult
  $Res call(
      {String? searchField,
      String? mainID,
      String? componentID,
      String? repairNumber,
      List<String>? mainIDArr,
      String? orderName,
      String? orderEquipment,
      String? orderClientName,
      int? area,
      int? branch,
      int? limit,
      int? offset,
      int? recentShipmentWeeks,
      bool? overDeadline,
      bool? finished,
      bool? archiveInYear,
      bool? createdAtSort,
      String? projection,
      @JsonKey(name: "new") bool? newBool,
      String? searchClient,
      String? searchEquipment});
}

/// @nodoc
class _$SearchModelCopyWithImpl<$Res, $Val extends SearchModel>
    implements $SearchModelCopyWith<$Res> {
  _$SearchModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SearchModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? searchField = freezed,
    Object? mainID = freezed,
    Object? componentID = freezed,
    Object? repairNumber = freezed,
    Object? mainIDArr = freezed,
    Object? orderName = freezed,
    Object? orderEquipment = freezed,
    Object? orderClientName = freezed,
    Object? area = freezed,
    Object? branch = freezed,
    Object? limit = freezed,
    Object? offset = freezed,
    Object? recentShipmentWeeks = freezed,
    Object? overDeadline = freezed,
    Object? finished = freezed,
    Object? archiveInYear = freezed,
    Object? createdAtSort = freezed,
    Object? projection = freezed,
    Object? newBool = freezed,
    Object? searchClient = freezed,
    Object? searchEquipment = freezed,
  }) {
    return _then(_value.copyWith(
      searchField: freezed == searchField
          ? _value.searchField
          : searchField // ignore: cast_nullable_to_non_nullable
              as String?,
      mainID: freezed == mainID
          ? _value.mainID
          : mainID // ignore: cast_nullable_to_non_nullable
              as String?,
      componentID: freezed == componentID
          ? _value.componentID
          : componentID // ignore: cast_nullable_to_non_nullable
              as String?,
      repairNumber: freezed == repairNumber
          ? _value.repairNumber
          : repairNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      mainIDArr: freezed == mainIDArr
          ? _value.mainIDArr
          : mainIDArr // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      orderName: freezed == orderName
          ? _value.orderName
          : orderName // ignore: cast_nullable_to_non_nullable
              as String?,
      orderEquipment: freezed == orderEquipment
          ? _value.orderEquipment
          : orderEquipment // ignore: cast_nullable_to_non_nullable
              as String?,
      orderClientName: freezed == orderClientName
          ? _value.orderClientName
          : orderClientName // ignore: cast_nullable_to_non_nullable
              as String?,
      area: freezed == area
          ? _value.area
          : area // ignore: cast_nullable_to_non_nullable
              as int?,
      branch: freezed == branch
          ? _value.branch
          : branch // ignore: cast_nullable_to_non_nullable
              as int?,
      limit: freezed == limit
          ? _value.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int?,
      offset: freezed == offset
          ? _value.offset
          : offset // ignore: cast_nullable_to_non_nullable
              as int?,
      recentShipmentWeeks: freezed == recentShipmentWeeks
          ? _value.recentShipmentWeeks
          : recentShipmentWeeks // ignore: cast_nullable_to_non_nullable
              as int?,
      overDeadline: freezed == overDeadline
          ? _value.overDeadline
          : overDeadline // ignore: cast_nullable_to_non_nullable
              as bool?,
      finished: freezed == finished
          ? _value.finished
          : finished // ignore: cast_nullable_to_non_nullable
              as bool?,
      archiveInYear: freezed == archiveInYear
          ? _value.archiveInYear
          : archiveInYear // ignore: cast_nullable_to_non_nullable
              as bool?,
      createdAtSort: freezed == createdAtSort
          ? _value.createdAtSort
          : createdAtSort // ignore: cast_nullable_to_non_nullable
              as bool?,
      projection: freezed == projection
          ? _value.projection
          : projection // ignore: cast_nullable_to_non_nullable
              as String?,
      newBool: freezed == newBool
          ? _value.newBool
          : newBool // ignore: cast_nullable_to_non_nullable
              as bool?,
      searchClient: freezed == searchClient
          ? _value.searchClient
          : searchClient // ignore: cast_nullable_to_non_nullable
              as String?,
      searchEquipment: freezed == searchEquipment
          ? _value.searchEquipment
          : searchEquipment // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SearchModelImplCopyWith<$Res>
    implements $SearchModelCopyWith<$Res> {
  factory _$$SearchModelImplCopyWith(
          _$SearchModelImpl value, $Res Function(_$SearchModelImpl) then) =
      __$$SearchModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? searchField,
      String? mainID,
      String? componentID,
      String? repairNumber,
      List<String>? mainIDArr,
      String? orderName,
      String? orderEquipment,
      String? orderClientName,
      int? area,
      int? branch,
      int? limit,
      int? offset,
      int? recentShipmentWeeks,
      bool? overDeadline,
      bool? finished,
      bool? archiveInYear,
      bool? createdAtSort,
      String? projection,
      @JsonKey(name: "new") bool? newBool,
      String? searchClient,
      String? searchEquipment});
}

/// @nodoc
class __$$SearchModelImplCopyWithImpl<$Res>
    extends _$SearchModelCopyWithImpl<$Res, _$SearchModelImpl>
    implements _$$SearchModelImplCopyWith<$Res> {
  __$$SearchModelImplCopyWithImpl(
      _$SearchModelImpl _value, $Res Function(_$SearchModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of SearchModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? searchField = freezed,
    Object? mainID = freezed,
    Object? componentID = freezed,
    Object? repairNumber = freezed,
    Object? mainIDArr = freezed,
    Object? orderName = freezed,
    Object? orderEquipment = freezed,
    Object? orderClientName = freezed,
    Object? area = freezed,
    Object? branch = freezed,
    Object? limit = freezed,
    Object? offset = freezed,
    Object? recentShipmentWeeks = freezed,
    Object? overDeadline = freezed,
    Object? finished = freezed,
    Object? archiveInYear = freezed,
    Object? createdAtSort = freezed,
    Object? projection = freezed,
    Object? newBool = freezed,
    Object? searchClient = freezed,
    Object? searchEquipment = freezed,
  }) {
    return _then(_$SearchModelImpl(
      searchField: freezed == searchField
          ? _value.searchField
          : searchField // ignore: cast_nullable_to_non_nullable
              as String?,
      mainID: freezed == mainID
          ? _value.mainID
          : mainID // ignore: cast_nullable_to_non_nullable
              as String?,
      componentID: freezed == componentID
          ? _value.componentID
          : componentID // ignore: cast_nullable_to_non_nullable
              as String?,
      repairNumber: freezed == repairNumber
          ? _value.repairNumber
          : repairNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      mainIDArr: freezed == mainIDArr
          ? _value._mainIDArr
          : mainIDArr // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      orderName: freezed == orderName
          ? _value.orderName
          : orderName // ignore: cast_nullable_to_non_nullable
              as String?,
      orderEquipment: freezed == orderEquipment
          ? _value.orderEquipment
          : orderEquipment // ignore: cast_nullable_to_non_nullable
              as String?,
      orderClientName: freezed == orderClientName
          ? _value.orderClientName
          : orderClientName // ignore: cast_nullable_to_non_nullable
              as String?,
      area: freezed == area
          ? _value.area
          : area // ignore: cast_nullable_to_non_nullable
              as int?,
      branch: freezed == branch
          ? _value.branch
          : branch // ignore: cast_nullable_to_non_nullable
              as int?,
      limit: freezed == limit
          ? _value.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int?,
      offset: freezed == offset
          ? _value.offset
          : offset // ignore: cast_nullable_to_non_nullable
              as int?,
      recentShipmentWeeks: freezed == recentShipmentWeeks
          ? _value.recentShipmentWeeks
          : recentShipmentWeeks // ignore: cast_nullable_to_non_nullable
              as int?,
      overDeadline: freezed == overDeadline
          ? _value.overDeadline
          : overDeadline // ignore: cast_nullable_to_non_nullable
              as bool?,
      finished: freezed == finished
          ? _value.finished
          : finished // ignore: cast_nullable_to_non_nullable
              as bool?,
      archiveInYear: freezed == archiveInYear
          ? _value.archiveInYear
          : archiveInYear // ignore: cast_nullable_to_non_nullable
              as bool?,
      createdAtSort: freezed == createdAtSort
          ? _value.createdAtSort
          : createdAtSort // ignore: cast_nullable_to_non_nullable
              as bool?,
      projection: freezed == projection
          ? _value.projection
          : projection // ignore: cast_nullable_to_non_nullable
              as String?,
      newBool: freezed == newBool
          ? _value.newBool
          : newBool // ignore: cast_nullable_to_non_nullable
              as bool?,
      searchClient: freezed == searchClient
          ? _value.searchClient
          : searchClient // ignore: cast_nullable_to_non_nullable
              as String?,
      searchEquipment: freezed == searchEquipment
          ? _value.searchEquipment
          : searchEquipment // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class _$SearchModelImpl extends _SearchModel with DiagnosticableTreeMixin {
  const _$SearchModelImpl(
      {this.searchField,
      this.mainID,
      this.componentID,
      this.repairNumber,
      final List<String>? mainIDArr,
      this.orderName,
      this.orderEquipment,
      this.orderClientName,
      this.area,
      this.branch,
      this.limit,
      this.offset,
      this.recentShipmentWeeks,
      this.overDeadline,
      this.finished,
      this.archiveInYear,
      this.createdAtSort,
      this.projection,
      @JsonKey(name: "new") this.newBool,
      this.searchClient,
      this.searchEquipment})
      : _mainIDArr = mainIDArr,
        super._();

  factory _$SearchModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$SearchModelImplFromJson(json);

  @override
  final String? searchField;
  @override
  final String? mainID;
  @override
  final String? componentID;
  @override
  final String? repairNumber;
  final List<String>? _mainIDArr;
  @override
  List<String>? get mainIDArr {
    final value = _mainIDArr;
    if (value == null) return null;
    if (_mainIDArr is EqualUnmodifiableListView) return _mainIDArr;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? orderName;
  @override
  final String? orderEquipment;
  @override
  final String? orderClientName;
  @override
  final int? area;
  @override
  final int? branch;
  @override
  final int? limit;
  @override
  final int? offset;
  @override
  final int? recentShipmentWeeks;
  @override
  final bool? overDeadline;
  @override
  final bool? finished;
  @override
  final bool? archiveInYear;
  @override
  final bool? createdAtSort;
  @override
  final String? projection;
  @override
  @JsonKey(name: "new")
  final bool? newBool;
  @override
  final String? searchClient;
  @override
  final String? searchEquipment;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'SearchModel(searchField: $searchField, mainID: $mainID, componentID: $componentID, repairNumber: $repairNumber, mainIDArr: $mainIDArr, orderName: $orderName, orderEquipment: $orderEquipment, orderClientName: $orderClientName, area: $area, branch: $branch, limit: $limit, offset: $offset, recentShipmentWeeks: $recentShipmentWeeks, overDeadline: $overDeadline, finished: $finished, archiveInYear: $archiveInYear, createdAtSort: $createdAtSort, projection: $projection, newBool: $newBool, searchClient: $searchClient, searchEquipment: $searchEquipment)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'SearchModel'))
      ..add(DiagnosticsProperty('searchField', searchField))
      ..add(DiagnosticsProperty('mainID', mainID))
      ..add(DiagnosticsProperty('componentID', componentID))
      ..add(DiagnosticsProperty('repairNumber', repairNumber))
      ..add(DiagnosticsProperty('mainIDArr', mainIDArr))
      ..add(DiagnosticsProperty('orderName', orderName))
      ..add(DiagnosticsProperty('orderEquipment', orderEquipment))
      ..add(DiagnosticsProperty('orderClientName', orderClientName))
      ..add(DiagnosticsProperty('area', area))
      ..add(DiagnosticsProperty('branch', branch))
      ..add(DiagnosticsProperty('limit', limit))
      ..add(DiagnosticsProperty('offset', offset))
      ..add(DiagnosticsProperty('recentShipmentWeeks', recentShipmentWeeks))
      ..add(DiagnosticsProperty('overDeadline', overDeadline))
      ..add(DiagnosticsProperty('finished', finished))
      ..add(DiagnosticsProperty('archiveInYear', archiveInYear))
      ..add(DiagnosticsProperty('createdAtSort', createdAtSort))
      ..add(DiagnosticsProperty('projection', projection))
      ..add(DiagnosticsProperty('newBool', newBool))
      ..add(DiagnosticsProperty('searchClient', searchClient))
      ..add(DiagnosticsProperty('searchEquipment', searchEquipment));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SearchModelImpl &&
            (identical(other.searchField, searchField) ||
                other.searchField == searchField) &&
            (identical(other.mainID, mainID) || other.mainID == mainID) &&
            (identical(other.componentID, componentID) ||
                other.componentID == componentID) &&
            (identical(other.repairNumber, repairNumber) ||
                other.repairNumber == repairNumber) &&
            const DeepCollectionEquality()
                .equals(other._mainIDArr, _mainIDArr) &&
            (identical(other.orderName, orderName) ||
                other.orderName == orderName) &&
            (identical(other.orderEquipment, orderEquipment) ||
                other.orderEquipment == orderEquipment) &&
            (identical(other.orderClientName, orderClientName) ||
                other.orderClientName == orderClientName) &&
            (identical(other.area, area) || other.area == area) &&
            (identical(other.branch, branch) || other.branch == branch) &&
            (identical(other.limit, limit) || other.limit == limit) &&
            (identical(other.offset, offset) || other.offset == offset) &&
            (identical(other.recentShipmentWeeks, recentShipmentWeeks) ||
                other.recentShipmentWeeks == recentShipmentWeeks) &&
            (identical(other.overDeadline, overDeadline) ||
                other.overDeadline == overDeadline) &&
            (identical(other.finished, finished) ||
                other.finished == finished) &&
            (identical(other.archiveInYear, archiveInYear) ||
                other.archiveInYear == archiveInYear) &&
            (identical(other.createdAtSort, createdAtSort) ||
                other.createdAtSort == createdAtSort) &&
            (identical(other.projection, projection) ||
                other.projection == projection) &&
            (identical(other.newBool, newBool) || other.newBool == newBool) &&
            (identical(other.searchClient, searchClient) ||
                other.searchClient == searchClient) &&
            (identical(other.searchEquipment, searchEquipment) ||
                other.searchEquipment == searchEquipment));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        searchField,
        mainID,
        componentID,
        repairNumber,
        const DeepCollectionEquality().hash(_mainIDArr),
        orderName,
        orderEquipment,
        orderClientName,
        area,
        branch,
        limit,
        offset,
        recentShipmentWeeks,
        overDeadline,
        finished,
        archiveInYear,
        createdAtSort,
        projection,
        newBool,
        searchClient,
        searchEquipment
      ]);

  /// Create a copy of SearchModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SearchModelImplCopyWith<_$SearchModelImpl> get copyWith =>
      __$$SearchModelImplCopyWithImpl<_$SearchModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SearchModelImplToJson(
      this,
    );
  }
}

abstract class _SearchModel extends SearchModel {
  const factory _SearchModel(
      {final String? searchField,
      final String? mainID,
      final String? componentID,
      final String? repairNumber,
      final List<String>? mainIDArr,
      final String? orderName,
      final String? orderEquipment,
      final String? orderClientName,
      final int? area,
      final int? branch,
      final int? limit,
      final int? offset,
      final int? recentShipmentWeeks,
      final bool? overDeadline,
      final bool? finished,
      final bool? archiveInYear,
      final bool? createdAtSort,
      final String? projection,
      @JsonKey(name: "new") final bool? newBool,
      final String? searchClient,
      final String? searchEquipment}) = _$SearchModelImpl;
  const _SearchModel._() : super._();

  factory _SearchModel.fromJson(Map<String, dynamic> json) =
      _$SearchModelImpl.fromJson;

  @override
  String? get searchField;
  @override
  String? get mainID;
  @override
  String? get componentID;
  @override
  String? get repairNumber;
  @override
  List<String>? get mainIDArr;
  @override
  String? get orderName;
  @override
  String? get orderEquipment;
  @override
  String? get orderClientName;
  @override
  int? get area;
  @override
  int? get branch;
  @override
  int? get limit;
  @override
  int? get offset;
  @override
  int? get recentShipmentWeeks;
  @override
  bool? get overDeadline;
  @override
  bool? get finished;
  @override
  bool? get archiveInYear;
  @override
  bool? get createdAtSort;
  @override
  String? get projection;
  @override
  @JsonKey(name: "new")
  bool? get newBool;
  @override
  String? get searchClient;
  @override
  String? get searchEquipment;

  /// Create a copy of SearchModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SearchModelImplCopyWith<_$SearchModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
