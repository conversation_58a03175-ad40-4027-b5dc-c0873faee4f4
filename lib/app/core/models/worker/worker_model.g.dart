// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'worker_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$WorkerModelImpl _$$WorkerModelImplFromJson(Map<String, dynamic> json) =>
    _$WorkerModelImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      areas:
          (json['areas'] as List<dynamic>?)?.map((e) => e as String).toList(),
      branch: json['branch'] as String?,
      statuses: (json['statuses'] as List<dynamic>?)
          ?.map((e) => WorkerStatusModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$WorkerModelImplToJson(_$WorkerModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      if (instance.areas case final value?) 'areas': value,
      if (instance.branch case final value?) 'branch': value,
      if (instance.statuses?.map((e) => e.toJson()).toList() case final value?)
        'statuses': value,
    };

_$WorkerStatusModelImpl _$$WorkerStatusModelImplFromJson(
        Map<String, dynamic> json) =>
    _$WorkerStatusModelImpl(
      status: $enumDecode(_$HistoryStatusTypeEnumMap, json['status']),
      createdAt: (json['createdAt'] as num?)?.toDouble(),
      updatedAt: (json['updatedAt'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$WorkerStatusModelImplToJson(
        _$WorkerStatusModelImpl instance) =>
    <String, dynamic>{
      'status': _$HistoryStatusTypeEnumMap[instance.status]!,
      if (instance.createdAt case final value?) 'createdAt': value,
      if (instance.updatedAt case final value?) 'updatedAt': value,
    };

const _$HistoryStatusTypeEnumMap = {
  HistoryStatusType.inwork: 'inwork',
  HistoryStatusType.finish: 'finish',
  HistoryStatusType.pause: 'pause',
  HistoryStatusType.info: 'info',
};

_$WorkerListModelImpl _$$WorkerListModelImplFromJson(
        Map<String, dynamic> json) =>
    _$WorkerListModelImpl(
      (json['data'] as List<dynamic>)
          .map((e) => WorkerModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$WorkerListModelImplToJson(
        _$WorkerListModelImpl instance) =>
    <String, dynamic>{
      'data': instance.data,
    };
