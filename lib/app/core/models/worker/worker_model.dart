import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter/foundation.dart';

import '../enums/history_status_enum.dart';

part 'worker_model.freezed.dart';
part 'worker_model.g.dart';

@freezed
class WorkerModel with _$WorkerModel {
  const WorkerModel._();
  @JsonSerializable(explicitToJson: true, includeIfNull: false)
  const factory WorkerModel({
    required String id,
    required String name,
    List<String>? areas,
    String? branch,
    List<WorkerStatusModel>? statuses,
  }) = _WorkerModel;

  factory WorkerModel.fromJson(Map<String, Object?> json) =>
      _$WorkerModelFromJson(json);
}

@freezed
class WorkerStatusModel with _$WorkerStatusModel {
  const WorkerStatusModel._();
  @JsonSerializable(explicitToJson: true, includeIfNull: false)
  const factory WorkerStatusModel({
    required HistoryStatusType status,
    double? createdAt,
    double? updatedAt,
  }) = _WorkerStatusModel;

  factory WorkerStatusModel.fromJson(Map<String, Object?> json) =>
      _$WorkerStatusModelFromJson(json);
}

@freezed
class WorkerListModel with _$WorkerListModel {
  factory WorkerListModel(List<WorkerModel> data) = _WorkerListModel;

  factory WorkerListModel.fromJson(Map<String, dynamic> json) =>
      _$WorkerListModelFromJson(json);
}
