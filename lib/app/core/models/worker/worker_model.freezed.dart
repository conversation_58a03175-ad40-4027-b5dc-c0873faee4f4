// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'worker_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

WorkerModel _$WorkerModelFromJson(Map<String, dynamic> json) {
  return _WorkerModel.fromJson(json);
}

/// @nodoc
mixin _$WorkerModel {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  List<String>? get areas => throw _privateConstructorUsedError;
  String? get branch => throw _privateConstructorUsedError;
  List<WorkerStatusModel>? get statuses => throw _privateConstructorUsedError;

  /// Serializes this WorkerModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of WorkerModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WorkerModelCopyWith<WorkerModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WorkerModelCopyWith<$Res> {
  factory $WorkerModelCopyWith(
          WorkerModel value, $Res Function(WorkerModel) then) =
      _$WorkerModelCopyWithImpl<$Res, WorkerModel>;
  @useResult
  $Res call(
      {String id,
      String name,
      List<String>? areas,
      String? branch,
      List<WorkerStatusModel>? statuses});
}

/// @nodoc
class _$WorkerModelCopyWithImpl<$Res, $Val extends WorkerModel>
    implements $WorkerModelCopyWith<$Res> {
  _$WorkerModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WorkerModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? areas = freezed,
    Object? branch = freezed,
    Object? statuses = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      areas: freezed == areas
          ? _value.areas
          : areas // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      branch: freezed == branch
          ? _value.branch
          : branch // ignore: cast_nullable_to_non_nullable
              as String?,
      statuses: freezed == statuses
          ? _value.statuses
          : statuses // ignore: cast_nullable_to_non_nullable
              as List<WorkerStatusModel>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WorkerModelImplCopyWith<$Res>
    implements $WorkerModelCopyWith<$Res> {
  factory _$$WorkerModelImplCopyWith(
          _$WorkerModelImpl value, $Res Function(_$WorkerModelImpl) then) =
      __$$WorkerModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String name,
      List<String>? areas,
      String? branch,
      List<WorkerStatusModel>? statuses});
}

/// @nodoc
class __$$WorkerModelImplCopyWithImpl<$Res>
    extends _$WorkerModelCopyWithImpl<$Res, _$WorkerModelImpl>
    implements _$$WorkerModelImplCopyWith<$Res> {
  __$$WorkerModelImplCopyWithImpl(
      _$WorkerModelImpl _value, $Res Function(_$WorkerModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of WorkerModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? areas = freezed,
    Object? branch = freezed,
    Object? statuses = freezed,
  }) {
    return _then(_$WorkerModelImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      areas: freezed == areas
          ? _value._areas
          : areas // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      branch: freezed == branch
          ? _value.branch
          : branch // ignore: cast_nullable_to_non_nullable
              as String?,
      statuses: freezed == statuses
          ? _value._statuses
          : statuses // ignore: cast_nullable_to_non_nullable
              as List<WorkerStatusModel>?,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class _$WorkerModelImpl extends _WorkerModel with DiagnosticableTreeMixin {
  const _$WorkerModelImpl(
      {required this.id,
      required this.name,
      final List<String>? areas,
      this.branch,
      final List<WorkerStatusModel>? statuses})
      : _areas = areas,
        _statuses = statuses,
        super._();

  factory _$WorkerModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$WorkerModelImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  final List<String>? _areas;
  @override
  List<String>? get areas {
    final value = _areas;
    if (value == null) return null;
    if (_areas is EqualUnmodifiableListView) return _areas;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? branch;
  final List<WorkerStatusModel>? _statuses;
  @override
  List<WorkerStatusModel>? get statuses {
    final value = _statuses;
    if (value == null) return null;
    if (_statuses is EqualUnmodifiableListView) return _statuses;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'WorkerModel(id: $id, name: $name, areas: $areas, branch: $branch, statuses: $statuses)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'WorkerModel'))
      ..add(DiagnosticsProperty('id', id))
      ..add(DiagnosticsProperty('name', name))
      ..add(DiagnosticsProperty('areas', areas))
      ..add(DiagnosticsProperty('branch', branch))
      ..add(DiagnosticsProperty('statuses', statuses));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WorkerModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            const DeepCollectionEquality().equals(other._areas, _areas) &&
            (identical(other.branch, branch) || other.branch == branch) &&
            const DeepCollectionEquality().equals(other._statuses, _statuses));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      const DeepCollectionEquality().hash(_areas),
      branch,
      const DeepCollectionEquality().hash(_statuses));

  /// Create a copy of WorkerModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WorkerModelImplCopyWith<_$WorkerModelImpl> get copyWith =>
      __$$WorkerModelImplCopyWithImpl<_$WorkerModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WorkerModelImplToJson(
      this,
    );
  }
}

abstract class _WorkerModel extends WorkerModel {
  const factory _WorkerModel(
      {required final String id,
      required final String name,
      final List<String>? areas,
      final String? branch,
      final List<WorkerStatusModel>? statuses}) = _$WorkerModelImpl;
  const _WorkerModel._() : super._();

  factory _WorkerModel.fromJson(Map<String, dynamic> json) =
      _$WorkerModelImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  List<String>? get areas;
  @override
  String? get branch;
  @override
  List<WorkerStatusModel>? get statuses;

  /// Create a copy of WorkerModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WorkerModelImplCopyWith<_$WorkerModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

WorkerStatusModel _$WorkerStatusModelFromJson(Map<String, dynamic> json) {
  return _WorkerStatusModel.fromJson(json);
}

/// @nodoc
mixin _$WorkerStatusModel {
  HistoryStatusType<dynamic> get status => throw _privateConstructorUsedError;
  double? get createdAt => throw _privateConstructorUsedError;
  double? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this WorkerStatusModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of WorkerStatusModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WorkerStatusModelCopyWith<WorkerStatusModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WorkerStatusModelCopyWith<$Res> {
  factory $WorkerStatusModelCopyWith(
          WorkerStatusModel value, $Res Function(WorkerStatusModel) then) =
      _$WorkerStatusModelCopyWithImpl<$Res, WorkerStatusModel>;
  @useResult
  $Res call(
      {HistoryStatusType<dynamic> status,
      double? createdAt,
      double? updatedAt});
}

/// @nodoc
class _$WorkerStatusModelCopyWithImpl<$Res, $Val extends WorkerStatusModel>
    implements $WorkerStatusModelCopyWith<$Res> {
  _$WorkerStatusModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WorkerStatusModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_value.copyWith(
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as HistoryStatusType<dynamic>,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as double?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WorkerStatusModelImplCopyWith<$Res>
    implements $WorkerStatusModelCopyWith<$Res> {
  factory _$$WorkerStatusModelImplCopyWith(_$WorkerStatusModelImpl value,
          $Res Function(_$WorkerStatusModelImpl) then) =
      __$$WorkerStatusModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {HistoryStatusType<dynamic> status,
      double? createdAt,
      double? updatedAt});
}

/// @nodoc
class __$$WorkerStatusModelImplCopyWithImpl<$Res>
    extends _$WorkerStatusModelCopyWithImpl<$Res, _$WorkerStatusModelImpl>
    implements _$$WorkerStatusModelImplCopyWith<$Res> {
  __$$WorkerStatusModelImplCopyWithImpl(_$WorkerStatusModelImpl _value,
      $Res Function(_$WorkerStatusModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of WorkerStatusModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_$WorkerStatusModelImpl(
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as HistoryStatusType<dynamic>,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as double?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class _$WorkerStatusModelImpl extends _WorkerStatusModel
    with DiagnosticableTreeMixin {
  const _$WorkerStatusModelImpl(
      {required this.status, this.createdAt, this.updatedAt})
      : super._();

  factory _$WorkerStatusModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$WorkerStatusModelImplFromJson(json);

  @override
  final HistoryStatusType<dynamic> status;
  @override
  final double? createdAt;
  @override
  final double? updatedAt;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'WorkerStatusModel(status: $status, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'WorkerStatusModel'))
      ..add(DiagnosticsProperty('status', status))
      ..add(DiagnosticsProperty('createdAt', createdAt))
      ..add(DiagnosticsProperty('updatedAt', updatedAt));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WorkerStatusModelImpl &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, status, createdAt, updatedAt);

  /// Create a copy of WorkerStatusModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WorkerStatusModelImplCopyWith<_$WorkerStatusModelImpl> get copyWith =>
      __$$WorkerStatusModelImplCopyWithImpl<_$WorkerStatusModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WorkerStatusModelImplToJson(
      this,
    );
  }
}

abstract class _WorkerStatusModel extends WorkerStatusModel {
  const factory _WorkerStatusModel(
      {required final HistoryStatusType<dynamic> status,
      final double? createdAt,
      final double? updatedAt}) = _$WorkerStatusModelImpl;
  const _WorkerStatusModel._() : super._();

  factory _WorkerStatusModel.fromJson(Map<String, dynamic> json) =
      _$WorkerStatusModelImpl.fromJson;

  @override
  HistoryStatusType<dynamic> get status;
  @override
  double? get createdAt;
  @override
  double? get updatedAt;

  /// Create a copy of WorkerStatusModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WorkerStatusModelImplCopyWith<_$WorkerStatusModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

WorkerListModel _$WorkerListModelFromJson(Map<String, dynamic> json) {
  return _WorkerListModel.fromJson(json);
}

/// @nodoc
mixin _$WorkerListModel {
  List<WorkerModel> get data => throw _privateConstructorUsedError;

  /// Serializes this WorkerListModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of WorkerListModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WorkerListModelCopyWith<WorkerListModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WorkerListModelCopyWith<$Res> {
  factory $WorkerListModelCopyWith(
          WorkerListModel value, $Res Function(WorkerListModel) then) =
      _$WorkerListModelCopyWithImpl<$Res, WorkerListModel>;
  @useResult
  $Res call({List<WorkerModel> data});
}

/// @nodoc
class _$WorkerListModelCopyWithImpl<$Res, $Val extends WorkerListModel>
    implements $WorkerListModelCopyWith<$Res> {
  _$WorkerListModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WorkerListModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
  }) {
    return _then(_value.copyWith(
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<WorkerModel>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WorkerListModelImplCopyWith<$Res>
    implements $WorkerListModelCopyWith<$Res> {
  factory _$$WorkerListModelImplCopyWith(_$WorkerListModelImpl value,
          $Res Function(_$WorkerListModelImpl) then) =
      __$$WorkerListModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<WorkerModel> data});
}

/// @nodoc
class __$$WorkerListModelImplCopyWithImpl<$Res>
    extends _$WorkerListModelCopyWithImpl<$Res, _$WorkerListModelImpl>
    implements _$$WorkerListModelImplCopyWith<$Res> {
  __$$WorkerListModelImplCopyWithImpl(
      _$WorkerListModelImpl _value, $Res Function(_$WorkerListModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of WorkerListModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
  }) {
    return _then(_$WorkerListModelImpl(
      null == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as List<WorkerModel>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$WorkerListModelImpl
    with DiagnosticableTreeMixin
    implements _WorkerListModel {
  _$WorkerListModelImpl(final List<WorkerModel> data) : _data = data;

  factory _$WorkerListModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$WorkerListModelImplFromJson(json);

  final List<WorkerModel> _data;
  @override
  List<WorkerModel> get data {
    if (_data is EqualUnmodifiableListView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_data);
  }

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'WorkerListModel(data: $data)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'WorkerListModel'))
      ..add(DiagnosticsProperty('data', data));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WorkerListModelImpl &&
            const DeepCollectionEquality().equals(other._data, _data));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_data));

  /// Create a copy of WorkerListModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WorkerListModelImplCopyWith<_$WorkerListModelImpl> get copyWith =>
      __$$WorkerListModelImplCopyWithImpl<_$WorkerListModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WorkerListModelImplToJson(
      this,
    );
  }
}

abstract class _WorkerListModel implements WorkerListModel {
  factory _WorkerListModel(final List<WorkerModel> data) =
      _$WorkerListModelImpl;

  factory _WorkerListModel.fromJson(Map<String, dynamic> json) =
      _$WorkerListModelImpl.fromJson;

  @override
  List<WorkerModel> get data;

  /// Create a copy of WorkerListModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WorkerListModelImplCopyWith<_$WorkerListModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
