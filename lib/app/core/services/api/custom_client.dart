import 'dart:convert';

import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:http/http.dart' as http;
import 'package:uer_flutter/app/core/services/settings_data_manager.dart';
import 'package:uer_flutter/app/helpers/logger.dart';

class CustomClient extends http.BaseClient {
  //final String userAgent;
  final http.Client _inner;

  CustomClient(this._inner);

  static const serverPrefix = "/api/v3/";
  // static const serverAddress = "http://localhost:3000";
  static const serverAddress = "https://uer.uer-ural.ru";

  // TODO: сделать взятие адреса и префикса из конфига .env

  // static const serverAddress = "http://localhost:3000"; // debug local server

  @override
  Future<http.StreamedResponse> send(http.BaseRequest request) async {
    // use for multipart
    //request.headers['user-agent'] = userAgent;
    //print("CustomClient: send method");

    var headers = addSecretKeyToHeader(request.headers);
    final login = await SettingsDataManager.shared.getUserLogin();
    final password = await SettingsDataManager.shared.getUserPassword();

    if (login?.isNotEmpty == true && password?.isNotEmpty == true) {
      headers = addAuthDataToHeader(login!, password!, headers);
    }

    //request.headers.addAll(headers);

    return _inner.send(request);
  }

  @override
  Future<http.Response> post(
    Uri url, {
    Map<String, String>? headers,
    Object? body,
    Encoding? encoding,
  }) async {
    //print("CustomClient: post method: $url");

    headers = addSecretKeyToHeader(headers ?? <String, String>{});

    final login = await SettingsDataManager.shared.getUserLogin();
    final password = await SettingsDataManager.shared.getUserPassword();

    if (login?.isNotEmpty == true && password?.isNotEmpty == true) {
      headers = addAuthDataToHeader(login!, password!, headers);
    }

    headers["Content-Type"] = 'application/json';

    String? encode;

    if (body != null) {
      encode = jsonEncode(body);
    }

    // input body
    logger.t('BODY:\n$encode');

    // output
    final data = _inner.post(
      url,
      headers: headers,
      body: encode,
      encoding: encoding,
    );
    prettyPrintJson((await data).body);
    logger.t('RESPONSE END');

    return data;
  }

  Map<String, String> addSecretKeyToHeader(Map<String, String> headers) {
    headers["secretKey"] = dotenv.env['SECRET_API_KEY']!;
    return headers;
  }

  Map<String, String> addAuthDataToHeader(
      String login, String password, Map<String, String> headers) {
    var credentials = "$login:$password";
    String encoded = base64.encode(utf8.encode(credentials));
    headers["auth"] = encoded;
    // headers["Accept"] = 'application/json';
    return headers;
  }

  void prettyPrintJson(String input) {
    const JsonDecoder decoder = JsonDecoder();
    const JsonEncoder encoder = JsonEncoder.withIndent('  ');

    try {
      String str = '';

      var object = decoder.convert(input);
      var prettyString = encoder.convert(object);
      prettyString.split('\n').forEach((element) => str += '$element\n');
      logger.t('RESPONSE:\n$str');
    } catch (e) {
      print('Error pretty printing JSON: $e');
      print(input); // Print original if parsing fails
    }
  }
}
