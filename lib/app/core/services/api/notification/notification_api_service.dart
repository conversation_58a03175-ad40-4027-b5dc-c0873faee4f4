import 'dart:convert';
import 'package:http/http.dart' as http;
import '../../../models/notification/notification_model.dart';
import 'notification_api_methods.dart';

class NotificationApi {
  final http.Client _client;

  NotificationApi(this._client);

  Future<List<NotificationModel>> getNotifications(
    String login,
    int limit,
    int offset,
  ) async {
    var url = NotificationAPIMethod.notificationsGetNew.url;
    Map<String, dynamic> body = {
      "login": login,
      "limit": limit,
      "offset": offset
    };

    var response = await _client.post(url, body: body);

    final parsed = jsonDecode(response.body);
    final list = NotificationListModel.fromJson({"data": parsed});
    return list.data;
  }
}
