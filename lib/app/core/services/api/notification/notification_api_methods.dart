import '../custom_client.dart';

enum NotificationAPIMethod {
  notificationsGet, // old method uses in native iOS app
  notificationsGetNew,
}

extension NotificationAPIMethodExtension on NotificationAPIMethod {
  static const _serverPrefix = CustomClient.serverPrefix;
  static const _serverAddress = CustomClient.serverAddress;

  Uri get url {
    var method = "";

    switch (this) {
      case NotificationAPIMethod.notificationsGet:
        method = 'notifications/get';
        break;
      case NotificationAPIMethod.notificationsGetNew:
        method = 'notifications/getNew';
        break;
      default:
        break;
    }

    return Uri.parse('$_serverAddress$_serverPrefix$method');
  }
}
