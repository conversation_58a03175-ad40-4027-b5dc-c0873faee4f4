import '../custom_client.dart';

enum StatsAPIMethod {
  getOutAgeStats,
  getSSZTimeStats,
  getAverageTimeRepair,
  getAverageDowntimes,
  sumWorkHoursByDay,
  getTagsStats,
  getWorkStats,
  searchTasks,
}

extension StatsAPIMethodExtension on StatsAPIMethod {
  static const _serverPrefix = CustomClient.serverPrefix;
  static const _serverAddress = CustomClient.serverAddress;

  Uri get url {
    var method = "";

    switch (this) {
      case StatsAPIMethod.getOutAgeStats:
        method = 'stats/getOutAgeStats';
        break;
      case StatsAPIMethod.getSSZTimeStats:
        method = 'stats/getSSZTimeStats';
        break;
      case StatsAPIMethod.getAverageTimeRepair:
        method = 'stats/getAverageTimeRepair';
        break;
      case StatsAPIMethod.sumWorkHoursByDay:
        method = 'stats/sumWorkHoursByDay';
        break;
      case StatsAPIMethod.getTagsStats:
        method = 'stats/getTagsStats';
        break;
      case StatsAPIMethod.getAverageDowntimes:
        method = 'stats/getAverageOutageTime';
        break;
      case StatsAPIMethod.getWorkStats:
        method = 'stats/getTaskStatisticsByYear';
        break;
      case StatsAPIMethod.searchTasks:
        method = 'tasks/search';
        break;
      default:
        break;
    }

    return Uri.parse('$_serverAddress$_serverPrefix$method');
  }
}
