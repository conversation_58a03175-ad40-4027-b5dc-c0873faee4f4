import 'dart:convert';

import 'package:http/http.dart' as http;
import 'package:uer_flutter/app/core/models/Stats_outage/stats_sum_works_time_model.dart';
import 'package:uer_flutter/app/core/models/Stats_outage/tasks_stats_model.dart';
import 'package:uer_flutter/app/core/models/stats/tags_stats_model.dart';

import '../../../models/Stats_outage/stats_outage_model.dart';
import '../../../models/Stats_outage/stats_ssz_time_result_model.dart';
import 'stats_api_methods.dart';

class StatsApi {
  final http.Client _client;

  StatsApi(this._client);

  // Метод для получения статистики по простоям
  Future<List<StatsOutAgeModel>> getOutAgeStats(
      int? branch, bool archive, bool full) async {
    Map<String, Object> body = {"full": full, "archive": archive};

    if (branch != null) {
      body["branch"] = branch;
    }

    var url = StatsAPIMethod.getOutAgeStats.url;
    var response = await _client.post(url, body: body);

    if (response.statusCode == 200) {
      final parsed = jsonDecode(response.body);
      final list = StatsOutAgeListModel.fromJson({"data": parsed});
      return list.data;
    } else {
      return [];
    }
  }

  // Метод для получения статистики по одному простою
  Future<StatsOutAgeModel?> getOutAgeStatsOne(String? mainID, bool full) async {
    Map<String, Object> body = {
      "full": full,
    };

    if (mainID != null) {
      body["mainID"] = mainID;
    }

    var url = StatsAPIMethod.getOutAgeStats.url;
    var response = await _client.post(url, body: body);

    if (response.statusCode == 200 && (response.contentLength ?? 0) > 0) {
      final parsed = jsonDecode(response.body);
      final model = StatsOutAgeModel.fromJson(parsed);
      return model;
    } else {
      return null;
    }
  }

  // Метод для получения статистики по времени SSZ
  Future<List<StatsSSZTimeResultModel>> getSSZTimeStats(
      List<int> areaIds) async {
    Map<String, Object> body = {"areaIds": areaIds};

    var url = StatsAPIMethod.getSSZTimeStats.url;
    var response = await _client.post(url, body: body);

    if (response.statusCode == 200) {
      final parsed = jsonDecode(response.body);
      final list = StatsSSZTimeResultListModel.fromJson({"data": parsed});
      return list.data;
    } else {
      return [];
    }
  }

  // Метод для получения среднего времени ремонта
  Future<StatsSumWorkHoursByMonthModel?> getAverageTimeRepair({
    required int year,
    int? branchId,
  }) async {
    final Map<String, Object> body = {
      "year": year,
      if (branchId != null) "branch": branchId,
    };

    var url = StatsAPIMethod.getAverageTimeRepair.url;
    var response = await _client.post(url, body: body);

    if (response.statusCode == 200) {
      final parsed = jsonDecode(response.body);
      final data = StatsSumWorkHoursByMonthModel.fromJson(parsed);
      return data;
    } else {
      // print(response.body);
      return null;
    }
  }

  // Метод для получения средних простоев по месяцам
  Future<StatsAverageDowntimesByMonthModel?> getAverageDowntimes({
    required int year,
    int? branchId,
  }) async {
    final Map<String, Object> body = {
      "year": year,
      if (branchId != null) "branch": branchId,
    };

    var url = StatsAPIMethod.getAverageDowntimes.url;
    var response = await _client.post(url, body: body);

    if (response.statusCode == 200) {
      final parsed = jsonDecode(response.body);
      final data = StatsAverageDowntimesByMonthModel.fromJson(parsed);
      return data;
    } else {
      // print(response.body);
      return null;
    }
  }

  // Метод для получения статистики по задачам
  Future<List<FoundTaskModel>?> getWorkStats({
    required int year,
    required String taskId,
    int? power,
    int? turnovers,
  }) async {
    final Map<String, Object> body = {
      "year": year,
      "taskId": taskId,
      if (power != null) "power": power,
      if (turnovers != null) "turnovers": turnovers,
    };

    var url = StatsAPIMethod.getWorkStats.url;
    var response = await _client.post(url, body: body);

    if (response.statusCode == 200) {
      final parsed = jsonDecode(response.body);

      if (parsed is List) {
        final data = parsed
            .map<FoundTaskModel>((json) => FoundTaskModel.fromJson(json))
            .toList();
        return data;
      } else {
        print("Ожидался список, но получен: $parsed");
        return null;
      }
    } else {
      // print(response.body);
      return null;
    }
  }

  // Метод для поиска работ по названию
  Future<List<TaskStatsModel>?> searchTasks({
    required String query,
    int? branchId,
    int? year,
  }) async {
    final Map<String, Object> body = {
      "query": query,
      if (branchId != null) "branch": branchId,
      if (year != null) "year": year,
    };

    var url = StatsAPIMethod.searchTasks.url;
    var response = await _client.post(url, body: body);

    if (response.statusCode == 200) {
      final parsed = jsonDecode(response.body);

      if (parsed is List) {
        final data = parsed
            .map<TaskStatsModel>((json) => TaskStatsModel.fromJson(json))
            .toList();
        return data;
      } else {
        print("Ожидался список, но получен: $parsed");
        return null;
      }
    } else {
      // print(response.body);
      return null;
    }
  }

  // Метод для получения статистики по тегам
  Future<TagsStatsModel?> getTagsStats({GetTagsStatsModel? data}) async {
    final body = data?.toJson();

    var url = StatsAPIMethod.getTagsStats.url;
    var response = await _client.post(url, body: body);

    if (response.statusCode == 200) {
      final parsed = jsonDecode(response.body);
      final data = TagsStatsModel.fromJson(parsed);
      return data;
    } else {
      return null;
    }
  }
}
