import 'package:http/http.dart' as http;
import 'push_api_methods.dart';

class PushApi {
  final http.Client _client;

  PushApi(this._client);

  Future<bool> sendPush(
      String toLogin, String? message, String? fromLogin) async {
    var url = PushAPIMethod.pushSend.url;
    Map<String, dynamic> body = {"login": toLogin};

    if (message?.isNotEmpty == true) {
      body["message"] = message!;
    }

    if (fromLogin?.isNotEmpty == true) {
      body["from"] = fromLogin!;
    }

    var response = await _client.post(url, body: body);

    if (response.statusCode == 200) {
      return true;
    }

    return false;
  }
}
