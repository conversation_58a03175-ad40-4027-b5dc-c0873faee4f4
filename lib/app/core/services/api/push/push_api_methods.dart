import '../custom_client.dart';

enum PushAPIMethod {
  pushSend,
}

extension PushAPIMethodExtension on PushAPIMethod {
  static const _serverPrefix = CustomClient.serverPrefix;
  static const _serverAddress = CustomClient.serverAddress;

  Uri get url {
    var method = "";

    switch (this) {
      case PushAPIMethod.pushSend:
        method = 'pushes/send';
        break;
      default:
        break;
    }

    return Uri.parse('$_serverAddress$_serverPrefix$method');
  }
}
