import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:http/http.dart' as http;
import 'photo/photo_api_service.dart';
import 'stats/stats_api_service.dart';
import 'status/status_api_service.dart';
import 'tasks/task_api_service.dart';
import 'user/user_api_service.dart';
import 'area/area_api_service.dart';
import 'branch/branch_api_service.dart';
import 'custom_client.dart';
import 'items/item_api_service.dart';
import 'notification/notification_api_service.dart';
import 'push/push_api_service.dart';

var client = CustomClient(http.Client());

final statusRepositoryProvider = Provider<StatusApi>((_) => StatusApi(client));

final branchRepositoryProvider = Provider<BranchApi>((_) => BranchApi(client));

final areaRepositoryProvider = Provider<AreaApi>((_) => AreaApi(client));

final userRepositoryProvider = Provider<UserApi>((_) => UserApi(client));

final itemRepositoryProvider = Provider<ItemApi>((_) => ItemApi(client));

final photoRepositoryProvider = Provider<PhotoApi>((_) => PhotoApi(client));

final taskRepositoryProvider = Provider<TaskSSZApi>((_) => TaskSSZApi(client));

final pushRepositoryProvider = Provider<PushApi>((_) => PushApi(client));

final statsRepositoryProvider = Provider<StatsApi>((_) => StatsApi(client));

final notificationRepositoryProvider =
    Provider<NotificationApi>((_) => NotificationApi(client));
