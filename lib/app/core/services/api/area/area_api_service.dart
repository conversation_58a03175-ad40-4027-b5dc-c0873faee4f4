import 'dart:convert';
import 'package:http/http.dart' as http;
import '../../../models/area/area_model.dart';
import 'area_api_methods.dart';

class AreaApi {
  final http.Client _client;

  AreaApi(this._client);

  Future<List<AreaModel>> getAreas() async {
    var url = AreaAPIMethod.areasGet.url;
    var response = await _client.post(url);
    final parsed = jsonDecode(response.body);
    final list = AreaListModel.fromJson({"data": parsed});
    return list.data;
  }

  Future<bool> addArea(AreaModel area) async {
    var url = AreaAPIMethod.areaAdd.url;
    var body = {'area': area.toJson()};
    var response = await _client.post(url, body: body);

    if (response.statusCode == 200) {
      return true;
    } else {
      return false;
    }
  }

  Future<bool> editArea(AreaModel area) async {
    var url = AreaAPIMethod.areaEdit.url;
    var body = {'area': area.toJson()};
    var response = await _client.post(url, body: body);

    if (response.statusCode == 200) {
      return true;
    } else {
      return false;
    }
  }
}
