import '../custom_client.dart';

enum AreaAPIMethod {
  areaAdd,
  areaEdit,
  areasGet,
}

extension AreaAPIMethodExtension on AreaAPIMethod {
  static const _serverPrefix = CustomClient.serverPrefix;
  static const _serverAddress = CustomClient.serverAddress;

  Uri get url {
    var method = "";

    switch (this) {
      case AreaAPIMethod.areaAdd:
        method = 'areas/add';
        break;
      case AreaAPIMethod.areaEdit:
        method = 'areas/edit';
        break;
      case AreaAPIMethod.areasGet:
        method = 'areas/get';
        break;
      default:
        break;
    }

    return Uri.parse('$_serverAddress$_serverPrefix$method');
  }
}
