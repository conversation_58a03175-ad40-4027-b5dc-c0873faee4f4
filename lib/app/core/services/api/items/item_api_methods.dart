import '../custom_client.dart';

enum ItemAPIMethod {
  addNewHistory,
  changeStatusHistory,
  changeStatusWorkers,
  transferToArea,
  changeAccessArea,
  changeAssembled,
  itemEdit,
  itemsGet,
  itemStats,
  getReports,
  lastOrderSync,
  itemsMerge,
  update,
}

extension ItemAPIMethodExtension on ItemAPIMethod {
  static const _serverPrefix = CustomClient.serverPrefix;
  static const _serverAddress = CustomClient.serverAddress;

  Uri get url {
    var method = "";

    switch (this) {
      case ItemAPIMethod.addNewHistory:
        method = 'items/addNewHistory';
        break;
      case ItemAPIMethod.changeStatusHistory:
        method = 'items/changeStatusHistory';
        break;
      case ItemAPIMethod.changeStatusWorkers:
        method = 'items/changeStatusWorkers';
        break;
      case ItemAPIMethod.transferToArea:
        method = 'items/transferToArea';
        break;
      case ItemAPIMethod.changeAccessArea:
        method = 'items/changeAccessArea';
        break;
      case ItemAPIMethod.changeAssembled:
        method = 'items/changeAssembled';
        break;
      case ItemAPIMethod.itemEdit:
        method = 'items/edit';
        break;
      case ItemAPIMethod.itemsGet:
        method = 'items/get';
        break;
      case ItemAPIMethod.itemStats:
        method = 'items/stats';
        break;
      case ItemAPIMethod.getReports:
        method = 'items/getReports';
        break;
      case ItemAPIMethod.lastOrderSync:
        method = 'items/lastSync';
        break;
      case ItemAPIMethod.itemsMerge:
        method = 'items/mergeItems';
        break;
      case ItemAPIMethod.update:
        method = 'items/update';
        break;
      default:
        break;
    }

    return Uri.parse('$_serverAddress$_serverPrefix$method');
  }
}
