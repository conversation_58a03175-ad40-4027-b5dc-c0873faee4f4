import 'dart:convert';

import 'package:http/http.dart' as http;
import 'package:uer_flutter/app/core/models/stats/tags_stats_model.dart';

import '../../../models/component_history/component_history_model.dart';
import '../../../models/item/item_model.dart';
import '../../../models/report/report_model.dart';
import '../../../models/search/search_model.dart';
import '../../../models/stats/stats_model.dart';
import '../../../models/worker/worker_model.dart';
import 'item_api_methods.dart';

class ItemApi {
  final http.Client _client;

  ItemApi(this._client);

  Future<List<ItemModel>?> getItems(SearchModel search) async {
    var url = ItemAPIMethod.itemsGet.url;

    // print(search.toJson().toString());

    var body = {"search": search.toJson()};
    var response = await _client.post(url, body: body);

    if (response.statusCode == 200) {
      final parsed = jsonDecode(response.body);
      // print(parsed);
      final list = ItemListModel.fromJson({"data": parsed});
      // print(list);
      return list.data;
    } else {
      return null;
    }
  }

  Future<int?> lastOrderSync() async {
    var url = ItemAPIMethod.lastOrderSync.url;
    var response = await _client.post(url);
    return int.parse(response.body);
  }

  Future<ItemModel?> addNewHistory(
      ComponentHistoryModel history, String componentID) async {
    var url = ItemAPIMethod.addNewHistory.url;

    var body = {"componentID": componentID, "history": history.toJson()};
    var response = await _client.post(url, body: body);
    final parsed = jsonDecode(response.body);
    final item = ItemModel.fromJson(parsed);
    return item;
  }

  Future<ItemModel?> changeStatusHistory(ComponentStatusModel statusModel,
      String componentID, String identifier) async {
    var url = ItemAPIMethod.changeStatusHistory.url;

    var body = {
      "componentID": componentID,
      "identifier": identifier,
      "statusObj": statusModel.toJson()
    };
    var response = await _client.post(url, body: body);
    final parsed = jsonDecode(response.body);
    final item = ItemModel.fromJson(parsed);
    return item;
  }

  Future<ItemModel?> changeStatusWorkers(WorkerListModel workers,
      String componentID, String identifier, String owner) async {
    var url = ItemAPIMethod.changeStatusWorkers.url;

    var body = {
      "componentID": componentID,
      "identifier": identifier,
      "owner": owner,
      "workers": workers.toJson()["data"]
    };

    var response = await _client.post(url, body: body);

    final parsed = jsonDecode(response.body);
    final item = ItemModel.fromJson(parsed);
    return item;
  }

  Future<ItemModel?> transferToArea(ComponentHistoryModel history,
      String componentID, int transferArea) async {
    var url = ItemAPIMethod.transferToArea.url;

    var body = {
      "componentID": componentID,
      "history": history.toJson(),
      "transferArea": transferArea,
    };

    var response = await _client.post(url, body: body);
    final parsed = jsonDecode(response.body);
    final item = ItemModel.fromJson(parsed);
    return item;
  }

  Future<ItemModel?> changeAccessArea(
      String componentID, List<int> accessAreas) async {
    var url = ItemAPIMethod.changeAccessArea.url;

    var body = {
      "componentID": componentID,
      "accessAreas": accessAreas,
    };

    var response = await _client.post(url, body: body);
    final parsed = jsonDecode(response.body);
    final item = ItemModel.fromJson(parsed);
    return item;
  }

  Future<ItemModel?> changeAssembled(
      String mainID, bool assembled, ComponentHistoryModel history) async {
    var url = ItemAPIMethod.changeAssembled.url;

    var body = {
      "mainID": mainID,
      "assembled": assembled,
      "history": history.toJson()
    };

    var response = await _client.post(url, body: body);
    final parsed = jsonDecode(response.body);
    final item = ItemModel.fromJson(parsed);
    return item;
  }

  Future<bool> editItem(ItemModel item) async {
    var url = ItemAPIMethod.itemEdit.url;

    var body = {
      "item": item.toJson(),
    };

    var response = await _client.post(url, body: body);

    if (response.statusCode == 200) {
      return true;
    }

    return false;

    // final parsed = jsonDecode(response.body);
    // final newItem = ItemModel.fromJson(parsed);
    // return newItem;
  }

  Future updateTagItem(UpdateTagsInItemModel data) async {
    final url = ItemAPIMethod.itemEdit.url;

    final body = data.toJson();

    await _client.post(url, body: body);

    // if (response.statusCode == 200) {
    //   return true;
    // }

    // return false;
  }

  Future<StatsModel?> getStats(List<int> branchs, List<int> areas) async {
    var url = ItemAPIMethod.itemStats.url;

    var body = {"branches": branchs, "areas": areas};

    var response = await _client.post(url, body: body);
    final parsed = jsonDecode(response.body);
    final model = StatsModel.fromJson(parsed);
    return model;
  }

  Future<List<ReportModel>?> getReports(
      int? branch, bool archive, String? mainID) async {
    var url = ItemAPIMethod.getReports.url;

    Map<String, dynamic> body = {
      "archive": archive,
    };

    if ((branch ?? 0) > 0) {
      body["branch"] = branch!;
    }

    if (mainID?.isNotEmpty == true) {
      body["mainID"] = mainID!;
    }

    var response = await _client.post(url, body: body);
    final parsed = jsonDecode(response.body);
    final model = ReportListModel.fromJson({"data": parsed});
    return model.data;
  }

  Future<ItemModel?> mergeItems(String mainItemID, String altItemID) async {
    var url = ItemAPIMethod.itemsMerge.url;

    var body = {"mainID": mainItemID, "altMainID": altItemID};

    var response = await _client.post(url, body: body);
    final parsed = jsonDecode(response.body);
    final item = ItemModel.fromJson(parsed);
    return item;
  }
}
