import '../custom_client.dart';

enum StatusAPIMethod {
  // Job in DB (old name)
  statusAdd,
  statusEdit,
  statusesGet,
}

extension StatusAPIMethodExtension on StatusAPIMethod {
  static const _serverPrefix = CustomClient.serverPrefix;
  static const _serverAddress = CustomClient.serverAddress;

  Uri get url {
    var method = "";

    switch (this) {
      case StatusAPIMethod.statusAdd:
        method = 'jobs/add';
        break;
      case StatusAPIMethod.statusEdit:
        method = 'jobs/edit';
        break;
      case StatusAPIMethod.statusesGet:
        method = 'jobs/get';
        break;
      default:
        break;
    }

    return Uri.parse('$_serverAddress$_serverPrefix$method');
  }
}
