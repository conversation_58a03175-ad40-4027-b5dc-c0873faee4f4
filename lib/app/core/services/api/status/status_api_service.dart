import 'dart:convert';
import 'package:http/http.dart' as http;
import '../../../../helpers/logger.dart';
import '../../../models/status/status_model.dart';
import 'status_api_methods.dart';

class StatusApi {
  final http.Client _client;

  StatusApi(this._client);

  Future<List<StatusModel>> getStatuses() async {
    var url = StatusAPIMethod.statusesGet.url;
    var response = await _client.post(url);
    final parsed = jsonDecode(response.body);
    final list = StatusListModel.fromJson({"data": parsed});
    return list.data;
  }

  Future<bool> addStatus(String name, bool outage) async {
    var url = StatusAPIMethod.statusAdd.url;
    var body = {'name': name, 'outage': outage};
    var response = await _client.post(url, body: body);
    final parsed = jsonDecode(response.body);
    final status = StatusModel.fromJson(parsed);

    logger.d(status);

    if (response.statusCode == 200) {
      return true;
    } else {
      return false;
    }
  }

  Future<bool> editStatus(StatusModel status) async {
    var url = StatusAPIMethod.statusEdit.url;
    var body = {'job': status.toJson()};
    var response = await _client.post(url, body: body);

    if (response.statusCode == 200) {
      return true;
    } else {
      return false;
    }
  }
}
