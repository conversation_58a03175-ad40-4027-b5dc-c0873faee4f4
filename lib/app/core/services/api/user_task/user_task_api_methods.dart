import '../custom_client.dart';

enum UserTaskAPIMethod {
  userTaskGet,
  userTaskGetCurrent,
  userTaskCreate,
  userTaskUpdateStatus
}

extension UserTaskAPIMethodExtension on UserTask<PERSON>IMethod {
  static const _serverPrefix = CustomClient.serverPrefix;
  static const _serverAddress = CustomClient.serverAddress;

  Uri get url {
    var method = "";

    switch (this) {
      case UserTaskAPIMethod.userTaskGet:
        method = 'user_tasks/get';
        break;
      case UserTaskAPIMethod.userTaskGetCurrent:
        method = 'user_tasks/getCurrent';
        break;
      case UserTaskAPIMethod.userTaskCreate:
        method = 'user_tasks/create';
        break;
      case UserTaskAPIMethod.userTaskUpdateStatus:
        method = 'user_tasks/updateStatus';
        break;
      default:
        break;
    }

    return Uri.parse('$_serverAddress$_serverPrefix$method');
  }
}
