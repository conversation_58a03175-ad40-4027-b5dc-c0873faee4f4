import 'dart:convert';
import 'package:http/http.dart' as http;
import '../../../models/user_task/result_user_task_model.dart';
import '../../../models/user_task/user_task_model.dart';
import '../../../models/user_task/user_task_status_model.dart';
import 'user_task_api_methods.dart';

class UserTaskApi {
  final http.Client _client;

  UserTaskApi(this._client);

  Future<ResultUserTaskModel> getUserTasks(String login, double? cursor) async {
    var url = UserTaskAPIMethod.userTaskGet.url;

    Map<String, dynamic> body = {
      "login": login,
      "limit": 20,
    };

    if ((cursor ?? 0) > 0) {
      body["cursor"] = cursor;
    }

    var response = await _client.post(url);

    final parsed = jsonDecode(response.body);
    final result = ResultUserTaskModel.fromJson(parsed);
    return result;
  }

  Future<List<UserTaskModel>> getCurrentUserTasks(int area, int branch) async {
    var url = UserTaskAPIMethod.userTaskGetCurrent.url;
    Map<String, dynamic> body = {
      "area": area,
      "branch": branch,
    };
    var response = await _client.post(url, body: body);
    final parsed = jsonDecode(response.body);
    final userTasks = UserTaskListModel.fromJson({"data": parsed});
    return userTasks.data;
  }

  Future<UserTaskModel> createUserTask(
      String login, int area, int branch, String owner, String nameArea) async {
    var url = UserTaskAPIMethod.userTaskCreate.url;
    Map<String, dynamic> body = {
      "login": login,
      "area": area,
      "branch": branch,
      "owner": owner,
      "nameArea": nameArea
    };
    var response = await _client.post(url, body: body);
    final parsed = jsonDecode(response.body);
    final userTask = UserTaskModel.fromJson(parsed);
    return userTask;
  }

  Future<UserTaskModel> updateUserTask(
      String id, UserTaskStatusModel status) async {
    var url = UserTaskAPIMethod.userTaskUpdateStatus.url;
    Map<String, dynamic> body = {
      "id": id,
      "status": status.toJson(),
    };
    var response = await _client.post(url, body: body);
    final parsed = jsonDecode(response.body);
    final userTask = UserTaskModel.fromJson(parsed);
    return userTask;
  }
}
