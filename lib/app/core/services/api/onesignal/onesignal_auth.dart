import 'package:onesignal_flutter/onesignal_flutter.dart';
import '../../../../helpers/platform_check.dart';

import '../../../../helpers/web/onesignal_web_wrapper.dart';

class OneSignalAuth {
  void loginToOneSignal(String login) {
    if (isAndroidOriOS() == true) {
      // You will supply the external id to the OneSignal SDK
      OneSignal.login(login);
    } else if (isWeb() == true) {
      OnesignalWebWrapper.instance?.setExternalUserId(uid: login);
    }
  }

  void removeLoginOneSignal() {
    if (isAndroidOriOS() == true) {
      OneSignal.logout();
    } else if (isWeb() == true) {
      OnesignalWebWrapper.instance?.disablePush();
    }
  }
}
