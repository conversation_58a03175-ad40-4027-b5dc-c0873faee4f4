import '../custom_client.dart';

enum PhotoAPIMethod {
  photoUpload,
  photoEditComment,
}

extension PhotoAPIMethodExtension on PhotoAPIMethod {
  static const _serverPrefix = CustomClient.serverPrefix;
  static const _serverAddress = CustomClient.serverAddress;

  Uri get url {
    var method = "";

    switch (this) {
      case PhotoAPIMethod.photoUpload:
        method = 'photo/upload';
        break;
      case PhotoAPIMethod.photoEditComment:
        method = 'photo/editComment';
        break;
      default:
        break;
    }

    return Uri.parse('$_serverAddress$_serverPrefix$method');
  }
}
