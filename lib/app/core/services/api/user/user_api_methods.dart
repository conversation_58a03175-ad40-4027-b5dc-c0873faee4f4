import '../custom_client.dart';

enum UserAPIMethod {
  userGet,
  usersGet,
  userCreate,
  userEdit,
  userSubscribeChange,
  userSubscribeItemChange,
  userSubscribeOutageChange,
  userSubscribeSSZChange,
  userSubscribeZPRChange,
  authentication,
}

extension UserAPIMethodExtension on UserAPIMethod {
  static const _serverPrefix = CustomClient.serverPrefix;
  static const _serverAddress = CustomClient.serverAddress;

  Uri get url {
    var method = "";

    switch (this) {
      case UserAPIMethod.userGet:
        method = 'users/getOne';
        break;
      case UserAPIMethod.usersGet:
        method = 'users/get';
        break;
      case UserAPIMethod.userCreate:
        method = 'users/create';
        break;
      case UserAPIMethod.userEdit:
        method = 'users/edit';
        break;
      case UserAPIMethod.userSubscribeChange:
        method = 'users/subscribeChange';
        break;
      case UserAPIMethod.userSubscribeItemChange:
        method = 'users/subscribeItemChange';
        break;
      case UserAPIMethod.userSubscribeOutageChange:
        method = 'users/subscribeOutageChange';
        break;
      case UserAPIMethod.userSubscribeSSZChange:
        method = 'users/subscribeSSZChange';
        break;
      case UserAPIMethod.userSubscribeZPRChange:
        method = 'users/subscribeZPRChange';
        break;
      case UserAPIMethod.authentication:
        method = 'auth';
        break;
      default:
        break;
    }

    return Uri.parse('$_serverAddress$_serverPrefix$method');
  }
}
