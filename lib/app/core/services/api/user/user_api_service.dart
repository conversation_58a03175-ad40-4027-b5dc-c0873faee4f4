import 'dart:convert';

import 'package:http/http.dart' as http;
import 'package:uer_flutter/app/core/services/api/http_ext.dart';
import '../../../../helpers/logger.dart';
import 'user_api_methods.dart';

import '../../../models/user/user_model.dart';

class UserApi {
  final http.Client _client;

  UserApi(this._client);

  Future<UserModel> getUser(String login) async {
    var url = UserAPIMethod.userGet.url;
    var body = {"login": login};
    var response = await _client.post(url, body: body);

    if (response.ok) {
      final parsed = jsonDecode(response.body);
      final user = UserModel.fromJson(parsed);
      return user;
    } else {
      throw response.body;
    }
  }

  Future<List<UserModel>> getUsers(List<String>? logins) async {
    var url = UserAPIMethod.usersGet.url;

    Map<String, dynamic>? body;

    if (logins != null) {
      body = {"logins": logins};
    }

    var response = await _client.post(url, body: body);

    if (response.ok) {
      final parsed = jsonDecode(response.body);
      final list = UserListModel.fromJson({"data": parsed});
      return list.data;
    } else {
      throw response.body;
    }
  }

  Future<UserModel> auth(String login, String password) async {
    var url = UserAPIMethod.authentication.url;
    var body = {"login": login, "password": password};
    var response = await _client.post(url, body: body);

    if (response.ok) {
      final parsed = jsonDecode(response.body);
      final user = UserModel.fromJson(parsed);
      return user;
    } else {
      throw response.body;
    }
  }

  Future<bool> createUser(UserModel user) async {
    var url = UserAPIMethod.userCreate.url;
    var body = {"user": user.toJson()};
    var response = await _client.post(url, body: body);

    logger.d(response.body);
    // final parsed = jsonDecode(response.body);
    // final user = UserModel.fromJson(parsed);
    return true;
  }

  Future<bool> editUser(UserModel user) async {
    var url = UserAPIMethod.userEdit.url;
    var body = {"user": user.toJson()};
    var response = await _client.post(url, body: body);
    logger.d(response.body);
    // final parsed = jsonDecode(response.body);
    // final user = UserModel.fromJson(parsed);
    return true;
  }

  Future<bool> subscribeChange(
      String login, List<String> addID, List<String> delID) async {
    var url = UserAPIMethod.userSubscribeChange.url;
    var body = {"login": login, "addIds": addID, "delIds": delID};
    var response = await _client.post(url, body: body);
    logger.d(response.body);

    if (response.statusCode == 200) {
      return true;
    }

    return false;
  }

  Future<bool> subscribeItemChange(
      String login, String mainID, bool subscribe) async {
    var url = UserAPIMethod.userSubscribeItemChange.url;
    var body = {"login": login, "mainID": mainID, "subscribe": subscribe};
    var response = await _client.post(url, body: body);
    logger.d(response.body);

    if (response.statusCode == 200) {
      return true;
    }

    return false;
  }

  Future<bool> subscribeOutageChange(
      String login, String idAndDays, bool subscribe) async {
    var url = UserAPIMethod.userSubscribeOutageChange.url;
    var body = {"login": login, "idAndDays": idAndDays, "subscribe": subscribe};
    var response = await _client.post(url, body: body);
    logger.d(response.body);

    if (response.statusCode == 200) {
      return true;
    }

    return false;
  }

  Future<bool> subscribeSSZChange(
      String login, List<String> addID, List<String> delID) async {
    var url = UserAPIMethod.userSubscribeSSZChange.url;
    var body = {"login": login, "addIds": addID, "delIds": delID};
    var response = await _client.post(url, body: body);
    logger.d(response.body);

    if (response.statusCode == 200) {
      return true;
    }

    return false;
  }

  Future<bool> subscribeZPRChange(String login, bool value) async {
    var url = UserAPIMethod.userSubscribeZPRChange.url;
    var body = {"login": login, "value": value};
    var response = await _client.post(url, body: body);
    logger.d(response.body);

    if (response.statusCode == 200) {
      return true;
    }

    return false;
  }
}
