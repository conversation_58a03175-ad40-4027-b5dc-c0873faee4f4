import 'dart:convert';
import 'package:http/http.dart' as http;
import '../../../models/task_ssz/task_ssz_model.dart';
import '../../../models/task_ssz_info/task_ssz_info_model.dart';
import 'task_api_methods.dart';

class TaskSSZApi {
  final http.Client _client;

  TaskSSZApi(this._client);

  Future<List<TaskSSZModel>> getTasks(
    String? mainID,
    String? idArea,
    String? idBranch,
    String? dateStr,
  ) async {
    var url = TaskSSZAPIMethod.tasksGet.url;

    Map<String, dynamic> body = {};

    if (mainID?.isNotEmpty == true) {
      body["mainID"] = mainID!;
    }

    if (idArea?.isNotEmpty == true) {
      body["idArea"] = idArea!;
    }

    if (idBranch?.isNotEmpty == true) {
      body["idBranch"] = idBranch!;
    }

    if (dateStr?.isNotEmpty == true) {
      body["dateStr"] = dateStr!;
    }

    var response = await _client.post(url, body: body);

    final parsed = jsonDecode(response.body);
    final list = TaskSSZListModel.fromJson({"data": parsed});
    return list.data;
  }

  Future<List<TaskSSZInfoModel>> getInfoTasksSSZ(
    String? idArea,
    String? idBranch,
    String? dateStr,
  ) async {
    var url = TaskSSZAPIMethod.tasksInfoGet.url;

    Map<String, dynamic> body = {};

    if (idArea?.isNotEmpty == true) {
      body["idArea"] = idArea!;
    }

    if (idBranch?.isNotEmpty == true) {
      body["idBranch"] = idBranch!;
    }

    if (dateStr?.isNotEmpty == true) {
      body["dateStr"] = dateStr!;
    }

    var response = await _client.post(url, body: body);

    final parsed = jsonDecode(response.body);
    final list = TaskSSZInfoListModel.fromJson({"data": parsed});
    return list.data;
  }
}
