import '../custom_client.dart';

enum TaskSSZAPIMethod {
  tasksGet,
  tasksInfoGet,
}

extension TaskAPIMethodExtension on TaskSSZAPIMethod {
  static const _serverPrefix = CustomClient.serverPrefix;
  static const _serverAddress = CustomClient.serverAddress;

  Uri get url {
    var method = "";

    switch (this) {
      case TaskSSZAPIMethod.tasksGet:
        method = 'tasks/get';
        break;
      case TaskSSZAPIMethod.tasksInfoGet:
        method = 'tasks/getInfoTasksSSZ';
        break;
      default:
        break;
    }

    return Uri.parse('$_serverAddress$_serverPrefix$method');
  }
}
