import '../custom_client.dart';

enum BranchAPIMethod {
  branchAdd,
  branchEdit,
  branchsGet,
}

extension BranchAPIMethodExtension on BranchAPIMethod {
  static const _serverPrefix = CustomClient.serverPrefix;
  static const _serverAddress = CustomClient.serverAddress;

  Uri get url {
    var method = "";

    switch (this) {
      case BranchAPIMethod.branchAdd:
        method = 'branches/add';
        break;
      case BranchAPIMethod.branchEdit:
        method = 'branches/edit';
        break;
      case BranchAPIMethod.branchsGet:
        method = 'branches/get';
        break;
      default:
        break;
    }

    return Uri.parse('$_serverAddress$_serverPrefix$method');
  }
}
