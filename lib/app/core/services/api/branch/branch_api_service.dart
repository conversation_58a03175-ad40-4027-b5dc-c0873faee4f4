import 'dart:convert';
import 'package:http/http.dart' as http;
import '../../../models/branch/branch_model.dart';
import 'branch_api_methods.dart';

class BranchApi {
  final http.Client _client;

  BranchApi(this._client);

  Future<List<BranchModel>> getBranchs() async {
    var url = BranchAPIMethod.branchsGet.url;
    var response = await _client.post(url);
    final parsed = jsonDecode(response.body);
    final list = BranchListModel.fromJson({"data": parsed});
    return list.data;
  }

  Future<bool> addBranch(BranchModel branch) async {
    var url = BranchAPIMethod.branchAdd.url;
    var body = branch.toJson();
    var response = await _client.post(url, body: body);

    if (response.statusCode == 200) {
      return true;
    } else {
      return false;
    }
  }

  Future<bool> editBranch(BranchModel branch) async {
    var url = BranchAPIMethod.branchEdit.url;
    var body = {'branch': branch.toJson()};
    var response = await _client.post(url, body: body);

    if (response.statusCode == 200) {
      return true;
    } else {
      return false;
    }
  }
}
