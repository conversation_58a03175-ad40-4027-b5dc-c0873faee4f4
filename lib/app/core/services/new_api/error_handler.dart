// import 'package:dio/dio.dart';
// import 'package:flutter/material.dart';
// import 'package:uer_flutter/app/helpers/snack_bar.dart';

// void handleError(dynamic error, dynamic stack, BuildContext context) async {
//   String message;
//   if (error is DioException) {
//     if (error.response != null) {
//       message = "Ошибка ${error.response?.statusCode} ${error.response?.data}";
//     } else {
//       message = "Ошибка сети или сервера";
//     }
//   } else {
//     message = 'Неизвестная ошибка ${error.toString()}';
//   }

//   // await Sentry.captureException(
//   //   error,
//   //   stackTrace: stack,
//   // );

//   showInSnackBar(message, context);
// }
