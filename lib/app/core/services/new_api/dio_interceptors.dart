// import 'package:dio/dio.dart';
// import 'package:flutter_dotenv/flutter_dotenv.dart';
// import 'package:logger/logger.dart';

// class AppInterceptors extends Interceptor {
//   final Dio dio;
//   final logger = Logger();

//   AppInterceptors(this.dio);

//   @override
//   Future<void> onRequest(
//       RequestOptions options, RequestInterceptorHandler handler) async {
//     var apiKey = dotenv.env['SECRET_API_KEY']!;
//     options.headers['secretKey'] = apiKey;

//     handler.next(options);
//   }

//   @override
//   void onResponse(Response response, ResponseInterceptorHandler handler) {
//     logger.d('Response: $response');
//     handler.next(response);
//   }

//   // @override
//   // void onError(DioException err, ErrorInterceptorHandler handler) {

//   //   return handler.next(err);
//   // }
// }

// // switch (err.type) {
// //       case DioExceptionType.connectionTimeout:
// //       case DioExceptionType.sendTimeout:
// //       case DioExceptionType.receiveTimeout:
// //         throw DeadlineExceededException(err.requestOptions);
// //       case DioExceptionType.badResponse:
// //         switch (err.response?.statusCode) {
// //           case 400:
// //             throw BadRequestException(err.requestOptions);
// //           case 401:
// //             throw UnauthorizedException(err.requestOptions);
// //           case 404:
// //             throw NotFoundException(err.requestOptions);
// //           case 409:
// //             throw ConflictException(err.requestOptions);
// //           case 500:
// //             throw InternalServerErrorException(err.requestOptions);
// //         }
// //         break;
// //       case DioExceptionType.cancel:
// //         break;
// //       case DioExceptionType.unknown:
// //         throw NoInternetConnectionException(err.requestOptions);
// //       case DioExceptionType.connectionError:
// //         throw ConnectionErrorException(err.requestOptions);
// //       case DioExceptionType.badCertificate:
// //         throw BadCertificateException(err.requestOptions);
// //     }

// class BadRequestException extends DioException {
//   BadRequestException(RequestOptions r) : super(requestOptions: r);

//   @override
//   String toString() {
//     return 'Invalid request';
//   }
// }

// class BadCertificateException extends DioException {
//   BadCertificateException(RequestOptions r) : super(requestOptions: r);

//   @override
//   String toString() {
//     return 'Bad Certificate';
//   }
// }

// class ConnectionErrorException extends DioException {
//   ConnectionErrorException(RequestOptions r) : super(requestOptions: r);

//   @override
//   String toString() {
//     return 'Connection Error';
//   }
// }

// class InternalServerErrorException extends DioException {
//   InternalServerErrorException(RequestOptions r) : super(requestOptions: r);

//   @override
//   String toString() {
//     return 'Unknown error occurred, please try again later.';
//   }
// }

// class ConflictException extends DioException {
//   ConflictException(RequestOptions r) : super(requestOptions: r);

//   @override
//   String toString() {
//     return 'Conflict occurred';
//   }
// }

// class UnauthorizedException extends DioException {
//   UnauthorizedException(RequestOptions r) : super(requestOptions: r);

//   @override
//   String toString() {
//     return 'Access denied';
//   }
// }

// class NotFoundException extends DioException {
//   NotFoundException(RequestOptions r) : super(requestOptions: r);

//   @override
//   String toString() {
//     return 'The requested information could not be found';
//   }
// }

// class NoInternetConnectionException extends DioException {
//   NoInternetConnectionException(RequestOptions r) : super(requestOptions: r);

//   @override
//   String toString() {
//     return 'No internet connection detected, please try again.';
//   }
// }

// class DeadlineExceededException extends DioException {
//   DeadlineExceededException(RequestOptions r) : super(requestOptions: r);

//   @override
//   String toString() {
//     return 'The connection has timed out, please try again.';
//   }
// }
