import 'package:intl/intl.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:uer_flutter/app/core/models/area/area_model.dart';
import 'package:uer_flutter/app/core/models/component_history/component_history_model.dart';
import 'package:uer_flutter/app/core/models/enums/component_history_type_enum.dart';
import 'package:uer_flutter/app/core/models/enums/history_status_enum.dart';
import 'package:uer_flutter/app/helpers/date_extension.dart';
import 'package:uer_flutter/app/helpers/string_helper.dart';

Future<String> getVersionApp() async {
  PackageInfo packageInfo = await PackageInfo.fromPlatform();
  String version = packageInfo.version;
  String buildNumber = packageInfo.buildNumber;

  return "$version ($buildNumber)";
}

String dateToString(DateTime date) {
  final day = date.day.toString().padLeft(2, '0');
  final month = date.month.toString().padLeft(2, '0');
  final year = date.year.toString().padLeft(4, '0');
  final hours = date.hour.toString().padLeft(2, '0');
  final minute = date.minute.toString().padLeft(2, '0');

  return '$day.$month.$year $hours:$minute';
}

String timeFromUnix(double unix) {
  var minuts = unix / 60;

  if (minuts > 60) {
    var hours = minuts / 60;

    if (hours > 24) {
      var days = hours / 24;
      var str = days.toStringAsFixed(2);
      return "$str д.";
    }
    var str = hours.toStringAsFixed(1);
    return "$str ч.";
  }
  var str = minuts.toStringAsFixed(0);
  return "$str мин.";
}

List<String> getLastNDays(String format, int days) {
  final dateNow = DateTime.now();

  List<String> daysArr = [];

  for (var i = days - 1; i > 0; i--) {
    final dateDaysAgo = DateTime.now().subtract(Duration(days: i));
    daysArr.add(dateDaysAgo.dateWithFormat(format));
  }

  daysArr.add(dateNow.dateWithFormat(format));

  return daysArr;
}

int compareDate(String formatStr, String date1, String date2) {
  // date2, -1 future, 1 past, 0 now

  var format = DateFormat(formatStr);

  final dateOne = format.parse(date1);
  final dateTwo = format.parse(date2);

  return dateOne.compareTo(dateTwo);
}

ComponentHistoryModel generateCommentHistory(
    String comment, AreaModel area, String owner) {
  var name = "${area.name} | Комментарий";

  var identifier = getRandomString(16);

  var statusModel = ComponentStatusModel(
      owner: owner, status: HistoryStatusType.info, comment: comment);

  var model = ComponentHistoryModel(
      name: name,
      area: area.identifier,
      identifier: identifier,
      type: ComponentHistoryType.info,
      statuses: [statusModel]);

  return model;
}

// String getAgoFromDate(double dateTime) {
//   var date = DateTime.fromMillisecondsSinceEpoch(dateTime.toInt() * 1000);
//   timeago.setLocaleMessages("ru", timeago.RuMessages());
//   return timeago.format(date, locale: "ru");
// }
