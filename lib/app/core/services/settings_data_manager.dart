import 'package:shared_preferences/shared_preferences.dart';

class SettingsDataManager {
  static final shared = SettingsDataManager();

  Future<bool> checkAuth() async {
    var login = await getUserLogin();
    if (login?.isNotEmpty == true) {
      return true;
    } else {
      return false;
    }
  }

  // User login data

  Future<bool> saveAuthData(String login, String password) async {
    await _saveUserLogin(login);
    return await _saveUserPassword(password);
  }

  Future<bool> _saveUserLogin(String value) async {
    final prefs = await SharedPreferences.getInstance();
    return await prefs.setString("user_login", value);
  }

  Future<bool> _saveUserPassword(String value) async {
    final prefs = await SharedPreferences.getInstance();
    return await prefs.setString("user_password", value);
  }

  Future<String?> getUserLogin() async {
    final prefs = await SharedPreferences.getInstance();
    final String? value = prefs.getString("user_login");
    return value;
  }

  Future<String?> getUserPassword() async {
    final prefs = await SharedPreferences.getInstance();
    final String? value = prefs.getString("user_password");
    return value;
  }

  Future<bool> removeUserLoginData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove("user_login");
    return await prefs.remove("user_password");
  }

  // Version app

  Future<bool> saveLastVersion(String value) async {
    final prefs = await SharedPreferences.getInstance();
    return await prefs.setString("version", value);
  }

  Future<String?> getLastVersion(String value) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString("version");
  }
}
