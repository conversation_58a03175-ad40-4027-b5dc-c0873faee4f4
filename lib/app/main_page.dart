import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'core/models/enums/user_role_enum.dart';
import 'features/branch/branch_page.dart';
import 'features/main_user/main_user_page.dart';
import 'features/concern/concern_page.dart';
import 'core/providers/states/user/user_state.dart';

class MainPage extends ConsumerStatefulWidget {
  const MainPage({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _MainPageState();
}

class _MainPageState extends ConsumerState<MainPage> {
  @override
  Widget build(BuildContext context) {
    var userState = ref.watch(currentUserProvider);

    var user = userState.maybeWhen(auth: (user) => user, orElse: () {});

    var role = user?.role;

    switch (role) {
      case UserRole.admin:
        return const MainConcernPage();
      case UserRole.user:
        return const MainUserPage();
      case UserRole.manager:
        return const MainConcernPage();
      case UserRole.director:
        return MainBranchPage(
          branchID: user?.branch,
        );
      case UserRole.station:
        return const Text("Стационарный аккаунт. Экран не реализован.");
      case UserRole.client:
        return const Text("Клиентский аккаунт. Экран не реализован.");
      case UserRole.block:
        return const Text("Пользователь заблокирован");
      default:
        return const Text("Ошибка: Неопознаная роль");
    }
  }
}
