import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:uer_flutter/app/core/providers/states/user/user_state.dart';
import 'package:uer_flutter/app/features/settings/user_card.dart';
import 'package:uer_flutter/app/helpers/date_extension.dart';
import 'package:uer_flutter/app/widgets/bars/app_bar.dart';
import 'package:uer_flutter/gen/assets.gen.dart';

import '../../core/models/enums/user_role_enum.dart';
import '../../core/providers/common/common_providers.dart';
import '../../core/services/api/service_provider.dart';
import '../../helpers/platform_check.dart';
import '../../helpers/snack_bar.dart';
import '../../helpers/web/onesignal_web_wrapper.dart';
import '../../routes/routes_name.dart';
import 'header_row.dart';
import 'settings_button.dart';
import 'settings_switch.dart';
import 'version_app.dart';

class SettingsPage extends ConsumerStatefulWidget {
  const SettingsPage({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _SettingsPageState();
}

class _SettingsPageState extends ConsumerState<SettingsPage> {
  void testPush() async {
    var user = ref.read(currentUserModelProvider);

    if (user != null) {
      var pushProvider = ref.read(pushRepositoryProvider);
      await pushProvider.sendPush(user.login, null, null);
      showInSnackBar("Уведомление отправлено.", context);
    } else {
      showInSnackBar("Ошибка. Пользователь не авторизирован", context);
    }
  }

  void pushUserIDAdd() async {
    OnesignalWebWrapper.instance?.notificationPromptWeb();

    // var userState = ref.read(currentUserProvider);

    // var user = userState.maybeMap(
    //   auth: (value) => value.user,
    //   orElse: () => null,
    // );

    // if (user != null) {
    //   var result = await OneSignalAuth().transferOneSignalUserIDWeb(user.login);

    //   if (result == true) {
    //     showInSnackBar("Успешно сохранено!", context);
    //   } else {
    //     showInSnackBar("Ошибка. Не сохранено!", context);
    //   }
    // }
  }

  void openTypeEventPush() {
    context.pushNamed(RoutesName.pushEventSettings.named);
  }

  void openSszPush() {
    context.pushNamed(RoutesName.pushSSZSettings.named);
  }

  void subscribeZPRChange(bool value) async {
    var user = ref.read(currentUserModelProvider);
    var area = ref.read(currentUserAreaProvider);

    if (area?.start == true && value == false) {
      showInSnackBar("Невозможно отключить на аккаунте приёмки", context);
      return;
    }

    if (user == null) {
      showInSnackBar("Ошибка. Пользователь не авторизирован", context);
      return;
    }

    var userProvider = ref.read(userRepositoryProvider);
    var success = await userProvider.subscribeZPRChange(user.login, value);

    if (success == true) {
      var userNew = user.copyWith(subscribeZPR: value);
      ref.read(currentUserProvider.notifier).updateUserWithUser(userNew);
    } else {
      showInSnackBar("Ошибка. Не сохранено!", context);
    }
  }

  void openUsers() {
    context.pushNamed(RoutesName.usersList.named);
  }

  void openAreas() {
    context.pushNamed(RoutesName.areasList.named);
  }

  void openStatuses() {
    context.pushNamed(RoutesName.statusSettings.named);
  }

  void openBranchs() {
    context.pushNamed(RoutesName.branchsList.named);
  }

  void refreshLastOrderStatus() async {
    var value = await ref.read(itemRepositoryProvider).lastOrderSync();

    if (value != null) {
      var date = DateTime.fromMillisecondsSinceEpoch(value * 1000)
          .dateWithFormat("HH:mm:ss dd/MM/yyyy");

      showInSnackBar("Последнее обновление: $date", context);
    } else {
      showInSnackBar("Ошибка. Повторите позднее.", context);
    }
  }

  void openTutorialsPage() {
    context.pushNamed(RoutesName.tutorialList.named);
  }

  void logout() {
    ref.read(currentUserProvider.notifier).logout();
  }

  @override
  Widget build(BuildContext context) {
    var user = ref.watch(currentUserModelProvider);

    var role = user?.role;

    return Scaffold(
      appBar: const CustomAppBar(text: "Настройки"),
      body: ListView(
        children: [
          // const UserRow(),
          const Padding(
            padding: EdgeInsets.fromLTRB(16.0, 16.0, 16.0, 0),
            child: UserCard(),
          ),
          const SizedBox(height: 50.0),
          const HeaderRow(
            title: "Инструкция",
          ),
          SettingsButton(
            callback: openTutorialsPage,
            title: "Видеоинструкции",
            iconAsset: Assets.icons.videocam,
          ),
          const SizedBox(height: 50.0),

          const HeaderRow(
            title: "Уведомления",
          ),

          SettingsButton(
            callback: openTypeEventPush,
            title: "Уведомления на статусы",
            iconAsset: Assets.icons.notifications,
            description: 'Уведомления по цехам на изменение статусов',
          ),
          const Divider(height: 1.0),
          SettingsButton(
            callback: openSszPush,
            title: "Уведомления ССЗ",
            iconAsset: Assets.icons.assignmentLate,
            description: 'Уведомления по цехам на сменно-суточные задания',
          ),
          const Divider(height: 1.0),
          SettingsSwitch(
              title: "Уведомления на новый заказ (ЗПР)",
              description:
                  'Когда появится новый заказ, вам придёт уведомление о нём',
              iconAsset: Assets.icons.addShoppingCart,
              switchValue: user?.subscribeZPR ?? false,
              callback: subscribeZPRChange),
          const Divider(height: 1.0),
          SettingsButton(
            callback: testPush,
            title: "Тест уведомлений",
            iconAsset: Assets.icons.notificationsActive,
          ),
          if (isWeb()) const Divider(height: 1.0),
          if (isWeb())
            SettingsButton(
              callback: pushUserIDAdd,
              title: "Привязать аккаунт к уведомлениям",
              iconAsset: Assets.icons.personAdd,
              description: 'Привязать текущий аккаунт к уведомлениям',
            ),
          // SettingsButton(
          //     callback: sendMessage,
          //     title: "Отправить сообщение",
          //     iconData: Icons.message,
          //     iconColor: Colors.green),

          role == UserRole.admin
              ? Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: 32.0),
                    const HeaderRow(
                      title: "Администрирование",
                    ),
                    SettingsButton(
                      callback: openUsers,
                      title: "Пользователи",
                      iconAsset: Assets.icons.accountCircle,
                    ),
                    SettingsButton(
                      callback: openAreas,
                      title: "Участки",
                      iconAsset: Assets.icons.close,
                    ),
                    SettingsButton(
                      callback: openStatuses,
                      title: "Статусы",
                      iconAsset: Assets.icons.errorOutline,
                    ),
                    SettingsButton(
                      callback: openBranchs,
                      title: "Филиалы",
                      iconAsset: Assets.icons.errorOutline,
                    ),
                    SettingsButton(
                      callback: refreshLastOrderStatus,
                      title: "Посл. обновл. заказов",
                      iconAsset: Assets.icons.errorOutline,
                    ),
                  ],
                )
              : const SizedBox(),

          //const Spacer(),
          const SizedBox(
            height: 16,
          ),
          // Center(
          //   child: ElevatedButton(
          //       onPressed: logout,
          //       style: ElevatedButton.styleFrom(
          //           backgroundColor: Colors.orange,
          //           foregroundColor: Colors.white),
          //       child: const Text(
          //         "Выйти из аккаунта",
          //         //style: TextStyle(color: Colors.black),
          //       )),
          // ),
          // const SizedBox(
          //   height: 12,
          // ),
          const VersionApp(),
          SizedBox(height: 32.0),
        ],
      ),
    );
  }
}
