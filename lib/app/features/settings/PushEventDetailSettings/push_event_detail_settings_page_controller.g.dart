// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'push_event_detail_settings_page_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$pushEventDetailSettingsPageControllerHash() =>
    r'0885e93925ded6a7ff87fa439fcac95e8c3c6b84';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$PushEventDetailSettingsPageController
    extends BuildlessAutoDisposeAsyncNotifier<List<StatusModel>> {
  late final int areaID;

  FutureOr<List<StatusModel>> build(
    int areaID,
  );
}

/// See also [PushEventDetailSettingsPageController].
@ProviderFor(PushEventDetailSettingsPageController)
const pushEventDetailSettingsPageControllerProvider =
    PushEventDetailSettingsPageControllerFamily();

/// See also [PushEventDetailSettingsPageController].
class PushEventDetailSettingsPageControllerFamily
    extends Family<AsyncValue<List<StatusModel>>> {
  /// See also [PushEventDetailSettingsPageController].
  const PushEventDetailSettingsPageControllerFamily();

  /// See also [PushEventDetailSettingsPageController].
  PushEventDetailSettingsPageControllerProvider call(
    int areaID,
  ) {
    return PushEventDetailSettingsPageControllerProvider(
      areaID,
    );
  }

  @override
  PushEventDetailSettingsPageControllerProvider getProviderOverride(
    covariant PushEventDetailSettingsPageControllerProvider provider,
  ) {
    return call(
      provider.areaID,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'pushEventDetailSettingsPageControllerProvider';
}

/// See also [PushEventDetailSettingsPageController].
class PushEventDetailSettingsPageControllerProvider
    extends AutoDisposeAsyncNotifierProviderImpl<
        PushEventDetailSettingsPageController, List<StatusModel>> {
  /// See also [PushEventDetailSettingsPageController].
  PushEventDetailSettingsPageControllerProvider(
    int areaID,
  ) : this._internal(
          () => PushEventDetailSettingsPageController()..areaID = areaID,
          from: pushEventDetailSettingsPageControllerProvider,
          name: r'pushEventDetailSettingsPageControllerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$pushEventDetailSettingsPageControllerHash,
          dependencies:
              PushEventDetailSettingsPageControllerFamily._dependencies,
          allTransitiveDependencies: PushEventDetailSettingsPageControllerFamily
              ._allTransitiveDependencies,
          areaID: areaID,
        );

  PushEventDetailSettingsPageControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.areaID,
  }) : super.internal();

  final int areaID;

  @override
  FutureOr<List<StatusModel>> runNotifierBuild(
    covariant PushEventDetailSettingsPageController notifier,
  ) {
    return notifier.build(
      areaID,
    );
  }

  @override
  Override overrideWith(
      PushEventDetailSettingsPageController Function() create) {
    return ProviderOverride(
      origin: this,
      override: PushEventDetailSettingsPageControllerProvider._internal(
        () => create()..areaID = areaID,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        areaID: areaID,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<PushEventDetailSettingsPageController,
      List<StatusModel>> createElement() {
    return _PushEventDetailSettingsPageControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is PushEventDetailSettingsPageControllerProvider &&
        other.areaID == areaID;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, areaID.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin PushEventDetailSettingsPageControllerRef
    on AutoDisposeAsyncNotifierProviderRef<List<StatusModel>> {
  /// The parameter `areaID` of this provider.
  int get areaID;
}

class _PushEventDetailSettingsPageControllerProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<
        PushEventDetailSettingsPageController,
        List<StatusModel>> with PushEventDetailSettingsPageControllerRef {
  _PushEventDetailSettingsPageControllerProviderElement(super.provider);

  @override
  int get areaID =>
      (origin as PushEventDetailSettingsPageControllerProvider).areaID;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
