import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:uer_flutter/app/core/providers/notifier_mouted.dart';
import 'package:uer_flutter/app/core/providers/states/statuses/statuses_state.dart';
import '../../../core/models/status/status_model.dart';
import '../../../core/providers/common/common_providers.dart';

part 'push_event_detail_settings_page_controller.g.dart';

@riverpod
class PushEventDetailSettingsPageController
    extends _$PushEventDetailSettingsPageController with NotifierMounted {
  @override
  FutureOr<List<StatusModel>> build(int areaID) {
    ref.onDispose(setUnmounted);
    return _fetchStatuses(areaID);
  }

  Future<List<StatusModel>> _fetchStatuses(int areaID) async {
    var area = ref.watch(areaForIDProvider(areaID));

    if (area == null) {
      return [];
    }

    var jobIds = area.jobs;

    var statuses = ref.watch(statusesProvider);

    var filtered = statuses
        .where((element) => jobIds.contains(element.identifier))
        .toList();

    return filtered;
  }
}
