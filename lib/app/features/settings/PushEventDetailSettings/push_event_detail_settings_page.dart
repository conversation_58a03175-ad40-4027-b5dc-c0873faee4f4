import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:tuple/tuple.dart';
import 'package:uer_flutter/app/widgets/bars/app_bar.dart';
import 'package:uer_flutter/app/widgets/checkbox_cell.dart';

import '../../../core/models/status/status_model.dart';
import '../../../core/models/user/user_model.dart';
import '../../../core/providers/common/common_providers.dart';
import 'push_event_detail_settings_page_controller.dart';

class PushEventDetailSettingsPage extends ConsumerStatefulWidget {
  const PushEventDetailSettingsPage({super.key, required this.areaID});

  final int areaID;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _PushEventDetailSettingsPageState();
}

class _PushEventDetailSettingsPageState
    extends ConsumerState<PushEventDetailSettingsPage> {
  var infoStatus =
      const StatusModel(identifier: 0, name: "Передачи и комментарии");

  List<String> addArr = [];
  List<String> delArr = [];

  void changePushEventSettings(UserModel user, StatusModel status, bool value) {
    var subscribeID = "${widget.areaID}_${status.identifier}";

    setState(() {
      if (user.subscribe?.contains(subscribeID) == true) {
        if (delArr.contains(subscribeID) == false && value == false) {
          delArr.add(subscribeID);
        } else if (delArr.contains(subscribeID) == true && value == true) {
          delArr.remove(subscribeID);
        }
      } else if (user.subscribe?.contains(subscribeID) == false) {
        if (addArr.contains(subscribeID) == false && value == true) {
          addArr.add(subscribeID);
        } else if (addArr.contains(subscribeID) == true && value == false) {
          addArr.remove(subscribeID);
        }
      }
    });
  }

  void savePushEventSettings(UserModel? user) async {
    if (user == null) {
      return;
    }

    context.pop(Tuple2(addArr, delArr));
  }

  @override
  Widget build(BuildContext context) {
    var user = ref.watch(currentUserModelProvider);

    var area = ref.watch(areaForIDProvider(widget.areaID));

    var statuses =
        ref.watch(pushEventDetailSettingsPageControllerProvider(widget.areaID));

    return Scaffold(
        appBar: CustomAppBar(
          text: area?.name ?? "Уведомления: Статусы",
          actions: [
            TextButton(
                onPressed: () {
                  savePushEventSettings(user);
                },
                child: const Text(
                  "Сохранить",
                  style: TextStyle(color: Colors.white),
                ))
          ],
        ),
        body: statuses.when(data: (data) {
          if (data.isEmpty) {
            return const CircularProgressIndicator.adaptive();
          }

          return ListView.builder(
            itemCount: data.length + 1,
            itemBuilder: (BuildContext context, int index) {
              StatusModel status;

              if (index == 0) {
                status = infoStatus;
              } else {
                status = data[index - 1];
              }

              callback(bool? value) {
                changePushEventSettings(user!, status, value ?? false);
              }

              var checkBoxValue =
                  user?.checkSubscribeEvent(widget.areaID, status.identifier);

              var subscribeID = "${widget.areaID}_${status.identifier}";

              if (delArr.contains(subscribeID) == true) {
                checkBoxValue = false;
              } else if (addArr.contains(subscribeID) == true) {
                checkBoxValue = true;
              }

              return CheckBoxCell(
                  title: status.name,
                  checkboxValue: checkBoxValue ?? false,
                  callback: callback);
            },
          );
        }, error: (error, stacktrace) {
          return const Center(
            child: Text("Ошибка загрузки экрана"),
          );
        }, loading: () {
          return const CircularProgressIndicator.adaptive();
        }));
  }
}
