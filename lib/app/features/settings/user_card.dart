import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uer_flutter/app/core/providers/states/user/user_state.dart';
import 'package:uer_flutter/app/styles/colors.dart';
import 'package:uer_flutter/app/styles/fonts.dart';
import 'package:uer_flutter/app/widgets/svg_icon/index.dart';
import 'package:uer_flutter/gen/assets.gen.dart';

class UserCard extends ConsumerStatefulWidget {
  const UserCard({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _UserCardState();
}

class _UserCardState extends ConsumerState<UserCard> {
  @override
  Widget build(BuildContext context) {
    var userState = ref.watch(currentUserProvider);

    var user = userState.maybeWhen(
      auth: (user) {
        return user;
      },
      orElse: () {},
    );
    void logout() {
      ref.read(currentUserProvider.notifier).logout();
    }

    return Card(
      child: Ink(
        padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 12.0),
        child: Row(children: [
          SVGIcon(Assets.icons.accountCircle, width: 48.0),
          const SizedBox(width: 12.0),
          Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Text(
              user?.login ?? '',
              style: AppFonts.titleMedium.merge(
                const TextStyle(
                  fontVariations: [
                    FontVariation.weight(600),
                  ],
                ),
              ),
            ),
            Text(user?.role.name ?? '', style: AppFonts.labelMedium),
          ]),
          const Spacer(),
          IconButton(
            onPressed: logout,
            icon: SVGIcon(
              Assets.icons.logout,
              color: AppLightColors.error,
            ),
          ),
        ]),
      ),
    );
  }
}
