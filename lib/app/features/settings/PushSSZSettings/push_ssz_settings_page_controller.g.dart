// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'push_ssz_settings_page_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$pushSSZSettingsPageControllerHash() =>
    r'2f05ef104cb4c08fc105bee143f7f68ea4485277';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$PushSSZSettingsPageController
    extends BuildlessAutoDisposeAsyncNotifier<List<AreaModel>> {
  late final int? branchID;

  FutureOr<List<AreaModel>> build(
    int? branchID,
  );
}

/// See also [PushSSZSettingsPageController].
@ProviderFor(PushSSZSettingsPageController)
const pushSSZSettingsPageControllerProvider =
    PushSSZSettingsPageControllerFamily();

/// See also [PushSSZSettingsPageController].
class PushSSZSettingsPageControllerFamily
    extends Family<AsyncValue<List<AreaModel>>> {
  /// See also [PushSSZSettingsPageController].
  const PushSSZSettingsPageControllerFamily();

  /// See also [PushSSZSettingsPageController].
  PushSSZSettingsPageControllerProvider call(
    int? branchID,
  ) {
    return PushSSZSettingsPageControllerProvider(
      branchID,
    );
  }

  @override
  PushSSZSettingsPageControllerProvider getProviderOverride(
    covariant PushSSZSettingsPageControllerProvider provider,
  ) {
    return call(
      provider.branchID,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'pushSSZSettingsPageControllerProvider';
}

/// See also [PushSSZSettingsPageController].
class PushSSZSettingsPageControllerProvider
    extends AutoDisposeAsyncNotifierProviderImpl<PushSSZSettingsPageController,
        List<AreaModel>> {
  /// See also [PushSSZSettingsPageController].
  PushSSZSettingsPageControllerProvider(
    int? branchID,
  ) : this._internal(
          () => PushSSZSettingsPageController()..branchID = branchID,
          from: pushSSZSettingsPageControllerProvider,
          name: r'pushSSZSettingsPageControllerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$pushSSZSettingsPageControllerHash,
          dependencies: PushSSZSettingsPageControllerFamily._dependencies,
          allTransitiveDependencies:
              PushSSZSettingsPageControllerFamily._allTransitiveDependencies,
          branchID: branchID,
        );

  PushSSZSettingsPageControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.branchID,
  }) : super.internal();

  final int? branchID;

  @override
  FutureOr<List<AreaModel>> runNotifierBuild(
    covariant PushSSZSettingsPageController notifier,
  ) {
    return notifier.build(
      branchID,
    );
  }

  @override
  Override overrideWith(PushSSZSettingsPageController Function() create) {
    return ProviderOverride(
      origin: this,
      override: PushSSZSettingsPageControllerProvider._internal(
        () => create()..branchID = branchID,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        branchID: branchID,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<PushSSZSettingsPageController,
      List<AreaModel>> createElement() {
    return _PushSSZSettingsPageControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is PushSSZSettingsPageControllerProvider &&
        other.branchID == branchID;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, branchID.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin PushSSZSettingsPageControllerRef
    on AutoDisposeAsyncNotifierProviderRef<List<AreaModel>> {
  /// The parameter `branchID` of this provider.
  int? get branchID;
}

class _PushSSZSettingsPageControllerProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<
        PushSSZSettingsPageController,
        List<AreaModel>> with PushSSZSettingsPageControllerRef {
  _PushSSZSettingsPageControllerProviderElement(super.provider);

  @override
  int? get branchID =>
      (origin as PushSSZSettingsPageControllerProvider).branchID;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
