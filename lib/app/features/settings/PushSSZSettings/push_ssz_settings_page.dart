import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uer_flutter/app/core/providers/states/user/user_state.dart';
import 'package:uer_flutter/app/widgets/bars/app_bar.dart';
import 'package:uer_flutter/app/widgets/checkbox_cell.dart';

import '../../../core/models/area/area_model.dart';
import '../../../core/models/user/user_model.dart';
import '../../../core/providers/common/common_providers.dart';
import '../../../helpers/snack_bar.dart';
import 'push_ssz_settings_page_controller.dart';

class PushSSZSettingsPage extends ConsumerStatefulWidget {
  const PushSSZSettingsPage({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _PushSSZSettingsPageState();
}

class _PushSSZSettingsPageState extends ConsumerState<PushSSZSettingsPage> {
  void changeSSZAreaNotify(UserModel user, AreaModel area, bool value) {
    if (value == true) {
      addSSZAreaNotify(user, area);
    } else {
      removeSSZAreaNotify(user, area);
    }
  }

  // TODO: добавить обновление модели юзера

  void addSSZAreaNotify(UserModel user, AreaModel area) async {
    var result = await ref
        .read(pushSSZSettingsPageControllerProvider(user.branch).notifier)
        .changeSSZAreaNotify(user.login, "${area.identifier}", null);

    if (result == true) {
      showInSnackBar("Успешно сохранено!", context);

      ref.read(currentUserProvider.notifier).updateCurrentUser();
    } else {
      showInSnackBar("Не сохранено, повторите позднее", context);
    }
  }

  void removeSSZAreaNotify(UserModel user, AreaModel area) async {
    var result = await ref
        .read(pushSSZSettingsPageControllerProvider(user.branch).notifier)
        .changeSSZAreaNotify(user.login, null, "${area.identifier}");

    if (result == true) {
      showInSnackBar("Успешно сохранено!", context);

      ref.read(currentUserProvider.notifier).updateCurrentUser();
    } else {
      showInSnackBar("Не сохранено, повторите позднее", context);
    }
  }

  @override
  Widget build(BuildContext context) {
    var user = ref.watch(currentUserModelProvider);

    var areas = ref.watch(pushSSZSettingsPageControllerProvider(user?.branch));

    return Scaffold(
        appBar: const CustomAppBar(text: "Уведомления: ССЗ"),
        body: areas.when(data: (data) {
          if (data.isEmpty) {
            return const CircularProgressIndicator.adaptive();
          }

          return ListView.builder(
            itemCount: data.length,
            itemBuilder: (BuildContext context, int index) {
              var area = data[index];

              callback(bool? value) {
                changeSSZAreaNotify(user!, area, value ?? false);
              }

              var checkBoxValue = user?.checkSubscribeSSZ("${area.identifier}");

              return CheckBoxCell(
                  title: area.name,
                  checkboxValue: checkBoxValue ?? false,
                  callback: callback);
            },
          );
        }, error: (error, stacktrace) {
          return const Center(
            child: Text("Ошибка загрузки экрана"),
          );
        }, loading: () {
          return const CircularProgressIndicator.adaptive();
        }));
  }
}
