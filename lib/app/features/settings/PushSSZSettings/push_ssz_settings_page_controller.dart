import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:uer_flutter/app/core/providers/notifier_mouted.dart';
import 'package:uer_flutter/app/core/providers/states/areas/areas_state.dart';
import '../../../core/models/area/area_model.dart';
import '../../../core/services/api/service_provider.dart';

part 'push_ssz_settings_page_controller.g.dart';

@riverpod
class PushSSZSettingsPageController extends _$PushSSZSettingsPageController
    with NotifierMounted {
  @override
  FutureOr<List<AreaModel>> build(int? branchID) {
    ref.onDispose(setUnmounted);
    return _fetchAreas(branchID);
  }

  Future<List<AreaModel>> _fetchAreas(int? branchID) async {
    var areas = ref.watch(areasProvider);

    if (branchID != null && branchID != 0) {
      areas = areas.where((element) => element.branch == branchID).toList();
    }

    return areas;
  }

  Future<bool> changeSSZAreaNotify(
      String login, String? addID, String? delID) async {
    List<String> addList = [];
    List<String> delList = [];

    if (addID != null) {
      addList = [addID];
    }

    if (delID != null) {
      delList = [delID];
    }

    var userProvider = ref.read(userRepositoryProvider);
    var response =
        await userProvider.subscribeSSZChange(login, addList, delList);
    return response;
  }
}
