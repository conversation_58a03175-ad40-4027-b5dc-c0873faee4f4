import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uer_flutter/app/core/services/utils.dart';
import 'package:uer_flutter/app/styles/colors.dart';
import 'package:uer_flutter/app/styles/fonts.dart';

class VersionApp extends ConsumerStatefulWidget {
  const VersionApp({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _VersionAppState();
}

class _VersionAppState extends ConsumerState<VersionApp> {
  @override
  Widget build(BuildContext context) {
    return FutureBuilder<String>(
      future: getVersionApp(), // function where you call your api
      builder: (BuildContext context, AsyncSnapshot<String> snapshot) {
        // AsyncSnapshot<Your object type>
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Center(
            child: Text(
              'Версия приложения: ---',
              style: AppFonts.labelLarge.merge(
                const TextStyle(
                  fontFamily: AppFonts.monoFontFamily,
                  color: AppLightColors.medium,
                ),
              ),
            ),
          );
        } else {
          if (snapshot.hasError) {
            return Center(
              child: Text(
                'Версия приложения: ---',
                style: AppFonts.labelLarge.merge(
                  const TextStyle(
                    fontFamily: AppFonts.monoFontFamily,
                    color: AppLightColors.medium,
                  ),
                ),
              ),
            );
          } else {
            return Center(
              child: Text(
                'Версия приложения: ${snapshot.data}',
                style: AppFonts.labelLarge.merge(
                  const TextStyle(
                    fontFamily: AppFonts.monoFontFamily,
                    color: AppLightColors.medium,
                  ),
                ),
              ),
            );
          }
        }
      },
    );
  }
}
