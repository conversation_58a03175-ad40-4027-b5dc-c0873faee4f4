import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:uer_flutter/app/core/providers/notifier_mouted.dart';
import 'package:uer_flutter/app/core/providers/states/areas/areas_state.dart';
import '../../../core/models/area/area_model.dart';

import '../../../core/services/api/service_provider.dart';

part 'push_event_settings_page_controller.g.dart';

@riverpod
class PushEventSettingsPageController extends _$PushEventSettingsPageController
    with NotifierMounted {
  @override
  FutureOr<List<AreaModel>> build(int? branchID) {
    ref.onDispose(setUnmounted);
    return _fetchAreas(branchID);
  }

  Future<List<AreaModel>> _fetchAreas(int? branchID) async {
    var areas = ref.watch(areasProvider);

    if (branchID != null && branchID != 0) {
      areas = areas.where((element) => element.branch == branchID).toList();
    }

    return areas;
  }

  Future<bool> changeEventNotify(
      String login, List<String>? addList, List<String>? delList) async {
    var userProvider = ref.read(userRepositoryProvider);
    var response =
        await userProvider.subscribeChange(login, addList ?? [], delList ?? []);
    return response;
  }
}
