// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'push_event_settings_page_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$pushEventSettingsPageControllerHash() =>
    r'44309b19fd4e75f17cd19d728fc329b21d0e465a';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$PushEventSettingsPageController
    extends BuildlessAutoDisposeAsyncNotifier<List<AreaModel>> {
  late final int? branchID;

  FutureOr<List<AreaModel>> build(
    int? branchID,
  );
}

/// See also [PushEventSettingsPageController].
@ProviderFor(PushEventSettingsPageController)
const pushEventSettingsPageControllerProvider =
    PushEventSettingsPageControllerFamily();

/// See also [PushEventSettingsPageController].
class PushEventSettingsPageControllerFamily
    extends Family<AsyncValue<List<AreaModel>>> {
  /// See also [PushEventSettingsPageController].
  const PushEventSettingsPageControllerFamily();

  /// See also [PushEventSettingsPageController].
  PushEventSettingsPageControllerProvider call(
    int? branchID,
  ) {
    return PushEventSettingsPageControllerProvider(
      branchID,
    );
  }

  @override
  PushEventSettingsPageControllerProvider getProviderOverride(
    covariant PushEventSettingsPageControllerProvider provider,
  ) {
    return call(
      provider.branchID,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'pushEventSettingsPageControllerProvider';
}

/// See also [PushEventSettingsPageController].
class PushEventSettingsPageControllerProvider
    extends AutoDisposeAsyncNotifierProviderImpl<
        PushEventSettingsPageController, List<AreaModel>> {
  /// See also [PushEventSettingsPageController].
  PushEventSettingsPageControllerProvider(
    int? branchID,
  ) : this._internal(
          () => PushEventSettingsPageController()..branchID = branchID,
          from: pushEventSettingsPageControllerProvider,
          name: r'pushEventSettingsPageControllerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$pushEventSettingsPageControllerHash,
          dependencies: PushEventSettingsPageControllerFamily._dependencies,
          allTransitiveDependencies:
              PushEventSettingsPageControllerFamily._allTransitiveDependencies,
          branchID: branchID,
        );

  PushEventSettingsPageControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.branchID,
  }) : super.internal();

  final int? branchID;

  @override
  FutureOr<List<AreaModel>> runNotifierBuild(
    covariant PushEventSettingsPageController notifier,
  ) {
    return notifier.build(
      branchID,
    );
  }

  @override
  Override overrideWith(PushEventSettingsPageController Function() create) {
    return ProviderOverride(
      origin: this,
      override: PushEventSettingsPageControllerProvider._internal(
        () => create()..branchID = branchID,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        branchID: branchID,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<PushEventSettingsPageController,
      List<AreaModel>> createElement() {
    return _PushEventSettingsPageControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is PushEventSettingsPageControllerProvider &&
        other.branchID == branchID;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, branchID.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin PushEventSettingsPageControllerRef
    on AutoDisposeAsyncNotifierProviderRef<List<AreaModel>> {
  /// The parameter `branchID` of this provider.
  int? get branchID;
}

class _PushEventSettingsPageControllerProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<
        PushEventSettingsPageController,
        List<AreaModel>> with PushEventSettingsPageControllerRef {
  _PushEventSettingsPageControllerProviderElement(super.provider);

  @override
  int? get branchID =>
      (origin as PushEventSettingsPageControllerProvider).branchID;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
