import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:tuple/tuple.dart';
import 'package:uer_flutter/app/core/providers/states/user/user_state.dart';
import 'package:uer_flutter/app/widgets/bars/app_bar.dart';

import '../../../core/models/area/area_model.dart';
import '../../../core/providers/common/common_providers.dart';
import '../../../helpers/snack_bar.dart';
import '../../../routes/routes_name.dart';
import '../../../widgets/text_cell.dart';
import 'push_event_settings_page_controller.dart';

class PushEventSettingsPage extends ConsumerStatefulWidget {
  const PushEventSettingsPage({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _PushEventSettingsPageState();
}

class _PushEventSettingsPageState extends ConsumerState<PushEventSettingsPage> {
  void openEventDetailSettingsPage(AreaModel area) async {
    var resultTuple = await context.pushNamed(
        RoutesName.pushEventDetailSettings.named,
        extra: area.identifier);

    if (resultTuple != null) {
      if (resultTuple is Tuple2) {
        savePushEvents(resultTuple.item1, resultTuple.item2);
      }
    }
  }

  void savePushEvents(List<String> addArr, List<String> delArr) async {
    var user = ref.read(currentUserModelProvider);

    if (user == null) {
      return;
    }

    var controller =
        ref.read(pushEventSettingsPageControllerProvider(user.branch).notifier);

    var resultValue =
        await controller.changeEventNotify(user.login, addArr, delArr);

    if (resultValue == true) {
      showInSnackBar("Успешно сохранено!", context);

      ref.read(currentUserProvider.notifier).updateCurrentUser();
    } else {
      showInSnackBar("Ошибка сохранения! Повторите позднее.", context);
    }
  }

  @override
  Widget build(BuildContext context) {
    var user = ref.watch(currentUserModelProvider);

    var areas =
        ref.watch(pushEventSettingsPageControllerProvider(user?.branch));

    return Scaffold(
        appBar: const CustomAppBar(text: "Выберите участок"),
        body: areas.when(data: (data) {
          if (data.isEmpty) {
            return const CircularProgressIndicator.adaptive();
          }

          return ListView.builder(
            itemCount: data.length,
            itemBuilder: (BuildContext context, int index) {
              var area = data[index];

              callback() {
                openEventDetailSettingsPage(area);
              }

              return TextCell(title: area.name, callback: callback);
            },
          );
        }, error: (error, stacktrace) {
          return const Center(
            child: Text("Ошибка загрузки экрана"),
          );
        }, loading: () {
          return const CircularProgressIndicator.adaptive();
        }));
  }
}
