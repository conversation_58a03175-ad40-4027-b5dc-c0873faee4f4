import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:uer_flutter/app/core/models/area/area_model.dart';
import 'package:uer_flutter/app/core/models/branch/branch_model.dart';
import 'package:uer_flutter/app/core/models/enums/user_role_enum.dart';
import 'package:uer_flutter/app/core/models/enums/user_type_enum.dart';
import 'package:uer_flutter/app/core/providers/common/common_providers.dart';
import 'package:uer_flutter/app/core/providers/states/branches/branches_state.dart';
import 'package:uer_flutter/app/core/providers/users/users_controller.dart';
import 'package:uer_flutter/app/helpers/snack_bar.dart';
import 'package:uer_flutter/app/routes/router_provider.dart';
import 'package:uer_flutter/app/routes/routes_name.dart';

import '../../../core/models/user/user_model.dart';
import '../../../widgets/bars/app_bar.dart';
import '../../../widgets/common_button.dart';
import '../../../widgets/dialogs/actions_alert_dialog.dart';

class UserAddEditPage extends ConsumerStatefulWidget {
  const UserAddEditPage({super.key, this.user});

  final UserModel? user;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _UserAddEditPageState();
}

class _UserAddEditPageState extends ConsumerState<UserAddEditPage> {
  var controllerLogin = TextEditingController(text: "");
  var controllerPassword = TextEditingController(text: "");

  bool simpleAccess = false;
  UserRole role = UserRole.user;
  AreaModel? area;
  BranchModel? branch;
  UserType? userType;

  bool showLoadingIndicator = false;

  @override
  void initState() {
    controllerLogin.text = widget.user?.login ?? "";
    controllerPassword.text = widget.user?.password ?? "";
    simpleAccess = widget.user?.simpleJobAdd ?? false;
    area = ref.read(areaForIDProvider(widget.user?.area));
    branch = ref.read(branchForIDProvider(widget.user?.branch));
    userType = widget.user?.userType;
    role = widget.user?.role ?? UserRole.user;
    super.initState();
  }

  void userAdd() async {
    if (checkUserFields() == false) {
      return;
    }
    setState(() {
      showLoadingIndicator = true;
    });

    final login = controllerLogin.text.toLowerCase();
    final password = controllerPassword.text;

    final user = UserModel(
        login: login,
        password: password,
        simpleJobAdd: simpleAccess,
        role: role,
        area: area?.identifier,
        branch: branch?.identifier,
        userType: userType);

    final result =
        await ref.read(usersControllerProvider(null).notifier).createUser(user);

    setState(() {
      showLoadingIndicator = false;
    });

    if (result == true) {
      showInSnackBar("Пользователь добавлен", context);
      context.pop();
    } else {
      showInSnackBar("Ошибка. Пользователь не добавлен", context);
    }
  }

  void userEdit() async {
    if (checkUserFields() == false) {
      return;
    }
    setState(() {
      showLoadingIndicator = true;
    });

    final password = controllerPassword.text;

    final user = UserModel(
        login: widget.user?.login ?? "",
        password: password,
        simpleJobAdd: simpleAccess,
        role: role,
        area: area?.identifier ?? 0,
        branch: branch?.identifier ?? 0,
        userType: userType);

    final result =
        await ref.read(usersControllerProvider(null).notifier).editUser(user);

    setState(() {
      showLoadingIndicator = false;
    });

    if (result == true) {
      showInSnackBar("Пользователь сохранен", context);
      context.pop();
    } else {
      showInSnackBar("Ошибка. Пользователь не сохранен", context);
    }
  }

  bool checkUserFields() {
    if (showLoadingIndicator == true) {
      return false;
    }
    if (controllerLogin.text.length <= 3) {
      showInSnackBar("Логин должен содержать более 3 символов", context);
      return false;
    }
    if (controllerLogin.text.contains(" ") == true) {
      showInSnackBar("Логин не должен содержать пробелов", context);
      return false;
    }
    if (controllerPassword.text.length < 8) {
      showInSnackBar("Пароль должен содержать 8 или больше символов", context);
      return false;
    }
    if (controllerPassword.text.contains(" ") == true) {
      showInSnackBar("Пароль не должен содержать пробелов", context);
      return false;
    }
    if (role == UserRole.user || role == UserRole.station) {
      if (area == null) {
        showInSnackBar("Выберите участок для пользователя", context);
        return false;
      } else {
        branch = ref.read(branchForIDProvider(area?.branch));
      }
      if (branch == null) {
        showInSnackBar("Ошибка в выборке филиала из участка", context);
        return false;
      }
    } else if (role == UserRole.manager) {
      area = null;
      branch = null;
    } else if (role == UserRole.admin) {
      area = null;
      branch = null;
      userType = UserType.empty;
    } else if (role == UserRole.director) {
      area = null;
      if (branch == null) {
        showInSnackBar("Выберите филиал", context);
        return false;
      }
    }
    return true;
  }

  void changeSimpleAccess(bool value) {
    setState(() {
      simpleAccess = value;
    });
  }

  void selectRole() {
    List<UserRole> roles = UserRole.values;

    List<CommonButtonModel> actionButtons = [];

    for (var i = 0; i < roles.length; i++) {
      var roleTemp = roles[i];

      if (roleTemp == UserRole.client || roleTemp == UserRole.station) {
        continue;
      }

      var btn = CommonButtonModel(
          name: roleTemp.desc,
          callback: () {
            setState(() {
              role = roleTemp;
            });
          });
      actionButtons.add(btn);
    }

    var cancelBtn = CommonButtonModel(name: "Отмена", callback: () {});

    actionButtons.add(cancelBtn);

    showDialog(
        context: context,
        builder: (_) => ActionsAlertDialog(
            title: "Выберите роль", actionButtons: actionButtons));
  }

  void selectArea() async {
    final result = await ref
        .read(routerProvider)
        .pushNamed(RoutesName.selectAreaList.named);

    if (result != null && result is AreaModel) {
      setState(() {
        area = result;
      });
    }
  }

  void selectBranch() {
    List<BranchModel> branchs = ref.read(branchesProvider);

    List<CommonButtonModel> actionButtons = [];

    for (var i = 0; i < branchs.length; i++) {
      var branchTemp = branchs[i];

      var btn = CommonButtonModel(
          name: branchTemp.name,
          callback: () {
            setState(() {
              branch = branchTemp;
            });
          });
      actionButtons.add(btn);
    }

    var cancelBtn = CommonButtonModel(name: "Отмена", callback: () {});

    actionButtons.add(cancelBtn);

    showDialog(
        context: context,
        builder: (_) => ActionsAlertDialog(
            title: "Выберите Филиал", actionButtons: actionButtons));
  }

  void selectTypeUser() {
    List<UserType> userTypes = UserType.values;

    List<CommonButtonModel> actionButtons = [];

    for (var i = 0; i < userTypes.length; i++) {
      var userTypeTemp = userTypes[i];

      var btn = CommonButtonModel(
          name: userTypeTemp.desc,
          callback: () {
            setState(() {
              userType = userTypeTemp;
            });
          });
      actionButtons.add(btn);
    }

    var cancelBtn = CommonButtonModel(name: "Отмена", callback: () {});

    actionButtons.add(cancelBtn);

    showDialog(
        context: context,
        builder: (_) => ActionsAlertDialog(
            title: "Тип пользователя", actionButtons: actionButtons));
  }

  @override
  Widget build(BuildContext context) {
    final editMode = widget.user != null;

    return Scaffold(
        appBar: CustomAppBar(
          text: editMode == true
              ? "Редактирование Пользователя"
              : "Новый Пользователь",
        ),
        body: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text("Логин"),
              TextField(
                controller: controllerLogin,
                readOnly: editMode,
              ),

              // TextField(
              //   readOnly: editMode,
              //   controller: controllerLogin,
              //   onChanged: (value) {},
              // ),
              const SizedBox(
                height: 12,
              ),
              const Text("Пароль"),
              TextField(
                controller: controllerPassword,
              ),
              const SizedBox(
                height: 12,
              ),
              Column(
                children: [
                  const Text("Права доступа:"),
                  const SizedBox(
                    width: 12,
                  ),
                  ElevatedButton(onPressed: selectRole, child: Text(role.desc)),
                  const SizedBox(
                    height: 12,
                  ),
                  const Divider()
                ],
              ),
              if (role == UserRole.user || role == UserRole.station)
                Column(
                  children: [
                    const Text("Выберите участок:"),
                    const SizedBox(
                      width: 12,
                    ),
                    ElevatedButton(
                        onPressed: selectArea,
                        child: Text(area?.name ?? "Выбрать")),
                    const SizedBox(
                      height: 12,
                    ),
                    const Divider()
                  ],
                ),
              if (role == UserRole.director)
                Column(
                  children: [
                    const Text("Выберите филиал:"),
                    const SizedBox(
                      width: 12,
                    ),
                    ElevatedButton(
                        onPressed: selectBranch,
                        child: Text(branch?.name ?? "Выбрать")),
                    const SizedBox(
                      height: 12,
                    ),
                    const Divider()
                  ],
                ),
              if (role != UserRole.admin)
                Column(
                  children: [
                    const Text("Тип пользователя:"),
                    const SizedBox(
                      width: 12,
                    ),
                    ElevatedButton(
                        onPressed: selectTypeUser,
                        child: Text(userType?.desc ?? "Выбрать")),
                    const SizedBox(
                      height: 12,
                    ),
                    const Divider()
                  ],
                ),
              Row(
                children: [
                  const Text(
                    "Упрощенный ввод:",
                  ),
                  const SizedBox(
                    width: 8,
                  ),
                  Switch.adaptive(
                    value: simpleAccess,
                    onChanged: changeSimpleAccess,
                  ),
                ],
              ),
              const SizedBox(
                height: 12,
              ),
              Row(
                children: [
                  const Spacer(),
                  ElevatedButton(
                      onPressed: editMode == true ? userEdit : userAdd,
                      child: Row(
                        children: [
                          showLoadingIndicator
                              ? const CircularProgressIndicator.adaptive()
                              : const SizedBox(),
                          Text(editMode == true ? "Сохранить" : "Добавить"),
                        ],
                      )),
                  const Spacer(),
                ],
              )
            ],
          ),
        ));
  }
}
