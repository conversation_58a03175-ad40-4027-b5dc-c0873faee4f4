import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uer_flutter/app/core/providers/users/users_controller.dart';
import 'package:uer_flutter/app/features/settings/Users/<USER>';
import 'package:uer_flutter/app/routes/router_provider.dart';
import 'package:uer_flutter/app/routes/routes_name.dart';

import '../../../core/models/user/user_model.dart';
import '../../../widgets/bars/app_bar.dart';
import '../../../widgets/search/search_field.dart';

class UsersSettingsPage extends ConsumerStatefulWidget {
  const UsersSettingsPage({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _UsersSettingsPageState();
}

class _UsersSettingsPageState extends ConsumerState<UsersSettingsPage> {
  List<UserModel> usersFiltered = [];
  final _controller = TextEditingController();
  void openAddUser() {
    ref.read(routerProvider).pushNamed(RoutesName.userAddEdit.named);
  }

  void openEditUser(UserModel user) {
    ref
        .read(routerProvider)
        .pushNamed(RoutesName.userAddEdit.named, extra: user);
  }

  void newSearch(String str) {
    final usersState = ref.watch(usersControllerProvider(null));
    final users = usersState.value;

    setState(() {
      usersFiltered = users
              ?.where((element) =>
                  element.login.toLowerCase().contains(str.toLowerCase()))
              .toList() ??
          [];
    });
  }

  @override
  Widget build(BuildContext context) {
    final usersState = ref.watch(usersControllerProvider(null));

    final users = usersState.value;

    final showFiltered = usersFiltered.isNotEmpty == true;

    return Scaffold(
        appBar: CustomAppBar(
          text: "Пользователи",
          actions: [
            IconButton(onPressed: openAddUser, icon: const Icon(Icons.add))
          ],
        ),
        body: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(children: [
              SearchField(
                hint: "Введите логин пользователя",
                controller: _controller,
                callback: newSearch,
              ),
            ]),
            Expanded(
              child: ListView.builder(
                itemCount: showFiltered == true
                    ? usersFiltered.length
                    : users?.length ?? 0,
                itemBuilder: (BuildContext context, int index) {
                  var user = showFiltered == true
                      ? usersFiltered[index]
                      : users![index];

                  callback() {
                    openEditUser(user);
                  }

                  return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        UserCell(user: user, callback: callback),
                        const Divider()
                      ]);
                },
              ),
            ),
          ],
        ));
  }
}
