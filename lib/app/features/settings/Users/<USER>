import 'package:flutter/material.dart';
import 'package:uer_flutter/app/core/models/enums/user_role_enum.dart';
import 'package:uer_flutter/app/core/models/enums/user_type_enum.dart';

import '../../../core/models/user/user_model.dart';

typedef UserCellCallback = Function();

class UserCell extends StatelessWidget {
  const UserCell({super.key, required this.user, required this.callback});

  final UserModel user;
  final UserCellCallback callback;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: callback,
      child: Padding(
        padding: const EdgeInsets.fromLTRB(8, 8, 8, 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Text(
              user.login,
              style: const TextStyle(fontWeight: FontWeight.w700),
            ),
            Text(user.role.desc),
            if (user.userType != null && user.userType != UserType.empty)
              Text(user.userType!.desc),
            const Row(
              children: [Spacer()],
            )
          ],
        ),
      ),
    );
  }
}
