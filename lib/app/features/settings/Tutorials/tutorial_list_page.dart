import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:uer_flutter/app/core/models/enums/tutorial_enum.dart';
import 'package:uer_flutter/app/features/settings/header_row.dart';
import 'package:uer_flutter/gen/assets.gen.dart';

import '../../../core/providers/common/common_providers.dart';
import '../../../routes/routes_name.dart';
import '../../../widgets/bars/app_bar.dart';
import '../settings_button.dart';

class TutorialListPage extends ConsumerStatefulWidget {
  const TutorialListPage({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _TutorialListPageState();
}

class _TutorialListPageState extends ConsumerState<TutorialListPage> {
  void openTutorialDetail(TutorialEnum tutorial) async {
    hideBottomBar(true);

    await context.pushNamed(RoutesName.tutorialDetail.named, queryParameters: {
      "name": tutorial.name,
      "url": tutorial.fullUrl,
    });

    hideBottomBar(false);
  }

  void hideBottomBar(hide) {
    if (hide == true) {
      ref.read(showBottomBarProvider.notifier).hide();
    } else {
      ref.read(showBottomBarProvider.notifier).show();
    }
  }

  @override
  Widget build(BuildContext context) {
    var user = ref.watch(currentUserModelProvider);

    var role = user?.role;

    List<Widget> widgets = [];

    for (var category in TutorialEnumExtension.allCategories()) {
      widgets.add(HeaderRow(title: category));

      for (var tutorial in TutorialEnum.values) {
        if (tutorial.category != category) {
          continue;
        }

        widgets.add(SettingsButton(
          callback: () => openTutorialDetail(tutorial),
          title: tutorial.name,
          iconAsset: Assets.icons.errorOutline,
        ));
      }
    }

    return Scaffold(
      appBar: const CustomAppBar(text: "Видеоинструкции"),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [...widgets],
          ),
        ),
      ),
    );
  }
}
