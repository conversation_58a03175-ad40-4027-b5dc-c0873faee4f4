import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:media_kit/media_kit.dart';
import 'package:media_kit_video/media_kit_video.dart';
import 'package:uer_flutter/app/widgets/bars/app_bar.dart';

class TutorialDetailPage extends ConsumerStatefulWidget {
  const TutorialDetailPage({super.key, required this.name, required this.url});

  final String name;
  final String url;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _TutorialDetailPageState();
}

class _TutorialDetailPageState extends ConsumerState<TutorialDetailPage> {
  late final player = Player();
  late final controller = VideoController(player);

  @override
  void initState() {
    super.initState();

    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
    ]);

    player.open(Media(widget.url));

    // Wait until the fisrt render the avoid posible errors when use an context while the view is rendering
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _init();
    });
  }

  /// play a video from network
  _init() {}

  @override
  void dispose() {
    player.dispose();

    SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeRight,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(text: widget.name),
      body: Center(
        child: SafeArea(
          child: SizedBox(
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height,
            child: Video(
              //aspectRatio: 16 / 9,
              filterQuality: FilterQuality.medium,
              controller: controller,
              onEnterFullscreen: () async {},
              onExitFullscreen: () async {},
              //controls: NoVideoControls,
            ),
          ),
        ),
      ),
    );
  }
}
