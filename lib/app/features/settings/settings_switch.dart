import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uer_flutter/app/styles/colors.dart';
import 'package:uer_flutter/app/styles/fonts.dart';
import 'package:uer_flutter/app/widgets/svg_icon/index.dart';

typedef SettingsSwitchCallback = void Function(bool);

class SettingsSwitch extends ConsumerWidget {
  const SettingsSwitch({
    super.key,
    required this.title,
    required this.iconAsset,
    required this.switchValue,
    required this.callback,
    this.description,
  });

  final String title;
  final String iconAsset;
  final bool switchValue;
  final SettingsSwitchCallback callback;
  final String? description;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          SVGIcon(iconAsset, width: 28.0),
          const SizedBox(width: 16.0),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppFonts.titleSmall,
                ),
                if (description != null) const SizedBox(height: 6.0),
                if (description != null)
                  Text(
                    description!,
                    style: AppFonts.bodyMedium.merge(
                      const TextStyle(
                        color: AppLightColors.medium,
                        fontVariations: [
                          FontVariation.weight(500),
                        ],
                      ),
                    ),
                  ),
              ],
            ),
          ),
          const SizedBox(width: 16.0),
          Switch(
            value: switchValue,
            onChanged: callback,
          ),
        ],
      ),
    );
  }
}
