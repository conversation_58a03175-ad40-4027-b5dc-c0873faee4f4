import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:uer_flutter/app/core/models/branch/branch_model.dart';
import 'package:uer_flutter/app/core/providers/common/common_providers.dart';
import 'package:uer_flutter/app/core/providers/states/areas/areas_state.dart';
import 'package:uer_flutter/app/core/providers/states/statuses/statuses_state.dart';

import '../../../core/models/area/area_model.dart';
import '../../../core/models/status/status_model.dart';
import '../../../helpers/snack_bar.dart';
import '../../../routes/routes_name.dart';
import '../../../widgets/bars/app_bar.dart';

class AreaAddEditPage extends ConsumerStatefulWidget {
  const AreaAddEditPage({super.key, this.area});

  final AreaModel? area;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _AreaAddEditPageState();
}

class _AreaAddEditPageState extends ConsumerState<AreaAddEditPage> {
  var controllerName = TextEditingController(text: "");
  var controllerIDArea1C = TextEditingController(text: "");
  List<StatusModel> currentStatuses = [];
  BranchModel? currentBranch;
  bool startArea = false;

  @override
  void initState() {
    controllerName.text = widget.area?.name ?? "";
    controllerIDArea1C.text = widget.area?.idArea ?? "";

    final branchID = widget.area?.branch;
    final branchEdit = ref.read(branchForIDProvider(branchID));
    currentBranch = branchEdit;

    startArea = widget.area?.start ?? false;

    if (widget.area != null) {
      currentStatuses = widget.area!.jobs
          .map((e) => ref
              .read(statusesProvider)
              .firstWhere((element) => element.identifier == e))
          .toList();
    }

    super.initState();
  }

  bool checkForm() {
    if (currentBranch == null) {
      showInSnackBar("Выберите филиал", context);
      return false;
    }

    final name = controllerName.text;

    if (name.length < 2) {
      showInSnackBar("Слишком короткое название", context);
      return false;
    }

    return true;
  }

  void addNewArea() async {
    if (widget.area != null) {
      return;
    }

    if (checkForm() == false) {
      return;
    }

    final name = controllerName.text;
    final idArea = controllerIDArea1C.text;

    final jobsIds = currentStatuses.map((e) => e.identifier).toList();

    final areaModel = AreaModel(
        identifier: 0,
        name: name,
        branch: currentBranch!.identifier,
        jobs: jobsIds,
        start: startArea,
        idArea: idArea);

    var result = await ref.read(areasProvider.notifier).addArea(areaModel);

    if (result == true) {
      showInSnackBar("Успешно добавлено!", context);
      context.pop(true);
    } else {
      showInSnackBar("Ошибка. Не добавлено, повторите позднее.", context);
    }
  }

  void editArea() async {
    if (widget.area == null) {
      return;
    }

    if (checkForm() == false) {
      return;
    }

    final name = controllerName.text;
    final idArea = controllerIDArea1C.text;

    final jobsIds = currentStatuses.map((e) => e.identifier).toList();

    final areaModel = AreaModel(
        identifier: widget.area!.identifier,
        name: name,
        branch: currentBranch!.identifier,
        jobs: jobsIds,
        start: startArea,
        idArea: idArea);

    var result = await ref.read(areasProvider.notifier).editArea(areaModel);

    if (result == true) {
      showInSnackBar("Успешно сохранено!", context);
      context.pop(true);
    } else {
      showInSnackBar("Ошибка. Не сохранено, повторите позднее.", context);
    }
  }

  void changeStartArea(bool value) {
    setState(() {
      startArea = value;
    });
  }

  void openBranchList() async {
    var result = await context.pushNamed(RoutesName.selectBranchList.named,
        extra: currentBranch?.identifier);

    if (result != null && result is BranchModel) {
      setState(() {
        currentBranch = result;
      });
    }
  }

  void openStatusList() async {
    var result = await context.pushNamed(RoutesName.statusSettings.named,
        extra: currentStatuses);

    if (result != null && result is List<StatusModel>) {
      setState(() {
        currentStatuses = result;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final editMode = widget.area != null;

    final countStatuses = currentStatuses.length;

    return Scaffold(
        appBar: CustomAppBar(
          text: editMode == true ? "Редактирование Участка" : "Новый Участок",
        ),
        body: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text("Название Участка"),
              TextField(
                controller: controllerName,
                onChanged: (value) {},
              ),
              const SizedBox(
                height: 12,
              ),
              const Text("ИД Участка в 1С"),
              TextField(
                controller: controllerIDArea1C,
                onChanged: (value) {},
              ),
              const SizedBox(
                height: 12,
              ),
              Row(
                children: [
                  const Text("Выберите статусы:"),
                  const SizedBox(
                    width: 12,
                  ),
                  ElevatedButton(
                      onPressed: openStatusList,
                      child: Text(countStatuses > 0
                          ? "Выбрано $countStatuses стат."
                          : "Выбрать")),
                ],
              ),
              const SizedBox(
                height: 12,
              ),
              Row(
                children: [
                  const Text("Выберите филиал:"),
                  const SizedBox(
                    width: 12,
                  ),
                  ElevatedButton(
                      onPressed: openBranchList,
                      child: Text(currentBranch?.name ?? "Выбрать")),
                ],
              ),
              const SizedBox(
                height: 12,
              ),
              Row(
                children: [
                  const Text(
                    "Является приемкой:",
                  ),
                  const SizedBox(
                    width: 8,
                  ),
                  Switch.adaptive(
                    value: startArea,
                    onChanged: changeStartArea,
                  ),
                ],
              ),
              const SizedBox(
                height: 12,
              ),
              Row(
                children: [
                  const Spacer(),
                  ElevatedButton(
                      onPressed: editMode == true ? editArea : addNewArea,
                      child: Text(editMode == true ? "Сохранить" : "Добавить")),
                  const Spacer(),
                ],
              )
            ],
          ),
        ));
  }
}
