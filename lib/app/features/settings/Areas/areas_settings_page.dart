import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:uer_flutter/app/core/models/area/area_model.dart';
import 'package:uer_flutter/app/core/providers/states/areas/areas_state.dart';

import '../../../routes/routes_name.dart';
import '../../../widgets/bars/app_bar.dart';
import '../../../widgets/text_cell.dart';

class AreasSettingsPage extends ConsumerStatefulWidget {
  const AreasSettingsPage({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _AreasSettingsPageState();
}

class _AreasSettingsPageState extends ConsumerState<AreasSettingsPage> {
  void openAddAreaPage() async {
    await context.pushNamed(RoutesName.areaAddEdit.named);
  }

  void openEditAreaPage(AreaModel area) async {
    await context.pushNamed(RoutesName.areaAddEdit.named, extra: area);
  }

  @override
  Widget build(BuildContext context) {
    final areas = ref.watch(areasProvider);

    return Scaffold(
        appBar: CustomAppBar(
          text: "Участки",
          actions: [
            IconButton(onPressed: openAddAreaPage, icon: const Icon(Icons.add))
          ],
        ),
        body: ListView.builder(
          itemCount: areas.length,
          itemBuilder: (BuildContext context, int index) {
            var area = areas[index];

            callback() {
              openEditAreaPage(area);
            }

            return Column(children: [
              TextCell(title: area.name, callback: callback),
              const Divider()
            ]);
          },
        ));
  }
}
