import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:uer_flutter/app/core/providers/states/statuses/statuses_state.dart';
import 'package:uer_flutter/app/widgets/bars/app_bar.dart';
import 'package:uer_flutter/app/widgets/common_button.dart';

import '../../../../core/models/status/status_model.dart';
import '../../../../helpers/snack_bar.dart';

class StatusAddEditPage extends ConsumerStatefulWidget {
  const StatusAddEditPage({super.key, required this.status});

  final StatusModel? status;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _StatusAddEditPageState();
}

class _StatusAddEditPageState extends ConsumerState<StatusAddEditPage> {
  var controller = TextEditingController(text: "");
  bool outAgeValue = false;

  void saveAction() async {
    bool resultValue;

    if (widget.status != null) {
      var editModel =
          widget.status!.copyWith(name: controller.text, outage: outAgeValue);

      resultValue =
          await ref.read(statusesProvider.notifier).editStatus(editModel);
    } else {
      final model = StatusModel(
          identifier: 0, name: controller.text, outage: outAgeValue);

      resultValue = await ref.read(statusesProvider.notifier).addStatus(model);
    }

    if (resultValue == true) {
      showInSnackBar("Успешно сохранено!", context);
      context.pop(true);
    } else {
      showInSnackBar("Ошибка. Не сохранено, повторите позднее.", context);
    }
  }

  void changeOutage(bool value) {
    setState(() {
      outAgeValue = value;
    });
  }

  @override
  void initState() {
    controller.text = widget.status?.name ?? "";
    outAgeValue = widget.status?.outage ?? false;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var title = "Новый статус";

    if (widget.status != null) {
      title = "Редактирование статуса";
    }

    return Scaffold(
      appBar: CustomAppBar(text: title),
      body: Padding(
        padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              "Название статуса",
              textAlign: TextAlign.left,
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
            TextField(
              controller: controller,
              onChanged: (value) {},
            ),
            const Divider(),
            Row(
              children: [
                const Text(
                  "Считать простоем",
                  style: TextStyle(fontWeight: FontWeight.w600),
                ),
                const SizedBox(
                  width: 8,
                ),
                Switch.adaptive(
                  value: outAgeValue,
                  onChanged: changeOutage,
                ),
              ],
            ),
            const Divider(),
            CommonButton(
                name: widget.status != null ? "Сохранить" : "Добавить",
                callback: saveAction)
          ],
        ),
      ),
    );
  }
}
