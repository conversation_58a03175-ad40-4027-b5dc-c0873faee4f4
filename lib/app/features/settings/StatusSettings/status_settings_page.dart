import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:uer_flutter/app/core/providers/states/statuses/statuses_state.dart';
import 'package:uer_flutter/app/widgets/bars/app_bar.dart';
import 'package:uer_flutter/app/widgets/text_cell.dart';

import '../../../core/models/status/status_model.dart';
import '../../../routes/routes_name.dart';

class StatusSettingsPage extends ConsumerStatefulWidget {
  const StatusSettingsPage({super.key, this.selectedModels});

  final List<StatusModel>? selectedModels;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _StatusSettingsPageState();
}

class _StatusSettingsPageState extends ConsumerState<StatusSettingsPage> {
  List<StatusModel> selectedStatuses = [];

  @override
  void initState() {
    if (widget.selectedModels != null) {
      selectedStatuses = widget.selectedModels!;
    }

    super.initState();
  }

  void openAddStatusPage() async {
    await context.pushNamed(RoutesName.statusAddEdit.named);
  }

  void openEditStatusPage(StatusModel status) async {
    await context.pushNamed(RoutesName.statusAddEdit.named, extra: status);
  }

  void saveStatuses() {
    context.pop(selectedStatuses);
  }

  @override
  Widget build(BuildContext context) {
    var statuses = ref.watch(statusesProvider);

    return Scaffold(
        appBar: CustomAppBar(
          text: "Статусы",
          actions: [
            widget.selectedModels == null
                ? IconButton(
                    onPressed: openAddStatusPage, icon: const Icon(Icons.add))
                : IconButton(
                    onPressed: saveStatuses, icon: const Icon(Icons.save))
          ],
        ),
        body: ListView.builder(
          itemCount: statuses.length,
          itemBuilder: (BuildContext context, int index) {
            var status = statuses[index];

            callback() {
              if (widget.selectedModels == null) {
                openEditStatusPage(status);
              } else {
                setState(() {
                  if (selectedStatuses.contains(status)) {
                    selectedStatuses.remove(status);
                  } else {
                    selectedStatuses.add(status);
                  }
                });
              }
            }

            final selected = selectedStatuses.contains(status);

            return Column(children: [
              TextCell(
                  title: status.name, callback: callback, selected: selected),
              const Divider()
            ]);
          },
        ));
  }
}
