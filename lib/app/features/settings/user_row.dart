import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uer_flutter/app/core/models/enums/user_role_enum.dart';
import 'package:uer_flutter/app/core/providers/states/user/user_state.dart';

class UserRow extends ConsumerStatefulWidget {
  const UserRow({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _UserRowState();
}

class _UserRowState extends ConsumerState<UserRow> {
  @override
  Widget build(BuildContext context) {
    var userState = ref.watch(currentUserProvider);

    var user = userState.maybeWhen(
        auth: (user) {
          return user;
        },
        orElse: () {});

    return Row(
      children: [
        const Icon(
          Icons.account_circle,
          color: Colors.blue,
          size: 32,
        ),
        const SizedBox(
          width: 8,
        ),
        Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              user?.login ?? "---",
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            Text(
              user?.role.desc ?? "---",
              style: const TextStyle(color: Colors.black45),
            )
          ],
        )
      ],
    );
  }
}
