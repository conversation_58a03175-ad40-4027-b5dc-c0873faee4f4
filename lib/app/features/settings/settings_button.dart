import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uer_flutter/app/styles/colors.dart';
import 'package:uer_flutter/app/styles/fonts.dart';
import 'package:uer_flutter/app/widgets/svg_icon/index.dart';

typedef SettingsButtonCallback = void Function();

class SettingsButton extends ConsumerWidget {
  const SettingsButton({
    super.key,
    required this.title,
    required this.iconAsset,
    required this.callback,
    this.description,
  });

  final String title;
  final String iconAsset;
  final SettingsButtonCallback callback;
  final String? description;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return OutlinedButton(
      style: OutlinedButton.styleFrom(
        padding: const EdgeInsets.symmetric(
          horizontal: 16.0,
          vertical: 24.0,
        ),
        // minimumSize: const Size(double.infinity, 52),
        side: BorderSide.none,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(0.0),
        ),
      ),
      onPressed: callback,
      child: Row(
        children: [
          SVGIcon(iconAsset, width: 28.0),
          const SizedBox(width: 16.0),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.end,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                title,
                style: AppFonts.titleSmall,
              ),
              if (description != null) const SizedBox(height: 6.0),
              if (description != null)
                Text(
                  description!,
                  style: AppFonts.bodyMedium.merge(
                    const TextStyle(
                      color: AppLightColors.medium,
                      fontVariations: [
                        FontVariation.weight(500),
                      ],
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }
}
