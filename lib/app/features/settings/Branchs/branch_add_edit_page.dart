import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:uer_flutter/app/core/models/branch/branch_model.dart';
import 'package:uer_flutter/app/core/providers/states/branches/branches_state.dart';

import '../../../helpers/snack_bar.dart';
import '../../../widgets/bars/app_bar.dart';

class BranchAddEditPage extends ConsumerStatefulWidget {
  const BranchAddEditPage({super.key, this.branch});

  final BranchModel? branch;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _BranchAddEditPageState();
}

class _BranchAddEditPageState extends ConsumerState<BranchAddEditPage> {
  var controllerName = TextEditingController(text: "");
  var controllerShortName = TextEditingController(text: "");
  var controllerIDBranch1C = TextEditingController(text: "");

  @override
  void initState() {
    controllerName.text = widget.branch?.name ?? "";
    controllerShortName.text = widget.branch?.shortName ?? "";
    controllerIDBranch1C.text = widget.branch?.idBranch ?? "";
    super.initState();
  }

  void addNewBranch() async {
    if (widget.branch != null) {
      return;
    }

    if (checkFields() == false) {
      return;
    }

    final name = controllerName.text;
    final shortName = controllerShortName.text;
    final idBranch = controllerIDBranch1C.text;

    var result = await ref.read(branchesProvider.notifier).addBranch(
        BranchModel(
            identifier: 0,
            name: name,
            shortName: shortName,
            idBranch: idBranch));

    if (result == true) {
      showInSnackBar("Успешно добавлено!", context);
      context.pop(true);
    } else {
      showInSnackBar("Ошибка. Не добавлено, повторите позднее.", context);
    }
  }

  void editBranch() async {
    if (widget.branch == null) {
      return;
    }

    if (checkFields() == false) {
      return;
    }

    final name = controllerName.text;
    final shortName = controllerShortName.text;
    final idBranch = controllerIDBranch1C.text;

    var result = await ref.read(branchesProvider.notifier).editBranch(
        BranchModel(
            identifier: widget.branch!.identifier,
            name: name,
            shortName: shortName,
            idBranch: idBranch));

    if (result == true) {
      showInSnackBar("Успешно сохранено!", context);
      context.pop(true);
    } else {
      showInSnackBar("Ошибка. Не сохранено, повторите позднее.", context);
    }
  }

  bool checkFields() {
    final name = controllerName.text;

    if (name.isEmpty == true) {
      showInSnackBar("Введите название филиала", context);
      return false;
    }

    final shortName = controllerShortName.text;

    if (shortName.isEmpty == true) {
      showInSnackBar("Введите короткое название", context);
      return false;
    }

    final idBranch = controllerIDBranch1C.text;

    if (idBranch.isEmpty == true) {
      showInSnackBar("Введите ИД в 1с филиала", context);
      return false;
    }

    return true;
  }

  @override
  Widget build(BuildContext context) {
    final editMode = widget.branch != null;

    return Scaffold(
        appBar: CustomAppBar(
          text: editMode == true ? "Редактирование Филиала" : "Новый Филиал",
        ),
        body: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text("Название Филиала"),
              TextField(
                controller: controllerName,
                onChanged: (value) {},
              ),
              const SizedBox(
                height: 12,
              ),
              const Text("Сокращенное название"),
              TextField(
                controller: controllerShortName,
                onChanged: (value) {},
              ),
              const SizedBox(
                height: 12,
              ),
              const Text("ИД Филиала в 1С"),
              TextField(
                controller: controllerIDBranch1C,
                onChanged: (value) {},
              ),
              const SizedBox(
                height: 12,
              ),
              Row(
                children: [
                  const Spacer(),
                  ElevatedButton(
                      onPressed: editMode == true ? editBranch : addNewBranch,
                      child: Text(editMode == true ? "Сохранить" : "Добавить")),
                  const Spacer(),
                ],
              )
            ],
          ),
        ));
  }
}
