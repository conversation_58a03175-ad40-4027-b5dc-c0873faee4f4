import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:uer_flutter/app/core/providers/states/branches/branches_state.dart';

import '../../../core/models/branch/branch_model.dart';
import '../../../routes/routes_name.dart';
import '../../../widgets/bars/app_bar.dart';
import '../../../widgets/text_cell.dart';

class BranchsSettingsPage extends ConsumerStatefulWidget {
  const BranchsSettingsPage({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _BranchsSettingsPageState();
}

class _BranchsSettingsPageState extends ConsumerState<BranchsSettingsPage> {
  void openAddBranchPage() async {
    await context.pushNamed(RoutesName.branchAddEdit.named);
  }

  void openEditBranchPage(BranchModel branch) async {
    await context.pushNamed(RoutesName.branchAddEdit.named, extra: branch);
  }

  @override
  Widget build(BuildContext context) {
    final branchs = ref.watch(branchesProvider);

    return Scaffold(
        appBar: CustomAppBar(
          text: "Филиалы",
          actions: [
            IconButton(
                onPressed: openAddBranchPage, icon: const Icon(Icons.add))
          ],
        ),
        body: ListView.builder(
          itemCount: branchs.length,
          itemBuilder: (BuildContext context, int index) {
            var branch = branchs[index];

            callback() {
              openEditBranchPage(branch);
            }

            return Column(children: [
              TextCell(title: branch.name, callback: callback),
              const Divider()
            ]);
          },
        ));
  }
}
