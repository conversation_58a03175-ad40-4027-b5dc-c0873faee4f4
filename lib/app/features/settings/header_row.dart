import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uer_flutter/app/styles/colors.dart';
import 'package:uer_flutter/app/styles/fonts.dart';

class HeaderRow extends ConsumerWidget {
  const HeaderRow({super.key, required this.title});

  final String title;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.only(left: 16.0, bottom: 16.0),
      child: Text(
        title,
        style: AppFonts.titleSmall.merge(
          const TextStyle(
            color: AppLightColors.medium,
          ),
        ),
      ),
    );
  }
}
