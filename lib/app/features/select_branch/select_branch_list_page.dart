import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../core/models/branch/branch_model.dart';
import '../../core/providers/states/branches/branches_state.dart';
import '../../widgets/bars/app_bar.dart';

class SelectBranchListPage extends ConsumerStatefulWidget {
  const SelectBranchListPage({
    super.key,
    this.removeBranchID,
  });

  final int? removeBranchID;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _SelectBranchListPageState();
}

class _SelectBranchListPageState extends ConsumerState<SelectBranchListPage> {
  void tapToBranch(BranchModel branchModel) {
    context.pop(branchModel);
  }

  @override
  Widget build(BuildContext context) {
    var branches = ref.watch(branchesProvider);

    var filteredBranches = branches.where((branch) {
      if (branch.identifier == widget.removeBranchID) {
        return false;
      }
      return true;
    }).toList();

    return Scaffold(
        appBar: const CustomAppBar(
          text: "Выберите филиал",
        ),
        body: ListView.builder(
          shrinkWrap: true,
          itemCount: filteredBranches.length,
          itemBuilder: (context, position) {
            final branch = filteredBranches[position];

            callback() {
              tapToBranch(branch);
            }

            return ListTile(
              title: Text(branch.name),
              onTap: callback,
            );
          },
        ));
  }
}
