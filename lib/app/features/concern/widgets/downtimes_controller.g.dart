// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'downtimes_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$downtimesControllerHash() =>
    r'5be84fcf011ee5b022e7870ef14542dbc2d0b205';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$DowntimesController extends BuildlessAutoDisposeAsyncNotifier<
    StatsAverageDowntimesByMonthModel?> {
  late final int? branchId;
  late final int? year;

  FutureOr<StatsAverageDowntimesByMonthModel?> build({
    int? branchId,
    int? year,
  });
}

/// See also [DowntimesController].
@ProviderFor(DowntimesController)
const downtimesControllerProvider = DowntimesControllerFamily();

/// See also [DowntimesController].
class DowntimesControllerFamily
    extends Family<AsyncValue<StatsAverageDowntimesByMonthModel?>> {
  /// See also [DowntimesController].
  const DowntimesControllerFamily();

  /// See also [DowntimesController].
  DowntimesControllerProvider call({
    int? branchId,
    int? year,
  }) {
    return DowntimesControllerProvider(
      branchId: branchId,
      year: year,
    );
  }

  @override
  DowntimesControllerProvider getProviderOverride(
    covariant DowntimesControllerProvider provider,
  ) {
    return call(
      branchId: provider.branchId,
      year: provider.year,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'downtimesControllerProvider';
}

/// See also [DowntimesController].
class DowntimesControllerProvider extends AutoDisposeAsyncNotifierProviderImpl<
    DowntimesController, StatsAverageDowntimesByMonthModel?> {
  /// See also [DowntimesController].
  DowntimesControllerProvider({
    int? branchId,
    int? year,
  }) : this._internal(
          () => DowntimesController()
            ..branchId = branchId
            ..year = year,
          from: downtimesControllerProvider,
          name: r'downtimesControllerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$downtimesControllerHash,
          dependencies: DowntimesControllerFamily._dependencies,
          allTransitiveDependencies:
              DowntimesControllerFamily._allTransitiveDependencies,
          branchId: branchId,
          year: year,
        );

  DowntimesControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.branchId,
    required this.year,
  }) : super.internal();

  final int? branchId;
  final int? year;

  @override
  FutureOr<StatsAverageDowntimesByMonthModel?> runNotifierBuild(
    covariant DowntimesController notifier,
  ) {
    return notifier.build(
      branchId: branchId,
      year: year,
    );
  }

  @override
  Override overrideWith(DowntimesController Function() create) {
    return ProviderOverride(
      origin: this,
      override: DowntimesControllerProvider._internal(
        () => create()
          ..branchId = branchId
          ..year = year,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        branchId: branchId,
        year: year,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<DowntimesController,
      StatsAverageDowntimesByMonthModel?> createElement() {
    return _DowntimesControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is DowntimesControllerProvider &&
        other.branchId == branchId &&
        other.year == year;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, branchId.hashCode);
    hash = _SystemHash.combine(hash, year.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin DowntimesControllerRef
    on AutoDisposeAsyncNotifierProviderRef<StatsAverageDowntimesByMonthModel?> {
  /// The parameter `branchId` of this provider.
  int? get branchId;

  /// The parameter `year` of this provider.
  int? get year;
}

class _DowntimesControllerProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<DowntimesController,
        StatsAverageDowntimesByMonthModel?> with DowntimesControllerRef {
  _DowntimesControllerProviderElement(super.provider);

  @override
  int? get branchId => (origin as DowntimesControllerProvider).branchId;
  @override
  int? get year => (origin as DowntimesControllerProvider).year;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
