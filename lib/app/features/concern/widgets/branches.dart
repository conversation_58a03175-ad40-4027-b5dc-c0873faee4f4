import 'package:flutter/material.dart';
import 'package:uer_flutter/app/core/models/branch/branch_model.dart';
import 'package:uer_flutter/app/core/models/stats/branch_stats_model.dart';
import 'package:uer_flutter/app/core/models/stats/stats_model.dart';
import 'package:uer_flutter/app/styles/colors.dart';
import 'package:uer_flutter/app/styles/fonts.dart';
import 'package:uer_flutter/app/widgets/cards/main_card.dart';
import 'package:uer_flutter/app/widgets/containers/grid.dart';

class ConcernScreenBranches extends StatelessWidget {
  const ConcernScreenBranches({
    super.key,
    required this.branches,
    required this.stats,
    required this.moveToBranch,
  });

  final List<BranchModel> branches;
  final StatsModel stats;
  final void Function(int) moveToBranch;

  @override
  Widget build(BuildContext context) {
    final filteredBranches = branches.toList();

    // Sorting areas by statistics
    filteredBranches.sort((a1, a2) {
      final stat1 = stats.branchStats.firstWhere(
          (stat) => stat.id == a1.identifier,
          orElse: () => const BranchStatsModel(id: 0, count: 0));
      final stat2 = stats.branchStats.firstWhere(
          (stat) => stat.id == a2.identifier,
          orElse: () => const BranchStatsModel(id: 0, count: 0));
      return (stat2.count ?? 0) - (stat1.count ?? 0);
    });

    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Text(
        'Филиалы',
        style: AppFonts.titleSmall.merge(
          const TextStyle(color: AppLightColors.medium),
        ),
      ),
      const SizedBox(height: 16.0),
      Grid(
        length: filteredBranches.length,
        gap: 4.0,
        builder: (rowIndex, itemIndex) {
          return _buildMainCard(
            filteredBranches[rowIndex + itemIndex],
            stats,
            moveToBranch,
          );
        },
        columns: 1,
      ),
    ]);
  }

  Widget _buildMainCard(
    BranchModel branch,
    StatsModel? stats,
    void Function(int) moveToBranch,
  ) {
    var value = 0;

    for (var stat in stats!.branchStats) {
      if (branch.identifier == stat.id) {
        value = stat.count;
      }
    }

    return MainCard(
      title: branch.name,
      description: 'Заказов: $value',
      onTap: () => moveToBranch(branch.identifier),
    );
  }
}
