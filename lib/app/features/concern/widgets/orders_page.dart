import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uer_flutter/app/core/models/stats/tags_stats_model.dart';
import 'package:uer_flutter/app/features/concern/widgets/orders_controller.dart';
import 'package:uer_flutter/app/styles/fonts.dart';
import 'package:uer_flutter/app/widgets/bars/app_bar.dart';
import 'package:uer_flutter/app/widgets/charts/helpers/indicator.dart';
import 'package:uer_flutter/app/widgets/charts/pie_chart/index.dart';
import 'package:uer_flutter/app/widgets/interactive/chip.dart';
import 'package:uer_flutter/app/widgets/interactive/custom_card.dart';

class OrdersPage extends ConsumerStatefulWidget {
  const OrdersPage({
    super.key,
    this.branchId,
  });

  final int? branchId;

  @override
  ConsumerState<OrdersPage> createState() => _OrdersPageState();
}

class _OrdersPageState extends ConsumerState<OrdersPage> {
  @override
  Widget build(BuildContext context) {
    final model =
        ref.watch(ordersControllerProvider.call(branchId: widget.branchId));
    final data = model.value;

    return Scaffold(
      appBar: CustomAppBar(
        text: 'Концерн',
      ),
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          SegmentedButton<bool?>(
            segments: [
              ButtonSegment(
                  value: null,
                  label: Text(
                    'Все заказы',
                    style: AppFonts.labelMedium,
                  )),
              ButtonSegment(
                  value: false,
                  label: Text(
                    'Архивные',
                    style: AppFonts.labelMedium,
                  )),
              ButtonSegment(
                  value: true,
                  label: Text(
                    'В работе',
                    style: AppFonts.labelMedium,
                  )),
            ],
            selected: {
              ref
                  .watch(
                      ordersControllerProvider.call(branchId: widget.branchId))
                  .value
                  ?.inWork
            },
            onSelectionChanged: (value) {
              ref
                  .read(ordersControllerProvider
                      .call(branchId: widget.branchId)
                      .notifier)
                  .changeInWork(branchId: widget.branchId, inWork: value.first);
            },
          ),
          SizedBox(height: 16),
          Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
            Text(
              'Всего заказов',
              style: AppFonts.titleMedium,
            ),
            Text(
              '${data?.firstInitial?.totalItems ?? 0} шт.',
              style: AppFonts.titleMedium,
            ),
          ]),
          SizedBox(height: 16),
          CustomCard(
            child:
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              Wrap(
                // crossAxisAlignment: CrossAxisAlignment.start,
                spacing: 12.0,
                runSpacing: 12.0,
                children: [
                  CustomPieChart(
                      data: CustomPieChartProps(
                          items: (data?.firstInitial?.stats ?? []).map((stat) {
                    return CustomPieChartProp(
                      value: stat.count?.toDouble() ?? 0.0,
                      color: stat.tags?.first.getColor() ?? Colors.cyan,
                    );
                  }).toList())),
                  ConstrainedBox(
                    constraints: BoxConstraints(
                      minWidth: 200.0,
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        ...(data?.firstInitial?.stats ?? []).map((stat) {
                          return Indicator(
                            color: stat.tags?.first.getColor() ?? Colors.cyan,
                            text:
                                '${stat.tags?.first.getName()} — ${stat.percentage}%',
                          );
                        }),
                      ],
                    ),
                  ),
                ],
              ),
            ]),
          ),
          SizedBox(height: 32),
          Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
            Expanded(
              child: Text(
                'Включающих тег “${data?.selectedItemTag.getName()}”',
                style: AppFonts.titleMedium,
              ),
            ),
            Text(
              '${data?.secondWithFilters?.totalItems ?? 0} шт.',
              style: AppFonts.titleMedium,
            ),
          ]),
          SizedBox(height: 16.0),
          Wrap(spacing: 4.0, runSpacing: 4.0, children: [
            ...ItemTag.values.map((value) {
              return CustomChip(
                label: value.getName(),
                color: value.getColor(),
                isEnabled: value == data?.selectedItemTag,
                onTap: () {
                  print(widget.branchId);
                  ref
                      .read(ordersControllerProvider
                          .call(branchId: widget.branchId)
                          .notifier)
                      .setSelectedTag(
                        branchId: widget.branchId,
                        tag: value,
                      );
                },
              );
            })
          ]),
          SizedBox(height: 16.0),
          CustomCard(
            child:
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              Wrap(
                spacing: 12.0,
                runSpacing: 12.0,
                children: [
                  if ((data?.secondWithFilters?.stats ?? []).isEmpty)
                    Text(
                      'Статистики с такими фильтрами пока нет...',
                      style: AppFonts.labelMedium,
                    ),
                  CustomPieChart(
                      data: CustomPieChartProps(
                          items: (data?.secondWithFilters?.stats ?? [])
                              .map((stat) {
                    final tags = [...(stat.tags) ?? []];

                    tags.sort((i, j) {
                      if (i == data?.selectedItemTag) {
                        return -1;
                      }
                      return 1;
                    });
                    return CustomPieChartProp(
                      value: stat.count?.toDouble() ?? 0.0,
                      color: tags.first.getColor(),
                    );
                  }).toList())),
                  // const SizedBox(width: 12.0),
                  ConstrainedBox(
                    constraints: BoxConstraints(
                      minWidth: 200.0,
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        ...(data?.secondWithFilters?.stats ?? []).map((stat) {
                          final tags = [...(stat.tags) ?? []];

                          tags.sort((i, j) {
                            if (i == data?.selectedItemTag) {
                              return -1;
                            }
                            return 1;
                          });

                          return Indicator(
                            color: tags.first.getColor(),
                            text:
                                '${tags.map((tag) => tag.getName()).join(", ")} — ${stat.percentage}%',
                          );
                        }),
                      ],
                    ),
                  ),
                ],
              ),
            ]),
          ),
          SizedBox(height: 16),
        ],
      ),
    );
  }
}
