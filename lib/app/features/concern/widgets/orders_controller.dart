import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:uer_flutter/app/core/models/stats/stats_model.dart';
import 'package:uer_flutter/app/core/models/stats/tags_stats_model.dart';
import 'package:uer_flutter/app/core/providers/notifier_mouted.dart';
import 'package:uer_flutter/app/core/services/api/service_provider.dart';

part 'orders_controller.g.dart';

@riverpod
class OrdersController extends _$OrdersController with NotifierMounted {
  @override
  FutureOr<TagsScreenStatsModel?> build({
    required int? branchId,
  }) async {
    ref.onDispose(setUnmounted);

    return TagsScreenStatsModel(
      inWork: null,
      branchId: branchId,
      firstInitial: await getTagsStats(
        branchId: branchId,
      ),
      selectedItemTag: ItemTag.itemDefault,
      secondWithFilters: await getTagsStats(
        branchId: branchId,
        selectedTag: ItemTag.itemDefault,
      ),
    );
  }

  Future<TagsStatsModel?> getTagsStats({
    int? branchId,
    ItemTag? selectedTag,
    bool? inWork,
  }) async {
    final apiStats = ref.watch(statsRepositoryProvider);

    return await apiStats.getTagsStats(
        data: GetTagsStatsModel(
      branch: branchId,
      selectedTag: selectedTag,
      inWork: inWork,
    ));
  }

  Future<void> changeInWork({int? branchId, bool? inWork}) async {
    state = const AsyncValue.loading();

    final newState = AsyncValue.data(state.value?.copyWith(
      inWork: inWork,
      firstInitial: await getTagsStats(
          branchId: branchId ?? state.value?.branchId, inWork: inWork),
      secondWithFilters: await getTagsStats(
        branchId: branchId ?? state.value?.branchId,
        selectedTag: state.value?.selectedItemTag,
        inWork: inWork,
      ),
    ));

    if (mounted) {
      state = newState;
    }
  }

  Future<void> setSelectedTag({
    int? branchId,
    required ItemTag tag,
  }) async {
    state = const AsyncValue.loading();

    final newState = AsyncValue.data(state.value?.copyWith(
      selectedItemTag: tag,
      secondWithFilters: await getTagsStats(
        branchId: branchId ?? state.value?.branchId,
        selectedTag: tag,
      ),
    ));

    if (mounted) {
      state = newState;
    }
  }

  Future<void> refresh({
    int? branchId,
  }) async {
    state = const AsyncValue.loading();

    final newState = await AsyncValue.guard<TagsScreenStatsModel?>(() async {
      return TagsScreenStatsModel(
        firstInitial:
            await getTagsStats(branchId: branchId ?? state.value?.branchId),
        secondWithFilters: await getTagsStats(
          branchId: branchId ?? state.value?.branchId,
          selectedTag: ItemTag.itemDefault,
        ),
      );
    });

    if (mounted) {
      state = newState;
    }
  }
}
