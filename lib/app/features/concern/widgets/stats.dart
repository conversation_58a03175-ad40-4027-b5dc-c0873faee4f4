import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:uer_flutter/app/core/models/stats/stats_model.dart';
import 'package:uer_flutter/app/features/concern/widgets/stats_downtimes.dart';
import 'package:uer_flutter/app/features/concern/widgets/stats_orders.dart';
import 'package:uer_flutter/app/features/concern/widgets/stats_time_repair.dart';
import 'package:uer_flutter/app/routes/routes_name.dart';
import 'package:uer_flutter/app/styles/colors.dart';
import 'package:uer_flutter/app/styles/fonts.dart';
import 'package:uer_flutter/app/widgets/interactive/elevated_button.dart';

class ConcernScreenStats extends StatelessWidget {
  const ConcernScreenStats({
    super.key,
    required this.stats,
    this.branchID,
  });

  final StatsModel stats;
  final int? branchID;

  @override
  Widget build(BuildContext context) {
    void openConcernBranchStats() {
      context.pushNamed(
        RoutesName.concernBranchStats.named,
        extra: branchID,
      );
    }

    void openWorkStats() {
      context.pushNamed(
        RoutesName.workStats.named,
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Text(
          'Статистика',
          style: AppFonts.titleSmall.merge(
            const TextStyle(color: AppLightColors.medium),
          ),
        ),
        const SizedBox(height: 16.0),
        StatsOrders(
          branchId: branchID,
        ),
        const SizedBox(height: 24.0),
        StatsTimeRepair(
          branchId: branchID,
        ),
        const SizedBox(height: 24.0),
        StatsDowntimes(
          branchId: branchID,
        ),
        // const SizedBox(height: 16.0),
        // StatsColumn(children: [
        //   StatRow(
        //     'Затрачено рабочих дней',
        //     (stats.allHours / 8).toStringAsFixed(1),
        //   ),
        //   StatRow(
        //     'Из них простоев',
        //     (stats.allOutAgeHours / 8).toStringAsFixed(1),
        //   ),
        //   StatRow(
        //     'Всего выполнено заказов',
        //     (stats.archiveCount.toString()),
        //   ),
        //   StatRow('Всего выполнено заказов за год',
        //       (stats.archiveInYearCount.toString())),
        //   StatRow(
        //     'Просроченных заказов',
        //     (stats.overDeadlineCount.toString()),
        //   ),
        //   StatRow(
        //     'Просрочка в днях',
        //     (stats.overDeadlineAvr.toStringAsFixed(1)),
        //   ),
        // ]),
        const SizedBox(height: 24.0),
        CustomElevatedButton(
          onPressed: () {
            openConcernBranchStats();
          },
          text: 'Смотреть всю статистику',
          type: ElevatedButtonTypes.inverse,
        ),
        const SizedBox(height: 24.0),
        CustomElevatedButton(
          onPressed: () {
            openWorkStats();
          },
          text: 'Отчёт по сравнению работ',
          type: ElevatedButtonTypes.initial,
        ),
        // const SizedBox(height: 12.0),
        // Opacity(
        //   opacity: 0.5,
        //   child: CustomElevatedButton(
        //     onPressed: () {},
        //     text: 'Статистика просроченных',
        //   ),
        // ),
        const SizedBox(height: 12.0),
        CustomElevatedButton(
          onPressed: () {
            context.pushNamed(RoutesName.sszHoursStats.named);
          },
          text: 'Сравнение работ между участками',
        ),
        const SizedBox(height: 8.0),
        Text(
          'Сравнение работ между участками филиалов по определённым участкам',
          style: AppFonts.bodySmall.merge(
            const TextStyle(color: AppLightColors.medium),
          ),
        ),
      ],
    );
  }
}
