import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uer_flutter/app/features/concern/widgets/downtimes_controller.dart';
import 'package:uer_flutter/app/styles/fonts.dart';
import 'package:uer_flutter/app/widgets/charts/helpers/indicator.dart';
import 'package:uer_flutter/app/widgets/charts/rounded_line_chart/index.dart';
import 'package:uer_flutter/app/widgets/interactive/chip.dart';
import 'package:uer_flutter/app/widgets/interactive/custom_card.dart';

class StatsDowntimes extends ConsumerStatefulWidget {
  const StatsDowntimes({super.key, this.branchId});

  final int? branchId;

  @override
  ConsumerState<StatsDowntimes> createState() => _StatsDowntimesState();
}

class _StatsDowntimesState extends ConsumerState<StatsDowntimes> {
  int currentYear = DateTime.now().year;
  Color? selectedColor;

  @override
  void initState() {
    super.initState();
  }

  // Function to generate a color based on an index
  Color generateColor(int index) {
    final hue = (index * 137.5) % 360; // Golden angle approximation
    return HSVColor.fromAHSV(1.0, hue, 0.8, 0.8).toColor();
  }

  @override
  Widget build(BuildContext context) {
    final data = ref.watch(downtimesControllerProvider.call(
        branchId: widget.branchId, year: currentYear));
    final stats = data.value;

    final Map<Color, List<FlSpot>> preparedStats = {};
    final Map<String, Color> typeToColor = {};

    void prepareStats() {
      if (stats?.monthlyAverages == null || stats!.monthlyAverages!.isEmpty) {
        return; // Если данные отсутствуют, выходим из функции
      }

      preparedStats.clear();
      typeToColor.clear();

      int colorIndex = 0; // Index to generate colors

      stats.monthlyAverages!.forEach((month, monthlyData) {
        monthlyData.byType?.forEach((type, value) {
          if (value.average == null) {
            return; // Пропускаем null значения
          }

          if (!typeToColor.containsKey(type)) {
            typeToColor[type] = generateColor(colorIndex++);
            preparedStats[typeToColor[type]!] = [];
          }

          preparedStats[typeToColor[type]]?.add(FlSpot(
            double.tryParse(month) ?? 0.0,
            value.average!.toDouble() / 8,
          ));
        });
      });
    }

    prepareStats();

    return CustomCard(
      onTap: () {},
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
          Text(
            'Среднее время простоев ($currentYear)',
            style: AppFonts.titleSmall,
          ),
          Icon(Icons.chevron_right_rounded)
        ]),
        SizedBox(height: 12.0),
        Wrap(spacing: 4.0, runSpacing: 4.0, children: [
          for (int year = 2022; year <= DateTime.now().year; year += 1)
            CustomChip(
              isEnabled: year == currentYear,
              label: year.toString(),
              onTap: () {
                setState(() => currentYear = year);
              },
            ),
        ]),
        SizedBox(height: 12.0),
        if (preparedStats.isNotEmpty)
          RoundedLineChart(
            extendedSpots: preparedStats,
            selectedColor: selectedColor,
          ),
        SizedBox(height: 8.0),
        Wrap(children: [
          ...typeToColor.entries.map((entry) {
            final text = entry.key;
            final color = entry.value;

            return GestureDetector(
              onTap: () {
                setState(() {
                  selectedColor = selectedColor == color ? null : color;
                });
              },
              child: Indicator(
                color: selectedColor == color || selectedColor == null
                    ? color
                    : color.withOpacity(0.2),
                text: text,
              ),
            );
          })
        ]),
      ]),
    );
  }
}
