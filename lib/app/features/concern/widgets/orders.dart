import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:uer_flutter/app/core/models/enums/common_items_screens_enum.dart';
import 'package:uer_flutter/app/core/models/stats/stats_model.dart';
import 'package:uer_flutter/app/routes/routes_name.dart';
import 'package:uer_flutter/app/styles/colors.dart';
import 'package:uer_flutter/app/styles/fonts.dart';
import 'package:uer_flutter/app/widgets/cards/main_card.dart';
import 'package:uer_flutter/app/widgets/containers/grid.dart';

class ConcernScreenOrders extends StatefulWidget {
  const ConcernScreenOrders({
    super.key,
    required this.stats,
    this.branchID,
  });

  final StatsModel? stats;
  final int? branchID;

  @override
  State<StatefulWidget> createState() => _ConcernScreenOrdersState();
}

class _ConcernScreenOrdersState extends State<ConcernScreenOrders> {
  @override
  Widget build(BuildContext context) {
    void openToAllItemsPage() {
      context.pushNamed(
        RoutesName.commonItems.named,
        extra: CommonItemsScreens.inWork.getData(
          branchID: widget.branchID,
        ),
      );
    }

    void openToOverDeadLineItemsPage() {
      context.pushNamed(
        RoutesName.overdeadlineItems.named,
        extra: widget.branchID,
      );
    }

    void openArchive() {
      context.pushNamed(
        RoutesName.commonItems.named,
        extra: CommonItemsScreens.archive.getData(
          branchID: widget.branchID,
        ),
      );
    }

    void openArchiveInYear() {
      context.pushNamed(
        RoutesName.commonItems.named,
        extra: CommonItemsScreens.archiveInYear.getData(
          branchID: widget.branchID,
        ),
      );
    }

    void openRecentDeadLine() {
      context.pushNamed(
        RoutesName.commonItems.named,
        extra: CommonItemsScreens.recentShipmentItems.getData(
          branchID: widget.branchID,
        ),
      );
    }

    final List<void Function()> onTapFunctions = [
      openToAllItemsPage,
      openArchive,
      openToOverDeadLineItemsPage,
      openArchiveInYear,
      openRecentDeadLine,
    ];

    final dataFunctions = [
      widget.stats?.getStatsInWork(),
      widget.stats?.getStatsArchive(),
      widget.stats?.getStatsOverDeadLine(),
      widget.stats?.getStatsArchiveYear(),
      widget.stats?.getStatsRecentDeadLine(),
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Заказы',
          style: AppFonts.titleSmall.merge(
            const TextStyle(color: AppLightColors.medium),
          ),
        ),
        const SizedBox(height: 16.0),
        Grid(
          length: dataFunctions.length,
          gap: 4.0,
          builder: (rowIndex, itemIndex) {
            final dataList =
                dataFunctions[rowIndex + itemIndex]?.entries.toList();

            return MainCard(
              title: dataList?[0].key ?? '',
              description: dataList?[0].value ?? '',
              onTap: onTapFunctions[rowIndex + itemIndex],
            );
          },
          columns: 2,
        ),
        // GanttChart(),
      ],
    );
  }
}
