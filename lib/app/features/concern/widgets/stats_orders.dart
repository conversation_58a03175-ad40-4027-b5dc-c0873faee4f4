import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:uer_flutter/app/features/concern/concern_controller.dart';
import 'package:uer_flutter/app/routes/routes_name.dart';
import 'package:uer_flutter/app/styles/fonts.dart';
import 'package:uer_flutter/app/widgets/charts/helpers/indicator.dart';
import 'package:uer_flutter/app/widgets/charts/pie_chart/index.dart';
import 'package:uer_flutter/app/widgets/interactive/custom_card.dart';

class StatsOrders extends ConsumerStatefulWidget {
  const StatsOrders({
    super.key,
    required this.branchId,
  });

  final int? branchId;

  @override
  ConsumerState<StatsOrders> createState() => _StatsTimeRepairState();
}

class _StatsTimeRepairState extends ConsumerState<StatsOrders> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final model =
        ref.watch(concernControllerProvider.call(branchId: widget.branchId));
    final data = model.value?.tagsStats;

    return CustomCard(
      onTap: () {
        context.pushNamed(
          RoutesName.statsOrders.named,
          extra: widget.branchId,
        );
      },
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        const Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
          Text(
            'Заказы по тегам',
            style: AppFonts.titleSmall,
          ),
          Icon(Icons.chevron_right_rounded)
        ]),
        // Text(
        //   'Концерн',
        //   style: AppFonts.labelMedium.merge(
        //     const TextStyle(color: AppLightColors.medium, height: 1.4),
        //   ),
        // ),
        const SizedBox(height: 12.0),
        Wrap(
          // crossAxisAlignment: CrossAxisAlignment.start,
          spacing: 12.0,
          runSpacing: 12.0,
          children: [
            CustomPieChart(
                data: CustomPieChartProps(
                    items: (data?.stats ?? []).map((stat) {
              return CustomPieChartProp(
                value: stat.count?.toDouble() ?? 0.0,
                color: stat.tags?.first.getColor() ?? Colors.cyan,
              );
            }).toList())),
            ConstrainedBox(
              constraints: BoxConstraints(
                minWidth: 200.0,
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  ...(data?.stats ?? []).map((stat) {
                    return Indicator(
                      color: stat.tags?.first.getColor() ?? Colors.cyan,
                      text:
                          '${stat.tags?.first.getName()} — ${stat.percentage}%',
                    );
                  }),
                ],
              ),
            ),
          ],
        ),
      ]),
    );
  }
}
