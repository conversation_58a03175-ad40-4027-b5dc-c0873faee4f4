import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:uer_flutter/app/core/models/Stats_outage/stats_sum_works_time_model.dart';
import 'package:uer_flutter/app/core/providers/notifier_mouted.dart';
import 'package:uer_flutter/app/core/services/api/service_provider.dart';

part 'downtimes_controller.g.dart';

@riverpod
class DowntimesController extends _$DowntimesController with NotifierMounted {
  @override
  FutureOr<StatsAverageDowntimesByMonthModel?> build({
    int? branchId,
    int? year,
  }) {
    ref.onDispose(setUnmounted);

    return getDowntimes(branchId: branchId, year: year);
  }

  Future<StatsAverageDowntimesByMonthModel?> getDowntimes({
    int? branchId,
    int? year,
  }) async {
    final apiStats = ref.watch(statsRepositoryProvider);

    return await apiStats.getAverageDowntimes(
      year: year ?? DateTime.now().year,
      branchId: branchId,
    );
  }

  Future<void> refresh() async {
    state = const AsyncValue.loading();

    var newState = await AsyncValue.guard(() async {
      return await getDowntimes();
    });

    if (mounted) {
      state = newState;
    }
  }
}
