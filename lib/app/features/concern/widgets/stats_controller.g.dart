// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'stats_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$statsControllerHash() => r'eaf467e161fb44bfa80df52b0699713dd603c8f5';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$StatsController
    extends BuildlessAutoDisposeAsyncNotifier<StatsSumWorkHoursByMonthModel?> {
  late final int? branchId;
  late final int? year;

  FutureOr<StatsSumWorkHoursByMonthModel?> build({
    int? branchId,
    int? year,
  });
}

/// See also [StatsController].
@ProviderFor(StatsController)
const statsControllerProvider = StatsControllerFamily();

/// See also [StatsController].
class StatsControllerFamily
    extends Family<AsyncValue<StatsSumWorkHoursByMonthModel?>> {
  /// See also [StatsController].
  const StatsControllerFamily();

  /// See also [StatsController].
  StatsControllerProvider call({
    int? branchId,
    int? year,
  }) {
    return StatsControllerProvider(
      branchId: branchId,
      year: year,
    );
  }

  @override
  StatsControllerProvider getProviderOverride(
    covariant StatsControllerProvider provider,
  ) {
    return call(
      branchId: provider.branchId,
      year: provider.year,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'statsControllerProvider';
}

/// See also [StatsController].
class StatsControllerProvider extends AutoDisposeAsyncNotifierProviderImpl<
    StatsController, StatsSumWorkHoursByMonthModel?> {
  /// See also [StatsController].
  StatsControllerProvider({
    int? branchId,
    int? year,
  }) : this._internal(
          () => StatsController()
            ..branchId = branchId
            ..year = year,
          from: statsControllerProvider,
          name: r'statsControllerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$statsControllerHash,
          dependencies: StatsControllerFamily._dependencies,
          allTransitiveDependencies:
              StatsControllerFamily._allTransitiveDependencies,
          branchId: branchId,
          year: year,
        );

  StatsControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.branchId,
    required this.year,
  }) : super.internal();

  final int? branchId;
  final int? year;

  @override
  FutureOr<StatsSumWorkHoursByMonthModel?> runNotifierBuild(
    covariant StatsController notifier,
  ) {
    return notifier.build(
      branchId: branchId,
      year: year,
    );
  }

  @override
  Override overrideWith(StatsController Function() create) {
    return ProviderOverride(
      origin: this,
      override: StatsControllerProvider._internal(
        () => create()
          ..branchId = branchId
          ..year = year,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        branchId: branchId,
        year: year,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<StatsController,
      StatsSumWorkHoursByMonthModel?> createElement() {
    return _StatsControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is StatsControllerProvider &&
        other.branchId == branchId &&
        other.year == year;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, branchId.hashCode);
    hash = _SystemHash.combine(hash, year.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin StatsControllerRef
    on AutoDisposeAsyncNotifierProviderRef<StatsSumWorkHoursByMonthModel?> {
  /// The parameter `branchId` of this provider.
  int? get branchId;

  /// The parameter `year` of this provider.
  int? get year;
}

class _StatsControllerProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<StatsController,
        StatsSumWorkHoursByMonthModel?> with StatsControllerRef {
  _StatsControllerProviderElement(super.provider);

  @override
  int? get branchId => (origin as StatsControllerProvider).branchId;
  @override
  int? get year => (origin as StatsControllerProvider).year;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
