// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'orders_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$ordersControllerHash() => r'8561f4224122bfe040c9849b6c572c7674fd7d7c';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$OrdersController
    extends BuildlessAutoDisposeAsyncNotifier<TagsScreenStatsModel?> {
  late final int? branchId;

  FutureOr<TagsScreenStatsModel?> build({
    required int? branchId,
  });
}

/// See also [OrdersController].
@ProviderFor(OrdersController)
const ordersControllerProvider = OrdersControllerFamily();

/// See also [OrdersController].
class OrdersControllerFamily extends Family<AsyncValue<TagsScreenStatsModel?>> {
  /// See also [OrdersController].
  const OrdersControllerFamily();

  /// See also [OrdersController].
  OrdersControllerProvider call({
    required int? branchId,
  }) {
    return OrdersControllerProvider(
      branchId: branchId,
    );
  }

  @override
  OrdersControllerProvider getProviderOverride(
    covariant OrdersControllerProvider provider,
  ) {
    return call(
      branchId: provider.branchId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'ordersControllerProvider';
}

/// See also [OrdersController].
class OrdersControllerProvider extends AutoDisposeAsyncNotifierProviderImpl<
    OrdersController, TagsScreenStatsModel?> {
  /// See also [OrdersController].
  OrdersControllerProvider({
    required int? branchId,
  }) : this._internal(
          () => OrdersController()..branchId = branchId,
          from: ordersControllerProvider,
          name: r'ordersControllerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$ordersControllerHash,
          dependencies: OrdersControllerFamily._dependencies,
          allTransitiveDependencies:
              OrdersControllerFamily._allTransitiveDependencies,
          branchId: branchId,
        );

  OrdersControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.branchId,
  }) : super.internal();

  final int? branchId;

  @override
  FutureOr<TagsScreenStatsModel?> runNotifierBuild(
    covariant OrdersController notifier,
  ) {
    return notifier.build(
      branchId: branchId,
    );
  }

  @override
  Override overrideWith(OrdersController Function() create) {
    return ProviderOverride(
      origin: this,
      override: OrdersControllerProvider._internal(
        () => create()..branchId = branchId,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        branchId: branchId,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<OrdersController,
      TagsScreenStatsModel?> createElement() {
    return _OrdersControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is OrdersControllerProvider && other.branchId == branchId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, branchId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin OrdersControllerRef
    on AutoDisposeAsyncNotifierProviderRef<TagsScreenStatsModel?> {
  /// The parameter `branchId` of this provider.
  int? get branchId;
}

class _OrdersControllerProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<OrdersController,
        TagsScreenStatsModel?> with OrdersControllerRef {
  _OrdersControllerProviderElement(super.provider);

  @override
  int? get branchId => (origin as OrdersControllerProvider).branchId;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
