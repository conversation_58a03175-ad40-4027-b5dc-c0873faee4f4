import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uer_flutter/app/features/concern/widgets/tasks_stats.dart';
import 'package:uer_flutter/app/styles/colors.dart';
import 'package:uer_flutter/app/styles/fonts.dart';
import 'package:uer_flutter/app/widgets/bars/app_bar.dart';
import 'package:uer_flutter/app/widgets/interactive/chip.dart';
import 'package:uer_flutter/app/widgets/interactive/elevated_button.dart';

class WorkStatsScreen extends ConsumerStatefulWidget {
  const WorkStatsScreen({super.key});

  @override
  ConsumerState<WorkStatsScreen> createState() => _WorkStatsScreen();
}

class _WorkStatsScreen extends ConsumerState<WorkStatsScreen> {
  final powerController = TextEditingController();
  final turnoversController = TextEditingController();
  int selectedYear = DateTime.now().year;

  @override
  void initState() {
    super.initState();
    HardwareKeyboard.instance.addHandler((event) {
      if (event.logicalKey.debugName == 'Enter') {
        _search();
      }
      return false;
    });
  }

  void _search() {
    final state = ref.read(tasksStatsControllerProvider.call()).value;
    if (state?.selectedStatTask?.id == null) return;

    ref.read(tasksStatsControllerProvider.call().notifier).searchStatTasks(
          taskId: state!.selectedStatTask!.id!,
          power: int.tryParse(powerController.text),
          turnovers: int.tryParse(turnoversController.text),
          year: selectedYear,
        );
  }

  @override
  Widget build(BuildContext context) {
    final data = ref.watch(tasksStatsControllerProvider.call());
    final state = data.value;

    return Scaffold(
      appBar: CustomAppBar(
        text: 'Отчёт по сравнению работ',
      ),
      body: Column(children: [
        SizedBox(height: 12.0),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 12.0),
          child: Wrap(spacing: 4.0, runSpacing: 4.0, children: [
            for (int year = 2022; year <= DateTime.now().year; year += 1)
              CustomChip(
                isEnabled: year == selectedYear,
                label: year.toString(),
                onTap: () {
                  setState(() => selectedYear = year);
                },
              ),
          ]),
        ),
        SizedBox(height: 12.0),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 12.0),
          child: Row(
              spacing: 8.0,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  flex: 3,
                  child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        TextField(
                          onChanged: (value) {
                            ref
                                .read(tasksStatsControllerProvider
                                    .call()
                                    .notifier)
                                .searchTasks(query: value);
                          },
                          style: AppFonts.labelLarge,
                          decoration: InputDecoration(
                            labelText: 'Название работы',
                          ),
                          onSubmitted: (_) {
                            _search();
                          },
                        ),
                        SizedBox(height: 8.0),
                        Wrap(spacing: 4.0, runSpacing: 4.0, children: [
                          if (state?.selectedStatTask?.name != null)
                            CustomChip(
                              isEnabled: true,
                              label: state!.selectedStatTask!.name!,
                              onTap: () {
                                ref
                                    .read(tasksStatsControllerProvider
                                        .call()
                                        .notifier)
                                    .resetSelectedTask();
                              },
                            ),
                          if (state?.selectedStatTask == null)
                            ...(state?.foundStatsTasks ?? [])
                                .where((statTask) =>
                                    statTask.id != state?.selectedStatTask?.id)
                                .map((statTask) {
                              return CustomChip(
                                isEnabled: false,
                                label: statTask.name ?? 'name',
                                onTap: () {
                                  ref
                                      .read(tasksStatsControllerProvider
                                          .call()
                                          .notifier)
                                      .selectTask(statTask);
                                },
                              );
                            }),
                        ])
                      ]),
                ),
                Expanded(
                  child: TextField(
                    controller: powerController,
                    keyboardType: TextInputType.numberWithOptions(),
                    style: AppFonts.labelLarge,
                    decoration: InputDecoration(
                      labelText: 'Мощность',
                    ),
                    onSubmitted: (_) {
                      _search();
                    },
                  ),
                ),
                Expanded(
                  child: TextField(
                    controller: turnoversController,
                    keyboardType: TextInputType.numberWithOptions(),
                    style: AppFonts.labelLarge,
                    decoration: InputDecoration(
                      labelText: 'Обороты',
                    ),
                    onSubmitted: (_) {
                      _search();
                    },
                  ),
                ),
                CustomElevatedButton(
                  onPressed: _search,
                  text: 'Поиск',
                ),
              ]),
        ),
        SizedBox(height: 8.0),
        _buildRow(['Рем №', 'Оборудование', 'н/ч', 'Филиал']),
        Divider(height: 1.0),
        if (data.isLoading) CircularProgressIndicator.adaptive(),
        if (state?.foundTasks == null || state!.foundTasks!.isEmpty)
          Expanded(
            child: Center(
              child: Text(
                'Работы не найдены',
                style: AppFonts.labelLarge,
              ),
            ),
          ),
        if (state?.foundTasks != null && state!.foundTasks!.isNotEmpty)
          Expanded(
            child: ListView.builder(
              itemCount: state.foundTasks?.length ?? 0,
              itemBuilder: (context, index) {
                final task = state.foundTasks![index];
                final isEven = index % 2 == 0;
                return _buildRow([
                  task.repairNumber ?? '',
                  task.equipmentName ?? '',
                  task.standardHours.toString(),
                  task.branch ?? '',
                ],
                    backgroundColor: isEven
                        ? AppLightColors.preWhite
                        : AppLightColors.prepreWhite);
              },
            ),
          ),
      ]),
    );
  }

  Widget _buildRow(
    List<String> children, {
    Color backgroundColor = AppLightColors.prepreWhite,
  }) {
    return Container(
      padding: const EdgeInsets.all(8.0),
      color: backgroundColor,
      child: Row(children: [
        ...children.map((text) => Expanded(
              child: SelectableText(
                text,
                style: AppFonts.labelLarge,
              ),
            )),
      ]),
    );
  }
}
