import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:uer_flutter/app/core/models/Stats_outage/tasks_stats_model.dart';
import 'package:uer_flutter/app/core/providers/notifier_mouted.dart';
import 'package:uer_flutter/app/core/services/api/service_provider.dart';

part 'tasks_stats.g.dart';

@riverpod
class TasksStatsController extends _$TasksStatsController with NotifierMounted {
  @override
  FutureOr<TasksStatsModel?> build({
    int? branchId,
    int? year,
  }) async {
    ref.onDispose(setUnmounted);

    // Загружаем начальные данные при инициализации
    return TasksStatsModel();
  }

  /// Метод для поиска статистики задач
  Future<TasksStatsModel?> searchStatTasks({
    int? year,
    required String taskId,
    int? power,
    int? turnovers,
  }) async {
    final apiStats = ref.watch(statsRepositoryProvider);

    try {
      // Получаем данные из API
      final foundTasks = await apiStats.getWorkStats(
        year: year ?? DateTime.now().year,
        taskId: taskId,
        power: power,
        turnovers: turnovers,
      );

      // Обновляем состояние
      final currentState = state.valueOrNull ?? TasksStatsModel();
      final newState = currentState.copyWith(foundTasks: foundTasks);

      state = AsyncValue.data(newState);
      return newState;
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
      return null;
    }
  }

  /// Метод для поиска работ
  Future<void> searchTasks({
    required String query,
    int? year,
  }) async {
    final apiStats = ref.read(statsRepositoryProvider);

    try {
      // Показываем состояние загрузки
      state = const AsyncValue.loading();

      // Выполняем поиск
      final foundStatsTasks = await apiStats.searchTasks(
        query: query,
        year: year,
      );

      // Обновляем состояние
      final currentState = state.valueOrNull ?? TasksStatsModel();
      final newState = currentState.copyWith(foundStatsTasks: foundStatsTasks);

      state = AsyncValue.data(newState);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  /// Метод для выбора конкретной задачи
  void selectTask(TaskStatsModel task) {
    final currentState = state.valueOrNull ?? TasksStatsModel();
    final newState = currentState.copyWith(selectedStatTask: task);

    state = AsyncValue.data(newState);
  }

  /// Метод для сброса выбранной задачи
  void resetSelectedTask() {
    final currentState = state.valueOrNull ?? TasksStatsModel();
    final newState = currentState.copyWith(selectedStatTask: null);

    state = AsyncValue.data(newState);
  }
}
