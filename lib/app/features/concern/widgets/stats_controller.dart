import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:uer_flutter/app/core/models/Stats_outage/stats_sum_works_time_model.dart';
import 'package:uer_flutter/app/core/providers/notifier_mouted.dart';
import 'package:uer_flutter/app/core/services/api/service_provider.dart';

part 'stats_controller.g.dart';

@riverpod
class StatsController extends _$StatsController with NotifierMounted {
  @override
  FutureOr<StatsSumWorkHoursByMonthModel?> build({
    int? branchId,
    int? year,
  }) {
    ref.onDispose(setUnmounted);

    return getStats(branchId: branchId, year: year);
  }

  Future<StatsSumWorkHoursByMonthModel?> getStats({
    int? branchId,
    int? year,
  }) async {
    final apiStats = ref.watch(statsRepositoryProvider);

    return await apiStats.getAverageTimeRepair(
      year: year ?? DateTime.now().year,
      branchId: branchId,
    );
  }

  Future<void> refresh() async {
    state = const AsyncValue.loading();

    var newState = await AsyncValue.guard(() async {
      return await getStats();
    });

    if (mounted) {
      state = newState;
    }
  }
}
