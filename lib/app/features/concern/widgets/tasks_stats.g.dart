// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tasks_stats.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$tasksStatsControllerHash() =>
    r'983bc7a2a87df32015021c47cb503742a2d9eefe';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$TasksStatsController
    extends BuildlessAutoDisposeAsyncNotifier<TasksStatsModel?> {
  late final int? branchId;
  late final int? year;

  FutureOr<TasksStatsModel?> build({
    int? branchId,
    int? year,
  });
}

/// See also [TasksStatsController].
@ProviderFor(TasksStatsController)
const tasksStatsControllerProvider = TasksStatsControllerFamily();

/// See also [TasksStatsController].
class TasksStatsControllerFamily extends Family<AsyncValue<TasksStatsModel?>> {
  /// See also [TasksStatsController].
  const TasksStatsControllerFamily();

  /// See also [TasksStatsController].
  TasksStatsControllerProvider call({
    int? branchId,
    int? year,
  }) {
    return TasksStatsControllerProvider(
      branchId: branchId,
      year: year,
    );
  }

  @override
  TasksStatsControllerProvider getProviderOverride(
    covariant TasksStatsControllerProvider provider,
  ) {
    return call(
      branchId: provider.branchId,
      year: provider.year,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'tasksStatsControllerProvider';
}

/// See also [TasksStatsController].
class TasksStatsControllerProvider extends AutoDisposeAsyncNotifierProviderImpl<
    TasksStatsController, TasksStatsModel?> {
  /// See also [TasksStatsController].
  TasksStatsControllerProvider({
    int? branchId,
    int? year,
  }) : this._internal(
          () => TasksStatsController()
            ..branchId = branchId
            ..year = year,
          from: tasksStatsControllerProvider,
          name: r'tasksStatsControllerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$tasksStatsControllerHash,
          dependencies: TasksStatsControllerFamily._dependencies,
          allTransitiveDependencies:
              TasksStatsControllerFamily._allTransitiveDependencies,
          branchId: branchId,
          year: year,
        );

  TasksStatsControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.branchId,
    required this.year,
  }) : super.internal();

  final int? branchId;
  final int? year;

  @override
  FutureOr<TasksStatsModel?> runNotifierBuild(
    covariant TasksStatsController notifier,
  ) {
    return notifier.build(
      branchId: branchId,
      year: year,
    );
  }

  @override
  Override overrideWith(TasksStatsController Function() create) {
    return ProviderOverride(
      origin: this,
      override: TasksStatsControllerProvider._internal(
        () => create()
          ..branchId = branchId
          ..year = year,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        branchId: branchId,
        year: year,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<TasksStatsController,
      TasksStatsModel?> createElement() {
    return _TasksStatsControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is TasksStatsControllerProvider &&
        other.branchId == branchId &&
        other.year == year;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, branchId.hashCode);
    hash = _SystemHash.combine(hash, year.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin TasksStatsControllerRef
    on AutoDisposeAsyncNotifierProviderRef<TasksStatsModel?> {
  /// The parameter `branchId` of this provider.
  int? get branchId;

  /// The parameter `year` of this provider.
  int? get year;
}

class _TasksStatsControllerProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<TasksStatsController,
        TasksStatsModel?> with TasksStatsControllerRef {
  _TasksStatsControllerProviderElement(super.provider);

  @override
  int? get branchId => (origin as TasksStatsControllerProvider).branchId;
  @override
  int? get year => (origin as TasksStatsControllerProvider).year;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
