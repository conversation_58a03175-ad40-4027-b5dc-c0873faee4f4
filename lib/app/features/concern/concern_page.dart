import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:uer_flutter/app/core/providers/states/branches/branches_state.dart';
import 'package:uer_flutter/app/features/concern/widgets/branches.dart';
import 'package:uer_flutter/app/features/concern/widgets/orders.dart';
import 'package:uer_flutter/app/features/concern/widgets/stats.dart';
import 'package:uer_flutter/app/widgets/bars/app_bar.dart';
import 'package:uer_flutter/app/widgets/items/item_list.dart';
import 'package:uer_flutter/app/widgets/search/search_panel.dart';

import '../../core/models/item/item_model.dart';
import '../../core/models/search/search_model.dart';
import '../../helpers/constants.dart';
import '../../helpers/debouncer.dart';
import '../../routes/routes_name.dart';
import 'concern_controller.dart';

class MainConcernPage extends ConsumerStatefulWidget {
  const MainConcernPage({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _MainConcernPageState();
}

class _MainConcernPageState extends ConsumerState<MainConcernPage> {
  SearchModel _searchModel = const SearchModel();
  final _debouncer = Debouncer(milliseconds: 650);

  @override
  void initState() {
    _searchModel = defaultSearch();
    super.initState();
  }

  SearchModel defaultSearch() {
    return const SearchModel(
      finished: false,
      projection: PROJECTION_SEARCH_ITEM,
    );
  }

  void updateSearchTerm(SearchModel? searchModelNew) {
    _debouncer.run(() {
      if (searchModelNew == null) {
        setState(() {
          _searchModel = defaultSearch();
        });
        return;
      } else {
        _searchModel = defaultSearch();
      }

      setState(() {
        if (searchModelNew.searchField != null) {
          _searchModel =
              _searchModel.copyWith(searchField: searchModelNew.searchField);
        } else if (searchModelNew.searchClient != null) {
          _searchModel =
              _searchModel.copyWith(searchClient: searchModelNew.searchClient);
        } else if (searchModelNew.mainID != null) {
          _searchModel = _searchModel.copyWith(mainID: searchModelNew.mainID);
        } else if (searchModelNew.searchEquipment != null) {
          _searchModel = _searchModel.copyWith(
              searchEquipment: searchModelNew.searchEquipment);
        }
      });
    });
  }

  void moveToItem(ItemModel item) {
    var mainID = item.mainID ?? "0";

    context.pushNamed(RoutesName.item.named, pathParameters: {
      'mid': mainID,
    });
  }

  void moveToBranch(int branchID) {
    context.pushNamed(RoutesName.branch.named, pathParameters: {
      'bid': "$branchID",
    });
  }

  void openNotificationHistory() {
    context.pushNamed(RoutesName.notificationHistory.named);
  }

  void refresh() async {
    await ref
        .read(concernControllerProvider.call(branchId: null).notifier)
        .refresh();
  }

  @override
  Widget build(BuildContext context) {
    var branches = ref.watch(branchesProvider);
    var model = ref.watch(concernControllerProvider.call(branchId: null));

    var showItems = _searchModel.showSearchResult();
    var stats = model.value;

    // var showCards = stats != null && branches.isNotEmpty;

    return Scaffold(
      appBar: CustomAppBar(
        text: "Концерн",
        actions: [
          IconButton(
            onPressed: openNotificationHistory,
            icon: const Icon(Icons.notifications),
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () => Future.sync(() => refresh()),
        child: LayoutBuilder(
          builder: (context, constraints) {
            if (constraints.maxWidth < 750) {
              // Мобильная версия
              return SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SearchPanel(callback: updateSearchTerm),
                    const SizedBox(height: 16.0),
                    if (showItems)
                      SizedBox(
                        height: 600.0,
                        child: ItemList(
                          callback: moveToItem,
                          searchModel: _searchModel,
                        ),
                      ),
                    const SizedBox(height: 16.0),
                    ConcernScreenOrders(stats: stats?.stats),
                    const SizedBox(height: 50.0),
                    if (stats?.stats != null)
                      ConcernScreenStats(stats: stats!.stats!),
                    const SizedBox(height: 50.0),
                    if (stats?.stats != null)
                      ConcernScreenBranches(
                        branches: branches,
                        stats: stats!.stats!,
                        moveToBranch: moveToBranch,
                      ),
                  ],
                ),
              );
            } else {
              // Десктопная версия
              return SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SearchPanel(callback: updateSearchTerm),
                    const SizedBox(height: 16.0),
                    if (showItems)
                      SizedBox(
                        height: 600.0,
                        child: ItemList(
                          callback: moveToItem,
                          searchModel: _searchModel,
                        ),
                      ),
                    if (showItems) const SizedBox(height: 16.0),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              if (showItems) const SizedBox(height: 50.0),
                              ConcernScreenOrders(stats: stats?.stats),
                              const SizedBox(height: 50.0),
                              if (stats?.stats != null)
                                ConcernScreenBranches(
                                  branches: branches,
                                  stats: stats!.stats!,
                                  moveToBranch: moveToBranch,
                                ),
                              if (stats != null) const SizedBox(height: 50.0),
                            ],
                          ),
                        ),
                        const SizedBox(width: 16.0),
                        if (stats?.stats != null)
                          Expanded(
                            child: Column(
                              children: [
                                ConcernScreenStats(stats: stats!.stats!),
                                const SizedBox(height: 20.0),
                              ],
                            ),
                          ),
                      ],
                    ),
                  ],
                ),
              );
            }
          },
        ),
      ),
    );
  }
}
