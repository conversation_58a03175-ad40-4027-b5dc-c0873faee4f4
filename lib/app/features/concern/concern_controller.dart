import 'dart:async';

import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:uer_flutter/app/core/models/stats/tags_stats_model.dart';
import 'package:uer_flutter/app/core/providers/notifier_mouted.dart';
import 'package:uer_flutter/app/core/providers/states/branches/branches_state.dart';

import '../../core/models/stats/stats_model.dart';
import '../../core/services/api/service_provider.dart';

part 'concern_controller.g.dart';

@riverpod
class ConcernController extends _$ConcernController with NotifierMounted {
  @override
  FutureOr<ScreenStatsModel?> build({
    int? branchId,
  }) async {
    ref.onDispose(setUnmounted);

    return ScreenStatsModel(
      stats: await getStats(),
      tagsStats: await getTagsStats(branchId: branchId),
    );
  }

  Future<StatsModel?> getStats() async {
    final branchs = ref.watch(branchesProvider);
    var branchsIds = branchs.map((e) => e.identifier).toList();

    if (branchsIds.isEmpty) {
      return null;
    }

    var apiClient = ref.watch(itemRepositoryProvider);
    return await apiClient.getStats(branchsIds, []);
  }

  Future<TagsStatsModel?> getTagsStats({
    int? branchId,
  }) async {
    final branchs = ref.watch(branchesProvider);
    var branchsIds = branchs.map((e) => e.identifier).toList();

    if (branchsIds.isEmpty) {
      return null;
    }

    var apiClient = ref.watch(statsRepositoryProvider);
    return await apiClient.getTagsStats(
        data: GetTagsStatsModel(
      branch: branchId,
    ));
  }

  Future<void> refresh() async {
    state = const AsyncValue.loading();

    var newState = await AsyncValue.guard(() async {
      return ScreenStatsModel(
        stats: await getStats(),
        tagsStats: await getTagsStats(),
      );
    });

    if (mounted) {
      state = newState;
    }
  }
}
