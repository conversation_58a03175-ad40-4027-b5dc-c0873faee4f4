// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'concern_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$concernControllerHash() => r'73ad532c860ed839d8c8942d9478703177538e6e';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$ConcernController
    extends BuildlessAutoDisposeAsyncNotifier<ScreenStatsModel?> {
  late final int? branchId;

  FutureOr<ScreenStatsModel?> build({
    int? branchId,
  });
}

/// See also [ConcernController].
@ProviderFor(ConcernController)
const concernControllerProvider = ConcernControllerFamily();

/// See also [ConcernController].
class ConcernControllerFamily extends Family<AsyncValue<ScreenStatsModel?>> {
  /// See also [ConcernController].
  const ConcernControllerFamily();

  /// See also [ConcernController].
  ConcernControllerProvider call({
    int? branchId,
  }) {
    return ConcernControllerProvider(
      branchId: branchId,
    );
  }

  @override
  ConcernControllerProvider getProviderOverride(
    covariant ConcernControllerProvider provider,
  ) {
    return call(
      branchId: provider.branchId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'concernControllerProvider';
}

/// See also [ConcernController].
class ConcernControllerProvider extends AutoDisposeAsyncNotifierProviderImpl<
    ConcernController, ScreenStatsModel?> {
  /// See also [ConcernController].
  ConcernControllerProvider({
    int? branchId,
  }) : this._internal(
          () => ConcernController()..branchId = branchId,
          from: concernControllerProvider,
          name: r'concernControllerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$concernControllerHash,
          dependencies: ConcernControllerFamily._dependencies,
          allTransitiveDependencies:
              ConcernControllerFamily._allTransitiveDependencies,
          branchId: branchId,
        );

  ConcernControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.branchId,
  }) : super.internal();

  final int? branchId;

  @override
  FutureOr<ScreenStatsModel?> runNotifierBuild(
    covariant ConcernController notifier,
  ) {
    return notifier.build(
      branchId: branchId,
    );
  }

  @override
  Override overrideWith(ConcernController Function() create) {
    return ProviderOverride(
      origin: this,
      override: ConcernControllerProvider._internal(
        () => create()..branchId = branchId,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        branchId: branchId,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<ConcernController, ScreenStatsModel?>
      createElement() {
    return _ConcernControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ConcernControllerProvider && other.branchId == branchId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, branchId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ConcernControllerRef
    on AutoDisposeAsyncNotifierProviderRef<ScreenStatsModel?> {
  /// The parameter `branchId` of this provider.
  int? get branchId;
}

class _ConcernControllerProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<ConcernController,
        ScreenStatsModel?> with ConcernControllerRef {
  _ConcernControllerProviderElement(super.provider);

  @override
  int? get branchId => (origin as ConcernControllerProvider).branchId;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
