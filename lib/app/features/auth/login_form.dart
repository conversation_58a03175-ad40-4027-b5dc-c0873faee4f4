import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uer_flutter/app/styles/fonts.dart';

class LoginForm extends ConsumerStatefulWidget {
  const LoginForm({super.key, required this.callback});

  final LoginFormCallback callback;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _LoginFormState();
}

typedef LoginFormCallback = void Function(String login, String password);

class _LoginFormState extends ConsumerState<LoginForm> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  String login = "";
  String password = "";

  final TextEditingController _usernameController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();

  bool _obscureText = true;

  void _toggleObscureText() {
    setState(() {
      _obscureText = !_obscureText;
    });
  }

  void checkValidateAndRequest() {
    if (_formKey.currentState?.validate() == true) {
      _formKey.currentState?.save();
      // Process data.

      widget.callback(login, password);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          TextFormField(
            controller: _usernameController,
            onSaved: (String? value) {
              login = value ?? "";
            },
            textInputAction: TextInputAction.next,
            autofillHints: const [AutofillHints.username],
            style: AppFonts.bodyLarge,
            decoration: const InputDecoration(
              hintText: 'Введите логин',
            ),
            autocorrect: false,
            validator: (String? value) {
              if (value == null || value.isEmpty == true) {
                return 'Введите логин';
              }
              return null;
            },
          ),
          const SizedBox(height: 16.0),
          TextFormField(
            controller: _passwordController,
            obscureText: _obscureText,
            onSaved: (String? value) {
              password = value ?? "";
            },
            autofillHints: const [AutofillHints.password],
            onFieldSubmitted: (value) {
              checkValidateAndRequest();
            },
            textInputAction: TextInputAction.done,
            autocorrect: false,
            style: AppFonts.bodyLarge,
            decoration: InputDecoration(
                hintText: 'Введите пароль',
                suffixIcon: IconButton(
                    color: _obscureText ? Colors.grey : Colors.red,
                    onPressed: _toggleObscureText,
                    icon: const Icon(Icons.remove_red_eye))),
            validator: (String? value) {
              if (value == null || value.isEmpty == true) {
                return 'Введите пароль';
              }
              return null;
            },
          ),
          const SizedBox(
            height: 12,
          ),
          Center(
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 16.0),
              child: ElevatedButton(
                onPressed: () {
                  checkValidateAndRequest();
                },
                child: const Text('Войти'),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
