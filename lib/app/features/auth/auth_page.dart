import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uer_flutter/app/core/providers/states/user/user_state.dart';
import 'package:uer_flutter/app/styles/colors.dart';
import 'package:uer_flutter/app/styles/fonts.dart';
import 'package:uer_flutter/app/widgets/bars/app_bar.dart';
import 'package:uer_flutter/app/widgets/containers/wrapper.dart';
import 'package:uer_flutter/app/widgets/svg_icon/index.dart';
import 'package:uer_flutter/gen/assets.gen.dart';

import 'login_form.dart';

class AuthPage extends ConsumerStatefulWidget {
  const AuthPage({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _AuthPageState();
}

class _AuthPageState extends ConsumerState<AuthPage> {
  void auth(String login, String password) async {
    final currentUser = ref.read(currentUserProvider.notifier);
    currentUser.auth(login, password);
  }

  @override
  Widget build(BuildContext context) {
    final currentUserState = ref.watch(currentUserProvider);

    final error = currentUserState.maybeWhen(
      error: (message) {
        return message;
      },
      orElse: () {},
    );

    return Scaffold(
      appBar: const CustomAppBar(text: "Авторизация"),
      body: Column(
        children: [
          Wrapper(
            maxWidth: 320.0,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SVGIcon(
                  Assets.icons.uerLogo,
                  width: 200.0,
                  color: AppLightColors.accent,
                ),
                const SizedBox(
                  height: 8.0,
                ),
                const Text(
                  "Уралэлектроремонт",
                  style: AppFonts.titleLarge,
                ),
                const SizedBox(
                  height: 16.0,
                ),
                LoginForm(
                  callback: auth,
                ),
                const SizedBox(
                  height: 8,
                ),
                error?.isNotEmpty == true
                    ? Text(
                        "Ошибка: $error",
                        style: AppFonts.labelMedium.merge(const TextStyle(
                          fontFamily: AppFonts.monoFontFamily,
                          color: AppLightColors.error,
                        )),
                      )
                    : const SizedBox(),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
