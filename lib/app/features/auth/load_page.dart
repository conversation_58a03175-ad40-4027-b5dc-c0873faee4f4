import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:uer_flutter/app/core/providers/states/areas/areas_state.dart';
import 'package:uer_flutter/app/core/providers/states/branches/branches_state.dart';
import 'package:uer_flutter/app/core/providers/states/statuses/statuses_state.dart';
import 'package:uer_flutter/app/styles/fonts.dart';

import '../../routes/routes_name.dart';

class LoadPage extends ConsumerStatefulWidget {
  const LoadPage({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _LoadPageState();
}

class _LoadPageState extends ConsumerState<LoadPage> {
  void goToMain() {
    Future.delayed(Duration.zero, () {
      context.goNamed(RoutesName.main.named);
    });
  }

  @override
  Widget build(BuildContext context) {
    var areas = ref.watch(areasProvider);
    var branchs = ref.watch(branchesProvider);
    var statuses = ref.watch(statusesProvider);

    if (areas.isNotEmpty && branchs.isNotEmpty && statuses.isNotEmpty) {
      goToMain();
    }

    var loadText = "Загрузка";

    if (areas.isNotEmpty) {
      loadText += ".";
    }

    if (branchs.isNotEmpty) {
      loadText += ".";
    }

    if (statuses.isNotEmpty) {
      loadText += ".";
    }

    return Scaffold(
      body: Center(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const CircularProgressIndicator.adaptive(),
            const SizedBox(width: 12.0),
            Text(
              loadText,
              style: AppFonts.labelLarge,
            )
          ],
        ),
      ),
    );
  }
}
