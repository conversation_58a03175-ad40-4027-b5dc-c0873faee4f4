import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:uer_flutter/app/core/providers/notifier_mouted.dart';

import '../../../core/models/component/component_model.dart';
import '../../../core/models/enums/component_type_enum.dart';
import '../../../core/models/item/item_model.dart';
import '../../../core/models/search/search_model.dart';
import '../../../core/services/api/service_provider.dart';
import '../../../helpers/constants.dart';

part 'area_controller.g.dart';

@riverpod
class AreaController extends _$AreaController with NotifierMounted {
  SearchModel search = const SearchModel(
      newBool: false, finished: false, projection: PROJECTION_COMPONENTS_LIST);

  @override
  FutureOr<List<ItemModel>> build(int areaID) {
    ref.onDispose(setUnmounted);

    search = search.copyWith(area: areaID);

    return _fetchItems(null);
  }

  // void addItem(ItemModel item) {
  //   if (state.value == null) {
  //     AsyncValue.data([item]);
  //   }

  //   var items = state.value!;

  //   for (var i = 0; i < items.length; i++) {
  //     var tempItem = items[i];
  //     if (tempItem.mainID == item.mainID) {
  //       items.removeAt(i);
  //       items.insert(i, item);

  //       AsyncValue.data(items);
  //       return;
  //     }
  //   }
  // }

  Future<List<ItemModel>> _fetchItems(String? searchText) async {
    SearchModel searchModel;
    if (searchText?.isNotEmpty == true) {
      searchModel = search.copyWith(searchField: searchText);
    } else {
      searchModel = search;
    }
    var apiClient = ref.read(itemRepositoryProvider);
    var items = await apiClient.getItems(searchModel);
    return items ?? [];
  }

  Future<void> getItems(String? searchText) async {
    state = const AsyncValue.loading();

    var newState = await AsyncValue.guard(() async {
      return _fetchItems(searchText);
    });

    if (mounted) {
      state = newState;
    }
  }

  ItemModel? findItemForComponent(ComponentModel model) {
    var items = state.value;

    if (items == null) {
      return null;
    }

    for (var item in items) {
      for (var comp in item.components) {
        if (comp.identifier == model.identifier) {
          return item;
        }
      }
    }

    return null;
  }

  void updateItem(ItemModel item) async {
    if (state.value == null) {
      return;
    }

    var newArr = state.value!.toList();

    for (var i = 0; i < state.value!.length; i++) {
      var value = state.value![i];

      if (value.mainID == item.mainID) {
        newArr[i] = item;
        break;
      }
    }

    var newState = await AsyncValue.guard(() async {
      return newArr;
    });

    if (mounted) {
      state = newState;
    }
  }
}

@riverpod
List<ComponentModel> areaComponents(Ref ref, int areaID) {
  final itemProvider = ref.watch(areaControllerProvider(areaID));

  final areaIdentifier = areaID;

  var items = itemProvider.value;

  if (items == null) {
    return [];
  }

  List<ComponentModel> listComponents = [];

  for (var item in items) {
    if (item.assembled == true) {
      var components = item.components.where((component) {
        if (component.type == ComponentType.stator ||
            component.type == ComponentType.inductor) {
          return true;
        }
        return false;
      }).toList();

      var comp = components.first;
      comp = comp.copyWith(tags: item.tags);

      if (comp.currentArea == areaIdentifier) {
        listComponents.add(comp);
      } else if (comp.accessArea?.contains(areaIdentifier) == true) {
        listComponents.add(comp);
      }
    } else {
      for (var comp in item.components) {
        comp = comp.copyWith(tags: item.tags);

        if (comp.currentArea == areaIdentifier) {
          listComponents.add(comp);
        } else if (comp.accessArea?.contains(areaIdentifier) == true) {
          listComponents.add(comp);
        }
      }
    }
  }

  listComponents.sort((a, b) {
    var aDate = a.newHistory?.last.updatedAt ?? 0;
    var bDate = b.newHistory?.last.updatedAt ?? 0;
    return bDate.compareTo(aDate);
  });

  return listComponents;
}
