import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:uer_flutter/app/widgets/bars/app_bar.dart';
import 'package:uer_flutter/app/widgets/component_cell.dart';

import '../../../core/providers/common/common_providers.dart';
import '../../../helpers/debouncer.dart';
import '../../../routes/routes_name.dart';
import 'area_controller.dart';

class AreaPage extends ConsumerStatefulWidget {
  const AreaPage({super.key, required this.areaID});

  final int areaID;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _AreaPageState();
}

class _AreaPageState extends ConsumerState<AreaPage> {
  void searchForString(str) {
    _debouncer.run(() {
      ref.read(areaControllerProvider(widget.areaID).notifier).getItems(str);
    });
  }

  void moveToComponent(String componentID) {
    context.pushNamed(RoutesName.component.named, pathParameters: {
      'cid': componentID,
    });
  }

  final _debouncer = Debouncer(milliseconds: 650);

  @override
  Widget build(BuildContext context) {
    var components = ref.watch(areaComponentsProvider(widget.areaID));
    var area = ref.watch(areaForIDProvider(widget.areaID));

    return Scaffold(
      appBar: CustomAppBar(text: area?.name ?? "---"),
      body: Padding(
        padding: const EdgeInsets.fromLTRB(16.0, 16.0, 16.0, 0.0),
        child: Column(
          children: [
            TextField(
              decoration: const InputDecoration(
                hintText: 'Введите рем. номер',
                prefixIcon: Icon(Icons.search),
              ),
              onChanged: searchForString,
            ),
            const SizedBox(height: 16.0),
            const Divider(height: 1.0),
            Expanded(
              child: ListView.builder(
                itemCount: components.length,
                itemBuilder: (context, position) {
                  var component = components[position];

                  var item = ref
                      .read(areaControllerProvider(widget.areaID).notifier)
                      .findItemForComponent(component);

                  callback() {
                    moveToComponent(component.identifier);
                  }

                  if (item != null) {
                    return ComponentCell(
                      item: item,
                      component: component,
                      currentAreaID: widget.areaID,
                      callback: callback,
                    );
                  }
                  return const SizedBox();
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
