// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'area_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$areaComponentsHash() => r'73396573eca604cab318a61dafc43d9c1923bc17';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [areaComponents].
@ProviderFor(areaComponents)
const areaComponentsProvider = AreaComponentsFamily();

/// See also [areaComponents].
class AreaComponentsFamily extends Family<List<ComponentModel>> {
  /// See also [areaComponents].
  const AreaComponentsFamily();

  /// See also [areaComponents].
  AreaComponentsProvider call(
    int areaID,
  ) {
    return AreaComponentsProvider(
      areaID,
    );
  }

  @override
  AreaComponentsProvider getProviderOverride(
    covariant AreaComponentsProvider provider,
  ) {
    return call(
      provider.areaID,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'areaComponentsProvider';
}

/// See also [areaComponents].
class AreaComponentsProvider extends AutoDisposeProvider<List<ComponentModel>> {
  /// See also [areaComponents].
  AreaComponentsProvider(
    int areaID,
  ) : this._internal(
          (ref) => areaComponents(
            ref as AreaComponentsRef,
            areaID,
          ),
          from: areaComponentsProvider,
          name: r'areaComponentsProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$areaComponentsHash,
          dependencies: AreaComponentsFamily._dependencies,
          allTransitiveDependencies:
              AreaComponentsFamily._allTransitiveDependencies,
          areaID: areaID,
        );

  AreaComponentsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.areaID,
  }) : super.internal();

  final int areaID;

  @override
  Override overrideWith(
    List<ComponentModel> Function(AreaComponentsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: AreaComponentsProvider._internal(
        (ref) => create(ref as AreaComponentsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        areaID: areaID,
      ),
    );
  }

  @override
  AutoDisposeProviderElement<List<ComponentModel>> createElement() {
    return _AreaComponentsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is AreaComponentsProvider && other.areaID == areaID;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, areaID.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin AreaComponentsRef on AutoDisposeProviderRef<List<ComponentModel>> {
  /// The parameter `areaID` of this provider.
  int get areaID;
}

class _AreaComponentsProviderElement
    extends AutoDisposeProviderElement<List<ComponentModel>>
    with AreaComponentsRef {
  _AreaComponentsProviderElement(super.provider);

  @override
  int get areaID => (origin as AreaComponentsProvider).areaID;
}

String _$areaControllerHash() => r'28e7c93ef6d9e3a7c6bce9b35ba512f8c513dd8f';

abstract class _$AreaController
    extends BuildlessAutoDisposeAsyncNotifier<List<ItemModel>> {
  late final int areaID;

  FutureOr<List<ItemModel>> build(
    int areaID,
  );
}

/// See also [AreaController].
@ProviderFor(AreaController)
const areaControllerProvider = AreaControllerFamily();

/// See also [AreaController].
class AreaControllerFamily extends Family<AsyncValue<List<ItemModel>>> {
  /// See also [AreaController].
  const AreaControllerFamily();

  /// See also [AreaController].
  AreaControllerProvider call(
    int areaID,
  ) {
    return AreaControllerProvider(
      areaID,
    );
  }

  @override
  AreaControllerProvider getProviderOverride(
    covariant AreaControllerProvider provider,
  ) {
    return call(
      provider.areaID,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'areaControllerProvider';
}

/// See also [AreaController].
class AreaControllerProvider extends AutoDisposeAsyncNotifierProviderImpl<
    AreaController, List<ItemModel>> {
  /// See also [AreaController].
  AreaControllerProvider(
    int areaID,
  ) : this._internal(
          () => AreaController()..areaID = areaID,
          from: areaControllerProvider,
          name: r'areaControllerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$areaControllerHash,
          dependencies: AreaControllerFamily._dependencies,
          allTransitiveDependencies:
              AreaControllerFamily._allTransitiveDependencies,
          areaID: areaID,
        );

  AreaControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.areaID,
  }) : super.internal();

  final int areaID;

  @override
  FutureOr<List<ItemModel>> runNotifierBuild(
    covariant AreaController notifier,
  ) {
    return notifier.build(
      areaID,
    );
  }

  @override
  Override overrideWith(AreaController Function() create) {
    return ProviderOverride(
      origin: this,
      override: AreaControllerProvider._internal(
        () => create()..areaID = areaID,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        areaID: areaID,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<AreaController, List<ItemModel>>
      createElement() {
    return _AreaControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is AreaControllerProvider && other.areaID == areaID;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, areaID.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin AreaControllerRef
    on AutoDisposeAsyncNotifierProviderRef<List<ItemModel>> {
  /// The parameter `areaID` of this provider.
  int get areaID;
}

class _AreaControllerProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<AreaController,
        List<ItemModel>> with AreaControllerRef {
  _AreaControllerProviderElement(super.provider);

  @override
  int get areaID => (origin as AreaControllerProvider).areaID;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
