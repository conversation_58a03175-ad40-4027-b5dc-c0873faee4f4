import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:uer_flutter/app/core/models/enums/component_type_enum.dart';
import 'package:uer_flutter/app/widgets/bars/app_bar.dart';
import 'package:uer_flutter/app/widgets/common_button.dart';
import 'package:uer_flutter/app/widgets/component_info.dart';
import 'package:uer_flutter/app/widgets/dialogs/actions_alert_dialog.dart';
import 'package:uer_flutter/app/widgets/dialogs/textfield_alert_dialog.dart';
import 'package:uer_flutter/app/widgets/history_card.dart';
import 'package:uer_flutter/app/widgets/history_detail_view.dart';
import 'package:uer_flutter/app/widgets/interactive/chip.dart';

import '../../core/models/area/area_model.dart';
import '../../core/models/component_history/component_history_model.dart';
import '../../core/providers/common/common_providers.dart';
import '../../helpers/constants.dart';
import '../../routes/routes_name.dart';
import 'component_page_controller.dart';

class ComponentPage extends ConsumerStatefulWidget {
  const ComponentPage({super.key, required this.componentID});

  final String componentID;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _ComponentPageState();
}

class _ComponentPageState extends ConsumerState<ComponentPage> {
  void addComment() {
    showDialog(
        context: context,
        builder: (_) => TextFieldAlertDialog(
              title: "Введите комментарий",
              actionButtonText: "Добавить",
            )).then((comment) {
      if (comment != null) {
        ref
            .read(componentPageControllerProvider(widget.componentID).notifier)
            .addComment(comment);
      }
    });
  }

  void tapToShowWorks() {
    var controller =
        ref.watch(componentPageControllerProvider(widget.componentID).notifier);
    var showChoiceArea = controller.showChoiceArea();

    var area = ref.read(currentUserAreaProvider);

    if (showChoiceArea == true) {
      openChoiceArea();
    } else if (area != null) {
      openSelectJobPage(area.identifier);
    }
  }

  void openSelectJobPage(int areaID) {
    var values = ref.watch(componentPageControllerProvider(widget.componentID));
    var mainID = values.value?.item1.mainID;

    if (mainID == null) {
      return;
    }

    context.pushNamed(RoutesName.selectJob.named, queryParameters: {
      "mainID": mainID,
      "componentID": widget.componentID,
      "areaID": areaID.toString()
    });
  }

  void openChoiceArea() async {
    var values = ref.watch(componentPageControllerProvider(widget.componentID));
    var component = values.value?.item2;

    if (component == null) {
      return;
    }

    List<AreaModel> areas = [];

    var currentArea = ref.read(areaForIDProvider(component.currentArea));

    if (currentArea == null) {
      return;
    }

    areas.add(currentArea);

    for (var i = 0; i < (component.accessArea?.length ?? 0); i++) {
      var areaID = component.accessArea![i];
      if (areaID != currentArea.identifier) {
        var area = ref.read(areaForIDProvider(areaID));

        if (area != null) {
          areas.add(area);
        }
      }
    }

    List<CommonButtonModel> actionButtons = [];

    for (var i = 0; i < areas.length; i++) {
      var area = areas[i];

      var btn = CommonButtonModel(
          name: area.name,
          callback: () {
            openSelectJobPage(area.identifier);
          });
      actionButtons.add(btn);
    }

    var cancelBtn = CommonButtonModel(name: "Отмена", callback: () {});

    actionButtons.add(cancelBtn);

    await showDialog(
        context: context,
        builder: (_) => ActionsAlertDialog(
            title: "Выберите участок для показа работ",
            actionButtons: actionButtons));
  }

  void openItem() {
    var values = ref.watch(componentPageControllerProvider(widget.componentID));
    var tuple = values.value;
    var mainID = tuple?.item1.mainID ?? "0";

    context.pushNamed(RoutesName.item.named, pathParameters: {
      'mid': mainID,
    });
  }

  void openQRGeneratePage() {
    context.pushNamed(RoutesName.qrGenerator.named, queryParameters: {
      'cid': widget.componentID,
    });
  }

  void openHistoryDetail(ComponentHistoryModel history) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      builder: (BuildContext context) {
        return SizedBox(
          height: 400,
          child: Center(
            child: HistoryDetailView(history: history),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    var values = ref.watch(componentPageControllerProvider(widget.componentID));
    var tupleTest = values.value;

    var item = tupleTest?.item1;
    var component = tupleTest?.item2;

    var areaID = component?.currentArea;
    var area = ref.watch(areaForIDProvider(areaID));

    var histories = component?.newHistory?.reversed.toList();

    var lastHistory = component?.newHistory?.last;
    var showCommentBtn = lastHistory?.job == SHIPMENT_JOB_ID ? false : true;

    var showSimpleAddJobBtn = ref
        .watch(componentPageControllerProvider(widget.componentID).notifier)
        .showSimpleAddJobBtn();

    return Scaffold(
        appBar: CustomAppBar(
          text: item?.repairNumberFormat() ?? "",
          actions: [
            showSimpleAddJobBtn == true
                ? IconButton(
                    icon: const Icon(
                      Icons.add,
                      color: Colors.white,
                    ),
                    tooltip: 'Показать работы',
                    onPressed: tapToShowWorks,
                  )
                : const SizedBox(),
            IconButton(
                onPressed: openQRGeneratePage, icon: const Icon(Icons.qr_code))
          ],
        ),
        body: values.when(
          data: (tuple) => Column(
            children: [
              ComponentInfo(
                  repairNumber: item?.repairNumberFormat() ?? "",
                  componentType: component?.type.desc ?? "",
                  equipment: item?.order?.equipment ?? "",
                  currentArea: area?.name ?? ""),
              const SizedBox(height: 12.0),
              Wrap(spacing: 4.0, runSpacing: 4.0, children: [
                ...(item?.tags ?? []).map((tag) => CustomChip(
                      isEnabled: true,
                      label: tag.getName(),
                      color: tag.getColor(),
                    )),
              ]),
              SizedBox(height: 16.0),
              SizedBox(
                  height: 36,
                  child: CommonButton(name: "К заказу", callback: openItem)),
              const SizedBox(
                height: 8,
              ),
              showCommentBtn
                  ? Column(
                      children: [
                        SizedBox(
                            height: 36,
                            child: CommonButton(
                                name: "Добавить комментарий",
                                callback: addComment)),
                        const SizedBox(
                          height: 8,
                        ),
                      ],
                    )
                  : const SizedBox(),
              Expanded(
                child: ListView.builder(
                  itemCount: histories?.length,
                  itemBuilder: (context, position) {
                    var history = histories![position];

                    callback() {
                      openHistoryDetail(history);
                    }

                    return HistoryCard(
                      history: history,
                      callback: callback,
                    );
                  },
                ),
              ),
            ],
          ),
          loading: () => const Center(
            child: CircularProgressIndicator(),
          ),
          error: (err, stack) => Text('Ошибка: $err'),
        ));
  }
}
