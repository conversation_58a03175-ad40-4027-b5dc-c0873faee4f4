import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:tuple/tuple.dart';
import 'package:uer_flutter/app/core/providers/notifier_mouted.dart';
import 'package:uer_flutter/app/core/providers/states/user/user_state.dart';
import 'package:uer_flutter/app/core/services/utils.dart';
import '../../core/models/enums/component_type_enum.dart';
import '../../core/models/item/item_model.dart';
import '../../core/models/search/search_model.dart';
import '../../core/services/api/service_provider.dart';

import '../../core/providers/common/common_providers.dart';
import '../../helpers/constants.dart';
import '../../core/models/component/component_model.dart';
import '../../core/models/enums/user_role_enum.dart';

part 'component_page_controller.g.dart';

@riverpod
class ComponentPageController extends _$ComponentPageController
    with NotifierMounted {
  @override
  FutureOr<Tuple2<ItemModel, ComponentModel>?> build(String componentID) {
    ref.onDispose(setUnmounted);

    return _fetchItemAndComponent(componentID);
  }

  Future<Tuple2<ItemModel, ComponentModel>?> _fetchItemAndComponent(
      String componentID) async {
    var itemRepository = ref.read(itemRepositoryProvider);
    var search = SearchModel(componentID: componentID);
    var items = await itemRepository.getItems(search);

    var item = items?.first;

    if (item == null) {
      return null;
    }

    var component = item.components.firstWhere(
      (element) {
        return element.identifier == componentID;
      },
    );

    return Tuple2(item, component);
  }

  Future<ItemModel?> _addComment(String comment) async {
    var tuple = state.value;

    if (tuple == null) {
      return null;
    }

    var currentUserState = ref.read(currentUserProvider);
    var user = currentUserState.maybeWhen(auth: (user) => user, orElse: () {});

    var currentUserArea = ref.read(currentUserAreaProvider);

    var currentAreaID = tuple.item2.currentArea;
    var currentArea = ref.read(areaForIDProvider(currentAreaID));

    var area = currentUserArea ?? currentArea;

    var itemRepository = ref.read(itemRepositoryProvider);

    var model = generateCommentHistory(comment, area!, user?.login ?? "");

    return await itemRepository.addNewHistory(model, tuple.item2.identifier);
  }

  Future<void> getItems(String componentID) async {
    state = const AsyncValue.loading();

    var newState = await AsyncValue.guard(() async {
      return _fetchItemAndComponent(componentID);
    });

    if (mounted) {
      state = newState;
    }
  }

  Future<void> addComment(String comment) async {
    if (comment.isEmpty) {
      return;
    }

    state = const AsyncValue.loading();

    var newState = await AsyncValue.guard(() async {
      var value = await _addComment(comment);

      if (value != null) {
        var component = value.getComponentForID(componentID);

        if (component != null) {
          return Tuple2(value, component);
        }
      }

      return _fetchItemAndComponent(componentID);
    });

    if (mounted) {
      state = newState;
    }
  }

  bool showSimpleAddJobBtn() {
    var item = state.value?.item1;
    var component = state.value?.item2;
    var lastJob = component?.newHistory?.last.job;

    var currentUserState = ref.read(currentUserProvider);
    var user = currentUserState.maybeWhen(auth: (user) => user, orElse: () {});

    var value = false;

    if (user?.simpleJobAdd == true) {
      value = true;
    } else if (user?.role == UserRole.admin) {
      value = true;
    }

    if (lastJob == SHIPMENT_JOB_ID) {
      value = false;
    }

    if (item?.assembled == true) {
      switch (component?.type) {
        case ComponentType.inductor:
          break;
        case ComponentType.stator:
          break;
        default:
          value = false;
      }
    }

    return value;
  }

  bool showChoiceArea() {
    var currentUserState = ref.read(currentUserProvider);
    var user = currentUserState.maybeWhen(auth: (user) => user, orElse: () {});

    if (user?.role == UserRole.admin ||
        user?.role == UserRole.director ||
        user?.role == UserRole.manager) {
      return true;
    }

    return false;
  }

  void updateData(Tuple2<ItemModel, ComponentModel> data) async {
    state = await AsyncValue.guard(() async {
      return data;
    });
  }
}
