// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'component_page_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$componentPageControllerHash() =>
    r'30df387170fee9ecd7f7632c17b1ff2716606d09';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$ComponentPageController
    extends BuildlessAutoDisposeAsyncNotifier<
        Tuple2<ItemModel, ComponentModel>?> {
  late final String componentID;

  FutureOr<Tuple2<ItemModel, ComponentModel>?> build(
    String componentID,
  );
}

/// See also [ComponentPageController].
@ProviderFor(ComponentPageController)
const componentPageControllerProvider = ComponentPageControllerFamily();

/// See also [ComponentPageController].
class ComponentPageControllerFamily
    extends Family<AsyncValue<Tuple2<ItemModel, ComponentModel>?>> {
  /// See also [ComponentPageController].
  const ComponentPageControllerFamily();

  /// See also [ComponentPageController].
  ComponentPageControllerProvider call(
    String componentID,
  ) {
    return ComponentPageControllerProvider(
      componentID,
    );
  }

  @override
  ComponentPageControllerProvider getProviderOverride(
    covariant ComponentPageControllerProvider provider,
  ) {
    return call(
      provider.componentID,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'componentPageControllerProvider';
}

/// See also [ComponentPageController].
class ComponentPageControllerProvider
    extends AutoDisposeAsyncNotifierProviderImpl<ComponentPageController,
        Tuple2<ItemModel, ComponentModel>?> {
  /// See also [ComponentPageController].
  ComponentPageControllerProvider(
    String componentID,
  ) : this._internal(
          () => ComponentPageController()..componentID = componentID,
          from: componentPageControllerProvider,
          name: r'componentPageControllerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$componentPageControllerHash,
          dependencies: ComponentPageControllerFamily._dependencies,
          allTransitiveDependencies:
              ComponentPageControllerFamily._allTransitiveDependencies,
          componentID: componentID,
        );

  ComponentPageControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.componentID,
  }) : super.internal();

  final String componentID;

  @override
  FutureOr<Tuple2<ItemModel, ComponentModel>?> runNotifierBuild(
    covariant ComponentPageController notifier,
  ) {
    return notifier.build(
      componentID,
    );
  }

  @override
  Override overrideWith(ComponentPageController Function() create) {
    return ProviderOverride(
      origin: this,
      override: ComponentPageControllerProvider._internal(
        () => create()..componentID = componentID,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        componentID: componentID,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<ComponentPageController,
      Tuple2<ItemModel, ComponentModel>?> createElement() {
    return _ComponentPageControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ComponentPageControllerProvider &&
        other.componentID == componentID;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, componentID.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ComponentPageControllerRef
    on AutoDisposeAsyncNotifierProviderRef<Tuple2<ItemModel, ComponentModel>?> {
  /// The parameter `componentID` of this provider.
  String get componentID;
}

class _ComponentPageControllerProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<ComponentPageController,
        Tuple2<ItemModel, ComponentModel>?> with ComponentPageControllerRef {
  _ComponentPageControllerProviderElement(super.provider);

  @override
  String get componentID =>
      (origin as ComponentPageControllerProvider).componentID;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
