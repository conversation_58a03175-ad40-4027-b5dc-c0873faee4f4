// import 'dart:async';

// import 'package:riverpod_annotation/riverpod_annotation.dart';
// import 'package:uer_flutter/models/item/item_model.dart';
// import 'package:uer_flutter/models/search/search_model.dart';
// import '../../helpers/notifier_mouted.dart';
// import '../../services/api/service_provider.dart';

// part 'overdeadline_items_controller.g.dart';

// @riverpod
// class OverDeadLineItemsController extends _$OverDeadLineItemsController
//     with NotifierMounted {
//   @override
//   FutureOr<List<ItemModel>> build(int? branchID) {
//     ref.onDispose(setUnmounted);

//     return _fetchItems(null);
//   }

//   Future<List<ItemModel>> _fetchItems(SearchModel? searchModel) async {
//     SearchModel? search;

//     if (searchModel == null) {
//       search = SearchModel(
//           newBool: false,
//           finished: false,
//           overDeadline: true,
//           branch: branchID);
//     } else {
//       search = searchModel.copyWith(
//           newBool: false,
//           finished: false,
//           overDeadline: true,
//           branch: branchID);
//     }

//     var apiClient = ref.read(itemRepositoryProvider);
//     var items = await apiClient.getItems(search);

//     if (items == null) {
//       return [];
//     }

//     return items.where((element) {
//       var valueBool =
//           element.components.first.newHistory?.last.job == 141 ? false : true;

//       if (valueBool == true) {
//         if (element.order?.statusOrder == "Дефектировка") {
//           return false;
//         }
//       }

//       return valueBool;
//     }).toList();
//   }

//   Future<void> getItems(SearchModel? searchModel) async {
//     state = const AsyncValue.loading();

//     var newState = await AsyncValue.guard(() async {
//       return _fetchItems(searchModel);
//     });

//     if (mounted) {
//       state = newState;
//     }
//   }

//   void updateItem(ItemModel item) async {
//     if (state.value == null) {
//       return;
//     }

//     var newArr = state.value!.toList();

//     for (var i = 0; i < state.value!.length; i++) {
//       var value = state.value![i];

//       if (value.mainID == item.mainID) {
//         newArr[i] = item;
//         break;
//       }
//     }

//     var newState = await AsyncValue.guard(() async {
//       return newArr;
//     });

//     if (mounted) {
//       state = newState;
//     }
//   }
// }
