import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:uer_flutter/app/widgets/bars/app_bar.dart';
import 'package:uer_flutter/app/widgets/items/item_list.dart';
import 'package:uer_flutter/app/widgets/search/search_panel.dart';

import '../../../core/models/item/item_model.dart';
import '../../../core/models/search/search_model.dart';
import '../../../helpers/constants.dart';
import '../../../helpers/debouncer.dart';
import '../../../routes/routes_name.dart';

class OverDeadLineItemsPage extends ConsumerStatefulWidget {
  const OverDeadLineItemsPage({super.key, required this.currentBranchID});

  final int? currentBranchID;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _OverDeadLineItemsPageState();
}

class _OverDeadLineItemsPageState extends ConsumerState<OverDeadLineItemsPage> {
  SearchModel searchModel = const SearchModel();
  final _debouncer = Debouncer(milliseconds: 650);

  @override
  void initState() {
    searchModel = defaultSearch();
    super.initState();
  }

  SearchModel defaultSearch() {
    return SearchModel(
      branch: widget.currentBranchID,
      newBool: false,
      finished: false,
      overDeadline: true,
      projection: PROJECTION_SEARCH_ITEM,
    );
  }

  void updateSearchTerm(SearchModel? searchModelNew) {
    _debouncer.run(() {
      if (searchModelNew == null) {
        setState(() {
          searchModel = defaultSearch();
        });
        return;
      } else {
        searchModel = defaultSearch();
      }

      setState(() {
        if (searchModelNew.searchField != null) {
          searchModel =
              searchModel.copyWith(searchField: searchModelNew.searchField);
        } else if (searchModelNew.searchClient != null) {
          searchModel =
              searchModel.copyWith(searchClient: searchModelNew.searchClient);
        } else if (searchModelNew.mainID != null) {
          searchModel = searchModel.copyWith(mainID: searchModelNew.mainID);
        } else if (searchModelNew.searchEquipment != null) {
          searchModel = searchModel.copyWith(
              searchEquipment: searchModelNew.searchEquipment);
        }
      });
    });
  }

  void moveToItem(ItemModel item) {
    var mainID = item.mainID ?? "0";

    context.pushNamed(RoutesName.item.named, pathParameters: {
      'mid': mainID,
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(text: "Просроченные заказы"),
      body: Column(
        children: [
          SearchPanel(callback: updateSearchTerm),
          Expanded(
            child: ItemList(
              callback: moveToItem,
              searchModel: searchModel,
            ),
          ),
        ],
      ),
    );
  }
}
