import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:uer_flutter/app/widgets/bars/app_bar.dart';
import 'package:uer_flutter/app/widgets/items/item_list.dart';
import 'package:uer_flutter/app/widgets/search/search_panel.dart';

import '../../../core/models/item/item_model.dart';
import '../../../core/models/search/search_model.dart';
import '../../../core/providers/common/common_providers.dart';
import '../../../helpers/constants.dart';
import '../../../helpers/debouncer.dart';
import '../../../routes/routes_name.dart';

class NewItemsPage extends ConsumerStatefulWidget {
  const NewItemsPage({super.key, required this.currentBranchID});

  final int? currentBranchID;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _NewItemsPageState();
}

class _NewItemsPageState extends ConsumerState<NewItemsPage> {
  SearchModel searchModel = const SearchModel();
  final _debouncer = Debouncer(milliseconds: 650);

  @override
  void initState() {
    searchModel = defaultSearch();
    super.initState();
  }

  SearchModel defaultSearch() {
    var branchID = widget.currentBranchID;

    return SearchModel(
        branch: branchID,
        finished: false,
        newBool: true,
        projection: PROJECTION_SEARCH_ITEM);
  }

  void updateSearchTerm(SearchModel? searchModelNew) {
    _debouncer.run(() {
      if (searchModelNew == null) {
        setState(() {
          searchModel = defaultSearch();
        });
        return;
      } else {
        searchModel = defaultSearch();
      }

      setState(() {
        if (searchModelNew.searchField != null) {
          searchModel =
              searchModel.copyWith(searchField: searchModelNew.searchField);
        } else if (searchModelNew.searchClient != null) {
          searchModel =
              searchModel.copyWith(searchClient: searchModelNew.searchClient);
        } else if (searchModelNew.mainID != null) {
          searchModel = searchModel.copyWith(mainID: searchModelNew.mainID);
        } else if (searchModelNew.searchEquipment != null) {
          searchModel = searchModel.copyWith(
              searchEquipment: searchModelNew.searchEquipment);
        }
      });
    });
  }

  void tapToItem(ItemModel item) {
    var mainID = item.mainID ?? "0";

    var area = ref.read(currentUserAreaProvider);

    if (area?.start == true) {
      openReceivingPage(mainID);
    } else {
      context.pushNamed(RoutesName.item.named, pathParameters: {
        'mid': mainID,
      });
    }
  }

  void openReceivingPage(String mainID) {
    context.pushNamed(RoutesName.receiving.named, extra: mainID);

    //context.pushReplacementNamed(name)
  }

  @override
  Widget build(BuildContext context) {
    //var showItems = searchModel.showSearchResult();

    return Scaffold(
      appBar: const CustomAppBar(text: "Новые двигатели"),
      body: Column(
        children: [
          SearchPanel(callback: updateSearchTerm),
          Expanded(
            child: ItemList(
              callback: tapToItem,
              searchModel: searchModel,
            ),
          )
        ],
      ),
    );
  }
}
