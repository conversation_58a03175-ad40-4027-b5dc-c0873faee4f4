// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'new_items_page_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$archiveItemsControllerHash() =>
    r'a3ea4a29799f5e7a3c5f00dd2c865ffc85c95f21';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$ArchiveItemsController
    extends BuildlessAutoDisposeAsyncNotifier<List<ItemModel>> {
  late final int? branchID;

  FutureOr<List<ItemModel>> build(
    int? branchID,
  );
}

/// See also [ArchiveItemsController].
@ProviderFor(ArchiveItemsController)
const archiveItemsControllerProvider = ArchiveItemsControllerFamily();

/// See also [ArchiveItemsController].
class ArchiveItemsControllerFamily extends Family<AsyncValue<List<ItemModel>>> {
  /// See also [ArchiveItemsController].
  const ArchiveItemsControllerFamily();

  /// See also [ArchiveItemsController].
  ArchiveItemsControllerProvider call(
    int? branchID,
  ) {
    return ArchiveItemsControllerProvider(
      branchID,
    );
  }

  @override
  ArchiveItemsControllerProvider getProviderOverride(
    covariant ArchiveItemsControllerProvider provider,
  ) {
    return call(
      provider.branchID,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'archiveItemsControllerProvider';
}

/// See also [ArchiveItemsController].
class ArchiveItemsControllerProvider
    extends AutoDisposeAsyncNotifierProviderImpl<ArchiveItemsController,
        List<ItemModel>> {
  /// See also [ArchiveItemsController].
  ArchiveItemsControllerProvider(
    int? branchID,
  ) : this._internal(
          () => ArchiveItemsController()..branchID = branchID,
          from: archiveItemsControllerProvider,
          name: r'archiveItemsControllerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$archiveItemsControllerHash,
          dependencies: ArchiveItemsControllerFamily._dependencies,
          allTransitiveDependencies:
              ArchiveItemsControllerFamily._allTransitiveDependencies,
          branchID: branchID,
        );

  ArchiveItemsControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.branchID,
  }) : super.internal();

  final int? branchID;

  @override
  FutureOr<List<ItemModel>> runNotifierBuild(
    covariant ArchiveItemsController notifier,
  ) {
    return notifier.build(
      branchID,
    );
  }

  @override
  Override overrideWith(ArchiveItemsController Function() create) {
    return ProviderOverride(
      origin: this,
      override: ArchiveItemsControllerProvider._internal(
        () => create()..branchID = branchID,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        branchID: branchID,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<ArchiveItemsController,
      List<ItemModel>> createElement() {
    return _ArchiveItemsControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ArchiveItemsControllerProvider &&
        other.branchID == branchID;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, branchID.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ArchiveItemsControllerRef
    on AutoDisposeAsyncNotifierProviderRef<List<ItemModel>> {
  /// The parameter `branchID` of this provider.
  int? get branchID;
}

class _ArchiveItemsControllerProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<ArchiveItemsController,
        List<ItemModel>> with ArchiveItemsControllerRef {
  _ArchiveItemsControllerProviderElement(super.provider);

  @override
  int? get branchID => (origin as ArchiveItemsControllerProvider).branchID;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
