import 'dart:async';

import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:uer_flutter/app/core/providers/notifier_mouted.dart';
import '../../../core/models/item/item_model.dart';
import '../../../core/models/search/search_model.dart';
import '../../../core/services/api/service_provider.dart';

part 'new_items_page_controller.g.dart';

@riverpod
class ArchiveItemsController extends _$ArchiveItemsController
    with NotifierMounted {
  @override
  FutureOr<List<ItemModel>> build(int? branchID) {
    ref.onDispose(setUnmounted);

    return _fetchItems(null);
  }

  Future<List<ItemModel>> _fetchItems(SearchModel? searchModel) async {
    SearchModel? search;

    if (searchModel == null) {
      search = SearchModel(newBool: true, finished: false, branch: branchID);
    } else {
      search = searchModel.copyWith(
          newBool: true, finished: false, branch: branchID);
    }

    var apiClient = ref.read(itemRepositoryProvider);
    var items = await apiClient.getItems(search);
    return items ?? [];
  }

  Future<void> getItems(SearchModel? searchModel) async {
    state = const AsyncValue.loading();

    var newState = await AsyncValue.guard(() async {
      return _fetchItems(searchModel);
    });

    if (mounted) {
      state = newState;
    }
  }

  void updateItem(ItemModel item) async {
    if (state.value == null) {
      return;
    }

    var newArr = state.value!.toList();

    for (var i = 0; i < state.value!.length; i++) {
      var value = state.value![i];

      if (value.mainID == item.mainID) {
        newArr[i] = item;
        break;
      }
    }

    var newState = await AsyncValue.guard(() async {
      return newArr;
    });

    if (mounted) {
      state = newState;
    }
  }
}
