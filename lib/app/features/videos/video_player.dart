import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:media_kit/media_kit.dart';
import 'package:media_kit_video/media_kit_video.dart';
import 'package:uer_flutter/app/widgets/bars/app_bar.dart';

class VideoPlayerPage extends ConsumerStatefulWidget {
  const VideoPlayerPage({super.key, required this.url});

  final String url;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _VideoPlayerPageState();
}

class _VideoPlayerPageState extends ConsumerState<VideoPlayerPage> {
  late final player = Player();
  late final controller = VideoController(player);

  @override
  void initState() {
    super.initState();

    print(widget.url.split("/").join('/'));

    player.open(Media(widget.url));

    // Wait until the fisrt render the avoid posible errors when use an context while the view is rendering
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _init();
    });
  }

  @override
  void dispose() {
    player.dispose();
    super.dispose();
  }

  /// play a video from network
  _init() {}

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        text: widget.url.split("/").last,
        // actions: [
        //   CustomElevatedButton(
        //     onPressed: () {
        //       Clipboard.setData(ClipboardData(text: widget.url));
        //     },
        //     text: 'Скопировать ссылку',
        //   ),
        // ],
      ),
      body: Center(
        child: SafeArea(
          child: AspectRatio(
            aspectRatio: 16 / 9,
            child: Video(
              controller: controller,
            ),
          ),
        ),
      ),
    );
  }
}
