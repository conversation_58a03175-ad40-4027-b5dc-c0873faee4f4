import 'package:flutter/material.dart';
import 'package:media_kit/media_kit.dart';
import 'package:media_kit_video/media_kit_video.dart';
import 'package:uer_flutter/app/helpers/date_extension.dart';
import 'package:uer_flutter/app/helpers/platform_check.dart';

import '../../core/models/video_test/video_test_model.dart';

typedef VideoItemCallback = Function();

class VideoItem extends StatefulWidget {
  const VideoItem({super.key, required this.model, required this.callback});

  final VideoTestModel model;
  final VideoItemCallback callback;

  @override
  State<VideoItem> createState() => _VideoItemState();
}

class _VideoItemState extends State<VideoItem> {
  final configuration = const PlayerConfiguration();

  late final player = Player(configuration: configuration);
  late final controller = VideoController(player);

  @override
  void initState() {
    super.initState();
    player.open(Media(widget.model.url));
    player.pause();
  }

  @override
  void dispose() {
    player.pause();
    player.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      color: Colors.white,
      elevation: 2.0,
      margin: const EdgeInsets.all(8),
      shape: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: const BorderSide(color: Colors.white)),
      child: InkWell(
        borderRadius: BorderRadius.circular(10.0),
        onTap: widget.callback,
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: SizedBox(
            child: Row(
              children: [
                IgnorePointer(
                  ignoring: true,
                  child: SizedBox(
                      height: stationComputer() ? 120 : 60,
                      width: stationComputer() ? 200 : 100,
                      child: Video(
                        controller: controller,
                        fit: BoxFit.fitHeight,
                      )),
                ),
                const SizedBox(
                  width: 8,
                ),
                Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(widget.model.name,
                        style: const TextStyle(fontWeight: FontWeight.w500)),
                    Text(
                        DateTime.fromMillisecondsSinceEpoch((widget
                                            .model.lastEdit ??
                                        (DateTime.now().millisecondsSinceEpoch /
                                            1000))
                                    .toInt() *
                                1000)
                            .timeAgo(),
                        style: const TextStyle(fontWeight: FontWeight.w400)),
                  ],
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
