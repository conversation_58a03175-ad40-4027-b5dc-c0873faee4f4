import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:uer_flutter/app/routes/models/video_detail_complex_data.dart';
import 'package:uer_flutter/app/widgets/bars/app_bar.dart';

import '../../core/models/video_test/video_test_model.dart';
import '../../core/providers/common/common_providers.dart';
import '../../routes/routes_name.dart';
import 'video_item.dart';

class VideosTestPage extends ConsumerStatefulWidget {
  const VideosTestPage({super.key, required this.videos});

  final List<VideoTestModel> videos;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _VideosTestPageState();
}

class _VideosTestPageState extends ConsumerState<VideosTestPage> {
  void openVideoTest(VideoTestModel model) async {
    hideBottomBar(true);

    var obj = VideoDetailPageComplexData(url: model.url);

    await context.pushNamed(RoutesName.videoDetail.named, extra: obj);

    hideBottomBar(false);
  }

  void hideBottomBar(hide) {
    if (hide == true) {
      ref.read(showBottomBarProvider.notifier).hide();
    } else {
      ref.read(showBottomBarProvider.notifier).show();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        text: "Видео Испытаний",
      ),
      body: ListView.builder(
        itemCount: widget.videos.length,
        itemBuilder: (context, position) {
          var video = widget.videos[position];

          callback() {
            openVideoTest(video);
          }

          return VideoItem(
            model: video,
            callback: callback,
          );
        },
      ),
    );
  }
}
