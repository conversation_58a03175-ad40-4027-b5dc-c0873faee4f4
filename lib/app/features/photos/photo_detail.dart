import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:uer_flutter/app/helpers/date_extension.dart';
import 'package:uer_flutter/app/helpers/platform_check.dart';
import 'package:uer_flutter/app/widgets/bars/app_bar.dart';
import 'package:uer_flutter/app/widgets/share_button.dart';

import '../../core/models/photo/photo_model.dart';

class PhotoDetail extends ConsumerStatefulWidget {
  // PhotoDetail
  const PhotoDetail({
    super.key,
    required this.photos,
    required this.initialIndex,
  });

  final List<PhotoModel> photos;
  final int initialIndex;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _PhotoDetailState();
}

class _PhotoDetailState extends ConsumerState<PhotoDetail> {
  int? currentIndex;

  void dissmis() {
    context.pop();
  }

  void next() {
    controller.nextPage(
        duration: const Duration(milliseconds: 500), curve: Curves.easeInOut);
  }

  void back() {
    controller.previousPage(
        duration: const Duration(milliseconds: 500), curve: Curves.easeInOut);
  }

  var controller = ExtendedPageController();

  @override
  Widget build(BuildContext context) {
    currentIndex ??= widget.initialIndex;

    controller = ExtendedPageController(
      initialPage: currentIndex ?? 0,
    );

    var owner = widget.photos[currentIndex!].owner;
    var dateStr = DateTime.fromMillisecondsSinceEpoch(
            ((widget.photos[currentIndex!].createdAt ?? 0).ceil() * 1000))
        .dateShortWithTimeString();

    return Scaffold(
      appBar: const CustomAppBar(
        text: "Фото",
      ), // TODO: make share btn in bar
      body: Stack(children: [
        ExtendedImageGesturePageView.builder(
          itemBuilder: (BuildContext context, int index) {
            var photo = widget.photos[index];
            Widget image = ExtendedImage.network(
              photo.getImgUrl(),
              fit: BoxFit.contain,
              cache: true,
              mode: ExtendedImageMode.gesture,
              initGestureConfigHandler: (state) {
                return GestureConfig(
                    inPageView: true,
                    initialScale: 1.0,
                    //you can cache gesture state even though page view page change.
                    //remember call clearGestureDetailsCache() method at the right time.(for example,this page dispose)
                    cacheGesture: false);
              },
            );

            //var owner = photo.owner;
            //var date = DateTime.fromMillisecondsSinceEpoch(
            //        ((photo.createdAt ?? 0).ceil() * 1000))
            //    .dateShortString();

            image = Container(padding: const EdgeInsets.all(5.0), child: image);

            if (index == currentIndex) {
              return Hero(
                tag: photo.getImgUrl() + index.toString(),
                child: image,
              );
            } else {
              return image;
            }
          },
          itemCount: widget.photos.length,
          onPageChanged: (int index) {
            setState(() {
              currentIndex = index;
            });
          },
          controller: controller,
          scrollDirection: Axis.horizontal,
        ),
        Row(
          children: [
            //IconButton(
            //    onPressed: dissmis, icon: const Icon(Icons.close_rounded)),
            const Spacer(),
            ShareBtn(
              photos: [widget.photos[currentIndex!]],
            )
          ],
        ),
        Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  "$owner $dateStr",
                  style: const TextStyle(
                      color: Colors.white,
                      fontSize: 21,
                      fontWeight: FontWeight.w600,
                      shadows: [
                        Shadow(color: Colors.black, offset: Offset(1, 1))
                      ]),
                )
              ],
            ),
            const SizedBox(
              height: 16,
            ),
          ],
        ),
        stationComputer()
            ? Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Row(
                    children: [
                      IconButton(
                          onPressed: back, icon: const Icon(Icons.arrow_back)),
                      const Spacer(),
                      IconButton(
                          onPressed: next,
                          icon: const Icon(Icons.arrow_forward)),
                    ],
                  ),
                ],
              )
            : const SizedBox(),
      ]),
    );
  }
}
