import 'package:flutter/material.dart';
import 'package:uer_flutter/app/helpers/date_extension.dart';

import '../../../core/models/notification/notification_model.dart';

//typedef CommentCardCallback = Function();

class PushHistoryCard extends StatelessWidget {
  const PushHistoryCard({
    super.key,
    required this.model,
  });

  final NotificationModel model;

  @override
  Widget build(BuildContext context) {
    var dateAgo = DateTime.fromMillisecondsSinceEpoch(
            (model.createdAt ?? 0).toInt() * 1000)
        .timeAgo();

    return Card(
      //color: Colors.wh, //: Colors.white,
      elevation: 2.0,
      margin: const EdgeInsets.all(8),
      shape: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: const BorderSide(color: Colors.white)),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: SizedBox(
          //height: 100,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(model.title ?? "",
                  maxLines: 1,
                  style: const TextStyle(fontWeight: FontWeight.w600)),
              const SizedBox(
                height: 4,
              ),
              Text(
                model.message ?? "",
                style: const TextStyle(
                    fontStyle: FontStyle.italic, fontWeight: FontWeight.w500),
              ),
              const SizedBox(
                height: 4,
              ),
              Row(
                children: [
                  model.from != null
                      ? Text(
                          "От: ${model.from ?? ""}",
                          style: const TextStyle(
                            color: Colors.black45,
                          ),
                        )
                      : const SizedBox(),
                  const Spacer(),
                  Text(
                    dateAgo,
                    style: const TextStyle(
                      color: Colors.black45,
                    ),
                  ),
                ],
              )
            ],
          ),
        ),
      ),
    );
  }
}
