import 'dart:async';

import 'package:flutter/material.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:uer_flutter/app/core/providers/notifier_mouted.dart';

import '../../core/models/notification/notification_model.dart';
import '../../core/providers/common/common_providers.dart';
import '../../core/services/api/service_provider.dart';

part 'push_history_page_controller.g.dart';

@riverpod
class PushHistoryPageController extends _$PushHistoryPageController
    with NotifierMounted {
  static const _pageSize = 20;

  @override
  FutureOr<PagingController<int, NotificationModel>> build(
      BuildContext context) {
    ref.onDispose(setUnmounted);

    final PagingController<int, NotificationModel> pagingController =
        PagingController(firstPageKey: 0);

    pagingController.addPageRequestListener((pageKey) {
      _fetchPage(pageKey);
    });

    pagingController.addStatusListener((status) {
      if (status == PagingStatus.subsequentPageError) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text(
              'Что-то пошло не так при загрузке новых данных',
            ),
            action: SnackBarAction(
              label: 'Повторить загрузку',
              onPressed: () => pagingController.retryLastFailedRequest(),
            ),
          ),
        );
      }
    });

    return pagingController;
  }

  String? getCurrentLogin() {
    var user = ref.watch(currentUserModelProvider);

    return user?.login;
  }

  Future<List<NotificationModel>?> _fetchNotifications(int offset) async {
    var login = getCurrentLogin();

    if (login == null) {
      return null;
    }

    var apiClient = ref.read(notificationRepositoryProvider);
    var notifications =
        await apiClient.getNotifications(login, _pageSize, offset);
    return notifications;
  }

  Future<void> _fetchPage(int pageKey) async {
    try {
      final newNotif = await _fetchNotifications(pageKey);

      if (newNotif == null) {
        return;
      }

      final isLastPage = newNotif.length < _pageSize;
      if (isLastPage) {
        state.value?.appendLastPage(newNotif);
      } else {
        final nextPageKey = pageKey + newNotif.length;
        state.value?.appendPage(newNotif, nextPageKey);
      }
    } catch (error) {
      // AppMetrica.reportError(
      //   message: 'Ошибка получения данных страницы (notifications)',
      //   errorDescription: AppMetricaErrorDescription(
      //     StackTrace.current,
      //     message: error.toString(),
      //   ),
      // );
      state.value?.error = error;
    }
  }
}
