import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:uer_flutter/app/widgets/bars/app_bar.dart';
import 'package:uer_flutter/app/widgets/templates/not_found_view.dart';

import '../../core/models/notification/notification_model.dart';
import 'PushHistoryCard/push_history_card.dart';
import 'push_history_page_controller.dart';

class PushHistoryPage extends ConsumerStatefulWidget {
  const PushHistoryPage({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _PushHistoryPageState();
}

class _PushHistoryPageState extends ConsumerState<PushHistoryPage> {
  @override
  Widget build(BuildContext context) {
    var controller = ref.watch(pushHistoryPageControllerProvider(context));

    return Scaffold(
        appBar: const CustomAppBar(text: "История уведомлений"),
        body: RefreshIndicator(
          onRefresh: () => Future.sync(
            () => controller.value?.refresh(),
          ),
          child: controller.when(data: (data) {
            return CustomScrollView(
              slivers: <Widget>[
                PagedSliverList<int, NotificationModel>(
                  pagingController: data,
                  builderDelegate: PagedChildBuilderDelegate<NotificationModel>(
                    itemBuilder: (context, notif, index) {
                      // mycallback() {
                      //   widget.callback(notif);
                      // }

                      return PushHistoryCard(
                        model: notif,
                      );
                    },
                    noItemsFoundIndicatorBuilder: (context) =>
                        const NotFoundView(),
                    firstPageErrorIndicatorBuilder: (_) => const SizedBox(),
                  ),
                ),
              ],
            );
          }, error: ((error, stackTrace) {
            return Center(child: Text(error.toString()));
          }), loading: () {
            return const CircularProgressIndicator.adaptive();
          }),
        ));
  }
}
