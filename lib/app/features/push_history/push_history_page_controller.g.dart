// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'push_history_page_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$pushHistoryPageControllerHash() =>
    r'316c816c15494ffb3f80e03e7178c1490059718a';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$PushHistoryPageController
    extends BuildlessAutoDisposeAsyncNotifier<
        PagingController<int, NotificationModel>> {
  late final BuildContext context;

  FutureOr<PagingController<int, NotificationModel>> build(
    BuildContext context,
  );
}

/// See also [PushHistoryPageController].
@ProviderFor(PushHistoryPageController)
const pushHistoryPageControllerProvider = PushHistoryPageControllerFamily();

/// See also [PushHistoryPageController].
class PushHistoryPageControllerFamily
    extends Family<AsyncValue<PagingController<int, NotificationModel>>> {
  /// See also [PushHistoryPageController].
  const PushHistoryPageControllerFamily();

  /// See also [PushHistoryPageController].
  PushHistoryPageControllerProvider call(
    BuildContext context,
  ) {
    return PushHistoryPageControllerProvider(
      context,
    );
  }

  @override
  PushHistoryPageControllerProvider getProviderOverride(
    covariant PushHistoryPageControllerProvider provider,
  ) {
    return call(
      provider.context,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'pushHistoryPageControllerProvider';
}

/// See also [PushHistoryPageController].
class PushHistoryPageControllerProvider
    extends AutoDisposeAsyncNotifierProviderImpl<PushHistoryPageController,
        PagingController<int, NotificationModel>> {
  /// See also [PushHistoryPageController].
  PushHistoryPageControllerProvider(
    BuildContext context,
  ) : this._internal(
          () => PushHistoryPageController()..context = context,
          from: pushHistoryPageControllerProvider,
          name: r'pushHistoryPageControllerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$pushHistoryPageControllerHash,
          dependencies: PushHistoryPageControllerFamily._dependencies,
          allTransitiveDependencies:
              PushHistoryPageControllerFamily._allTransitiveDependencies,
          context: context,
        );

  PushHistoryPageControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.context,
  }) : super.internal();

  final BuildContext context;

  @override
  FutureOr<PagingController<int, NotificationModel>> runNotifierBuild(
    covariant PushHistoryPageController notifier,
  ) {
    return notifier.build(
      context,
    );
  }

  @override
  Override overrideWith(PushHistoryPageController Function() create) {
    return ProviderOverride(
      origin: this,
      override: PushHistoryPageControllerProvider._internal(
        () => create()..context = context,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        context: context,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<PushHistoryPageController,
      PagingController<int, NotificationModel>> createElement() {
    return _PushHistoryPageControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is PushHistoryPageControllerProvider &&
        other.context == context;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, context.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin PushHistoryPageControllerRef on AutoDisposeAsyncNotifierProviderRef<
    PagingController<int, NotificationModel>> {
  /// The parameter `context` of this provider.
  BuildContext get context;
}

class _PushHistoryPageControllerProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<PushHistoryPageController,
        PagingController<int, NotificationModel>>
    with PushHistoryPageControllerRef {
  _PushHistoryPageControllerProviderElement(super.provider);

  @override
  BuildContext get context =>
      (origin as PushHistoryPageControllerProvider).context;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
