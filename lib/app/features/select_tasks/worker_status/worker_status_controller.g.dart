// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'worker_status_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$workerStatusControllerHash() =>
    r'475cee951ecad4f6499f6495deed337c5b67f0f5';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$WorkerStatusController
    extends BuildlessAutoDisposeNotifier<List<WorkerModel>> {
  late final ComponentHistoryModel? history;
  late final TaskSSZModel? task;

  List<WorkerModel> build(
    ComponentHistoryModel? history,
    TaskSSZModel? task,
  );
}

/// See also [WorkerStatusController].
@ProviderFor(WorkerStatusController)
const workerStatusControllerProvider = WorkerStatusControllerFamily();

/// See also [WorkerStatusController].
class WorkerStatusControllerFamily extends Family<List<WorkerModel>> {
  /// See also [WorkerStatusController].
  const WorkerStatusControllerFamily();

  /// See also [WorkerStatusController].
  WorkerStatusControllerProvider call(
    ComponentHistoryModel? history,
    TaskSSZModel? task,
  ) {
    return WorkerStatusControllerProvider(
      history,
      task,
    );
  }

  @override
  WorkerStatusControllerProvider getProviderOverride(
    covariant WorkerStatusControllerProvider provider,
  ) {
    return call(
      provider.history,
      provider.task,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'workerStatusControllerProvider';
}

/// See also [WorkerStatusController].
class WorkerStatusControllerProvider extends AutoDisposeNotifierProviderImpl<
    WorkerStatusController, List<WorkerModel>> {
  /// See also [WorkerStatusController].
  WorkerStatusControllerProvider(
    ComponentHistoryModel? history,
    TaskSSZModel? task,
  ) : this._internal(
          () => WorkerStatusController()
            ..history = history
            ..task = task,
          from: workerStatusControllerProvider,
          name: r'workerStatusControllerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$workerStatusControllerHash,
          dependencies: WorkerStatusControllerFamily._dependencies,
          allTransitiveDependencies:
              WorkerStatusControllerFamily._allTransitiveDependencies,
          history: history,
          task: task,
        );

  WorkerStatusControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.history,
    required this.task,
  }) : super.internal();

  final ComponentHistoryModel? history;
  final TaskSSZModel? task;

  @override
  List<WorkerModel> runNotifierBuild(
    covariant WorkerStatusController notifier,
  ) {
    return notifier.build(
      history,
      task,
    );
  }

  @override
  Override overrideWith(WorkerStatusController Function() create) {
    return ProviderOverride(
      origin: this,
      override: WorkerStatusControllerProvider._internal(
        () => create()
          ..history = history
          ..task = task,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        history: history,
        task: task,
      ),
    );
  }

  @override
  AutoDisposeNotifierProviderElement<WorkerStatusController, List<WorkerModel>>
      createElement() {
    return _WorkerStatusControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is WorkerStatusControllerProvider &&
        other.history == history &&
        other.task == task;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, history.hashCode);
    hash = _SystemHash.combine(hash, task.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin WorkerStatusControllerRef
    on AutoDisposeNotifierProviderRef<List<WorkerModel>> {
  /// The parameter `history` of this provider.
  ComponentHistoryModel? get history;

  /// The parameter `task` of this provider.
  TaskSSZModel? get task;
}

class _WorkerStatusControllerProviderElement
    extends AutoDisposeNotifierProviderElement<WorkerStatusController,
        List<WorkerModel>> with WorkerStatusControllerRef {
  _WorkerStatusControllerProviderElement(super.provider);

  @override
  ComponentHistoryModel? get history =>
      (origin as WorkerStatusControllerProvider).history;
  @override
  TaskSSZModel? get task => (origin as WorkerStatusControllerProvider).task;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
