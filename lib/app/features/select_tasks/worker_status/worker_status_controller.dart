import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:uer_flutter/app/core/providers/notifier_mouted.dart';
import '../../../core/models/task_ssz/task_ssz_model.dart';
import '../../../core/models/worker/worker_model.dart';

import '../../../core/models/component_history/component_history_model.dart';
import '../../../core/models/enums/history_status_enum.dart';

part 'worker_status_controller.g.dart';

@riverpod
class WorkerStatusController extends _$WorkerStatusController
    with NotifierMounted {
  @override
  List<WorkerModel> build(ComponentHistoryModel? history, TaskSSZModel? task) {
    ref.onDispose(setUnmounted);

    if (history?.workers?.isNotEmpty == true) {
      return [...history!.workers!];
    } else if (task?.workers.isNotEmpty == true) {
      return [...task!.workers];
    }

    return [];
  }

  List<WorkerModel> _getNotEditWorkers() {
    var originalWorkers = history?.workers?.isNotEmpty == true
        ? [...history!.workers!]
        : [...task!.workers];
    return originalWorkers;
  }

  WorkerModel? _getNotEditWorker(String id) {
    var workers = _getNotEditWorkers();

    for (var worker in workers) {
      if (worker.id == id) {
        return worker;
      }
    }

    return null;
  }

  void tapToWorkerAction(WorkerModel worker) {
    var workerNew = _getNotEditWorker(worker.id) ?? worker;
    var workers = state;

    if (workers.isEmpty == true) {
      return;
    }

    var findIndex = 0;

    for (var i = 0; i < workers.length; i++) {
      var tempWorker = workers[i];
      if (worker.id == tempWorker.id) {
        workers.removeAt(i);
        findIndex = i;
        break;
      }
    }

    var lastStatus = worker.statuses?.last;

    if (lastStatus != null) {
      WorkerStatusModel statusModel;

      switch (lastStatus.status) {
        case HistoryStatusType.inwork:
          statusModel =
              const WorkerStatusModel(status: HistoryStatusType.pause);

          break;
        case HistoryStatusType.pause:
          statusModel =
              const WorkerStatusModel(status: HistoryStatusType.inwork);
          break;
        default:
          return;
      }

      if (workerNew.statuses != null) {
        workerNew =
            workerNew.copyWith(statuses: [...workerNew.statuses!, statusModel]);
      } else {
        workerNew = workerNew.copyWith(statuses: [statusModel]);
      }
    } else {
      var statusModel =
          const WorkerStatusModel(status: HistoryStatusType.inwork);
      workerNew = workerNew.copyWith(statuses: [statusModel]);
    }

    workers.insert(findIndex, workerNew);

    state = [...workers];
  }
}
