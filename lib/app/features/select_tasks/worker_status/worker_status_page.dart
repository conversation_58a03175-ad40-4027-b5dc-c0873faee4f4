import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:uer_flutter/app/widgets/bars/app_bar.dart';
import 'package:uer_flutter/app/widgets/common_card.dart';

import '../../../core/models/component_history/component_history_model.dart';
import '../../../core/models/enums/history_status_enum.dart';
import '../../../core/models/task_ssz/task_ssz_model.dart';
import '../../../core/models/worker/worker_model.dart';
import '../../../helpers/snack_bar.dart';
import 'worker_status_card.dart';
import 'worker_status_controller.dart';

class WorkerStatusPage extends ConsumerStatefulWidget {
  const WorkerStatusPage({super.key, this.history, this.task});

  final ComponentHistoryModel? history;
  final TaskSSZModel? task;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _WorkerStatusPageState();
}

class _WorkerStatusPageState extends ConsumerState<WorkerStatusPage> {
  void tapToSaveBtn() {
    // check edit workers

    var originalWorkers = widget.history?.workers?.isNotEmpty == true
        ? [...widget.history!.workers!]
        : [...widget.task!.workers];

    var editWorkers =
        ref.read(workerStatusControllerProvider(widget.history, widget.task));

    var arrayEdit = false;

    for (var worker in originalWorkers) {
      var contains = editWorkers.contains(worker);

      if (contains == false) {
        arrayEdit = true;
        break;
      }
    }

    if (arrayEdit == false) {
      showInSnackBar("Статус пользователей небыл изменен!", context);
      return;
    }

    transferPageBackWithData(widget.task, widget.history, editWorkers);
  }

  void tapToStartAll() {
    changeAllWorkerStatus(HistoryStatusType.inwork);
  }

  void tapToPauseAll() {
    changeAllWorkerStatus(HistoryStatusType.pause);
  }

  void tapToFinishAll() {
    changeAllWorkerStatus(HistoryStatusType.finish);
  }

  void tapToWorker(WorkerModel worker) {
    ref
        .read(workerStatusControllerProvider(widget.history, widget.task)
            .notifier)
        .tapToWorkerAction(worker);
  }

  void changeAllWorkerStatus(HistoryStatusType status) {
    List<WorkerModel> editWorkers = [];

    var originalWorkers = widget.history?.workers?.isNotEmpty == true
        ? [...widget.history!.workers!]
        : [...widget.task!.workers]; // workers not edit in current screen

    for (var worker in originalWorkers) {
      var lastStatus = worker.statuses?.last;
      if (lastStatus != null) {
        WorkerStatusModel? statusModel;

        switch (status) {
          case HistoryStatusType.inwork:
            if (lastStatus.status == HistoryStatusType.pause) {
              statusModel =
                  const WorkerStatusModel(status: HistoryStatusType.inwork);
            }
            break;
          case HistoryStatusType.pause:
            if (lastStatus.status == HistoryStatusType.inwork) {
              statusModel =
                  const WorkerStatusModel(status: HistoryStatusType.pause);
            }
            break;
          case HistoryStatusType.finish:
            if (lastStatus.status != HistoryStatusType.finish) {
              statusModel =
                  const WorkerStatusModel(status: HistoryStatusType.finish);
            }
            break;
          default:
            return;
        }

        if (statusModel == null) {
          editWorkers.add(worker);
        } else {
          WorkerModel workerTap =
              worker.copyWith(statuses: [...?worker.statuses, statusModel]);
          editWorkers.add(workerTap);
        }
      } else {
        if (status == HistoryStatusType.inwork) {
          var statusModel =
              const WorkerStatusModel(status: HistoryStatusType.inwork);
          WorkerModel workerTap = worker.copyWith(statuses: [statusModel]);
          editWorkers.add(workerTap);
          continue;
        }
      }
    }

    transferPageBackWithData(widget.task, widget.history, editWorkers);
  }

  void transferPageBackWithData(TaskSSZModel? task,
      ComponentHistoryModel? history, List<WorkerModel> editWorkers) {
    Map<String, Object>? result = {"editWorkers": editWorkers};

    if (task != null) {
      // add new task
      //widget.addTaskCallback(widget.task!, editWorkers);
      result["task"] = task;
    } else if (history != null) {
      // change statuses
      //widget.changeStatusCallback(widget.history!, editWorkers);
      result["history"] = history;
    }

    context.pop(result);
  }

  @override
  Widget build(BuildContext context) {
    var history = widget.history;
    var task = widget.task;

    var workers =
        ref.watch(workerStatusControllerProvider(widget.history, widget.task));

    var showStart =
        history?.statuses.last.status == HistoryStatusType.pause ? true : false;

    if (task != null && history == null) {
      showStart = true;
    }

    var showPause = !showStart;
    var showFinish = history != null ? true : false;

    return Scaffold(
      appBar: CustomAppBar(
        text: "Статус рабочих",
        actions: [
          IconButton(
            onPressed: tapToSaveBtn,
            icon: const Icon(Icons.save),
            tooltip: "Сохранить",
          )
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            showStart
                ? CommonCardRow(
                    name: "Запустить для всех",
                    icon: Icons.start,
                    callback: tapToStartAll)
                : const SizedBox(),
            showPause
                ? CommonCardRow(
                    name: "Приостановить для всех",
                    icon: Icons.pause_circle,
                    callback: tapToPauseAll)
                : const SizedBox(),
            showFinish
                ? CommonCardRow(
                    name: "Завершить для всех",
                    icon: Icons.stop_circle,
                    callback: tapToFinishAll)
                : const SizedBox(),
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              //shrinkWrap: true,
              itemCount: workers.length,
              itemBuilder: (context, position) {
                final worker = workers[position];

                callback() {
                  tapToWorker(worker);
                }

                return WorkerStatusCard(worker: worker, callback: callback);
              },
            ),
          ],
        ),
      ),
    );
  }
}
