import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../core/models/enums/history_status_enum.dart';
import '../../../core/models/worker/worker_model.dart';

typedef WorkerStatusCardCallback = Function();

class WorkerStatusCard extends ConsumerWidget {
  const WorkerStatusCard(
      {super.key, required this.worker, required this.callback});

  final WorkerModel worker;
  final WorkerStatusCardCallback callback;

  Icon getStatusImg(HistoryStatusType? status) {
    if (status == HistoryStatusType.inwork) {
      return const Icon(
        Icons.pause_circle,
        color: Colors.orange,
      );
    } else if (status == HistoryStatusType.pause) {
      return const Icon(
        Icons.play_circle,
        color: Colors.green,
      );
    } else if (status == HistoryStatusType.finish) {
      return const Icon(
        Icons.check_circle,
        color: Colors.orange,
      );
    } else {
      return const Icon(
        Icons.play_circle,
        color: Colors.green,
      );
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    WorkerStatusModel? status;

    if (worker.statuses?.isNotEmpty == true) {
      status = worker.statuses?.last;
    }

    var statusText = "";

    if (status != null) {
      statusText = status.status.desc;
    } else {
      statusText = "Не приступал к работе";
    }

    return Card(
      color: Colors.white,
      elevation: 2.0,
      margin: const EdgeInsets.all(8),
      shape: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: const BorderSide(color: Colors.white)),
      child: InkWell(
        borderRadius: BorderRadius.circular(10.0),
        onTap: callback,
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: SizedBox(
            //height: 60,
            child: Row(
              children: [
                Column(
                  children: [
                    Text(worker.name,
                        style: const TextStyle(fontWeight: FontWeight.w500)),
                    const SizedBox(
                      width: 16,
                    ),
                    Text(statusText,
                        style: const TextStyle(fontWeight: FontWeight.w500)),
                  ],
                ),
                const SizedBox(
                  width: 16,
                ),
                getStatusImg(status?.status),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
