import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uer_flutter/app/widgets/containers/stat_row.dart';

class ShortSelectInfoComponent extends ConsumerWidget {
  const ShortSelectInfoComponent(
      {super.key, required this.repairNumber, required this.componentType});

  final String repairNumber;
  final String componentType;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Card(
      elevation: 2.0,
      margin: const EdgeInsets.all(8),
      shape: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: const BorderSide(color: Colors.white)),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: SizedBox(
          //height: 100,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              StatRow("Рем. номер:", repairNumber),
              const SizedBox(
                height: 4,
              ),
              const Divider(),
              StatRow("Тип компонента:", componentType),
              const SizedBox(
                height: 4,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
