// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'select_job_page_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$selectJobPageControllerHash() =>
    r'6605a4b9bc98a196f483f02834e8060382319afe';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$SelectJobPageController
    extends BuildlessAutoDisposeAsyncNotifier<ItemModel?> {
  late final String mainID;
  late final String componentID;

  FutureOr<ItemModel?> build(
    String mainID,
    String componentID,
  );
}

/// See also [SelectJobPageController].
@ProviderFor(SelectJobPageController)
const selectJobPageControllerProvider = SelectJobPageControllerFamily();

/// See also [SelectJobPageController].
class SelectJobPageControllerFamily extends Family<AsyncValue<ItemModel?>> {
  /// See also [SelectJobPageController].
  const SelectJobPageControllerFamily();

  /// See also [SelectJobPageController].
  SelectJobPageControllerProvider call(
    String mainID,
    String componentID,
  ) {
    return SelectJobPageControllerProvider(
      mainID,
      componentID,
    );
  }

  @override
  SelectJobPageControllerProvider getProviderOverride(
    covariant SelectJobPageControllerProvider provider,
  ) {
    return call(
      provider.mainID,
      provider.componentID,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'selectJobPageControllerProvider';
}

/// See also [SelectJobPageController].
class SelectJobPageControllerProvider
    extends AutoDisposeAsyncNotifierProviderImpl<SelectJobPageController,
        ItemModel?> {
  /// See also [SelectJobPageController].
  SelectJobPageControllerProvider(
    String mainID,
    String componentID,
  ) : this._internal(
          () => SelectJobPageController()
            ..mainID = mainID
            ..componentID = componentID,
          from: selectJobPageControllerProvider,
          name: r'selectJobPageControllerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$selectJobPageControllerHash,
          dependencies: SelectJobPageControllerFamily._dependencies,
          allTransitiveDependencies:
              SelectJobPageControllerFamily._allTransitiveDependencies,
          mainID: mainID,
          componentID: componentID,
        );

  SelectJobPageControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.mainID,
    required this.componentID,
  }) : super.internal();

  final String mainID;
  final String componentID;

  @override
  FutureOr<ItemModel?> runNotifierBuild(
    covariant SelectJobPageController notifier,
  ) {
    return notifier.build(
      mainID,
      componentID,
    );
  }

  @override
  Override overrideWith(SelectJobPageController Function() create) {
    return ProviderOverride(
      origin: this,
      override: SelectJobPageControllerProvider._internal(
        () => create()
          ..mainID = mainID
          ..componentID = componentID,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        mainID: mainID,
        componentID: componentID,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<SelectJobPageController, ItemModel?>
      createElement() {
    return _SelectJobPageControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SelectJobPageControllerProvider &&
        other.mainID == mainID &&
        other.componentID == componentID;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, mainID.hashCode);
    hash = _SystemHash.combine(hash, componentID.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin SelectJobPageControllerRef
    on AutoDisposeAsyncNotifierProviderRef<ItemModel?> {
  /// The parameter `mainID` of this provider.
  String get mainID;

  /// The parameter `componentID` of this provider.
  String get componentID;
}

class _SelectJobPageControllerProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<SelectJobPageController,
        ItemModel?> with SelectJobPageControllerRef {
  _SelectJobPageControllerProviderElement(super.provider);

  @override
  String get mainID => (origin as SelectJobPageControllerProvider).mainID;
  @override
  String get componentID =>
      (origin as SelectJobPageControllerProvider).componentID;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
