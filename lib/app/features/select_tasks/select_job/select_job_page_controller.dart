import 'dart:async';

import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:uer_flutter/app/core/providers/notifier_mouted.dart';
import 'package:uer_flutter/app/core/services/utils.dart';
import '../../../core/models/area/area_model.dart';
import '../../../core/models/component/component_model.dart';
import '../../../core/models/enums/component_history_type_enum.dart';
import '../../../core/models/enums/history_status_enum.dart';
import '../../../core/models/item/item_model.dart';
import '../../../core/models/search/search_model.dart';
import '../../../core/models/status/status_model.dart';
import '../../../core/models/task_ssz/task_ssz_model.dart';
import '../../../core/models/worker/worker_model.dart';
import '../../../core/providers/update_providers.dart';

import '../../../helpers/string_helper.dart';
import '../../../core/models/component_history/component_history_model.dart';

import '../../../core/providers/common/common_providers.dart';
import '../../../core/services/api/service_provider.dart';

import '../../../core/providers/states/user/user_state.dart';

part 'select_job_page_controller.g.dart';

@riverpod
class SelectJobPageController extends _$SelectJobPageController
    with NotifierMounted {
  @override
  FutureOr<ItemModel?> build(String mainID, String componentID) {
    ref.onDispose(setUnmounted);
    return _fetchItem(mainID);
  }

  Future<void> reloadItem() async {
    await getItem(state.value!.mainID!);
  }

  Future<ItemModel?> _fetchItem(String mainID) async {
    SearchModel search = SearchModel(mainID: mainID);

    var itemRepository = ref.read(itemRepositoryProvider);
    var items = await itemRepository.getItems(search);
    return items?.first;
  }

  Future<void> getItem(String mainID) async {
    state = const AsyncValue.loading();

    var newState = await AsyncValue.guard(() async {
      return _fetchItem(mainID);
    });

    if (mounted) {
      state = newState;
    }
  }

  void addNewStatusOrTask(AreaModel area, StatusModel? status,
      TaskSSZModel? task, List<WorkerModel> workers) {
    var identifier = getRandomString(16);
    var userState = ref.read(currentUserProvider);
    var user = userState.maybeWhen(auth: (user) => user, orElse: () {});

    var owner = user?.login ?? "";

    if (status != null) {
      var name = "${area.name} | ${status.name}";

      var statusModel =
          ComponentStatusModel(owner: owner, status: HistoryStatusType.inwork);

      if (status.isShipped()) {
        statusModel = statusModel.copyWith(status: HistoryStatusType.finish);
      }

      var history = ComponentHistoryModel(
          name: name,
          area: area.identifier,
          job: status.identifier,
          identifier: identifier,
          type: ComponentHistoryType.job,
          statuses: [statusModel]);

      _addNewHistory(history);
    } else if (task != null) {
      var name = "${area.name} | ${task.name}";

      var statusModel =
          ComponentStatusModel(owner: owner, status: HistoryStatusType.inwork);

      var history = ComponentHistoryModel(
          name: name,
          area: area.identifier,
          task: task.idJob,
          taskID: task.id,
          histMainID: task.mainID,
          identifier: identifier,
          workers: workers,
          type: ComponentHistoryType.task,
          statuses: [statusModel]);

      _addNewHistory(history);
    }
  }

  void _addNewHistory(ComponentHistoryModel history) async {
    state = const AsyncValue.loading();

    var itemProvider = ref.read(itemRepositoryProvider);

    var newState = await AsyncValue.guard(() async {
      return itemProvider.addNewHistory(history, componentID);
    });

    if (mounted) {
      if (newState.value != null) {
        updateItemInProvidersWithRef(newState.value!, ref);
      }

      state = newState;
    }
  }

  Future<ItemModel?> _addComment(String comment) async {
    var item = state.value;

    if (item == null) {
      return null;
    }

    var currentUserState = ref.read(currentUserProvider);
    var user = currentUserState.maybeWhen(auth: (user) => user, orElse: () {});

    var currentUserArea = ref.read(currentUserAreaProvider);

    var component = item.getComponentForID(componentID);

    if (component == null) {
      return null;
    }

    var currentAreaID = component.currentArea;
    var currentArea = ref.read(areaForIDProvider(currentAreaID));

    var area = currentUserArea ?? currentArea;

    if (area == null) {
      return null;
    }

    var itemRepository = ref.read(itemRepositoryProvider);

    var model = generateCommentHistory(comment, area, user?.login ?? "");

    return await itemRepository.addNewHistory(model, componentID);
  }

  Future<void> addComment(String comment) async {
    if (comment.isEmpty) {
      return;
    }

    state = const AsyncValue.loading();

    var newState = await AsyncValue.guard(() async {
      return _addComment(comment);
    });

    if (mounted) {
      state = newState;
    }

    if (newState.hasValue == true) {
      updateItemInProvidersWithRef(newState.value!, ref);
    }
  }

  void changeStatusWorkers(
      List<WorkerModel> workers, String historyIdentifier, String owner) async {
    state = const AsyncValue.loading();

    var itemProvider = ref.read(itemRepositoryProvider);

    var workersModel = WorkerListModel(workers);

    var newState = await AsyncValue.guard(() async {
      return itemProvider.changeStatusWorkers(
          workersModel, componentID, historyIdentifier, owner);
    });

    if (mounted) {
      if (newState.value != null) {
        updateItemInProvidersWithRef(newState.value!, ref);
      }

      state = newState;
    }
  }

  void changeStatusJob(
      HistoryStatusType status, String historyIdentifier) async {
    state = const AsyncValue.loading();

    var userState = ref.read(currentUserProvider);
    var user = userState.maybeWhen(auth: (user) => user, orElse: () {});

    var statusModel = ComponentStatusModel(
        owner: user?.login ?? "Пользователь неопределен", status: status);

    var itemProvider = ref.read(itemRepositoryProvider);

    var newState = await AsyncValue.guard(() async {
      return itemProvider.changeStatusHistory(
          statusModel, componentID, historyIdentifier);
    });

    if (mounted) {
      if (newState.value != null) {
        updateItemInProvidersWithRef(newState.value!, ref);
      }

      state = newState;
    }
  }

  void transferToArea(AreaModel areaModel) async {
    state = const AsyncValue.loading();

    var userState = ref.read(currentUserProvider);
    var user = userState.maybeWhen(auth: (user) => user, orElse: () {});

    var randomString = getRandomString(16);
    var nameHistory = "${areaModel.name} | Передано на участок";

    var status = ComponentStatusModel(
        owner: user?.login ?? "Пользователь не определен",
        status: HistoryStatusType.info);

    var history = ComponentHistoryModel(
        name: nameHistory,
        area: areaModel.identifier,
        identifier: randomString,
        type: ComponentHistoryType.info,
        statuses: [status]);

    var itemProvider = ref.read(itemRepositoryProvider);

    var newState = await AsyncValue.guard(() async {
      return itemProvider.transferToArea(
          history, componentID, areaModel.identifier);
    });

    if (mounted) {
      if (newState.value != null) {
        updateItemInProvidersWithRef(newState.value!, ref);
      }

      state = newState;
    }
  }

  ComponentModel? getComponent() {
    if (state.value == null) {
      return null;
    }

    for (var i = 0; i < state.value!.components.length; i++) {
      var component = state.value!.components[i];

      if (component.identifier == componentID) {
        return component;
      }
    }

    return null;
  }

  void updateData(ItemModel data) async {
    state = await AsyncValue.guard(() async {
      return data;
    });
  }
}
