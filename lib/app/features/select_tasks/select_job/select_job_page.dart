import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:tuple/tuple.dart';
import 'package:uer_flutter/app/core/models/enums/component_type_enum.dart';
import 'package:uer_flutter/app/routes/models/select_area_complex_data.dart';
import 'package:uer_flutter/app/routes/models/status_ssz_complex_data.dart';
import 'package:uer_flutter/app/routes/models/worker_status_complex_data.dart';

import '../../../core/models/area/area_model.dart';
import '../../../core/models/component_history/component_history_model.dart';
import '../../../core/models/enums/history_status_enum.dart';
import '../../../core/models/item/item_model.dart';
import '../../../core/models/status/status_model.dart';
import '../../../core/models/task_ssz/task_ssz_model.dart';
import '../../../core/models/worker/worker_model.dart';
import '../../../core/providers/common/common_providers.dart';
import '../../../core/providers/states/user/user_state.dart';
import '../../../helpers/snack_bar.dart';
import '../../../routes/routes_name.dart';
import '../../../widgets/bars/app_bar.dart';
import '../../../widgets/common_button.dart';
import '../../../widgets/common_card.dart';
import '../../../widgets/dialogs/actions_alert_dialog.dart';
import '../../../widgets/dialogs/textfield_alert_dialog.dart';
import '../../../widgets/header_text_view.dart';
import '../../../widgets/job_info_card.dart';
import 'select_job_page_controller.dart';
import 'short_select_job_info_component_card.dart';

class SelectJobPage extends ConsumerStatefulWidget {
  const SelectJobPage(
      {super.key,
      required this.mainID,
      required this.componentID,
      required this.areaID});

  final String mainID;
  final String componentID;
  final int areaID;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _SelectJobPageState();
}

class _SelectJobPageState extends ConsumerState<SelectJobPage> {
  void tapChangeAccessItem() async {
    // открытие экрана для изменения доступа к двигателю

    var item = ref.read(
        selectJobPageControllerProvider(widget.mainID, widget.componentID));
    var controller = ref.read(
        selectJobPageControllerProvider(widget.mainID, widget.componentID)
            .notifier);

    if (item.value == null) {
      return;
    }

    var component = controller.getComponent();

    if (component == null) {
      return;
    }

    var obj = SelectAreaAccessPageComplexData(
        branchID: item.value!.branch ?? 0,
        areaID: component.currentArea,
        componentID: component.identifier,
        selectedAreas: component.accessArea ?? []);

    var result =
        await context.pushNamed(RoutesName.selectAreaAccess.named, extra: obj);

    if (result is ItemModel) {
      controller.updateData(result);
    }
  }

  void tapTransferItemToArea() async {
    var controller = ref.watch(
        selectJobPageControllerProvider(widget.mainID, widget.componentID)
            .notifier);
    var activeHistories = getActiveHistories(controller);

    if (activeHistories.isNotEmpty == true) {
      showInSnackBar(
          "Невозможно передать. Завершите все текущие работы.", context);
      return;
    }

    var item = ref.read(
        selectJobPageControllerProvider(widget.mainID, widget.componentID));

    if (item.value == null) {
      return;
    }

    var component = controller.getComponent();

    if (component == null) {
      showInSnackBar("Ошибка. Не найден компонент.", context);
      return;
    }

    void callback(AreaModel areaModel) {
      var actionStr = "Передать компонент на участок: ${areaModel.name}";

      var continueBtn = CommonButtonModel(
          name: "Подтвердить",
          callback: () {
            controller.transferToArea(areaModel);
          });

      var cancelBtn = CommonButtonModel(
          name: "Отмена",
          callback: () {
            showInSnackBar("Передача компонента отменена.", context);
          });

      showDialog(
          context: context,
          builder: (_) => ActionsAlertDialog(
              title: "Подтвердите действие",
              text: actionStr,
              actionButtons: [continueBtn, cancelBtn]));
    }

    var result = await context
        .pushNamed(RoutesName.selectAreaList.named, queryParameters: {
      "branchID": item.value!.branch.toString(),
      "removeAreaID": component.currentArea.toString()
    });

    if (result != null && result is AreaModel) {
      callback(result);
    }
  } // открытие экрана для выбора участка для передачи двигателя

  void tapAddComment() {
    // открытие экрана для написания комментария
    showDialog(
        context: context,
        builder: (_) => TextFieldAlertDialog(
              title: "Введите комментарий",
              actionButtonText: "Добавить",
            )).then((comment) {
      if (comment != null) {
        var controller = ref.read(
            selectJobPageControllerProvider(widget.mainID, widget.componentID)
                .notifier);

        controller.addComment(comment);
      }
    });
  }

  void tapAddNewJob() async {
    // открытие экрана выбора работ при нажатии на кнопку добавить работы
    var area = ref.watch(areaForIDProvider(widget.areaID));

    var controller = ref.read(
        selectJobPageControllerProvider(widget.mainID, widget.componentID)
            .notifier);
    var activeHistories = getActiveHistories(controller);

    if (area == null) {
      showInSnackBar("Ошибка: Участок не был загружен.", context);
    }

    var obj = StatusSSZPageComplexData(
        mainID: widget.mainID,
        idArea: area!.identifier,
        histories: activeHistories,
        idAreaStr: area.idArea);

    var result =
        await context.pushNamed(RoutesName.statusSsz.named, extra: obj);

    if (result != null && result is Tuple2) {
      addNewStatusOrSSZ(result.item1, result.item2);
    }
  }

  void addNewStatusOrSSZ(StatusModel? status, TaskSSZModel? task) {
    void continueCallback() {
      var area = ref.read(areaForIDProvider(widget.areaID));
      var controller = ref.read(
          selectJobPageControllerProvider(widget.mainID, widget.componentID)
              .notifier);

      if (area != null) {
        controller.addNewStatusOrTask(
            area, status, null, []); // добавление нового статуса
      }
    }

    if (status != null) {
      var continueBtn =
          CommonButtonModel(name: "Добавить", callback: continueCallback);

      var cancelBtn = CommonButtonModel(name: "Отмена", callback: () {});

      showDialog(
          context: context,
          builder: (_) => ActionsAlertDialog(
              title: "Подтвердите действие",
              text: "Добавить новый статус: ${status.name}",
              actionButtons: [continueBtn, cancelBtn]));
    } else if (task != null) {
      showWorkerStatusPage(null, task); // показ экрана с статусами рабочих
    }
  }

  void showWorkerStatusPage(
      ComponentHistoryModel? history, TaskSSZModel? task) async {
    void addCallback(TaskSSZModel task, List<WorkerModel> workers) {
      //print({task, workers});

      var area = ref.read(areaForIDProvider(widget.areaID));
      var controller = ref.read(
          selectJobPageControllerProvider(widget.mainID, widget.componentID)
              .notifier);

      if (area != null) {
        controller.addNewStatusOrTask(
            area, null, task, workers); // добавление ССЗ с статусами работников
      }
    }

    void changeStatusCallback(
        ComponentHistoryModel history, List<WorkerModel> workers) {
      //print({history, workers});

      var userState = ref.read(currentUserProvider);
      var user = userState.maybeWhen(auth: (user) => user, orElse: () {});

      var controller = ref.read(
          selectJobPageControllerProvider(widget.mainID, widget.componentID)
              .notifier);

      if (user != null) {
        controller.changeStatusWorkers(workers, history.identifier!,
            user.login); // изменение статусов работников в текущей истории
      }
    }

    var obj = WorkerStatusPageComplexData(history: history, task: task);

    Map<String, Object>? result =
        await context.pushNamed(RoutesName.workerStatus.named, extra: obj)
            as Map<String, Object>?;

    if (result?.containsKey("task") == true) {
      addCallback(result!["task"] as TaskSSZModel,
          result["editWorkers"] as List<WorkerModel>);
    } else if (result?.containsKey("history") == true) {
      changeStatusCallback(result!["history"] as ComponentHistoryModel,
          result["editWorkers"] as List<WorkerModel>);
    }
  }

  void tapToActiveWork(ComponentHistoryModel history) {
    // действия при нажатии на текущую работу

    if (history.identifier == null) {
      showInSnackBar(
          "Ошибка. Нет идентификатора у истории. Обратитесь к администратору.",
          context);
      return;
    }

    if (history.task != null) {
      showWorkerStatusPage(history, null);
      return;
    }

    var controller = ref.read(
        selectJobPageControllerProvider(widget.mainID, widget.componentID)
            .notifier);

    var identifier = history.identifier!;

    // show choices what do with work.

    var finishBtn = CommonButtonModel(
        name: "Завершить",
        callback: () {
          controller.changeStatusJob(HistoryStatusType.finish, identifier);
        });
    var continueBtn = CommonButtonModel(
        name: "Возобновить",
        callback: () {
          controller.changeStatusJob(HistoryStatusType.inwork, identifier);
        });
    var pauseBtn = CommonButtonModel(
        name: "Приостановить",
        callback: () {
          controller.changeStatusJob(HistoryStatusType.pause, identifier);
        });
    var cancelBtn = CommonButtonModel(name: "Отмена", callback: () {});

    List<CommonButtonModel> actionButtons = [];

    var lastStatus = history.statuses.last.status;

    if (lastStatus == HistoryStatusType.inwork) {
      actionButtons = [finishBtn, pauseBtn, cancelBtn];
    } else if (lastStatus == HistoryStatusType.pause) {
      actionButtons = [finishBtn, continueBtn, cancelBtn];
    } else {
      showInSnackBar(
          "Неверный текущий статус работы. Обратитесь к администратору.",
          context);
      return;
    }

    showDialog(
        context: context,
        builder: (_) => ActionsAlertDialog(
            title: "Выберите новый статус", actionButtons: actionButtons));
  }

  List<ComponentHistoryModel> getActiveHistories(
      // фильтр текущих активных работ
      SelectJobPageController controller) {
    var component = controller.getComponent();

    var histories = component?.newHistory;

    if (histories == null) {
      return [];
    }

    var area = ref.watch(areaForIDProvider(widget.areaID));

    return histories.where((hist) {
      if (hist.area == (area?.identifier ?? 0)) {
        var lastStatus = hist.statuses.last.status;

        if (lastStatus == HistoryStatusType.pause ||
            lastStatus == HistoryStatusType.inwork) {
          return true;
        }
      }

      return false;
    }).toList();
  }

  Widget getActionButtons(SelectJobPageController controller) {
    // конфигурация кнопок действий в зависимости от текущего участка
    var area = ref.watch(areaForIDProvider(widget.areaID));

    var component = controller.getComponent();

    if (area?.identifier != component?.currentArea) {
      return Padding(
        padding: const EdgeInsets.all(8.0),
        child:
            CommonButton(name: "Добавить комментарий", callback: tapAddComment),
      );
    } else {
      return Padding(
        padding: const EdgeInsets.all(8),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CommonButton(
                name: "Изменить доступ к компоненту",
                callback: tapChangeAccessItem),
            const SizedBox(
              height: 8,
            ),
            CommonButton(
                name: "Передать на другой участок",
                callback: tapTransferItemToArea),
            const SizedBox(
              height: 8,
            ),
            CommonButton(name: "Добавить комментарий", callback: tapAddComment)
          ],
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    var controller = ref.watch(
        selectJobPageControllerProvider(widget.mainID, widget.componentID)
            .notifier);

    var activeHistories = getActiveHistories(controller);

    var component = controller.getComponent();

    var value = ref
        .watch(
            selectJobPageControllerProvider(widget.mainID, widget.componentID))
        .value;

    return Scaffold(
      appBar: CustomAppBar(
        text: value?.repairNumberFormat() ?? "Добавление работ",
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const ItemHeaderText(name: "Информация"),
            ShortSelectInfoComponent(
              repairNumber: value?.repairNumberFormat() ?? "---",
              componentType: component?.type.desc ?? "---",
            ),
            const ItemHeaderText(name: "Действия"),
            getActionButtons(controller),
            const ItemHeaderText(name: "Работы"),
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              //shrinkWrap: true,
              itemCount: activeHistories.length,
              itemBuilder: (context, position) {
                final history = activeHistories[position];

                callback() {
                  tapToActiveWork(history);
                }

                return JobInfoCard(
                  history: history,
                  callback: callback,
                );
              },
            ),
            CommonCardRow(
                name: "Добавить новую работу",
                icon: Icons.add,
                callback: tapAddNewJob),
          ],
        ),
      ),
    );
  }
}
