import 'dart:async';

import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:uer_flutter/app/core/providers/notifier_mouted.dart';
import '../../../core/models/area/area_model.dart';
import '../../../core/models/item/item_model.dart';
import '../../../core/services/api/service_provider.dart';
import '../../../core/providers/states/areas/areas_state.dart';

import '../../../core/providers/update_providers.dart';

part 'select_area_access_controller.g.dart';

class SelectAreaAccessModel {
  List<AreaModel> areas;
  List<int> selectedAreasIDs;

  SelectAreaAccessModel(this.areas, this.selectedAreasIDs);
}

@riverpod
class SelectAreaAccessController extends _$SelectAreaAccessController
    with NotifierMounted {
  @override
  SelectAreaAccessModel build(
      int branchID, int areaID, List<int> selectedAreas, String componentID) {
    ref.onDispose(setUnmounted);
    var areas = getAreas(branchID, areaID);
    return SelectAreaAccessModel(areas, selectedAreas);
  }

  List<AreaModel> getAreas(int branchID, int areaID) {
    var allAreas = ref.read(areasProvider);

    var filteredAreas = allAreas.where((element) {
      if (element.identifier == areaID) {
        return false;
      }

      return element.branch == branchID;
    });

    return filteredAreas.toList();
  }

  void changeSelectedArea(AreaModel areaModel) {
    if (state.selectedAreasIDs.contains(areaModel.identifier)) {
      List<int> newSelectedAreas = [];

      for (var id in state.selectedAreasIDs) {
        if (id != areaModel.identifier) {
          newSelectedAreas.add(id);
        }
      }

      var newState = SelectAreaAccessModel(state.areas, newSelectedAreas);

      if (mounted) {
        state = newState;
      }
    } else {
      var newState = SelectAreaAccessModel(
          state.areas, [areaModel.identifier, ...state.selectedAreasIDs]);

      if (mounted) {
        state = newState;
      }
    }
  }

  Future<ItemModel?> saveAreaAccess() async {
    var itemRepository = ref.read(itemRepositoryProvider);

    var newState = await AsyncValue.guard(() async {
      return await itemRepository.changeAccessArea(
          componentID, state.selectedAreasIDs);
    });

    if (newState.value != null) {
      updateItemInProvidersWithRef(newState.value!, ref);
      return newState.value;
    }

    return null;
  }
}
