import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/models/area/area_model.dart';
import '../../../helpers/snack_bar.dart';
import '../../../widgets/bars/app_bar.dart';
import 'select_area_access_controller.dart';

class SelectAreaAccessPage extends ConsumerStatefulWidget {
  const SelectAreaAccessPage(
      {super.key,
      required this.branchID,
      required this.areaID,
      required this.componentID,
      required this.selectedAreas});

  final int branchID;
  final int areaID;
  final String componentID;
  final List<int> selectedAreas;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _SelectAreaAccessPageState();
}

class _SelectAreaAccessPageState extends ConsumerState<SelectAreaAccessPage> {
  void tapToArea(AreaModel areaModel) {
    var controller = ref.read(selectAreaAccessControllerProvider(
            widget.branchID,
            widget.areaID,
            widget.selectedAreas,
            widget.componentID)
        .notifier);
    controller.changeSelectedArea(areaModel);
  }

  void tapToSaveBtn() async {
    var controller = ref.read(selectAreaAccessControllerProvider(
            widget.branchID,
            widget.areaID,
            widget.selectedAreas,
            widget.componentID)
        .notifier);
    var result = await controller.saveAreaAccess();

    if (result != null) {
      context.pop(result);
    } else {
      showInSnackBar("Ошибка. Не сохранено. Попробуйте позже.", context);
    }
  }

  @override
  Widget build(BuildContext context) {
    var model = ref.watch(selectAreaAccessControllerProvider(widget.branchID,
        widget.areaID, widget.selectedAreas, widget.componentID));

    return Scaffold(
        appBar: CustomAppBar(
          text: "Доступ к компоненту",
          actions: [
            IconButton(
              icon: const Icon(Icons.save),
              tooltip: 'Сохранить',
              onPressed: tapToSaveBtn,
            ),
          ],
        ),
        body: ListView.builder(
          shrinkWrap: true,
          itemCount: model.areas.length,
          itemBuilder: (context, position) {
            final area = model.areas[position];

            callback() {
              tapToArea(area);
            }

            var checkOn = model.selectedAreasIDs.contains(area.identifier);

            return ListTile(
              title: Text(area.name),
              trailing: checkOn
                  ? const Icon(
                      Icons.check_box,
                      color: Colors.orange,
                    )
                  : const SizedBox(),
              onTap: callback,
            );
          },
        ));
  }
}
