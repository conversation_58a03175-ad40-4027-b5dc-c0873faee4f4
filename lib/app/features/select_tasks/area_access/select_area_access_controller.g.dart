// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'select_area_access_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$selectAreaAccessControllerHash() =>
    r'fc4103c12007b9595c819445e6b8aa050d45905b';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$SelectAreaAccessController
    extends BuildlessAutoDisposeNotifier<SelectAreaAccessModel> {
  late final int branchID;
  late final int areaID;
  late final List<int> selectedAreas;
  late final String componentID;

  SelectAreaAccessModel build(
    int branchID,
    int areaID,
    List<int> selectedAreas,
    String componentID,
  );
}

/// See also [SelectAreaAccessController].
@ProviderFor(SelectAreaAccessController)
const selectAreaAccessControllerProvider = SelectAreaAccessControllerFamily();

/// See also [SelectAreaAccessController].
class SelectAreaAccessControllerFamily extends Family<SelectAreaAccessModel> {
  /// See also [SelectAreaAccessController].
  const SelectAreaAccessControllerFamily();

  /// See also [SelectAreaAccessController].
  SelectAreaAccessControllerProvider call(
    int branchID,
    int areaID,
    List<int> selectedAreas,
    String componentID,
  ) {
    return SelectAreaAccessControllerProvider(
      branchID,
      areaID,
      selectedAreas,
      componentID,
    );
  }

  @override
  SelectAreaAccessControllerProvider getProviderOverride(
    covariant SelectAreaAccessControllerProvider provider,
  ) {
    return call(
      provider.branchID,
      provider.areaID,
      provider.selectedAreas,
      provider.componentID,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'selectAreaAccessControllerProvider';
}

/// See also [SelectAreaAccessController].
class SelectAreaAccessControllerProvider
    extends AutoDisposeNotifierProviderImpl<SelectAreaAccessController,
        SelectAreaAccessModel> {
  /// See also [SelectAreaAccessController].
  SelectAreaAccessControllerProvider(
    int branchID,
    int areaID,
    List<int> selectedAreas,
    String componentID,
  ) : this._internal(
          () => SelectAreaAccessController()
            ..branchID = branchID
            ..areaID = areaID
            ..selectedAreas = selectedAreas
            ..componentID = componentID,
          from: selectAreaAccessControllerProvider,
          name: r'selectAreaAccessControllerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$selectAreaAccessControllerHash,
          dependencies: SelectAreaAccessControllerFamily._dependencies,
          allTransitiveDependencies:
              SelectAreaAccessControllerFamily._allTransitiveDependencies,
          branchID: branchID,
          areaID: areaID,
          selectedAreas: selectedAreas,
          componentID: componentID,
        );

  SelectAreaAccessControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.branchID,
    required this.areaID,
    required this.selectedAreas,
    required this.componentID,
  }) : super.internal();

  final int branchID;
  final int areaID;
  final List<int> selectedAreas;
  final String componentID;

  @override
  SelectAreaAccessModel runNotifierBuild(
    covariant SelectAreaAccessController notifier,
  ) {
    return notifier.build(
      branchID,
      areaID,
      selectedAreas,
      componentID,
    );
  }

  @override
  Override overrideWith(SelectAreaAccessController Function() create) {
    return ProviderOverride(
      origin: this,
      override: SelectAreaAccessControllerProvider._internal(
        () => create()
          ..branchID = branchID
          ..areaID = areaID
          ..selectedAreas = selectedAreas
          ..componentID = componentID,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        branchID: branchID,
        areaID: areaID,
        selectedAreas: selectedAreas,
        componentID: componentID,
      ),
    );
  }

  @override
  AutoDisposeNotifierProviderElement<SelectAreaAccessController,
      SelectAreaAccessModel> createElement() {
    return _SelectAreaAccessControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SelectAreaAccessControllerProvider &&
        other.branchID == branchID &&
        other.areaID == areaID &&
        other.selectedAreas == selectedAreas &&
        other.componentID == componentID;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, branchID.hashCode);
    hash = _SystemHash.combine(hash, areaID.hashCode);
    hash = _SystemHash.combine(hash, selectedAreas.hashCode);
    hash = _SystemHash.combine(hash, componentID.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin SelectAreaAccessControllerRef
    on AutoDisposeNotifierProviderRef<SelectAreaAccessModel> {
  /// The parameter `branchID` of this provider.
  int get branchID;

  /// The parameter `areaID` of this provider.
  int get areaID;

  /// The parameter `selectedAreas` of this provider.
  List<int> get selectedAreas;

  /// The parameter `componentID` of this provider.
  String get componentID;
}

class _SelectAreaAccessControllerProviderElement
    extends AutoDisposeNotifierProviderElement<SelectAreaAccessController,
        SelectAreaAccessModel> with SelectAreaAccessControllerRef {
  _SelectAreaAccessControllerProviderElement(super.provider);

  @override
  int get branchID => (origin as SelectAreaAccessControllerProvider).branchID;
  @override
  int get areaID => (origin as SelectAreaAccessControllerProvider).areaID;
  @override
  List<int> get selectedAreas =>
      (origin as SelectAreaAccessControllerProvider).selectedAreas;
  @override
  String get componentID =>
      (origin as SelectAreaAccessControllerProvider).componentID;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
