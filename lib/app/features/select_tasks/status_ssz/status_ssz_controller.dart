import 'dart:async';

import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:uer_flutter/app/core/providers/notifier_mouted.dart';
import '../../../core/models/task_ssz/task_ssz_model.dart';

import '../../../core/models/component_history/component_history_model.dart';
import '../../../core/services/api/service_provider.dart';

part 'status_ssz_controller.g.dart';

@riverpod
class StatusSszController extends _$StatusSszController with NotifierMounted {
  @override
  FutureOr<List<TaskSSZModel>> build(
      String mainID, String? idArea, List<ComponentHistoryModel> histories) {
    ref.onDispose(setUnmounted);

    if (idArea == null) {
      return [];
    }

    return _fetchItem(mainID, idArea, histories);
  }

  Future<List<TaskSSZModel>> _fetchItem(String mainID, String idArea,
      List<ComponentHistoryModel> histories) async {
    var taskRepository = ref.read(taskRepositoryProvider);
    var items = await taskRepository.getTasks(mainID, idArea, null, null);

    if (items.isNotEmpty) {
      if (histories.isNotEmpty) {
        items = items.where((task) {
          for (var hist in histories) {
            if (task.id == hist.taskID && task.idJob == hist.task) {
              return false;
            }
          }
          return true;
        }).toList();
      }
    }

    return items;
  }
}
