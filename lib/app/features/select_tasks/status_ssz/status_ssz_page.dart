import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:tuple/tuple.dart';

import '../../../core/models/component_history/component_history_model.dart';
import '../../../core/providers/common/common_providers.dart';
import '../../../widgets/bars/app_bar.dart';
import '../../../widgets/header_text_view.dart';
import 'status_ssz_controller.dart';

class StatusSszPage extends ConsumerStatefulWidget {
  const StatusSszPage({
    super.key,
    required this.mainID,
    required this.idArea,
    required this.histories,
    this.idAreaStr,
  });

  final String mainID;
  final int idArea;
  final List<ComponentHistoryModel> histories;
  final String? idAreaStr;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _StatusSszPageState();
}

class _StatusSszPageState extends ConsumerState<StatusSszPage> {
  @override
  Widget build(BuildContext context) {
    var sszTaskArr = ref.watch(statusSszControllerProvider(
        widget.mainID, widget.idAreaStr, widget.histories));

    var statusArr = ref.watch(getStatusesForAreaProvider(null, widget.idArea));

    return Scaffold(
        appBar: const CustomAppBar(
          text: "Выберите работы",
        ),
        body: sszTaskArr.when(
          data: (tasks) => SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const ItemHeaderText(name: "Смено-суточные задания"),
                tasks.isEmpty
                    ? const ListTile(
                        title: Text(
                            "Для этого компонента и участка новых ССЗ нет"),
                      )
                    : const SizedBox(),
                ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  //shrinkWrap: true,
                  itemCount: tasks.length,
                  itemBuilder: (context, position) {
                    final task = tasks[position];
                    final workers = task.workers.map((e) => e.name).toList();

                    callback() {
                      var tuple = Tuple2(null, task);
                      context.pop(tuple);
                    }

                    return ListTile(
                      title: Text(task.name),
                      subtitle: Text(workers.join(",")),
                      onTap: callback,
                    );
                  },
                ),
                const ItemHeaderText(name: "Статусы"),
                ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  //shrinkWrap: true,
                  itemCount: statusArr.length,
                  itemBuilder: (context, position) {
                    final status = statusArr[position];

                    callback() {
                      var tuple = Tuple2(status, null);
                      context.pop(tuple);
                    }

                    return ListTile(
                      title: Text(status.name),
                      onTap: callback,
                    );
                  },
                ),
              ],
            ),
          ),
          loading: () => const Center(
            child: CircularProgressIndicator(),
          ),
          error: (err, stack) => Text('Ошибка: $err'),
        ));
  }
}
