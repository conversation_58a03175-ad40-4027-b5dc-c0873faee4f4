// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'status_ssz_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$statusSszControllerHash() =>
    r'ab3619aefc80154ad6fbbca7afeb9da9fa732a46';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$StatusSszController
    extends BuildlessAutoDisposeAsyncNotifier<List<TaskSSZModel>> {
  late final String mainID;
  late final String? idArea;
  late final List<ComponentHistoryModel> histories;

  FutureOr<List<TaskSSZModel>> build(
    String mainID,
    String? idArea,
    List<ComponentHistoryModel> histories,
  );
}

/// See also [StatusSszController].
@ProviderFor(StatusSszController)
const statusSszControllerProvider = StatusSszControllerFamily();

/// See also [StatusSszController].
class StatusSszControllerFamily extends Family<AsyncValue<List<TaskSSZModel>>> {
  /// See also [StatusSszController].
  const StatusSszControllerFamily();

  /// See also [StatusSszController].
  StatusSszControllerProvider call(
    String mainID,
    String? idArea,
    List<ComponentHistoryModel> histories,
  ) {
    return StatusSszControllerProvider(
      mainID,
      idArea,
      histories,
    );
  }

  @override
  StatusSszControllerProvider getProviderOverride(
    covariant StatusSszControllerProvider provider,
  ) {
    return call(
      provider.mainID,
      provider.idArea,
      provider.histories,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'statusSszControllerProvider';
}

/// See also [StatusSszController].
class StatusSszControllerProvider extends AutoDisposeAsyncNotifierProviderImpl<
    StatusSszController, List<TaskSSZModel>> {
  /// See also [StatusSszController].
  StatusSszControllerProvider(
    String mainID,
    String? idArea,
    List<ComponentHistoryModel> histories,
  ) : this._internal(
          () => StatusSszController()
            ..mainID = mainID
            ..idArea = idArea
            ..histories = histories,
          from: statusSszControllerProvider,
          name: r'statusSszControllerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$statusSszControllerHash,
          dependencies: StatusSszControllerFamily._dependencies,
          allTransitiveDependencies:
              StatusSszControllerFamily._allTransitiveDependencies,
          mainID: mainID,
          idArea: idArea,
          histories: histories,
        );

  StatusSszControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.mainID,
    required this.idArea,
    required this.histories,
  }) : super.internal();

  final String mainID;
  final String? idArea;
  final List<ComponentHistoryModel> histories;

  @override
  FutureOr<List<TaskSSZModel>> runNotifierBuild(
    covariant StatusSszController notifier,
  ) {
    return notifier.build(
      mainID,
      idArea,
      histories,
    );
  }

  @override
  Override overrideWith(StatusSszController Function() create) {
    return ProviderOverride(
      origin: this,
      override: StatusSszControllerProvider._internal(
        () => create()
          ..mainID = mainID
          ..idArea = idArea
          ..histories = histories,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        mainID: mainID,
        idArea: idArea,
        histories: histories,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<StatusSszController,
      List<TaskSSZModel>> createElement() {
    return _StatusSszControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is StatusSszControllerProvider &&
        other.mainID == mainID &&
        other.idArea == idArea &&
        other.histories == histories;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, mainID.hashCode);
    hash = _SystemHash.combine(hash, idArea.hashCode);
    hash = _SystemHash.combine(hash, histories.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin StatusSszControllerRef
    on AutoDisposeAsyncNotifierProviderRef<List<TaskSSZModel>> {
  /// The parameter `mainID` of this provider.
  String get mainID;

  /// The parameter `idArea` of this provider.
  String? get idArea;

  /// The parameter `histories` of this provider.
  List<ComponentHistoryModel> get histories;
}

class _StatusSszControllerProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<StatusSszController,
        List<TaskSSZModel>> with StatusSszControllerRef {
  _StatusSszControllerProviderElement(super.provider);

  @override
  String get mainID => (origin as StatusSszControllerProvider).mainID;
  @override
  String? get idArea => (origin as StatusSszControllerProvider).idArea;
  @override
  List<ComponentHistoryModel> get histories =>
      (origin as StatusSszControllerProvider).histories;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
