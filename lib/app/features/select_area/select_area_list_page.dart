import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../core/models/area/area_model.dart';
import '../../core/providers/states/areas/areas_state.dart';
import '../../widgets/bars/app_bar.dart';

class SelectAreaListPage extends ConsumerStatefulWidget {
  const SelectAreaListPage({
    super.key,
    this.branchID,
    this.removeAreaID,
  });

  final int? branchID;
  final int? removeAreaID;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _SelectAreaListPageState();
}

class _SelectAreaListPageState extends ConsumerState<SelectAreaListPage> {
  void tapToArea(AreaModel areaModel) {
    context.pop(areaModel);
  }

  @override
  Widget build(BuildContext context) {
    var areas = ref.watch(areasProvider);

    var filteredAreas = areas.where((area) {
      if (widget.branchID != null) {
        if (area.branch != widget.branchID) {
          return false;
        }
      }
      if (area.identifier == widget.removeAreaID) {
        return false;
      }
      return true;
    }).toList();

    return Scaffold(
        appBar: const CustomAppBar(
          text: "Выберите участок",
        ),
        body: ListView.builder(
          shrinkWrap: true,
          itemCount: filteredAreas.length,
          itemBuilder: (context, position) {
            final area = filteredAreas[position];

            callback() {
              tapToArea(area);
            }

            return ListTile(
              title: Text(area.name),
              onTap: callback,
            );
          },
        ));
  }
}
