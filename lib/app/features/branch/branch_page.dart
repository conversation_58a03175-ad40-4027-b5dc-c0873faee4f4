import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:uer_flutter/app/core/models/stats/area_stats_model.dart';
import 'package:uer_flutter/app/features/branch/widgets/areas.dart';
import 'package:uer_flutter/app/features/concern/widgets/orders.dart';
import 'package:uer_flutter/app/features/concern/widgets/stats.dart';
import 'package:uer_flutter/app/widgets/bars/app_bar.dart';
import 'package:uer_flutter/app/widgets/items/item_list.dart';
import 'package:uer_flutter/app/widgets/search/search_panel.dart';

import '../../core/models/item/item_model.dart';
import '../../core/models/search/search_model.dart';
import '../../core/providers/common/common_providers.dart';
import '../../core/providers/states/areas/areas_state.dart';
import '../../helpers/constants.dart';
import '../../helpers/debouncer.dart';
import '../../routes/routes_name.dart';
import 'branch_controller.dart';

class MainBranchPage extends ConsumerStatefulWidget {
  const MainBranchPage({super.key, this.branchID});

  final int? branchID;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _MainBranchPageState();
}

class _MainBranchPageState extends ConsumerState<MainBranchPage> {
  SearchModel searchModel = const SearchModel();
  final _debouncer = Debouncer(milliseconds: 650);

  @override
  void initState() {
    searchModel = _defaultSearch();
    super.initState();
  }

  SearchModel _defaultSearch() {
    var branchID =
        widget.branchID ?? ref.read(currentUserBranchProvider)?.identifier ?? 0;

    return SearchModel(
        branch: branchID, finished: false, projection: PROJECTION_SEARCH_ITEM);
  }

  void _updateSearchTerm(SearchModel? newSearchModel) {
    _debouncer.run(() {
      setState(() {
        searchModel = newSearchModel ?? _defaultSearch();
        searchModel = searchModel.copyWith(
          searchField: newSearchModel?.searchField,
          searchClient: newSearchModel?.searchClient,
          mainID: newSearchModel?.mainID,
          searchEquipment: newSearchModel?.searchEquipment,
        );
      });
    });
  }

  void _moveToItem(ItemModel item) {
    var mainID = item.mainID ?? "0";
    context.pushNamed(RoutesName.item.named, pathParameters: {'mid': mainID});
  }

  void _moveToArea(int areaID) {
    context
        .pushNamed(RoutesName.area.named, pathParameters: {'aid': "$areaID"});
  }

  void _openNotificationHistory() {
    context.pushNamed(RoutesName.notificationHistory.named);
  }

  Future<void> _refreshStats() async {
    final branch = widget.branchID != null
        ? ref.read(branchForIDProvider(widget.branchID!))!
        : ref.read(currentUserBranchProvider)!;

    await ref
        .read(branchControllerProvider(branch.identifier).notifier)
        .refreshStats();
  }

  @override
  Widget build(BuildContext context) {
    final branch = widget.branchID != null
        ? ref.watch(branchForIDProvider(widget.branchID!))!
        : ref.watch(currentUserBranchProvider)!;

    final areas = ref.read(areasProvider);
    final filteredAreas =
        areas.where((area) => area.branch == branch.identifier).toList();
    final model = ref.watch(branchControllerProvider(branch.identifier));
    final stats = model.value?.item2;

    final showItems = searchModel.showSearchResult();
    final showCollection = stats != null;
    final screenWidth = MediaQuery.of(context).size.width;

    // Sorting areas by statistics
    filteredAreas.sort((a1, a2) {
      final stat1 = stats?.areaStats.firstWhere(
          (stat) => stat.id == a1.identifier,
          orElse: () => const AreaStatsModel(id: 0, count: 0));
      final stat2 = stats?.areaStats.firstWhere(
          (stat) => stat.id == a2.identifier,
          orElse: () => const AreaStatsModel(id: 0, count: 0));
      return (stat2?.count ?? 0) - (stat1?.count ?? 0);
    });

    return Scaffold(
      appBar: CustomAppBar(
        text: branch.name,
        actions: [
          IconButton(
            onPressed: _openNotificationHistory,
            icon: const Icon(Icons.notifications, color: Colors.white),
          )
        ],
      ),
      body: Column(
        children: [
          showCollection
              ? Expanded(
                  child: RefreshIndicator(
                    onRefresh: _refreshStats,
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0),
                      child: screenWidth < 750
                          // Mobile layout
                          ? Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                SearchPanel(callback: _updateSearchTerm),
                                const SizedBox(height: 16.0),
                                if (showItems)
                                  SizedBox(
                                    height: 600.0,
                                    child: ItemList(
                                      callback: _moveToItem,
                                      searchModel: searchModel,
                                    ),
                                  ),
                                const SizedBox(height: 16.0),
                                ...[
                                  ConcernScreenOrders(
                                    stats: stats,
                                    branchID: widget.branchID,
                                  ),
                                  const SizedBox(height: 50.0),
                                  ConcernScreenStats(
                                    stats: stats,
                                    branchID: widget.branchID,
                                  ),
                                  const SizedBox(height: 50.0),
                                  BranchScreenAreas(
                                    areas: filteredAreas,
                                    stats: stats,
                                    moveToBranch: _moveToArea,
                                  ),
                                ],
                              ],
                            )
                          // Desktop layout
                          : Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Expanded(
                                  flex: 3,
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      SearchPanel(callback: _updateSearchTerm),
                                      const SizedBox(height: 16.0),
                                      if (showItems)
                                        SizedBox(
                                          height: 600.0,
                                          child: ItemList(
                                            callback: _moveToItem,
                                            searchModel: searchModel,
                                          ),
                                        ),
                                      if (showItems)
                                        const SizedBox(height: 16.0),
                                      ConcernScreenOrders(
                                          stats: stats,
                                          branchID: widget.branchID),
                                      const SizedBox(height: 50.0),
                                      ConcernScreenStats(
                                        stats: stats,
                                        branchID: widget.branchID,
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(width: 16.0),
                                Expanded(
                                  flex: 2,
                                  child: BranchScreenAreas(
                                    areas: filteredAreas,
                                    stats: stats,
                                    moveToBranch: _moveToArea,
                                  ),
                                ),
                              ],
                            ),
                    ),
                  ),
                )
              : const SizedBox(),
        ],
      ),
    );
  }
}
