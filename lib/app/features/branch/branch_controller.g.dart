// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'branch_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$branchControllerHash() => r'3276990e11453d0e43ba4dbfef6c52c50e4669ba';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$BranchController extends BuildlessAutoDisposeAsyncNotifier<
    Tuple2<List<ItemModel>, StatsModel?>> {
  late final int branchID;

  FutureOr<Tuple2<List<ItemModel>, StatsModel?>> build(
    int branchID,
  );
}

/// See also [BranchController].
@ProviderFor(BranchController)
const branchControllerProvider = BranchControllerFamily();

/// See also [BranchController].
class BranchControllerFamily
    extends Family<AsyncValue<Tuple2<List<ItemModel>, StatsModel?>>> {
  /// See also [BranchController].
  const BranchControllerFamily();

  /// See also [BranchController].
  BranchControllerProvider call(
    int branchID,
  ) {
    return BranchControllerProvider(
      branchID,
    );
  }

  @override
  BranchControllerProvider getProviderOverride(
    covariant BranchControllerProvider provider,
  ) {
    return call(
      provider.branchID,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'branchControllerProvider';
}

/// See also [BranchController].
class BranchControllerProvider extends AutoDisposeAsyncNotifierProviderImpl<
    BranchController, Tuple2<List<ItemModel>, StatsModel?>> {
  /// See also [BranchController].
  BranchControllerProvider(
    int branchID,
  ) : this._internal(
          () => BranchController()..branchID = branchID,
          from: branchControllerProvider,
          name: r'branchControllerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$branchControllerHash,
          dependencies: BranchControllerFamily._dependencies,
          allTransitiveDependencies:
              BranchControllerFamily._allTransitiveDependencies,
          branchID: branchID,
        );

  BranchControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.branchID,
  }) : super.internal();

  final int branchID;

  @override
  FutureOr<Tuple2<List<ItemModel>, StatsModel?>> runNotifierBuild(
    covariant BranchController notifier,
  ) {
    return notifier.build(
      branchID,
    );
  }

  @override
  Override overrideWith(BranchController Function() create) {
    return ProviderOverride(
      origin: this,
      override: BranchControllerProvider._internal(
        () => create()..branchID = branchID,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        branchID: branchID,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<BranchController,
      Tuple2<List<ItemModel>, StatsModel?>> createElement() {
    return _BranchControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is BranchControllerProvider && other.branchID == branchID;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, branchID.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin BranchControllerRef on AutoDisposeAsyncNotifierProviderRef<
    Tuple2<List<ItemModel>, StatsModel?>> {
  /// The parameter `branchID` of this provider.
  int get branchID;
}

class _BranchControllerProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<BranchController,
        Tuple2<List<ItemModel>, StatsModel?>> with BranchControllerRef {
  _BranchControllerProviderElement(super.provider);

  @override
  int get branchID => (origin as BranchControllerProvider).branchID;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
