import 'dart:async';

import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:tuple/tuple.dart';
import 'package:uer_flutter/app/core/providers/notifier_mouted.dart';
import 'package:uer_flutter/app/core/providers/states/areas/areas_state.dart';
import '../../core/models/item/item_model.dart';
import '../../core/models/search/search_model.dart';

import '../../core/models/stats/stats_model.dart';
import '../../core/services/api/service_provider.dart';

part 'branch_controller.g.dart';

@riverpod
class BranchController extends _$BranchController with NotifierMounted {
  @override
  FutureOr<Tuple2<List<ItemModel>, StatsModel?>> build(int branchID) {
    ref.onDispose(setUnmounted);

    return getStats();
  }

  Future<List<ItemModel>> _fetchItems(SearchModel searchModel) async {
    searchModel = searchModel.copyWith(branch: branchID);

    var apiClient = ref.read(itemRepositoryProvider);
    var items = await apiClient.getItems(searchModel);
    return items ?? [];
  }

  Future<Tuple2<List<ItemModel>, StatsModel?>> getStats() async {
    var branchsIds = [branchID];

    var areas = ref.watch(areasProvider);

    List<int> areasForCurrentBranch = [];

    for (var area in areas) {
      if (area.branch == branchID) {
        areasForCurrentBranch.add(area.identifier);
      }
    }

    var apiClient = ref.watch(itemRepositoryProvider);

    var stats = await apiClient.getStats(branchsIds, areasForCurrentBranch);
    return Tuple2<List<ItemModel>, StatsModel?>([], stats);
  }

  Future<void> getItems(SearchModel searchModel) async {
    state = const AsyncValue.loading();

    var newState = await AsyncValue.guard(() async {
      return Tuple2(await _fetchItems(searchModel), state.value?.item2);
    });

    if (mounted) {
      state = newState;
    }
  }

  Future<void> refreshStats() async {
    state = const AsyncValue.loading();

    var newState = await AsyncValue.guard(() async {
      return getStats();
    });

    if (mounted) {
      state = newState;
    }
  }

  void updateItem(ItemModel item) async {
    if (state.value == null) {
      return;
    }

    var newArr = state.value!.item1.toList();

    for (var i = 0; i < state.value!.item1.length; i++) {
      var value = state.value!.item1[i];

      if (value.mainID == item.mainID) {
        newArr[i] = item;
        break;
      }
    }

    var newState = await AsyncValue.guard(() async {
      return Tuple2(newArr, state.value?.item2);
    });

    if (mounted) {
      state = newState;
    }
  }
}
