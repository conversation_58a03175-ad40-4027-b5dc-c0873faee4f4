import 'package:flutter/material.dart';
import 'package:uer_flutter/app/core/models/area/area_model.dart';
import 'package:uer_flutter/app/core/models/stats/stats_model.dart';
import 'package:uer_flutter/app/styles/colors.dart';
import 'package:uer_flutter/app/styles/fonts.dart';
import 'package:uer_flutter/app/widgets/cards/main_card.dart';
import 'package:uer_flutter/app/widgets/containers/grid.dart';

class BranchScreenAreas extends StatelessWidget {
  const BranchScreenAreas({
    super.key,
    required this.areas,
    required this.stats,
    required this.moveToBranch,
  });

  final List<AreaModel> areas;
  final StatsModel stats;
  final void Function(int) moveToBranch;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16.0),
        Text(
          'Участки',
          style: AppFonts.titleSmall.merge(
            const TextStyle(color: AppLightColors.medium),
          ),
        ),
        const SizedBox(height: 16.0),
        Grid(
          length: areas.length,
          gap: 4.0,
          builder: (rowIndex, itemIndex) {
            final area = areas[rowIndex + itemIndex];
            return _buildMainCard(area, stats, moveToBranch);
          },
          columns: 1,
        ),
      ],
    );
  }

  Widget _buildMainCard(
    AreaModel area,
    StatsModel? stats,
    void Function(int) moveToArea,
  ) {
    var value = 0;

    for (var stat in stats!.areaStats) {
      if (area.identifier == stat.id) {
        value = stat.count;
      }
    }

    return MainCard(
      title: area.name,
      description: 'Компонентов: $value',
      onTap: () => moveToBranch(area.identifier),
    );
  }
}
