import 'dart:async';

import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:uer_flutter/app/core/providers/notifier_mouted.dart';

import '../../../core/models/item/item_model.dart';
import '../../../core/models/search/search_model.dart';
import '../../../core/providers/update_providers.dart';

import '../../../core/services/api/service_provider.dart';

part 'merge_items_page_controller.g.dart';

@riverpod
class MergeItemsPageController extends _$MergeItemsPageController
    with NotifierMounted {
  @override
  FutureOr<List<ItemModel>> build(String mainID) {
    ref.onDispose(setUnmounted);

    SearchModel model = SearchModel(mainID: mainID);

    return configurateController(model);
  }

  Future<List<ItemModel>> configurateController(SearchModel searchModel) async {
    var items = await _fetchItems(searchModel);

    if (items.isEmpty) {
      return [];
    }

    var mergeItem = items.first;

    var searchModel2 = SearchModel(
        orderEquipment: mergeItem.order?.equipment,
        orderClientName: mergeItem.order?.client?.name);

    if (mergeItem.newBool == false) {
      searchModel2 = searchModel2.copyWith(newBool: true);
    }

    var filteredItems = await _fetchItems(searchModel2);

    return filteredItems;
  }

  ItemModel? getMainMergeItem() {
    if (state.value == null) {
      return null;
    }

    if (state.value?.isEmpty == true) {
      return null;
    }

    for (var item in state.value!) {
      if (item.mainID == mainID) {
        return item;
      }
    }

    return null;
  }

  List<ItemModel> getMergeListItems() {
    if (state.value == null) {
      return [];
    }

    if (state.value?.isEmpty == true) {
      return [];
    }

    var items =
        state.value!.where((element) => element.mainID != mainID).toList();

    return items;
  }

  Future<List<ItemModel>> _fetchItems(SearchModel searchModel) async {
    var apiClient = ref.read(itemRepositoryProvider);
    var items = await apiClient.getItems(searchModel);
    return items ?? [];
  }

  Future<bool> mergeSelectedItem(ItemModel selectedItem) async {
    var currentItem = getMainMergeItem();

    if (currentItem == null) {
      return false;
    }

    var itemProvider = ref.read(itemRepositoryProvider);
    var item = await itemProvider.mergeItems(
        currentItem.mainID!, selectedItem.mainID!);

    if (item != null) {
      updateItemInProvidersWithRef(item, ref);
      return true;
    }

    return false;
  }
}
