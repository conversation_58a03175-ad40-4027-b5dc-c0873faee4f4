// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'merge_items_page_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$mergeItemsPageControllerHash() =>
    r'53e82c579ea179faee9f186c3d831d6307b0f22e';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$MergeItemsPageController
    extends BuildlessAutoDisposeAsyncNotifier<List<ItemModel>> {
  late final String mainID;

  FutureOr<List<ItemModel>> build(
    String mainID,
  );
}

/// See also [MergeItemsPageController].
@ProviderFor(MergeItemsPageController)
const mergeItemsPageControllerProvider = MergeItemsPageControllerFamily();

/// See also [MergeItemsPageController].
class MergeItemsPageControllerFamily
    extends Family<AsyncValue<List<ItemModel>>> {
  /// See also [MergeItemsPageController].
  const MergeItemsPageControllerFamily();

  /// See also [MergeItemsPageController].
  MergeItemsPageControllerProvider call(
    String mainID,
  ) {
    return MergeItemsPageControllerProvider(
      mainID,
    );
  }

  @override
  MergeItemsPageControllerProvider getProviderOverride(
    covariant MergeItemsPageControllerProvider provider,
  ) {
    return call(
      provider.mainID,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'mergeItemsPageControllerProvider';
}

/// See also [MergeItemsPageController].
class MergeItemsPageControllerProvider
    extends AutoDisposeAsyncNotifierProviderImpl<MergeItemsPageController,
        List<ItemModel>> {
  /// See also [MergeItemsPageController].
  MergeItemsPageControllerProvider(
    String mainID,
  ) : this._internal(
          () => MergeItemsPageController()..mainID = mainID,
          from: mergeItemsPageControllerProvider,
          name: r'mergeItemsPageControllerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$mergeItemsPageControllerHash,
          dependencies: MergeItemsPageControllerFamily._dependencies,
          allTransitiveDependencies:
              MergeItemsPageControllerFamily._allTransitiveDependencies,
          mainID: mainID,
        );

  MergeItemsPageControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.mainID,
  }) : super.internal();

  final String mainID;

  @override
  FutureOr<List<ItemModel>> runNotifierBuild(
    covariant MergeItemsPageController notifier,
  ) {
    return notifier.build(
      mainID,
    );
  }

  @override
  Override overrideWith(MergeItemsPageController Function() create) {
    return ProviderOverride(
      origin: this,
      override: MergeItemsPageControllerProvider._internal(
        () => create()..mainID = mainID,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        mainID: mainID,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<MergeItemsPageController,
      List<ItemModel>> createElement() {
    return _MergeItemsPageControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is MergeItemsPageControllerProvider && other.mainID == mainID;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, mainID.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin MergeItemsPageControllerRef
    on AutoDisposeAsyncNotifierProviderRef<List<ItemModel>> {
  /// The parameter `mainID` of this provider.
  String get mainID;
}

class _MergeItemsPageControllerProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<MergeItemsPageController,
        List<ItemModel>> with MergeItemsPageControllerRef {
  _MergeItemsPageControllerProviderElement(super.provider);

  @override
  String get mainID => (origin as MergeItemsPageControllerProvider).mainID;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
