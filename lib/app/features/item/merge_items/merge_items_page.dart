import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:uer_flutter/app/widgets/bars/app_bar.dart';
import 'package:uer_flutter/app/widgets/common_button.dart';
import 'package:uer_flutter/app/widgets/items/item_card.dart';

import '../../../core/models/item/item_model.dart';
import '../../../helpers/snack_bar.dart';
import '../../../widgets/dialogs/actions_alert_dialog.dart';
import 'merge_items_page_controller.dart';

class MergeItemsPage extends ConsumerStatefulWidget {
  const MergeItemsPage({super.key, required this.mainID});

  final String mainID;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _MergeItemsPageState();
}

class _MergeItemsPageState extends ConsumerState<MergeItemsPage> {
  void selectToMerge(ItemModel item) async {
    var success = await ref
        .read(mergeItemsPageControllerProvider(widget.mainID).notifier)
        .mergeSelectedItem(item);

    if (success == true) {
      showInSnackBar("Слияние выполнено успешно.", context);
      context.pop();
    } else {
      showInSnackBar("Ошибка. Слияние не выполнено.", context);
    }
  }

  void showAlertConformationWithItem(ItemModel item) {
    var mainItem = ref
        .read(mergeItemsPageControllerProvider(widget.mainID).notifier)
        .getMainMergeItem();

    if (mainItem == null) {
      return;
    }

    if (mainItem.mainID == null) {
      showInSnackBar(
          "Ошибка. Главный компонент не имеет уникального индентификатора.",
          context);
      return;
    }

    if (item.mainID == null) {
      showInSnackBar(
          "Ошибка. Выбранный компонент не имеет уникального индентификатора.",
          context);
      return;
    }

    var messageStr =
        "Провести слияние двигателя с рем. номером ${mainItem.repairNumberFormat()} (главный) и двигателя с рем. номером ${item.repairNumberFormat()} ?";

    var continueBtn = CommonButtonModel(
        name: "Подтвердить",
        callback: () {
          selectToMerge(item);
        });

    var cancelBtn = CommonButtonModel(name: "Отмена", callback: () {});

    showDialog(
        context: context,
        builder: (_) => ActionsAlertDialog(
            title: "Подтвердите действие",
            text: messageStr,
            actionButtons: [continueBtn, cancelBtn]));
  }

  @override
  Widget build(BuildContext context) {
    var controller =
        ref.watch(mergeItemsPageControllerProvider(widget.mainID).notifier);

    var state = ref.watch(mergeItemsPageControllerProvider(widget.mainID));

    var items = controller.getMergeListItems();

    return Scaffold(
        appBar: const CustomAppBar(text: "Слияние двигателя"),
        body: state.when(data: (data) {
          return items.isNotEmpty
              ? ListView.builder(
                  itemCount: items.length,
                  itemBuilder: (BuildContext context, int index) {
                    var item = items[index];

                    mycallback() {
                      showAlertConformationWithItem(item);
                    }

                    return ItemCard(item: item, callback: mycallback);
                  },
                )
              : const Center(
                  child: Text("Нет подходящих двигателей к слиянию"),
                );
        }, error: (error, stackTrace) {
          return Center(
            child: Text(error.toString()),
          );
        }, loading: () {
          return const Center(child: CircularProgressIndicator());
        }));
  }
}
