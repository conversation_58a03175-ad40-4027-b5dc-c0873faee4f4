import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uer_flutter/app/widgets/containers/stat_row.dart';

import '../../../core/models/item/item_model.dart';
import '../../../widgets/bars/app_bar.dart';

class ItemInfoFullPage extends ConsumerStatefulWidget {
  const ItemInfoFullPage({super.key, required this.item});

  final ItemModel item;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _ItemInfoFullPageState();
}

class _ItemInfoFullPageState extends ConsumerState<ItemInfoFullPage> {
  @override
  Widget build(BuildContext context) {
    var infoArr = widget.item.getInfoItem(true);

    return Scaffold(
      appBar: const CustomAppBar(text: "Общая информация"),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: SizedBox(
            //height: 100,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                for (var info in infoArr)
                  Column(
                    children: [
                      StatRow(info.key, info.value),
                      const SizedBox(
                        height: 4,
                      ),
                    ],
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
