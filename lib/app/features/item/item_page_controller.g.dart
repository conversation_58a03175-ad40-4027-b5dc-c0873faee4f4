// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'item_page_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$itemPageControllerHash() =>
    r'3dda3e3abe02656323389c7451193a547622a0df';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$ItemPageController
    extends BuildlessAutoDisposeAsyncNotifier<ItemModel?> {
  late final String mainID;

  FutureOr<ItemModel?> build(
    String mainID,
  );
}

/// See also [ItemPageController].
@ProviderFor(ItemPageController)
const itemPageControllerProvider = ItemPageControllerFamily();

/// See also [ItemPageController].
class ItemPageControllerFamily extends Family<AsyncValue<ItemModel?>> {
  /// See also [ItemPageController].
  const ItemPageControllerFamily();

  /// See also [ItemPageController].
  ItemPageControllerProvider call(
    String mainID,
  ) {
    return ItemPageControllerProvider(
      mainID,
    );
  }

  @override
  ItemPageControllerProvider getProviderOverride(
    covariant ItemPageControllerProvider provider,
  ) {
    return call(
      provider.mainID,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'itemPageControllerProvider';
}

/// See also [ItemPageController].
class ItemPageControllerProvider extends AutoDisposeAsyncNotifierProviderImpl<
    ItemPageController, ItemModel?> {
  /// See also [ItemPageController].
  ItemPageControllerProvider(
    String mainID,
  ) : this._internal(
          () => ItemPageController()..mainID = mainID,
          from: itemPageControllerProvider,
          name: r'itemPageControllerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$itemPageControllerHash,
          dependencies: ItemPageControllerFamily._dependencies,
          allTransitiveDependencies:
              ItemPageControllerFamily._allTransitiveDependencies,
          mainID: mainID,
        );

  ItemPageControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.mainID,
  }) : super.internal();

  final String mainID;

  @override
  FutureOr<ItemModel?> runNotifierBuild(
    covariant ItemPageController notifier,
  ) {
    return notifier.build(
      mainID,
    );
  }

  @override
  Override overrideWith(ItemPageController Function() create) {
    return ProviderOverride(
      origin: this,
      override: ItemPageControllerProvider._internal(
        () => create()..mainID = mainID,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        mainID: mainID,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<ItemPageController, ItemModel?>
      createElement() {
    return _ItemPageControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ItemPageControllerProvider && other.mainID == mainID;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, mainID.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ItemPageControllerRef on AutoDisposeAsyncNotifierProviderRef<ItemModel?> {
  /// The parameter `mainID` of this provider.
  String get mainID;
}

class _ItemPageControllerProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<ItemPageController,
        ItemModel?> with ItemPageControllerRef {
  _ItemPageControllerProviderElement(super.provider);

  @override
  String get mainID => (origin as ItemPageControllerProvider).mainID;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
