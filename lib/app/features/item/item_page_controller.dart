import 'dart:async';

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:uer_flutter/app/core/providers/notifier_mouted.dart';
import 'package:uer_flutter/app/core/providers/states/user/user_state.dart';
import 'package:uer_flutter/app/widgets/dialogs/time_alert_dialog.dart';

import '../../core/models/component_history/component_history_model.dart';
import '../../core/models/enums/component_history_type_enum.dart';
import '../../core/models/enums/history_status_enum.dart';
import '../../core/models/enums/user_role_enum.dart';
import '../../core/models/enums/user_type_enum.dart';
import '../../core/models/item/item_model.dart';
import '../../core/models/search/search_model.dart';
import '../../core/providers/common/common_providers.dart';
import '../../core/providers/update_providers.dart';
import '../../core/services/api/service_provider.dart';
import '../../helpers/snack_bar.dart';
import '../../helpers/string_helper.dart';
import '../../routes/routes_name.dart';

part 'item_page_controller.g.dart';

@riverpod
class ItemPageController extends _$ItemPageController with NotifierMounted {
  @override
  FutureOr<ItemModel?> build(String mainID) {
    ref.onDispose(setUnmounted);
    return _fetchItem(mainID);
  }

  void changeAssembled(bool assembled, BuildContext context) async {
    // assembled true - собрать, false - разобрать

    var item = state.value;

    if (item == null) {
      return;
    }

    if (item.mainID == null) {
      showInSnackBar(
          "Изменение состояния для этого двигателя не поддерживается", context);
      return;
    }

    if (item.components.length < 2) {
      showInSnackBar("Действие невозможно. Меньше двух компонентов.", context);
      return;
    }

    if (item.hasInductorOrStator() == false) {
      showInSnackBar(
          "Действие возможно только на главном компоненте.", context);
      return;
    }

    if (item.checkAllComponentsOnSameArea() == false) {
      showInSnackBar(
          "Действие невозможно. Все компоненты должны находиться на одном участке.",
          context);
      return;
    }

    if (item.allJobsAndTaskFinished() == false) {
      showInSnackBar(
          "Действие невозможно. Работы на всех компонентах должны быть завершены.",
          context);
      return;
    }

    var currentAreaID = item.components.first.currentArea;
    var userState = ref.read(currentUserProvider);
    var user = userState.maybeWhen(auth: (user) => user, orElse: () {});

    var area = ref.read(areaForIDProvider(currentAreaID));

    if (area == null || user == null) {
      return;
    }

    var identifier = getRandomString(16);
    var owner = user.login;

    var name = "";
    var comment = "";

    if (assembled == true) {
      name = "${area.name} | Собран";
      comment = "Двигатель собран";
    } else {
      name = "${area.name} | Разобран";
      comment = "Двигатель разобран";
    }

    var status = ComponentStatusModel(
        owner: owner,
        comment: comment,
        status: HistoryStatusType.info,
        createdAt: null,
        updatedAt: null);

    var history = ComponentHistoryModel(
        name: name,
        area: area.identifier,
        job: null,
        task: null,
        identifier: identifier,
        type: ComponentHistoryType.info,
        statuses: [status],
        createdAt: null,
        updatedAt: null);

    if (assembled == true) {
      showInSnackBar("Двигатель собран", context);
    } else {
      showInSnackBar("Двигатель разобран", context);
    }

    state = const AsyncValue.loading();

    var newItem = await AsyncValue.guard(() async {
      var result = await ref
          .read(itemRepositoryProvider)
          .changeAssembled(mainID, assembled, history);

      return result;
    });

    if (newItem.value != null) {
      updateItemInProvidersWithRef(newItem.value!, ref);
    } else if (mounted) {
      state = newItem;
    }
  }

  void _subscribeItemChange(String mainID, bool subscribe) async {
    var userNotifier = ref.read(currentUserProvider.notifier);
    //userNotifier.changeSubscribeItem(mainID, subscribe);
    var user = ref.read(currentUserModelProvider);
    var userProvider = ref.read(userRepositoryProvider);

    var success = await userProvider.subscribeItemChange(
        user?.login ?? "", mainID, subscribe);

    if (success == true) {
      userNotifier.updateCurrentUser();
    }
  }

  void _subscribeOutageOpenSettings(String mainID, bool subscribed,
      BuildContext context, String? currentValue) async {
    var user = ref.read(currentUserModelProvider);
    var userNotifier = ref.read(currentUserProvider.notifier);

    var currentValueDaysStr = currentValue?.split(":").last;
    int? currentValueDays;

    if (currentValueDaysStr != null) {
      currentValueDays = int.parse(currentValueDaysStr);
    }

    var result = await showDialog(
        context: context,
        builder: (_) => TimeAlertDialog(
              title:
                  "Выберите количество дней, после которых вы хотите, чтобы вас уведомили о простое.",
              subscribed: subscribed,
              currentValue: currentValueDays,
            ));

    var userProvider = ref.read(userRepositoryProvider);

    if (result is int) {
      if (result == 0) {
        await userProvider.subscribeOutageChange(
            user?.login ?? "", currentValue ?? "$mainID:1", false);

        //userNotifier.changeSubscribeOutAge(mainID, result, false);

        // unsubcribe
      } else {
        await userProvider.subscribeOutageChange(
            user?.login ?? "", "$mainID:$result", true);

        //userNotifier.changeSubscribeOutAge(mainID, result, true);
        // subcribe or change timestamp
      }

      userNotifier.updateCurrentUser();
    }
  }

  List<PopupMenuItem<int>> getSubcribeMenu(BuildContext context) {
    var item = state.value;
    var user = ref.read(currentUserModelProvider);

    if (item == null || user == null) {
      return [];
    }

    var subscribeItems = user.subscribeItems;
    var subscribeOutAge = user.subscribeOutage;

    String? currentValue;

    if (subscribeOutAge != null) {
      for (var value in subscribeOutAge) {
        if (value.contains(item.mainID ?? "")) {
          final valueStr = value.split(":").last;
          currentValue = valueStr;
          break;
        }
      }
    }

    List<PopupMenuItem<int>> menuItems = [];

    if (subscribeItems?.contains(item.mainID) == true) {
      menuItems.add(PopupMenuItem<int>(
        value: 0,
        child: const Text('Отписаться от активности'),
        onTap: () {
          _subscribeItemChange(item.mainID!, false);
        },
      ));
    } else {
      menuItems.add(PopupMenuItem<int>(
        value: 0,
        child: const Text('Подписаться на активность'),
        onTap: () {
          _subscribeItemChange(item.mainID!, true);
        },
      ));
    }

    String? contains;

    if (subscribeOutAge?.isNotEmpty == true) {
      contains = subscribeOutAge?.firstWhere(
        (element) => element.contains(item.mainID ?? ""),
        orElse: () => "",
      );
    }

    if ((contains?.length ?? 0) > 0) {
      menuItems.add(PopupMenuItem<int>(
        value: 1,
        child: const Text('Настроить уведомления о простое'),
        onTap: () {
          _subscribeOutageOpenSettings(
              item.mainID ?? "", true, context, currentValue);
        },
      ));
    } else {
      menuItems.add(PopupMenuItem<int>(
        value: 1,
        child: const Text('Подписаться на уведомления о простое'),
        onTap: () {
          _subscribeOutageOpenSettings(
              item.mainID ?? "", false, context, currentValue);
        },
      ));
    }

    return menuItems;
  }

  List<PopupMenuItem<int>> getActionMenu(BuildContext context) {
    var item = state.value;
    var user = ref.read(currentUserModelProvider);
    var currentArea = ref.read(currentUserAreaProvider);

    if (item == null || user == null) {
      return [];
    }

    List<PopupMenuItem<int>> menuItems = [];

    if (user.userType == UserType.distributor || user.role == UserRole.admin) {
      if (item.components.length > 1 && item.hasInductorOrStator() == true) {
        if (item.assembled == false || item.assembled == null) {
          menuItems.add(PopupMenuItem<int>(
            value: 0,
            child: const Text('Собрать'),
            onTap: () => changeAssembled(true, context),
          ));
        } else {
          menuItems.add(PopupMenuItem<int>(
              value: 0,
              child: const Text('Разобрать'),
              onTap: () => changeAssembled(false, context)));
        }
      }
    }

    if (user.role == UserRole.admin || currentArea?.start == true) {
      menuItems.add(PopupMenuItem<int>(
          value: 0,
          child: const Text('QR коды'),
          onTap: () =>
              context.pushNamed(RoutesName.qrGenerator.named, queryParameters: {
                'mid': mainID,
              })));
    }

    if (user.userType == UserType.pdo || user.role == UserRole.admin) {
      if (item.newBool == true) {
        menuItems.add(PopupMenuItem<int>(
            value: 0,
            child: const Text('Слияние ЗПР'),
            onTap: () {
              context.pushNamed(RoutesName.mergeItems.named, extra: mainID);
            }));
      }
    }

    return menuItems;
  }

  Future<void> reloadItem() async {
    await getItem(state.value!.mainID!);
  }

  Future<ItemModel?> _fetchItem(String mainID) async {
    SearchModel search = SearchModel(mainID: mainID);

    var itemRepository = ref.read(itemRepositoryProvider);
    var items = await itemRepository.getItems(search);
    return items?.first;
  }

  Future<void> getItem(String mainID) async {
    state = const AsyncValue.loading();

    var newState = await AsyncValue.guard(() async {
      return _fetchItem(mainID);
    });

    if (mounted) {
      state = newState;
    }
  }

  void updateData(ItemModel data) async {
    state = await AsyncValue.guard(() async {
      return data;
    });
  }
}
