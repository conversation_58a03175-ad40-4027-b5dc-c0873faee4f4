import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:uer_flutter/app/styles/colors.dart';
import 'package:uer_flutter/app/widgets/bars/app_bar.dart';
import 'package:uer_flutter/app/widgets/cards/main_card.dart';
import 'package:uer_flutter/app/widgets/comment_short_info.dart';
import 'package:uer_flutter/app/widgets/component_cell.dart';
import 'package:uer_flutter/app/widgets/header_text_view.dart';
import 'package:uer_flutter/app/widgets/interactive/chip.dart';
import 'package:uer_flutter/app/widgets/item_info/item_info_short.dart';
import 'package:uer_flutter/gen/assets.gen.dart';

import '../../core/models/Stats_outage/stats_outage_model.dart';
import '../../core/models/enums/user_role_enum.dart';
import '../../core/models/item/item_model.dart';
import '../../core/providers/common/common_providers.dart';
import '../../routes/routes_name.dart';
import 'item_page_controller.dart';
import 'item_page_stats_controller.dart';

class ItemPage extends ConsumerStatefulWidget {
  const ItemPage({super.key, required this.mainID});

  final String mainID;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _ItemPageState();
}

class _ItemPageState extends ConsumerState<ItemPage> {
  void moveToComponent(int index) {
    var item = ref.read(itemPageControllerProvider(widget.mainID)).value;

    if (item == null) {
      return;
    }

    var component = item.components[index];
    context.pushNamed(RoutesName.component.named, pathParameters: {
      'cid': component.identifier,
    });
  }

  void openFullInfo() {
    var item = ref.read(itemPageControllerProvider(widget.mainID)).value;

    if (item == null) {
      return;
    }

    context.pushNamed(RoutesName.itemInfo.named, extra: item);
  }

  void openStats() {
    context.pushNamed(RoutesName.itemStats.named, extra: widget.mainID);
  }

  int? getPhotosQuantity() {
    final length = ref
        .read(itemPageControllerProvider(widget.mainID))
        .value
        ?.photos
        ?.length;
    return length;
  }

  int? getVideosQuantity() {
    final length = ref
        .read(itemPageControllerProvider(widget.mainID))
        .value
        ?.videosTest
        ?.length;
    return length;
  }

  void openPhoto() {
    var item = ref.read(itemPageControllerProvider(widget.mainID)).value;

    if (item == null) {
      return;
    }

    context.pushNamed(RoutesName.photos.named, extra: item);
  }

  void openVideoTest() {
    var item = ref.read(itemPageControllerProvider(widget.mainID)).value;

    if (item == null) {
      return;
    }

    var video = item.videosTest?.first;

    if (video == null) {
      return;
    }

    context.pushNamed(RoutesName.videos.named, extra: item.videosTest!);
  }

  void openCommentPage() {
    var item = ref.read(itemPageControllerProvider(widget.mainID)).value;

    if (item != null) {
      context.pushNamed(RoutesName.commentsPage.named,
          extra: item.components,
          pathParameters: {
            'mid': widget.mainID,
          });
    }
  }

  @override
  Widget build(BuildContext context) {
    var user = ref.watch(currentUserModelProvider);

    var value = ref.watch(itemPageControllerProvider(widget.mainID));
    var itemValue = value.value;

    var videoShow = (itemValue?.videosTest?.length ?? 0) > 0;

    var showStats = false;

    StatsOutAgeModel? stats;

    if (user?.role == UserRole.admin ||
        user?.role == UserRole.manager ||
        user?.role == UserRole.director) {
      stats = ref.watch(itemPageStatsControllerProvider(widget.mainID)).value;
      showStats = true;
    }

    return Scaffold(
        appBar: CustomAppBar(
          text: itemValue?.repairNumberFormat() ?? "Двигатель",
          actions: [
            PopupMenuButton<int>(
              icon: const Icon(Icons.notifications_active),
              onSelected: (item) {},
              itemBuilder: (context) => ref
                  .read(itemPageControllerProvider(widget.mainID).notifier)
                  .getSubcribeMenu(context),
            ),
            PopupMenuButton<int>(
              onSelected: (item) {},
              itemBuilder: (context) => ref
                  .read(itemPageControllerProvider(widget.mainID).notifier)
                  .getActionMenu(context),
            ),
          ],
        ),
        body: value.when(
          data: (item) => item != null
              ? ListView(
                  padding: const EdgeInsets.all(16.0),
                  children: [
                    ItemInfoBlock(
                      item: item,
                      stats: stats,
                      infoCallback: openFullInfo,
                      statsCallback: openStats,
                      showStats: showStats,
                      videoShow: videoShow,
                      openPhoto: openPhoto,
                      openVideoTest: openVideoTest,
                      photosQuantity: getPhotosQuantity() ?? 0,
                      videosQuantity: getVideosQuantity() ?? 0,
                    ),
                    const ItemHeaderText(name: "Комментарии"),
                    CommentShortInfoCard(
                        components: item.components, callback: openCommentPage),
                    const ItemHeaderText(name: "Компоненты"),
                    ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      //shrinkWrap: true,
                      itemCount: item.components.length,
                      itemBuilder: (context, position) {
                        final component = item.components[position];

                        callback() {
                          moveToComponent(position);
                        }

                        return ComponentCell(
                          item: item,
                          component: component,
                          currentAreaID: component.currentArea,
                          callback: callback,
                        );
                      },
                    ),
                  ],
                )
              : const SizedBox(),
          loading: () => const Center(
            child: CircularProgressIndicator(),
          ),
          error: (err, stack) => Text('Ошибка: $err'),
        ));
  }
}

class ItemInfoBlock extends StatelessWidget {
  const ItemInfoBlock({
    super.key,
    required this.item,
    this.stats,
    required this.showStats,
    required this.infoCallback,
    required this.statsCallback,
    required this.videoShow,
    required this.openPhoto,
    required this.openVideoTest,
    required this.photosQuantity,
    required this.videosQuantity,
  });

  final StatsOutAgeModel? stats;
  final ItemModel item;
  final bool showStats;
  final bool videoShow;
  final Function() statsCallback;
  final Function() infoCallback;
  final void Function() openPhoto;
  final void Function() openVideoTest;
  final int photosQuantity;
  final int videosQuantity;

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;

    return width > 760
        ? Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const ItemHeaderText(name: "Общая информация"),
                  const SizedBox(height: 16.0),
                  ItemInfoShort(
                    infoArr: item.getInfoItem(false),
                    showDetailBtn: true,
                    callback: infoCallback,
                  ),
                  const SizedBox(height: 12.0),
                  Wrap(spacing: 4.0, runSpacing: 4.0, children: [
                    ...(item.tags ?? []).map((tag) => CustomChip(
                          isEnabled: true,
                          label: tag.getName(),
                          color: tag.getColor(),
                        )),
                  ]),
                  const SizedBox(height: 8.0),
                  MainCard(
                    title: "Фото Галерея",
                    icon: Assets.icons.photoLibrary,
                    backgroundColor: AppLightColors.darkGray,
                    description: '$photosQuantity',
                    textColor: AppLightColors.white,
                    onTap: openPhoto,
                  ),
                  if (videoShow)
                    MainCard(
                      title: "Видео Испытаний",
                      icon: Assets.icons.videocam,
                      description: '$videosQuantity',
                      backgroundColor: AppLightColors.darkGray,
                      textColor: AppLightColors.white,
                      onTap: openVideoTest,
                    ),
                ],
              ),
            ),
            const SizedBox(width: 32.0),
            if (showStats)
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const ItemHeaderText(name: "Статистика"),
                    const SizedBox(height: 16.0),
                    ItemInfoShort(
                      infoArr:
                          stats?.getInfo() ?? StatsOutAgeModel.getEmptyInfo(),
                      showDetailBtn: true,
                      callback: statsCallback,
                    ),
                  ],
                ),
              ),
          ])
        : Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const ItemHeaderText(name: "Общая информация"),
              const SizedBox(height: 16.0),
              ItemInfoShort(
                infoArr: item.getInfoItem(false),
                showDetailBtn: true,
                callback: infoCallback,
              ),
              const SizedBox(height: 8.0),
              Row(
                children: [
                  Expanded(
                    child: MainCard(
                      title: "Фото Галерея",
                      icon: Assets.icons.photoLibrary,
                      backgroundColor: AppLightColors.darkGray,
                      description: '$photosQuantity',
                      textColor: AppLightColors.white,
                      onTap: openPhoto,
                    ),
                  ),
                  if (videoShow)
                    Expanded(
                      child: MainCard(
                        title: "Видео Испытаний",
                        description: '$videosQuantity',
                        icon: Assets.icons.videocam,
                        backgroundColor: AppLightColors.darkGray,
                        textColor: AppLightColors.white,
                        onTap: openVideoTest,
                      ),
                    ),
                ],
              ),
              if (showStats) const SizedBox(height: 32.0),
              if (showStats) const ItemHeaderText(name: "Статистика"),
              if (showStats) const SizedBox(height: 16.0),
              if (showStats)
                ItemInfoShort(
                  infoArr: stats?.getInfo() ?? StatsOutAgeModel.getEmptyInfo(),
                  showDetailBtn: true,
                  callback: statsCallback,
                )
            ],
          );
  }
}
