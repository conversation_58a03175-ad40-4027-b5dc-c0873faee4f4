import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tuple/tuple.dart';
import 'package:uer_flutter/app/core/models/enums/component_type_enum.dart';

import '../../../core/models/component/component_model.dart';
import '../../../core/models/component_history/component_history_model.dart';
import '../../../widgets/bars/app_bar.dart';
import '../../../widgets/comment_card.dart';

class CommentsPage extends ConsumerStatefulWidget {
  const CommentsPage({super.key, required this.components});

  final List<ComponentModel> components;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _CommentsPageState();
}

class _CommentsPageState extends ConsumerState<CommentsPage> {
  @override
  Widget build(BuildContext context) {
    List<Tuple3> comments = [];

    for (var i = 0; i < widget.components.length; i++) {
      var component = widget.components[i];
      var histories = component.newHistory;

      if (histories?.isEmpty == true) {
        continue;
      }

      for (int i = 0; i < component.newHistory!.length; i++) {
        var history = component.newHistory![i];
        var statuses = history.statuses
            .where((status) => ((status.comment?.length) ?? 0) > 1)
            .toList();
        var area = history.getAreaName(null);

        for (int j = 0; j < statuses.length; j++) {
          var status = statuses[j];
          var tuple = Tuple3(status, area, component.type.desc);
          comments.add(tuple);
        }
      }
    }

    comments.sort((a, b) {
      var statusA = a.item1 as ComponentStatusModel;
      var statusB = b.item1 as ComponentStatusModel;

      return (statusB.createdAt ?? 0).compareTo((statusA.createdAt ?? 0));
    });

    return Scaffold(
      appBar: const CustomAppBar(text: "Все комментарии"),
      body: ListView.builder(
        itemCount: comments.length,
        itemBuilder: (BuildContext context, int index) {
          var tuple = comments[index];

          var area = tuple.item2;

          return CommentCard(
              lastStatus: tuple.item1,
              componentName: tuple.item3,
              areaName: area);
        },
      ),
    );
  }
}
