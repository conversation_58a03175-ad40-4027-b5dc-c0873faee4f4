import 'dart:async';

import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:uer_flutter/app/core/providers/notifier_mouted.dart';
import '../../core/models/Stats_outage/stats_outage_model.dart';
import '../../core/services/api/service_provider.dart';

part 'item_page_stats_controller.g.dart';

@riverpod
class ItemPageStatsController extends _$ItemPageStatsController
    with NotifierMounted {
  @override
  FutureOr<StatsOutAgeModel?> build(String mainID) {
    ref.onDispose(setUnmounted);
    return getStats(mainID);
  }

  Future<StatsOutAgeModel?> getStats(String mainID) async {
    var statsProvider = ref.watch(statsRepositoryProvider);
    var result = await statsProvider.getOutAgeStatsOne(mainID, false);
    return result;
  }
}
