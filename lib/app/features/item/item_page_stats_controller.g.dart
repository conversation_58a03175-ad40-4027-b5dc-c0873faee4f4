// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'item_page_stats_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$itemPageStatsControllerHash() =>
    r'0994805a1ac349721b61ae515ecf7b99491d8358';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$ItemPageStatsController
    extends BuildlessAutoDisposeAsyncNotifier<StatsOutAgeModel?> {
  late final String mainID;

  FutureOr<StatsOutAgeModel?> build(
    String mainID,
  );
}

/// See also [ItemPageStatsController].
@ProviderFor(ItemPageStatsController)
const itemPageStatsControllerProvider = ItemPageStatsControllerFamily();

/// See also [ItemPageStatsController].
class ItemPageStatsControllerFamily
    extends Family<AsyncValue<StatsOutAgeModel?>> {
  /// See also [ItemPageStatsController].
  const ItemPageStatsControllerFamily();

  /// See also [ItemPageStatsController].
  ItemPageStatsControllerProvider call(
    String mainID,
  ) {
    return ItemPageStatsControllerProvider(
      mainID,
    );
  }

  @override
  ItemPageStatsControllerProvider getProviderOverride(
    covariant ItemPageStatsControllerProvider provider,
  ) {
    return call(
      provider.mainID,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'itemPageStatsControllerProvider';
}

/// See also [ItemPageStatsController].
class ItemPageStatsControllerProvider
    extends AutoDisposeAsyncNotifierProviderImpl<ItemPageStatsController,
        StatsOutAgeModel?> {
  /// See also [ItemPageStatsController].
  ItemPageStatsControllerProvider(
    String mainID,
  ) : this._internal(
          () => ItemPageStatsController()..mainID = mainID,
          from: itemPageStatsControllerProvider,
          name: r'itemPageStatsControllerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$itemPageStatsControllerHash,
          dependencies: ItemPageStatsControllerFamily._dependencies,
          allTransitiveDependencies:
              ItemPageStatsControllerFamily._allTransitiveDependencies,
          mainID: mainID,
        );

  ItemPageStatsControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.mainID,
  }) : super.internal();

  final String mainID;

  @override
  FutureOr<StatsOutAgeModel?> runNotifierBuild(
    covariant ItemPageStatsController notifier,
  ) {
    return notifier.build(
      mainID,
    );
  }

  @override
  Override overrideWith(ItemPageStatsController Function() create) {
    return ProviderOverride(
      origin: this,
      override: ItemPageStatsControllerProvider._internal(
        () => create()..mainID = mainID,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        mainID: mainID,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<ItemPageStatsController,
      StatsOutAgeModel?> createElement() {
    return _ItemPageStatsControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ItemPageStatsControllerProvider && other.mainID == mainID;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, mainID.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ItemPageStatsControllerRef
    on AutoDisposeAsyncNotifierProviderRef<StatsOutAgeModel?> {
  /// The parameter `mainID` of this provider.
  String get mainID;
}

class _ItemPageStatsControllerProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<ItemPageStatsController,
        StatsOutAgeModel?> with ItemPageStatsControllerRef {
  _ItemPageStatsControllerProviderElement(super.provider);

  @override
  String get mainID => (origin as ItemPageStatsControllerProvider).mainID;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
