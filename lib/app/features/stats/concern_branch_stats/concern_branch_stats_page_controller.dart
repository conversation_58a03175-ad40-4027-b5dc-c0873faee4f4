import 'dart:async';

import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../core/models/Stats_outage/stats_outage_model.dart';
import '../../../core/providers/notifier_mouted.dart';
import '../../../core/services/api/service_provider.dart';

part 'concern_branch_stats_page_controller.g.dart';

@riverpod
class ConcernBranchStatsPageController
    extends _$ConcernBranchStatsPageController with NotifierMounted {
  @override
  FutureOr<List<StatsOutAgeModel>> build(int? branch) {
    ref.onDispose(setUnmounted);

    return _fetchStats(branch);
  }

  Future<List<StatsOutAgeModel>> _fetchStats(int? branch) async {
    var statsProvider = ref.watch(statsRepositoryProvider);

    var stats = await statsProvider.getOutAgeStats(branch, false, false);

    return stats;
  }

  StatsCommonModel getCommonStats() {
    var stats = state.valueOrNull;

    if (stats == null) {
      return StatsCommonModel(
          workCounts: 0,
          allHours: 0,
          outAgeHours: 0,
          workerHours: 0,
          workHours: 0,
          statusHours: 0);
    }

    var allHours = 0.0;
    var outAgeHours = 0.0;
    var workerHours = 0.0;
    var workHours = 0.0;
    var statusHours = 0.0;
    var workCounts = 0;

    for (final stat in stats) {
      workCounts += stat.countWork ?? 0;
      workerHours += stat.workerHours ?? 0;
      workHours += stat.workHours ?? 0;
      outAgeHours += stat.outAgeHours;
      statusHours += stat.statusHours ?? 0;

      allHours += (stat.workHours ?? 0) + stat.outAgeHours;
    }

    return StatsCommonModel(
        workCounts: workCounts,
        allHours: allHours,
        outAgeHours: outAgeHours,
        workerHours: workerHours,
        workHours: workHours,
        statusHours: statusHours);
  }
}

class StatsCommonModel {
  final int workCounts;
  final double allHours;
  final double outAgeHours;
  final double workerHours;
  final double workHours;
  final double statusHours;

  StatsCommonModel(
      {required this.workCounts,
      required this.allHours,
      required this.outAgeHours,
      required this.workerHours,
      required this.workHours,
      required this.statusHours});
}
