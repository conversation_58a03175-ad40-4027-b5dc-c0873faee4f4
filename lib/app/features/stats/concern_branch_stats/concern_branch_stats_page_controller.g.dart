// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'concern_branch_stats_page_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$concernBranchStatsPageControllerHash() =>
    r'6bed0d2b1f0ae5e98ce5f45d780d22f2137b15d6';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$ConcernBranchStatsPageController
    extends BuildlessAutoDisposeAsyncNotifier<List<StatsOutAgeModel>> {
  late final int? branch;

  FutureOr<List<StatsOutAgeModel>> build(
    int? branch,
  );
}

/// See also [ConcernBranchStatsPageController].
@ProviderFor(ConcernBranchStatsPageController)
const concernBranchStatsPageControllerProvider =
    ConcernBranchStatsPageControllerFamily();

/// See also [ConcernBranchStatsPageController].
class ConcernBranchStatsPageControllerFamily
    extends Family<AsyncValue<List<StatsOutAgeModel>>> {
  /// See also [ConcernBranchStatsPageController].
  const ConcernBranchStatsPageControllerFamily();

  /// See also [ConcernBranchStatsPageController].
  ConcernBranchStatsPageControllerProvider call(
    int? branch,
  ) {
    return ConcernBranchStatsPageControllerProvider(
      branch,
    );
  }

  @override
  ConcernBranchStatsPageControllerProvider getProviderOverride(
    covariant ConcernBranchStatsPageControllerProvider provider,
  ) {
    return call(
      provider.branch,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'concernBranchStatsPageControllerProvider';
}

/// See also [ConcernBranchStatsPageController].
class ConcernBranchStatsPageControllerProvider
    extends AutoDisposeAsyncNotifierProviderImpl<
        ConcernBranchStatsPageController, List<StatsOutAgeModel>> {
  /// See also [ConcernBranchStatsPageController].
  ConcernBranchStatsPageControllerProvider(
    int? branch,
  ) : this._internal(
          () => ConcernBranchStatsPageController()..branch = branch,
          from: concernBranchStatsPageControllerProvider,
          name: r'concernBranchStatsPageControllerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$concernBranchStatsPageControllerHash,
          dependencies: ConcernBranchStatsPageControllerFamily._dependencies,
          allTransitiveDependencies:
              ConcernBranchStatsPageControllerFamily._allTransitiveDependencies,
          branch: branch,
        );

  ConcernBranchStatsPageControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.branch,
  }) : super.internal();

  final int? branch;

  @override
  FutureOr<List<StatsOutAgeModel>> runNotifierBuild(
    covariant ConcernBranchStatsPageController notifier,
  ) {
    return notifier.build(
      branch,
    );
  }

  @override
  Override overrideWith(ConcernBranchStatsPageController Function() create) {
    return ProviderOverride(
      origin: this,
      override: ConcernBranchStatsPageControllerProvider._internal(
        () => create()..branch = branch,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        branch: branch,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<ConcernBranchStatsPageController,
      List<StatsOutAgeModel>> createElement() {
    return _ConcernBranchStatsPageControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ConcernBranchStatsPageControllerProvider &&
        other.branch == branch;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, branch.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ConcernBranchStatsPageControllerRef
    on AutoDisposeAsyncNotifierProviderRef<List<StatsOutAgeModel>> {
  /// The parameter `branch` of this provider.
  int? get branch;
}

class _ConcernBranchStatsPageControllerProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<
        ConcernBranchStatsPageController,
        List<StatsOutAgeModel>> with ConcernBranchStatsPageControllerRef {
  _ConcernBranchStatsPageControllerProviderElement(super.provider);

  @override
  int? get branch =>
      (origin as ConcernBranchStatsPageControllerProvider).branch;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
