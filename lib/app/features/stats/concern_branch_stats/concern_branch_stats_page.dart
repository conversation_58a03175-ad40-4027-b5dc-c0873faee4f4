import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:screenshot/screenshot.dart';
import 'package:uer_flutter/app/helpers/date_extension.dart';
import 'package:uer_flutter/app/widgets/bars/app_bar.dart';
import 'package:uer_flutter/app/widgets/charts/pie_chart.dart';
import 'package:uer_flutter/app/widgets/item_info/item_info_short.dart';

import '../../../core/models/Stats_outage/stats_outage_model.dart';
import '../../../core/providers/common/common_providers.dart';
import '../../../helpers/KeyValueObject.dart';
import '../../../helpers/export_helper.dart';
import '../../../routes/routes_name.dart';
import 'concern_branch_stats_page_controller.dart';

class ConcernBranchStatsPage extends ConsumerStatefulWidget {
  const ConcernBranchStatsPage({super.key, this.branch});

  final int? branch;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _ConcernBranchStatsPageState();
}

var columsDefault = TableRow(children: [
  TableCell(
    verticalAlignment: TableCellVerticalAlignment.middle,
    child: Container(
        color: Colors.grey[300],
        padding: const EdgeInsets.all(12),
        child: const Text("Рем. номер")),
  ),
  Container(
      color: Colors.grey[300],
      padding: const EdgeInsets.all(12),
      child: const Text("Оборудование")),
  Container(
      color: Colors.grey[300],
      padding: const EdgeInsets.all(12),
      child: const Text("Кол-во ССЗ")),
  Container(
      color: Colors.grey[300],
      padding: const EdgeInsets.all(12),
      child: const Text("Выполнено, н/ч")),
  Container(
      color: Colors.grey[300],
      padding: const EdgeInsets.all(12),
      child: const Text("Выполнено, д")),
  Container(
      color: Colors.grey[300],
      padding: const EdgeInsets.all(12),
      child: const Text("Статусы, д")),
  Container(
      color: Colors.grey[300],
      padding: const EdgeInsets.all(12),
      child: const Text("Простои, д")),
  Container(
      color: Colors.grey[300],
      padding: const EdgeInsets.all(12),
      child: const Text("% простоя"))
]);

class _ConcernBranchStatsPageState
    extends ConsumerState<ConcernBranchStatsPage> {
  ScreenshotController screenshotController = ScreenshotController();

  void openDetailStats(String? mainID) {
    if (mainID != null) {
      context.pushNamed(RoutesName.itemStats.named, extra: mainID);
    }
  }

  List<List<dynamic>> prepareDataForExport(
      List<StatsOutAgeModel> list, double oneProcentOutAge) {
    List<List<dynamic>> rowsArr = [];

    final header = [
      "Рем. номер",
      "Оборудование",
      "Кол-во ССЗ",
      "Выполнено, н/ч",
      "Выполнено, д",
      "Статусы, д",
      "Простои, д",
      "% простоя"
    ];

    rowsArr.add(header);

    for (var stat in list) {
      final workTime = (stat.workHours ?? 0) / 8;
      final statusTime = (stat.statusHours ?? 0) / 8;
      final outAgeTime = (stat.outAgeHours / 8);
      final workCount = stat.countWork ?? 0;
      final equipment = stat.equipment ?? "";
      final workerHours = stat.workerHours ?? 0;

      var precentOutAge = 0.0;
      if (oneProcentOutAge > 0) {
        precentOutAge = outAgeTime / oneProcentOutAge;
      }

      final row = [
        stat.repairNumber,
        equipment,
        workCount,
        workerHours.toPrecision(1),
        workTime.toPrecision(1),
        statusTime.toPrecision(1),
        outAgeTime.toPrecision(1),
        precentOutAge.toPrecision(2)
      ];
      rowsArr.add(row);
    }

    return rowsArr;
  }

  void exportExcel() {
    var stats =
        ref.read(concernBranchStatsPageControllerProvider(widget.branch));

    var arr = stats.value;

    if (arr == null) {
      return;
    }

    var controller = ref
        .read(concernBranchStatsPageControllerProvider(widget.branch).notifier);
    var commonStats = controller.getCommonStats();
    final oneProcentOutAge =
        (commonStats.workHours + commonStats.outAgeHours) / 100;

    var name = nameExportGenerate();

    var data = prepareDataForExport(arr, oneProcentOutAge);

    Export.exportExcel(name, data);
  }

  String nameExportGenerate() {
    var dateStr = DateTime.now().dateShortString();

    var name = "";

    if (widget.branch != null) {
      var branch = ref.read(branchForIDProvider(widget.branch!));

      if (branch != null) {
        name = "${branch.name}_$dateStr";
      } else {
        name = "филиал_$dateStr";
      }
    } else {
      name = "концерн_$dateStr";
    }
    return name;
  }

  void exportPDFJPG(bool jpg) async {
    var value = await screenshotController.capture();

    var name = nameExportGenerate();

    if (value == null) {
      return;
    }

    if (jpg == true) {
      Export.exportJPG(name, value);
    } else {
      Export.exportPDF(name, value);
    }
  }

  PopupMenuButton exportBtnGenerate() {
    return PopupMenuButton<int>(
      itemBuilder: (context) {
        List<PopupMenuItem<int>> menuItems = [];

        menuItems.add(
          PopupMenuItem<int>(
            value: 0,
            onTap: exportExcel,
            child: const Text('Экспорт в Excel'),
          ),
        );

        // menuItems.add(
        //   PopupMenuItem<int>(
        //     value: 1,
        //     onTap: () {
        //       exportPDFJPG(false);
        //     },
        //     child: const Text('Экспорт в PDF'),
        //   ),
        // );

        menuItems.add(
          PopupMenuItem<int>(
            value: 1,
            onTap: () {
              exportPDFJPG(true);
            },
            child: const Text('Экспорт в JPG'),
          ),
        );

        return menuItems;
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    var stats =
        ref.watch(concernBranchStatsPageControllerProvider(widget.branch));
    var controller = ref.watch(
        concernBranchStatsPageControllerProvider(widget.branch).notifier);

    var title = "Статистика Концерна";

    if (widget.branch != null) {
      title = "Статистика Филиала";
    }

    List<PieItemModel> models = [];

    var commonStats = controller.getCommonStats();
    final oneProcentOutAge =
        (commonStats.workHours + commonStats.outAgeHours) / 100;

    final info = getInfo(commonStats);

    if (stats.value != null) {
      final workDays = commonStats.workHours / 8; // получаем рабочие дни
      final model1 = PieItemModel(
          name: "Выполнено, д", value: workDays, color: Colors.orange);

      final outAgeDays = commonStats.outAgeHours / 8; // получаем рабочие дни
      final model2 = PieItemModel(
          name: "Простои, д", value: outAgeDays, color: Colors.grey);

      models.add(model1);
      models.add(model2);
    }

    return Scaffold(
        appBar: CustomAppBar(
          text: title,
          actions: [exportBtnGenerate()],
        ),
        body: stats.when(data: (data) {
          if (data.isNotEmpty == true) {
            return SingleChildScrollView(
              scrollDirection: Axis.vertical,
              child: Screenshot(
                controller: screenshotController,
                child: Container(
                  color: Colors.white,
                  child: Column(
                    children: [
                      LayoutBuilder(
                        builder:
                            (BuildContext context, BoxConstraints constraints) {
                          return Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Expanded(
                                  child: PieItemStatsChart(
                                    models: models,
                                  ),
                                ),
                                Expanded(
                                  child: ItemInfoShort(
                                    infoArr: info,
                                    showDetailBtn: false,
                                    callback: () {},
                                  ),
                                )
                              ],
                            ),
                          );
                        },
                      ),
                      Table(
                        defaultVerticalAlignment:
                            TableCellVerticalAlignment.fill,
                        columnWidths: const {
                          0: FlexColumnWidth(),
                          1: FlexColumnWidth(),
                          2: FixedColumnWidth(110),
                          3: FixedColumnWidth(130),
                          4: FixedColumnWidth(130),
                          5: FixedColumnWidth(110),
                          6: FixedColumnWidth(110),
                          7: FixedColumnWidth(100),
                        },
                        children: [
                          columsDefault,
                          ...data.map((stat) {
                            // переводим в рабочие дни
                            final workTime = (stat.workHours ?? 0) / 8;
                            final statusTime = (stat.statusHours ?? 0) / 8;
                            final outAgeTime = (stat.outAgeHours / 8);
                            final workCount = stat.countWork ?? 0;
                            final equipment = stat.equipment ?? "";
                            final workerHours = stat.workerHours ?? 0;

                            var precentOutAge = 0.0;

                            if (oneProcentOutAge > 0) {
                              precentOutAge = outAgeTime / oneProcentOutAge;
                            }

                            Color color = Colors.grey[100]!;

                            return TableRow(children: [
                              TableCell(
                                verticalAlignment:
                                    TableCellVerticalAlignment.middle,
                                child: InkWell(
                                  onTap: () {
                                    openDetailStats(stat.mainID);
                                  },
                                  child: Container(
                                      color: color,
                                      padding: const EdgeInsets.all(12),
                                      child: Text(
                                        stat.repairNumber,
                                        maxLines: 3,
                                      )),
                                ),
                              ),
                              Container(
                                  color: color,
                                  padding: const EdgeInsets.all(12),
                                  child: Text(equipment)),
                              Container(
                                  color: color,
                                  padding: const EdgeInsets.all(12),
                                  child: Text(workCount.toString())),
                              Container(
                                  color: color,
                                  padding: const EdgeInsets.all(12),
                                  child: Text(workerHours.toStringAsFixed(1))),
                              Container(
                                  color: color,
                                  padding: const EdgeInsets.all(12),
                                  child: Text(workTime.toStringAsFixed(1))),
                              Container(
                                  color: color,
                                  padding: const EdgeInsets.all(12),
                                  child: Text(statusTime.toStringAsFixed(1))),
                              Container(
                                  color: color,
                                  padding: const EdgeInsets.all(12),
                                  child: Text(outAgeTime.toStringAsFixed(1))),
                              Container(
                                  color: color,
                                  padding: const EdgeInsets.all(12),
                                  child: Text(
                                      "${precentOutAge.toStringAsFixed(2)} %"))
                            ]);
                          })
                        ],
                        border: TableBorder.all(width: 1, color: Colors.black),
                      ),
                    ],
                  ),
                ),
              ),
            );
          }

          return const SizedBox();
        }, error: (error, stacktrace) {
          return const Center(
            child: Text("Ошибка загрузки, повторите позднее."),
          );
        }, loading: () {
          return const Center(
            child: CircularProgressIndicator.adaptive(),
          );
        }));
  }
}

List<KeyValueObject> getInfo(StatsCommonModel statsCommon) {
  List<KeyValueObject> arr = [];

  arr.add(
      KeyValueObject("Выполненых работ (ССЗ)", "${statsCommon.workCounts} шт"));
  arr.add(KeyValueObject("Выполненых работ, н/ч",
      "${statsCommon.workerHours.toStringAsFixed(0)} н/ч"));
  arr.add(KeyValueObject("Общее календ. время",
      "${(statsCommon.allHours / 8).toStringAsFixed(0)} д"));
  arr.add(KeyValueObject("Выполненых работ, д",
      "${(statsCommon.workHours / 8).toStringAsFixed(0)} д"));
  arr.add(KeyValueObject(
      "Статусы, д", "${(statsCommon.statusHours / 8).toStringAsFixed(0)} д"));
  arr.add(KeyValueObject("Общее время простоя",
      "${(statsCommon.outAgeHours / 8).toStringAsFixed(0)} д"));

  return arr;
}
