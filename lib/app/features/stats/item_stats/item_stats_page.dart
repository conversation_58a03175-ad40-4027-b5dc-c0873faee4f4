import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:screenshot/screenshot.dart';
import 'package:uer_flutter/app/helpers/date_extension.dart';
import 'package:uer_flutter/app/widgets/bars/app_bar.dart';
import 'package:uer_flutter/app/widgets/charts/gantt/index.dart';
import 'package:uer_flutter/app/widgets/charts/line_all_work_chart.dart';
import 'package:uer_flutter/app/widgets/charts/line_work_chart.dart';
import 'package:uer_flutter/app/widgets/interactive/custom_card.dart';
import 'package:uer_flutter/app/widgets/item_info/item_info_short.dart';

import '../../../core/models/Stats_outage/stats_outage_model.dart';
import '../../../core/models/Stats_outage/stats_work_model.dart';
import '../../../helpers/export_helper.dart';
import '../../../widgets/charts/pie_chart.dart';
import '../../../widgets/common_button.dart';
import 'item_stats_page_controller.dart';

class ItemStatsPage extends ConsumerStatefulWidget {
  const ItemStatsPage({super.key, required this.mainID});

  final String mainID;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _ItemStatsPageState();
}

var columsDefault = TableRow(children: [
  TableCell(
    verticalAlignment: TableCellVerticalAlignment.middle,
    child: Container(
        color: Colors.grey[300],
        padding: const EdgeInsets.all(12),
        child: const Text("Название")),
  ),
  Container(
      color: Colors.grey[300],
      padding: const EdgeInsets.all(12),
      child: const Text("Компонент")),
  Container(
      color: Colors.grey[300],
      padding: const EdgeInsets.all(12),
      child: const Text("Выполнено, н/ч")),
  Container(
      color: Colors.grey[300],
      padding: const EdgeInsets.all(12),
      child: const Text("Выполнено, д")),
  Container(
      color: Colors.grey[300],
      padding: const EdgeInsets.all(12),
      child: const Text("Статусы, д")),
  Container(
      color: Colors.grey[300],
      padding: const EdgeInsets.all(12),
      child: const Text("Простои, д")),
  Container(
      color: Colors.grey[300],
      padding: const EdgeInsets.all(12),
      child: const Text("% простоя"))
]);

class _ItemStatsPageState extends ConsumerState<ItemStatsPage> {
  //Create an instance of ScreenshotController
  ScreenshotController screenshotController = ScreenshotController();
  //ScrollController scrollController = ScrollController();

  List<List<dynamic>> prepareDataForExport(List<StatsWorkModel> works) {
    List<List<dynamic>> rowsArr = [];

    final header = [
      "Название",
      "Компонент",
      "Выполнено, н/ч",
      "Выполнено, д",
      "Статусы, д",
      "Простои, д",
      "% простоя"
    ];

    rowsArr.add(header);

    for (var work in works) {
      // переводим из секунд в рабочие дни
      final workTime = (work.workTime / 3600) / 8;
      final statusTime = (work.statusTime / 3600) / 8;
      final outAgeTime = (work.outAgeTime / 3600) / 8;
      final workerTime = work.workerTime / 3600;

      final row = [
        work.name,
        work.componentName,
        workerTime.toPrecision(1),
        workTime.toPrecision(1),
        statusTime.toPrecision(1),
        outAgeTime.toPrecision(1),
        work.percentOutAge.toPrecision(2)
      ];
      rowsArr.add(row);
    }

    return rowsArr;
  }

  void exportExcel() {
    var stats = ref.read(itemStatsPageControllerProvider(widget.mainID));

    var works = stats.value?.works;

    if (works == null) {
      return;
    }

    var dateStr = DateTime.now().dateShortString();

    var name = "${stats.value!.repairNumber}_$dateStr";

    var data = prepareDataForExport(works);

    Export.exportExcel(name, data);
  }

  void exportPDFJPG(bool jpg) async {
    var value = await screenshotController.capture();

    var stats = ref.read(itemStatsPageControllerProvider(widget.mainID));

    var name = stats.value?.repairNumber ?? "";

    if (value == null) {
      return;
    }

    var dateStr = DateTime.now().dateShortString();

    if (jpg == true) {
      Export.exportJPG("${name}_$dateStr", value);
    } else {
      Export.exportPDF("${name}_$dateStr", value);
    }
  }

  PopupMenuButton exportBtnGenerate() {
    return PopupMenuButton<int>(
      itemBuilder: (context) {
        List<PopupMenuItem<int>> menuItems = [];

        menuItems.add(
          PopupMenuItem<int>(
            value: 0,
            onTap: exportExcel,
            child: const Text('Экспорт в Excel'),
          ),
        );

        // menuItems.add(
        //   PopupMenuItem<int>(
        //     value: 1,
        //     onTap: () {
        //       exportPDFJPG(false);
        //     },
        //     child: const Text('Экспорт в PDF'),
        //   ),
        // );

        menuItems.add(
          PopupMenuItem<int>(
            value: 1,
            onTap: () {
              exportPDFJPG(true);
            },
            child: const Text('Экспорт в JPG'),
          ),
        );

        return menuItems;
      },
    );
  }

  List<PieItemModel> loadPieModels(StatsOutAgeModel stats) {
    List<PieItemModel> models = [];

    final workDays = (stats.workHours ?? 0) / 8;
    final model1 = PieItemModel(
        name: "Выполнено, д", value: workDays, color: Colors.orange);

    final outAgeDays = stats.outAgeHours / 8;
    final model2 =
        PieItemModel(name: "Простои, д", value: outAgeDays, color: Colors.grey);

    models.add(model1);
    models.add(model2);

    return models;
  }

  @override
  Widget build(BuildContext context) {
    var stats = ref.watch(itemStatsPageControllerProvider(widget.mainID));

    var repairNumber = "№ ${stats.value?.repairNumber ?? ""}";

    List<PieItemModel> models = [];

    if (stats.value != null) {
      models = loadPieModels(stats.value!);
    }

    return Scaffold(
        appBar: CustomAppBar(
          text: "Статистика $repairNumber",
          actions: [exportBtnGenerate()],
        ),
        body: stats.when(data: (data) {
          if (data?.works != null) {
            return SingleChildScrollView(
                //controller: scrollController,
                child: Screenshot(
              controller: screenshotController,
              child: ItemStatsColum(
                statsModel: data!,
                pieModels: models,
              ),
            ));
          }

          return const SizedBox();
        }, error: (error, stacktrace) {
          return Center(
            child: Text(error.toString()),
          );
        }, loading: () {
          return const Center(
            child: CircularProgressIndicator.adaptive(),
          );
        }));
  }
}

class ItemStatsColum extends StatefulWidget {
  const ItemStatsColum({
    super.key,
    required this.statsModel,
    required this.pieModels,
  });

  final StatsOutAgeModel statsModel;
  final List<PieItemModel> pieModels;

  @override
  State<ItemStatsColum> createState() => _ItemStatsColumState();
}

class _ItemStatsColumState extends State<ItemStatsColum> {
  var showAllStatsGraph = false;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(12.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: 12.0,
            children: [
              Expanded(
                child: PieItemStatsChart(
                  models: widget.pieModels,
                ),
              ),
              if (widget.statsModel.dayStats != null)
                CustomCard(
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(8, 12, 12, 4),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            const Text(
                              "Динамика работ, н/ч",
                              style: TextStyle(
                                  fontWeight: FontWeight.w600, fontSize: 18),
                            ),
                            const SizedBox(
                              width: 8,
                            ),
                            CommonButton(
                                name: showAllStatsGraph == false
                                    ? "30 дней"
                                    : "Всё время",
                                callback: () {
                                  setState(() {
                                    showAllStatsGraph = !showAllStatsGraph;
                                  });
                                })
                          ],
                        ),
                        SizedBox(
                          height: 280,
                          child: showAllStatsGraph == false
                              ? LineWorkChart(
                                  dayStats: widget.statsModel.dayStats!)
                              : LineAllWorkChart(
                                  dayStats: widget.statsModel.dayStats!),
                        ),
                      ],
                    ),
                  ),
                ),
              Flexible(
                child: ItemInfoShort(
                  infoArr: widget.statsModel.getInfo(),
                  showDetailBtn: false,
                  callback: () {},
                ),
              )
            ],
          ),
        ),
        SizedBox(height: 24.0),
        Padding(
          padding: const EdgeInsets.all(12.0),
          child: Row(
            children: [
              Expanded(
                child: GanttChart(timelines: widget.statsModel.timelines),
              ),
              SizedBox(width: 120.0),
            ],
          ),
        ),
        SizedBox(height: 24.0),
        Table(
          defaultVerticalAlignment: TableCellVerticalAlignment.fill,
          columnWidths: const {
            0: FlexColumnWidth(),
            1: FlexColumnWidth(0.8),
            2: FixedColumnWidth(130),
            3: FixedColumnWidth(130),
            4: FixedColumnWidth(110),
            5: FixedColumnWidth(110),
            6: FixedColumnWidth(100),
          },
          children: [
            columsDefault,
            ...widget.statsModel.works!.map((work) {
              // переводим из секунд в рабочие дни
              final workTime = (work.workTime / 3600) / 8;
              final statusTime = (work.statusTime / 3600) / 8;
              final outAgeTime = (work.outAgeTime / 3600) / 8;
              final workerTime = work.workerTime / 3600;

              Color color;

              if (workTime > 0) {
                color = Colors.orange[100]!;
              } else if (statusTime > outAgeTime) {
                color = Colors.blue[100]!;
              } else {
                color = Colors.grey[100]!;
              }

              return TableRow(children: [
                TableCell(
                  verticalAlignment: TableCellVerticalAlignment.middle,
                  child: Container(
                      color: color,
                      padding: const EdgeInsets.all(12),
                      child: Text(
                        work.name,
                        maxLines: 3,
                      )),
                ),
                Container(
                    color: color,
                    padding: const EdgeInsets.all(12),
                    child: Text(work.componentName)),
                Container(
                    color: color,
                    padding: const EdgeInsets.all(12),
                    child: Text(workerTime.toStringAsFixed(1))),
                Container(
                    color: color,
                    padding: const EdgeInsets.all(12),
                    child: Text(workTime.toStringAsFixed(1))),
                Container(
                    color: color,
                    padding: const EdgeInsets.all(12),
                    child: Text(statusTime.toStringAsFixed(1))),
                Container(
                    color: color,
                    padding: const EdgeInsets.all(12),
                    child: Text(outAgeTime.toStringAsFixed(1))),
                Container(
                    color: color,
                    padding: const EdgeInsets.all(12),
                    child: Text("${work.percentOutAge.toStringAsFixed(2)} %"))
              ]);
            })
          ],
          border: TableBorder.all(width: 1, color: Colors.black),
        ),
      ],
    );
  }
}
