// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'item_stats_page_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$itemStatsPageControllerHash() =>
    r'440f2280012043c7c4598aece4b4030c3c8f49e9';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$ItemStatsPageController
    extends BuildlessAutoDisposeAsyncNotifier<StatsOutAgeModel?> {
  late final String mainID;

  FutureOr<StatsOutAgeModel?> build(
    String mainID,
  );
}

/// See also [ItemStatsPageController].
@ProviderFor(ItemStatsPageController)
const itemStatsPageControllerProvider = ItemStatsPageControllerFamily();

/// See also [ItemStatsPageController].
class ItemStatsPageControllerFamily
    extends Family<AsyncValue<StatsOutAgeModel?>> {
  /// See also [ItemStatsPageController].
  const ItemStatsPageControllerFamily();

  /// See also [ItemStatsPageController].
  ItemStatsPageControllerProvider call(
    String mainID,
  ) {
    return ItemStatsPageControllerProvider(
      mainID,
    );
  }

  @override
  ItemStatsPageControllerProvider getProviderOverride(
    covariant ItemStatsPageControllerProvider provider,
  ) {
    return call(
      provider.mainID,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'itemStatsPageControllerProvider';
}

/// See also [ItemStatsPageController].
class ItemStatsPageControllerProvider
    extends AutoDisposeAsyncNotifierProviderImpl<ItemStatsPageController,
        StatsOutAgeModel?> {
  /// See also [ItemStatsPageController].
  ItemStatsPageControllerProvider(
    String mainID,
  ) : this._internal(
          () => ItemStatsPageController()..mainID = mainID,
          from: itemStatsPageControllerProvider,
          name: r'itemStatsPageControllerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$itemStatsPageControllerHash,
          dependencies: ItemStatsPageControllerFamily._dependencies,
          allTransitiveDependencies:
              ItemStatsPageControllerFamily._allTransitiveDependencies,
          mainID: mainID,
        );

  ItemStatsPageControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.mainID,
  }) : super.internal();

  final String mainID;

  @override
  FutureOr<StatsOutAgeModel?> runNotifierBuild(
    covariant ItemStatsPageController notifier,
  ) {
    return notifier.build(
      mainID,
    );
  }

  @override
  Override overrideWith(ItemStatsPageController Function() create) {
    return ProviderOverride(
      origin: this,
      override: ItemStatsPageControllerProvider._internal(
        () => create()..mainID = mainID,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        mainID: mainID,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<ItemStatsPageController,
      StatsOutAgeModel?> createElement() {
    return _ItemStatsPageControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ItemStatsPageControllerProvider && other.mainID == mainID;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, mainID.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ItemStatsPageControllerRef
    on AutoDisposeAsyncNotifierProviderRef<StatsOutAgeModel?> {
  /// The parameter `mainID` of this provider.
  String get mainID;
}

class _ItemStatsPageControllerProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<ItemStatsPageController,
        StatsOutAgeModel?> with ItemStatsPageControllerRef {
  _ItemStatsPageControllerProviderElement(super.provider);

  @override
  String get mainID => (origin as ItemStatsPageControllerProvider).mainID;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
