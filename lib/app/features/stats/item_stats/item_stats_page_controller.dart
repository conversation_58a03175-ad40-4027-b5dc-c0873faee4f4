import 'dart:async';

import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:uer_flutter/app/core/providers/notifier_mouted.dart';
import '../../../core/models/Stats_outage/stats_outage_model.dart';
import '../../../core/services/api/service_provider.dart';

part 'item_stats_page_controller.g.dart';

@riverpod
class ItemStatsPageController extends _$ItemStatsPageController
    with NotifierMounted {
  @override
  FutureOr<StatsOutAgeModel?> build(String mainID) {
    ref.onDispose(setUnmounted);

    return _fetchStats(mainID);
  }

  Future<StatsOutAgeModel?> _fetchStats(String mainID) async {
    var statsProvider = ref.watch(statsRepositoryProvider);

    var stats = await statsProvider.getOutAgeStatsOne(mainID, true);

    return stats;
  }
}
