// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ssz_hours_stats_page_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$sSZHoursStatsPageControllerHash() =>
    r'8debde56d09e397c176301078b0fa1a350fb0848';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$SSZHoursStatsPageController
    extends BuildlessAutoDisposeAsyncNotifier<List<StatsSSZTimeResultModel>> {
  late final List<int> areaIds;

  FutureOr<List<StatsSSZTimeResultModel>> build(
    List<int> areaIds,
  );
}

/// See also [SSZHoursStatsPageController].
@ProviderFor(SSZHoursStatsPageController)
const sSZHoursStatsPageControllerProvider = SSZHoursStatsPageControllerFamily();

/// See also [SSZHoursStatsPageController].
class SSZHoursStatsPageControllerFamily
    extends Family<AsyncValue<List<StatsSSZTimeResultModel>>> {
  /// See also [SSZHoursStatsPageController].
  const SSZHoursStatsPageControllerFamily();

  /// See also [SSZHoursStatsPageController].
  SSZHoursStatsPageControllerProvider call(
    List<int> areaIds,
  ) {
    return SSZHoursStatsPageControllerProvider(
      areaIds,
    );
  }

  @override
  SSZHoursStatsPageControllerProvider getProviderOverride(
    covariant SSZHoursStatsPageControllerProvider provider,
  ) {
    return call(
      provider.areaIds,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'sSZHoursStatsPageControllerProvider';
}

/// See also [SSZHoursStatsPageController].
class SSZHoursStatsPageControllerProvider
    extends AutoDisposeAsyncNotifierProviderImpl<SSZHoursStatsPageController,
        List<StatsSSZTimeResultModel>> {
  /// See also [SSZHoursStatsPageController].
  SSZHoursStatsPageControllerProvider(
    List<int> areaIds,
  ) : this._internal(
          () => SSZHoursStatsPageController()..areaIds = areaIds,
          from: sSZHoursStatsPageControllerProvider,
          name: r'sSZHoursStatsPageControllerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$sSZHoursStatsPageControllerHash,
          dependencies: SSZHoursStatsPageControllerFamily._dependencies,
          allTransitiveDependencies:
              SSZHoursStatsPageControllerFamily._allTransitiveDependencies,
          areaIds: areaIds,
        );

  SSZHoursStatsPageControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.areaIds,
  }) : super.internal();

  final List<int> areaIds;

  @override
  FutureOr<List<StatsSSZTimeResultModel>> runNotifierBuild(
    covariant SSZHoursStatsPageController notifier,
  ) {
    return notifier.build(
      areaIds,
    );
  }

  @override
  Override overrideWith(SSZHoursStatsPageController Function() create) {
    return ProviderOverride(
      origin: this,
      override: SSZHoursStatsPageControllerProvider._internal(
        () => create()..areaIds = areaIds,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        areaIds: areaIds,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<SSZHoursStatsPageController,
      List<StatsSSZTimeResultModel>> createElement() {
    return _SSZHoursStatsPageControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SSZHoursStatsPageControllerProvider &&
        other.areaIds == areaIds;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, areaIds.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin SSZHoursStatsPageControllerRef
    on AutoDisposeAsyncNotifierProviderRef<List<StatsSSZTimeResultModel>> {
  /// The parameter `areaIds` of this provider.
  List<int> get areaIds;
}

class _SSZHoursStatsPageControllerProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<SSZHoursStatsPageController,
        List<StatsSSZTimeResultModel>> with SSZHoursStatsPageControllerRef {
  _SSZHoursStatsPageControllerProviderElement(super.provider);

  @override
  List<int> get areaIds =>
      (origin as SSZHoursStatsPageControllerProvider).areaIds;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
