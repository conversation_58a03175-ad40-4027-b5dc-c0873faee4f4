import 'package:excel/excel.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:screenshot/screenshot.dart';
import 'package:uer_flutter/app/core/providers/states/areas/areas_state.dart';
import 'package:uer_flutter/app/helpers/date_extension.dart';
import 'package:uer_flutter/app/styles/fonts.dart';
import 'package:uer_flutter/app/widgets/bars/app_bar.dart';
import 'package:uer_flutter/app/widgets/common_button.dart';
import 'package:uer_flutter/app/widgets/dialogs/actions_alert_dialog.dart';
import 'package:uer_flutter/app/widgets/svg_icon/index.dart';
import 'package:uer_flutter/gen/assets.gen.dart';

import '../../../core/models/Stats_outage/stats_ssz_time_result_model.dart';
import '../../../core/models/branch/branch_model.dart';
import '../../../helpers/export_helper.dart';
import 'ssz_hours_stats_page_controller.dart';

class SSZHoursStatsPage extends ConsumerStatefulWidget {
  const SSZHoursStatsPage({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _SSZHoursStatsPageState();
}

class AreaCommonModel {
  final String name;
  final List<int> areaIds;

  AreaCommonModel({required this.name, required this.areaIds});
}

class _SSZHoursStatsPageState extends ConsumerState<SSZHoursStatsPage> {
  AreaCommonModel? currentAreaModel;
  ScreenshotController screenshotController = ScreenshotController();

  void showAlertDialogChoiceArea() {
    var areaCommonModels = generateAreaCommonModels();
    var models = generateAreaBtnModel(areaCommonModels);

    showDialog(
        context: context,
        builder: (_) => ActionsAlertDialog(
            title: "Выберите тип участка", text: "", actionButtons: models));
  }

  void selectArea(AreaCommonModel model) {
    // print(model);

    setState(() {
      currentAreaModel = model;
    });
  }

  List<TableRow> configurateStats(List<StatsSSZTimeResultModel> stats) {
    SSZHoursStatsPageController? controller;

    if (currentAreaModel != null) {
      controller = ref.watch(
          sSZHoursStatsPageControllerProvider(currentAreaModel!.areaIds)
              .notifier);
    } else {
      return const [TableRow(children: [])];
    }

    var branchs = controller!.getBranchsFromStats();
    branchs.sort(((a, b) => a.identifier.compareTo(b.identifier)));

    //print(branchs);

    List<TableRow> cells = [];

    var headerRow = TableRow(children: [
      TableCell(
        verticalAlignment: TableCellVerticalAlignment.middle,
        child: Container(
            color: Colors.grey[300],
            padding: const EdgeInsets.all(12),
            child: const Text("Работа")),
      ),
      for (final branch in branchs)
        Container(
            color: Colors.grey[300],
            padding: const EdgeInsets.all(12),
            child: Text("${branch.name}, н/ч")),
    ]);

    cells.add(headerRow);

    for (final stat in stats) {
      List<String> hours = [];

      for (final branch in branchs) {
        var added = false;

        for (final hb in stat.timeBranchs) {
          if (hb.branch == branch.identifier) {
            var hourStr = (hb.workerTime / 3600).toStringAsFixed(2);
            hours.add(hourStr);
            added = true;
          }
        }

        if (added == false) {
          hours.add("0");
        }
      }

      var row = TableRow(children: [
        TableCell(
          verticalAlignment: TableCellVerticalAlignment.middle,
          child: Container(
              color: Colors.grey[300],
              padding: const EdgeInsets.all(12),
              child: Text(stat.name)),
        ),
        for (final str in hours)
          Container(
              color: Colors.grey[300],
              padding: const EdgeInsets.all(12),
              child: Text(str)),
      ]);

      cells.add(row);
    }

    return cells;
  }

  List<AreaCommonModel> generateAreaCommonModels() {
    var areas = ref.watch(areasProvider);

    List<AreaCommonModel> areaCommonModels = [];

    for (final area in areas) {
      var name = area.name.split("-").last;

      if (name == "Приемка" || name == "Приёмка") {
        continue;
      }

      AreaCommonModel model = areaCommonModels.firstWhere(
        (element) {
          if (element.name == name) {
            return true;
          }
          return false;
        },
        orElse: () {
          var model = AreaCommonModel(name: name, areaIds: [area.identifier]);
          areaCommonModels.add(model);
          return model;
        },
      );

      if (model.areaIds.contains(area.identifier)) {
        continue;
      } else {
        model.areaIds.add(area.identifier);
      }
    }

    //print(areaCommonModels);

    return areaCommonModels;
  }

  List<CommonButtonModel> generateAreaBtnModel(
      List<AreaCommonModel> areaCommonModels) {
    List<CommonButtonModel> btns = [];

    for (final model in areaCommonModels) {
      var btn = CommonButtonModel(
          name: model.name,
          callback: () {
            selectArea(model);
          });
      btns.add(btn);
    }

    // var cancelBtn = CommonButtonModel(name: "Отмена", callback: () {});
    // btns.add(cancelBtn);

    return btns;
  }

  List<List<dynamic>> prepareDataForExport(
      List<StatsSSZTimeResultModel> stats, List<BranchModel> branchs) {
    List<List<dynamic>> rowsArr = [];

    final header = [
      TextCellValue("Работа"),
    ];

    for (var branch in branchs) {
      header.add(TextCellValue(branch.name));
    }

    rowsArr.add(header);

    for (var stat in stats) {
      List<String> hours = [];

      for (final branch in branchs) {
        var added = false;

        for (final hb in stat.timeBranchs) {
          if (hb.branch == branch.identifier) {
            var hourStr = (hb.workerTime / 3600).toStringAsFixed(2);
            hours.add(hourStr);
            added = true;
          }
        }

        if (added == false) {
          hours.add("0");
        }
      }

      final List<dynamic> row = [
        TextCellValue(stat.name),
      ];

      for (final str in hours) {
        row.add(DoubleCellValue(double.parse(str)));
      }

      rowsArr.add(row);
    }

    return rowsArr;
  }

  void exportExcel() {
    if (currentAreaModel == null) {
      return;
    }

    var controller = ref.read(
        sSZHoursStatsPageControllerProvider(currentAreaModel!.areaIds)
            .notifier);

    var branchs = controller.getBranchsFromStats();
    branchs.sort(((a, b) => a.identifier.compareTo(b.identifier)));

    var stats = ref
        .read(sSZHoursStatsPageControllerProvider(currentAreaModel!.areaIds));

    var arr = stats.value;

    if (arr == null) {
      return;
    }

    var name = nameExportGenerate();

    var data = prepareDataForExport(arr, branchs);

    Export.exportExcel(name, data);
  }

  String nameExportGenerate() {
    var dateStr = DateTime.now().dateShortString();

    if (currentAreaModel == null) {
      return dateStr;
    }

    var name = "${currentAreaModel!.name}_$dateStr";

    return name;
  }

  void exportPDFJPG(bool jpg) async {
    var value = await screenshotController.capture();

    var name = nameExportGenerate();

    if (value == null) {
      return;
    }

    if (jpg == true) {
      Export.exportJPG(name, value);
    } else {
      Export.exportPDF(name, value);
    }
  }

  PopupMenuButton exportBtnGenerate() {
    return PopupMenuButton<int>(
      itemBuilder: (context) {
        List<PopupMenuItem<int>> menuItems = [];

        menuItems.add(
          PopupMenuItem<int>(
            value: 0,
            onTap: exportExcel,
            child: const Text('Экспорт в Excel'),
          ),
        );

        // menuItems.add(
        //   PopupMenuItem<int>(
        //     value: 1,
        //     onTap: () {
        //       exportPDFJPG(false);
        //     },
        //     child: const Text('Экспорт в PDF'),
        //   ),
        // );

        menuItems.add(
          PopupMenuItem<int>(
            value: 1,
            onTap: () {
              exportPDFJPG(true);
            },
            child: const Text('Экспорт в JPG'),
          ),
        );

        return menuItems;
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    var areaCommonModels = generateAreaCommonModels();

    if (areaCommonModels.isNotEmpty && currentAreaModel == null) {
      selectArea(areaCommonModels.first);
    }

    List<StatsSSZTimeResultModel> stats = [];

    if (currentAreaModel != null) {
      var result = ref.watch(
          sSZHoursStatsPageControllerProvider(currentAreaModel!.areaIds));
      stats = result.value ?? [];
    }

    return Scaffold(
      appBar: CustomAppBar(
        text: "Сравнение работ между участками",
        actions: [exportBtnGenerate()],
      ),
      body: SingleChildScrollView(
        child: Screenshot(
          controller: screenshotController,
          child: Container(
            color: Colors.white,
            child: Column(children: [
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Row(
                  children: [
                    const Text("Сравнение участков:"),
                    const SizedBox(
                      width: 8,
                    ),
                    CommonButton(
                      name: currentAreaModel?.name ?? "Выберите участок",
                      callback: showAlertDialogChoiceArea,
                    ),
                    Spacer(),
                    IconButton(
                      onPressed: () {
                        showDialog(
                          context: context,
                          builder: (context) {
                            return Dialog(
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(20.0),
                              ),
                              child: ConstrainedBox(
                                constraints: BoxConstraints(
                                  maxWidth: 512.0,
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.all(16.0),
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'Как это считается?',
                                        style: AppFonts.titleMedium,
                                      ),
                                      SizedBox(height: 8.0),
                                      Text(
                                        'В выдачу попадают заказы, завершённые в указанный период. Среднее время работ расситываются по формуле:',
                                        style: AppFonts.bodyLarge,
                                      ),
                                      SizedBox(height: 20.0),
                                      Center(
                                        child: SVGIcon(
                                          Assets.icons.math,
                                          width: 360.0,
                                        ),
                                      ),
                                      SizedBox(height: 20.0),
                                      Text(
                                        'Где сумма времени ССЗ (∑t ссз) – это сумарное время выполения выбранной работы в завершённых заказах. А количество заказов (N зак) – количество заказов в которых эта работа встречалась.',
                                        style: AppFonts.bodyLarge,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          },
                        );
                      },
                      icon: Icon(Icons.question_mark_rounded),
                    )
                  ],
                ),
              ),
              stats.isNotEmpty
                  ? Table(
                      defaultVerticalAlignment: TableCellVerticalAlignment.fill,
                      children: configurateStats(stats),
                      border: TableBorder.all(width: 1, color: Colors.black),
                    )
                  : const SizedBox(),
            ]),
          ),
        ),
      ),
    );
  }
}
