import 'dart:async';

import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:uer_flutter/app/core/providers/notifier_mouted.dart';
import '../../../core/models/branch/branch_model.dart';
import '../../../core/providers/common/common_providers.dart';
import '../../../core/models/Stats_outage/stats_ssz_time_result_model.dart';
import '../../../core/services/api/service_provider.dart';

part 'ssz_hours_stats_page_controller.g.dart';

@riverpod
class SSZHoursStatsPageController extends _$SSZHoursStatsPageController
    with NotifierMounted {
  @override
  FutureOr<List<StatsSSZTimeResultModel>> build(List<int> areaIds) {
    ref.onDispose(setUnmounted);

    return _fetchStats(areaIds);
  }

  Future<List<StatsSSZTimeResultModel>> _fetchStats(List<int> areaIds) async {
    var statsProvider = ref.watch(statsRepositoryProvider);

    var stats = await statsProvider.getSSZTimeStats(areaIds);

    return stats;
  }

  List<BranchModel> getBranchsFromStats() {
    if (state.value == null) {
      return [];
    }

    var branchSet = <int>{};

    for (final stat in state.value!) {
      for (final brHours in stat.timeBranchs) {
        branchSet.add(brHours.branch);
      }
    }

    List<BranchModel> branchModels = [];

    for (final value in branchSet) {
      final branch = ref.read(branchForIDProvider(value));
      if (branch != null) {
        branchModels.add(branch);
      }
    }

    return branchModels;
  }
}
