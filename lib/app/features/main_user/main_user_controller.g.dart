// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'main_user_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$componentsHash() => r'caf92bfc5998a5ff8508b23b122c570541988ed6';

/// See also [components].
@ProviderFor(components)
final componentsProvider = AutoDisposeProvider<List<ComponentModel>>.internal(
  components,
  name: r'componentsProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$componentsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef ComponentsRef = AutoDisposeProviderRef<List<ComponentModel>>;
String _$mainUserControllerHash() =>
    r'a5d22a58bdfb931fbef146ff2ec0311ede8d448c';

/// See also [MainUserController].
@ProviderFor(MainUserController)
final mainUserControllerProvider = AutoDisposeAsyncNotifierProvider<
    MainUserController, List<ItemModel>>.internal(
  MainUserController.new,
  name: r'mainUserControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$mainUserControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$MainUserController = AutoDisposeAsyncNotifier<List<ItemModel>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
