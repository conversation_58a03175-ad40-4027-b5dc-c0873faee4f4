import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:uer_flutter/app/widgets/bars/app_bar.dart';
import 'package:uer_flutter/app/widgets/common_button.dart';
import 'package:uer_flutter/app/widgets/component_cell.dart';

import '../../core/models/enums/common_items_screens_enum.dart';
import '../../core/providers/common/common_providers.dart';
import '../../helpers/debouncer.dart';
import '../../helpers/platform_check.dart';
import '../../routes/routes_name.dart';
import 'main_user_controller.dart';

class MainUserPage extends ConsumerStatefulWidget {
  const MainUserPage({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _MainUserPageState();
}

class _MainUserPageState extends ConsumerState<MainUserPage> {
  void searchForString(str) {
    _debouncer.run(() {
      //print(str);
      ref.read(mainUserControllerProvider.notifier).getItems(str);
    });
  }

  void moveToComponent(int index) {
    var components = ref.read(componentsProvider);
    context.pushNamed(RoutesName.component.named, pathParameters: {
      'cid': components[index].identifier,
    });
  }

  void openQRCodeScanPage(bool search) async {
    var value = await context.pushNamed(RoutesName.qrScan.named);

    if (value != null) {
      if (value is String) {
        parseQRCode(value, search);
      }
    }
  }

  void parseQRCode(String qrCode, bool search) {
    var values = qrCode.split("//");

    if (values.length == 3) {
      var mainID = values[1];
      var componentID = values[0];

      if (search == true) {
        openItemPage(mainID);
      } else {
        openSelectJobPage(mainID, componentID);
      }
    }
  }

  void openItemPage(String mainID) {
    context.pushNamed(RoutesName.item.named, pathParameters: {"mid": mainID});
  }

  void openSelectJobPage(String mainID, String componentID) async {
    var area = ref.read(currentUserAreaProvider);

    if (area == null) {
      return;
    }

    var value = await ref
        .read(mainUserControllerProvider.notifier)
        .checkItemComponentForOpen(componentID, area, context);

    if (value == true) {
      context.pushNamed(RoutesName.selectJob.named, queryParameters: {
        "mainID": mainID,
        "componentID": componentID,
        "areaID": area.identifier.toString()
      });
    }
  }

  void openNewItems() {
    var area = ref.read(currentUserAreaProvider);
    context.pushNamed(RoutesName.newItems.named,
        queryParameters: {"branchID": area?.branch.toString()});
  }

  void openInWorkItems() {
    var area = ref.read(currentUserAreaProvider);
    context.pushNamed(RoutesName.commonItems.named,
        extra: CommonItemsScreens.inWork.getData(branchID: area?.branch));
  }

  void openArchiveItems() {
    var area = ref.read(currentUserAreaProvider);
    context.pushNamed(RoutesName.commonItems.named,
        extra: CommonItemsScreens.archive.getData(branchID: area?.branch));
  }

  void openNotificationHistory() {
    context.pushNamed(RoutesName.notificationHistory.named);
  }

  final _debouncer = Debouncer(milliseconds: 650);

  @override
  Widget build(BuildContext context) {
    var components = ref.watch(componentsProvider);
    var area = ref.watch(currentUserAreaProvider);

    var userArea = ref.watch(currentUserAreaProvider);

    var receiving = userArea?.start ?? false;

    return Scaffold(
      appBar: CustomAppBar(
        text: area?.name ?? "Главная",
        actions: [
          IconButton(
              onPressed: openNotificationHistory,
              icon: const Icon(
                Icons.notifications,
                color: Colors.white,
              ))
        ],
      ),
      body: Stack(
        children: [
          Column(
            children: [
              Padding(
                padding: const EdgeInsets.fromLTRB(16.0, 16.0, 16.0, 0.0),
                child: Row(
                  children: [
                    Expanded(
                      child: TextField(
                        decoration: const InputDecoration(
                          hintText: 'Введите рем. номер',
                          prefixIcon: Icon(Icons.search),
                        ),
                        onChanged: searchForString,
                      ),
                    ),
                    isWebOrAndroidOriOS() == true
                        ? IconButton(
                            // work only ios,android,windows,web
                            onPressed: () {
                              openQRCodeScanPage(true);
                            },
                            icon: const Icon(
                              Icons.qr_code_scanner,
                              color: Colors.orange,
                            ))
                        : const SizedBox()
                  ],
                ),
              ),
              receiving
                  ? Padding(
                      padding: const EdgeInsets.fromLTRB(16.0, 8.0, 16.0, 4.0),
                      child: Row(
                        children: [
                          CommonButton(name: "Новые", callback: openNewItems),
                          const SizedBox(width: 8),
                          CommonButton(
                              name: "В работе", callback: openInWorkItems),
                          const SizedBox(width: 8),
                          CommonButton(
                              name: "Архив", callback: openArchiveItems),
                        ],
                      ),
                    )
                  : const SizedBox(),
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.all(16.0),
                  itemCount: components.length,
                  itemBuilder: (context, index) {
                    var item = ref
                        .read(mainUserControllerProvider.notifier)
                        .findItemForComponent(components[index]);

                    callback() {
                      moveToComponent(index);
                    }

                    if (item != null) {
                      return ComponentCell(
                        item: item,
                        component: components[index],
                        currentAreaID: userArea?.identifier ?? 0,
                        callback: callback,
                      );
                    }
                    return const SizedBox();
                  },
                ),
              ),
            ],
          ),
          Column(
            mainAxisAlignment: MainAxisAlignment.end,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: CommonButton(
                        name: "Сканировать QR",
                        callback: () {
                          openQRCodeScanPage(false);
                        }),
                  ),
                ],
              ),
            ],
          )
        ],
      ),
    );
  }
}
