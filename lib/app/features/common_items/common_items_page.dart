import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:uer_flutter/app/widgets/bars/app_bar.dart';
import 'package:uer_flutter/app/widgets/items/item_list.dart';
import 'package:uer_flutter/app/widgets/search/search_panel.dart';

import '../../core/models/item/item_model.dart';
import '../../core/models/search/search_model.dart';
import '../../helpers/debouncer.dart';
import '../../routes/routes_name.dart';

class CommonItemsPage extends ConsumerStatefulWidget {
  const CommonItemsPage({
    super.key,
    required this.title,
    required this.searchModel,
  });

  final String title;
  final SearchModel searchModel;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _CommonItemsPageState();
}

class _CommonItemsPageState extends ConsumerState<CommonItemsPage> {
  late SearchModel searchModel;
  late String title;
  final _debouncer = Debouncer(milliseconds: 650);

  @override
  void initState() {
    searchModel = defaultSearch();
    title = widget.title; //?? "";
    super.initState();
  }

  SearchModel defaultSearch() {
    return widget.searchModel; //?? const SearchModel();

    //return SearchModel(branch: branchID, finished: false, newBool: false);
  }

  void updateSearchTerm(SearchModel? searchModelNew) {
    _debouncer.run(() {
      if (searchModelNew == null) {
        setState(() {
          searchModel = defaultSearch();
        });
        return;
      } else {
        searchModel = defaultSearch();
      }

      setState(() {
        if (searchModelNew.searchField != null) {
          searchModel =
              searchModel.copyWith(searchField: searchModelNew.searchField);
        } else if (searchModelNew.searchClient != null) {
          searchModel =
              searchModel.copyWith(searchClient: searchModelNew.searchClient);
        } else if (searchModelNew.mainID != null) {
          searchModel = searchModel.copyWith(mainID: searchModelNew.mainID);
        } else if (searchModelNew.searchEquipment != null) {
          searchModel = searchModel.copyWith(
              searchEquipment: searchModelNew.searchEquipment);
        }
      });
    });
  }

  void moveToItem(ItemModel item) {
    var mainID = item.mainID ?? "0";

    context.pushNamed(RoutesName.item.named, pathParameters: {
      'mid': mainID,
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(text: title),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SearchPanel(callback: updateSearchTerm),
            const SizedBox(height: 16.0),
            const Divider(height: 0.0),
            Expanded(
              child: ItemList(
                callback: moveToItem,
                searchModel: searchModel,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
