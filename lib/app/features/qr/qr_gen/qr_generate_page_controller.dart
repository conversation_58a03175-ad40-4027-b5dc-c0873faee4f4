import 'dart:async';

import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:uer_flutter/app/core/providers/notifier_mouted.dart';
import '../../../core/models/item/item_model.dart';
import '../../../core/models/search/search_model.dart';

import '../../../core/services/api/service_provider.dart';

part 'qr_generate_page_controller.g.dart';

@riverpod
class QRGeneratePageController extends _$QRGeneratePageController
    with NotifierMounted {
  @override
  FutureOr<ItemModel?> build(String? mainID, String? componentID) {
    ref.onDispose(setUnmounted);

    return _fetchItems(mainID, componentID);
  }

  Future<ItemModel?> _fetchItems(String? mainID, String? componentID) async {
    SearchModel search =
        SearchModel(finished: false, mainID: mainID, componentID: componentID);
    var apiClient = ref.read(itemRepositoryProvider);
    var items = await apiClient.getItems(search);
    return items?.first;
  }

  // Future<void> getItems(SearchModel? searchModel) async {
  //   state = const AsyncValue.loading();

  //   var newState = await AsyncValue.guard(() async {
  //     return _fetchItems(searchModel);
  //   });

  //   if (mounted) {
  //     state = newState;
  //   }
  // }
}
