import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:share_plus/share_plus.dart';
import 'package:uer_flutter/app/core/models/enums/component_type_enum.dart';

import '../../../helpers/empty_download.dart'
    if (dart.library.html) '../../../helpers/download.dart';
import '../../../helpers/platform_check.dart';
import '../../../widgets/bars/app_bar.dart';
import 'qr_generate_page_controller.dart';

class QRGeneratePage extends ConsumerStatefulWidget {
  const QRGeneratePage({super.key, this.mainID, this.componentID});

  final String? mainID;
  final String? componentID;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _QRGeneratePageState();
}

class _QRGeneratePageState extends ConsumerState<QRGeneratePage> {
  Future<Uint8List> _capturePng(GlobalKey key) async {
    RenderRepaintBoundary boundary =
        key.currentContext!.findRenderObject() as RenderRepaintBoundary;
    ui.Image image = await boundary.toImage(pixelRatio: 3.0);
    ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    Uint8List pngBytes = byteData!.buffer.asUint8List();
    return pngBytes;
  }

  void tapToExport(List<String> qrStrs, List<GlobalKey> keys) async {
    List<XFile> images = [];

    for (var i = 0; i < keys.length; i++) {
      var key = keys[i];
      var qrStr = qrStrs[i];
      var img = await _capturePng(key);

      if (isWeb() == true) {
        download(img, // takes bytes
            downloadName: "$qrStr.png");
      } else {
        images.add(
            XFile.fromData(img, mimeType: "image/png", name: "$qrStr.png"));
      }
    }

    if (images.isNotEmpty) {
      await Share.shareXFiles(images);
    }
  }

  List<GlobalKey> qrKeys = [];

  @override
  Widget build(BuildContext context) {
    var model = ref.watch(
        qRGeneratePageControllerProvider(widget.mainID, widget.componentID));
    var item = model.value;

    var componentOnly = widget.componentID != null ? true : false;

    List<QrCode> qrCodesArr = [];
    List<String> qrStr = [];
    List<String> qrStrNames = [];

    qrKeys = [];

    if (item != null) {
      for (var comp in item.components) {
        if (componentOnly == true && comp.identifier == widget.componentID) {
          var code = QrCode.fromData(
              data: [comp.identifier, item.mainID!, item.branch].join("//"),
              errorCorrectLevel: QrErrorCorrectLevel.H);

          qrCodesArr.add(code);
          qrStr.add("${item.repairNumber}_${comp.type.desc}");
          qrKeys.add(GlobalKey());
          qrStrNames.add("${comp.type.desc}, ${item.repairNumberFormat()}");
        } else if (componentOnly == false) {
          var code = QrCode.fromData(
              data: [comp.identifier, item.mainID!, item.branch].join("//"),
              errorCorrectLevel: QrErrorCorrectLevel.H);

          qrCodesArr.add(code);
          qrStr.add("${item.repairNumber}_${comp.type.desc}");
          qrKeys.add(GlobalKey());
          qrStrNames.add("${comp.type.desc}, ${item.repairNumberFormat()}");
        }
      }
    }

    return Scaffold(
      appBar: CustomAppBar(
        text: "Экспорт QR",
        actions: [
          IconButton(
              onPressed: () {
                tapToExport(qrStr, qrKeys);
              },
              icon: const Icon(Icons.share))
        ],
      ),
      body: item != null
          ? SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.only(top: 8),
                child: LayoutBuilder(
                  builder: (BuildContext context, BoxConstraints constraints) {
                    return stationComputer()
                        ? Row(children: [
                            for (var i = 0; i < qrCodesArr.length; i++)
                              QRCodeCard(
                                  keyView: qrKeys[i],
                                  name: qrStrNames[i],
                                  code: qrCodesArr[i])
                          ])
                        : Column(
                            children: [
                              for (var i = 0; i < qrCodesArr.length; i++)
                                QRCodeCard(
                                    keyView: qrKeys[i],
                                    name: qrStrNames[i],
                                    code: qrCodesArr[i])
                            ],
                          );
                  },
                ),
              ),
            )
          : const CircularProgressIndicator(),
    );
  }
}

class QRCodeCard extends StatelessWidget {
  const QRCodeCard(
      {super.key,
      required this.keyView,
      required this.name,
      required this.code});

  final GlobalKey keyView;
  final String name;
  final QrCode code;

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      key: keyView,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 16),
            child: Text(
              name,
              textAlign: TextAlign.start,
              style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 20),
            ),
          ),
          QrImageView.withQr(
            padding: const EdgeInsets.fromLTRB(16, 2, 16, 16),
            qr: code,
            size: 300,
          ),
        ],
      ),
    );
  }
}
