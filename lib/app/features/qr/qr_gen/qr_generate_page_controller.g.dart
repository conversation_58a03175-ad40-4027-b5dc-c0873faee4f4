// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'qr_generate_page_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$qRGeneratePageControllerHash() =>
    r'fed66cd75b217ec36e4457fdf11905c1cc993915';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$QRGeneratePageController
    extends BuildlessAutoDisposeAsyncNotifier<ItemModel?> {
  late final String? mainID;
  late final String? componentID;

  FutureOr<ItemModel?> build(
    String? mainID,
    String? componentID,
  );
}

/// See also [QRGeneratePageController].
@ProviderFor(QRGeneratePageController)
const qRGeneratePageControllerProvider = QRGeneratePageControllerFamily();

/// See also [QRGeneratePageController].
class QRGeneratePageControllerFamily extends Family<AsyncValue<ItemModel?>> {
  /// See also [QRGeneratePageController].
  const QRGeneratePageControllerFamily();

  /// See also [QRGeneratePageController].
  QRGeneratePageControllerProvider call(
    String? mainID,
    String? componentID,
  ) {
    return QRGeneratePageControllerProvider(
      mainID,
      componentID,
    );
  }

  @override
  QRGeneratePageControllerProvider getProviderOverride(
    covariant QRGeneratePageControllerProvider provider,
  ) {
    return call(
      provider.mainID,
      provider.componentID,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'qRGeneratePageControllerProvider';
}

/// See also [QRGeneratePageController].
class QRGeneratePageControllerProvider
    extends AutoDisposeAsyncNotifierProviderImpl<QRGeneratePageController,
        ItemModel?> {
  /// See also [QRGeneratePageController].
  QRGeneratePageControllerProvider(
    String? mainID,
    String? componentID,
  ) : this._internal(
          () => QRGeneratePageController()
            ..mainID = mainID
            ..componentID = componentID,
          from: qRGeneratePageControllerProvider,
          name: r'qRGeneratePageControllerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$qRGeneratePageControllerHash,
          dependencies: QRGeneratePageControllerFamily._dependencies,
          allTransitiveDependencies:
              QRGeneratePageControllerFamily._allTransitiveDependencies,
          mainID: mainID,
          componentID: componentID,
        );

  QRGeneratePageControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.mainID,
    required this.componentID,
  }) : super.internal();

  final String? mainID;
  final String? componentID;

  @override
  FutureOr<ItemModel?> runNotifierBuild(
    covariant QRGeneratePageController notifier,
  ) {
    return notifier.build(
      mainID,
      componentID,
    );
  }

  @override
  Override overrideWith(QRGeneratePageController Function() create) {
    return ProviderOverride(
      origin: this,
      override: QRGeneratePageControllerProvider._internal(
        () => create()
          ..mainID = mainID
          ..componentID = componentID,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        mainID: mainID,
        componentID: componentID,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<QRGeneratePageController, ItemModel?>
      createElement() {
    return _QRGeneratePageControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is QRGeneratePageControllerProvider &&
        other.mainID == mainID &&
        other.componentID == componentID;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, mainID.hashCode);
    hash = _SystemHash.combine(hash, componentID.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin QRGeneratePageControllerRef
    on AutoDisposeAsyncNotifierProviderRef<ItemModel?> {
  /// The parameter `mainID` of this provider.
  String? get mainID;

  /// The parameter `componentID` of this provider.
  String? get componentID;
}

class _QRGeneratePageControllerProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<QRGeneratePageController,
        ItemModel?> with QRGeneratePageControllerRef {
  _QRGeneratePageControllerProviderElement(super.provider);

  @override
  String? get mainID => (origin as QRGeneratePageControllerProvider).mainID;
  @override
  String? get componentID =>
      (origin as QRGeneratePageControllerProvider).componentID;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
