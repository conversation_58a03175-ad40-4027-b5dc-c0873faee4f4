import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:uer_flutter/app/widgets/bars/app_bar.dart';

class ScanQRAltPage extends ConsumerStatefulWidget {
  const ScanQRAltPage({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _ScanQRAltPageState();
}

class _ScanQRAltPageState extends ConsumerState<ScanQRAltPage> {
  final _controller = MobileScannerController(
      detectionSpeed: DetectionSpeed.normal,
      facing: CameraFacing.back,
      detectionTimeoutMs: 1000
      //torchEnabled: true,
      );

  @override
  void dispose() {
    //_controller.isStarting = false;
    _controller.dispose();

    super.dispose();
  }

  void changeCamera() {
    _controller.switchCamera();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        text: "Сканирование QR кода",
      ),
      body: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Center(
              child: AspectRatio(
                aspectRatio: 1 / 1,
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20.0),
                  ),
                  clipBehavior: Clip.hardEdge,
                  child: MobileScanner(
                    controller: _controller,
                    fit: BoxFit.cover,
                    onDetect: (capture) {
                      final List<Barcode> barcodes = capture.barcodes;
                      //final Uint8List? image = capture.image;
                      for (final barcode in barcodes) {
                        //debugPrint('Barcode found! ${barcode.rawValue}');

                        context.pop(barcode.rawValue);
                        break;
                      }
                    },
                  ),
                ),
              ),
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              IconButton(
                  onPressed: changeCamera,
                  icon: Icon(
                    _controller.facing == CameraFacing.front
                        ? Icons.camera_front_rounded
                        : Icons.camera_rear_rounded,
                    color: Colors.orange,
                  ))
            ],
          )
        ],
      ),
    );
  }
}
