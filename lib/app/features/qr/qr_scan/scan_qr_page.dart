import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:qr_mobile_vision/qr_camera.dart';

//typedef ScanQRPageCallback = Function(String);

class ScanQRPage extends ConsumerStatefulWidget {
  const ScanQRPage({super.key});

  //final ScanQRPageCallback callback;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _ScanQRPageState();
}

class _ScanQRPageState extends ConsumerState<ScanQRPage> {
  void closePage(String? code) {
    context.pop(code);
  }

  var codeSended = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          QrCamera(
            formats: const [BarcodeFormats.QR_CODE],
            qrCodeCallback: (code) {
              // 2300000672/2_in//2300000672/2//2
              //print("code!!");

              if (code != null && codeSended == false) {
                var values = code.split("//");

                if (values.length == 3 || values.length == 4) {
                  codeSended = true;

                  Future(() => closePage(code));
                }
              }
            },
          ),
          SafeArea(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(8, 8, 0, 0),
              child: IconButton(
                icon: const Icon(
                  Icons.close_rounded,
                  color: Colors.white,
                  size: 32,
                ),
                onPressed: () => closePage(null),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
