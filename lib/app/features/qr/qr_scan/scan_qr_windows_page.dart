import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:simple_barcode_scanner/simple_barcode_scanner.dart';
import 'package:uer_flutter/app/widgets/bars/app_bar.dart';

class WindowsQRScanPage extends StatefulWidget {
  const WindowsQRScanPage({super.key});

  @override
  State<WindowsQRScanPage> createState() => _WindowsQRScanPageState();
}

class _WindowsQRScanPageState extends State<WindowsQRScanPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        text: "Сканирование QR кода",
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text(
              'Нажмите кнопку, чтобы отсканировать QR-код',
              style: TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const Sized<PERSON><PERSON>(height: 20),
            ElevatedButton(
              onPressed: () async {
                // Запускаем встроенный сканер QR-кода
                String? res = await SimpleBarcodeScanner.scanBarcode(
                  context,
                  barcodeAppBar: const BarcodeAppBar(
                    appBarTitle: 'Сканирование QR кода',
                    centerTitle: true,
                    enableBackButton: true,
                    backButtonIcon: Icon(Icons.arrow_back_ios),
                  ),
                  isShowFlashIcon: true,
                  scanType: ScanType.qr,
                );

                // Проверяем результат сканирования
                if (res != null && res.isNotEmpty && context.mounted) {
                  context.pop(res); // Возвращаем результат через GoRouter
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).primaryColor,
                padding:
                    const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              ),
              child: const Text(
                'Открыть сканер QR',
                style: TextStyle(fontSize: 16, color: Colors.white),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
