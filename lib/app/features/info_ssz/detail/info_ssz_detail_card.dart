import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uer_flutter/app/core/models/enums/history_status_enum.dart';
import 'package:uer_flutter/app/widgets/status_badge_text.dart';

import '../../../core/models/task_ssz/task_ssz_model.dart';
import '../../../core/providers/common/common_providers.dart';

typedef InfoSSZDetailCardCallback = Function();

class InfoSSZDetailCard extends ConsumerStatefulWidget {
  const InfoSSZDetailCard(
      {super.key, required this.model, required this.callback});

  final TaskSSZModel model;
  final InfoSSZDetailCardCallback callback;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _InfoSSZDetailCardState();
}

class _InfoSSZDetailCardState extends ConsumerState<InfoSSZDetailCard> {
  @override
  Widget build(BuildContext context) {
    var area = ref.watch(areaForIDAreaStrProvider(widget.model.idArea));

    return Card(
      color: Colors.white,
      elevation: 2.0,
      margin: const EdgeInsets.all(8),
      shape: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: const BorderSide(color: Colors.white)),
      child: InkWell(
        borderRadius: BorderRadius.circular(10.0),
        onTap: widget.callback,
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: SizedBox(
            //height: 80,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.model.name,
                  style: const TextStyle(
                      fontWeight: FontWeight.w600, fontSize: 17),
                ),
                const SizedBox(
                  height: 8,
                ),
                Text(
                  area?.name ?? "---",
                  style: const TextStyle(
                      fontWeight: FontWeight.w500, fontSize: 15),
                ),
                StatusBadgeText(
                    icon: widget.model.lastStatus?.statusBadge ??
                        const Icon(
                          Icons.circle,
                          color: Colors.grey,
                          size: 14,
                        ),
                    text: widget.model.lastStatus?.desc ?? "Не выполнялось"),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
