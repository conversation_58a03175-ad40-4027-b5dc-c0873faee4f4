import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:uer_flutter/app/widgets/bars/app_bar.dart';
import 'package:uer_flutter/app/widgets/history_detail_view.dart';

import '../../../core/models/task_ssz/task_ssz_model.dart';
import '../../../helpers/snack_bar.dart';
import '../../../routes/routes_name.dart';
import 'info_ssz_detail_card.dart';
import 'info_ssz_detail_page_controller.dart';

class InfoSSZDetailPage extends ConsumerStatefulWidget {
  const InfoSSZDetailPage(
      {super.key,
      required this.mainID,
      required this.dateStr,
      required this.repairNumber,
      this.areaID,
      this.branchID});

  final String mainID;
  final String dateStr;
  final String repairNumber;
  final String? areaID;
  final String? branchID;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _InfoSSZDetailPageState();
}

class _InfoSSZDetailPageState extends ConsumerState<InfoSSZDetailPage> {
  void openHistoryDetail(TaskSSZModel model) async {
    var history = await ref
        .read(infoSSZDetailPageControllerProvider(
                widget.dateStr, widget.mainID, widget.areaID, widget.branchID)
            .notifier)
        .getHistoryModel(model);

    if (history != null) {
      showModalBottomSheet(
        context: context,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
        ),
        builder: (BuildContext context) {
          return SizedBox(
            height: 400,
            child: Center(
              child: HistoryDetailView(history: history),
            ),
          );
        },
      );
    } else {
      showInSnackBar("Ошибка в загрузке истории", context);
    }
  }

  void openItemPage() {
    context.pushNamed(RoutesName.item.named,
        pathParameters: {"mid": widget.mainID});
  }

  @override
  Widget build(BuildContext context) {
    var models = ref.watch(infoSSZDetailPageControllerProvider(
        widget.dateStr, widget.mainID, widget.areaID, widget.branchID));

    return Scaffold(
      appBar: CustomAppBar(text: "№ ${widget.repairNumber}", actions: [
        TextButton(
            onPressed: openItemPage,
            child: const Text(
              "К заказу",
              style:
                  TextStyle(color: Colors.white, fontWeight: FontWeight.w600),
            )),
      ]),
      body: models.when(data: (data) {
        return ListView.builder(
          itemCount: data?.length ?? 0,
          itemBuilder: (BuildContext context, int index) {
            final model = data![index];

            void callback() {
              openHistoryDetail(model);
            }

            return InfoSSZDetailCard(model: model, callback: callback);
          },
        );
      }, error: (error, stacktrace) {
        return Center(
          child: Text("Error: $error"),
        );
      }, loading: () {
        return const Center(
          child: CircularProgressIndicator(),
        );
      }),
    );
  }
}
