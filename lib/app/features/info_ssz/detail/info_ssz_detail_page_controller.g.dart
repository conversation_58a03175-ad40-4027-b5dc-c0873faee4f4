// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'info_ssz_detail_page_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$infoSSZDetailPageControllerHash() =>
    r'e9b88dc1ff31789b544612740351e83ff2c7ac1a';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$InfoSSZDetailPageController
    extends BuildlessAutoDisposeAsyncNotifier<List<TaskSSZModel>?> {
  late final String dateStr;
  late final String mainID;
  late final String? idArea;
  late final String? idBranch;

  FutureOr<List<TaskSSZModel>?> build(
    String dateStr,
    String mainID,
    String? idArea,
    String? idBranch,
  );
}

/// See also [InfoSSZDetailPageController].
@ProviderFor(InfoSSZDetailPageController)
const infoSSZDetailPageControllerProvider = InfoSSZDetailPageControllerFamily();

/// See also [InfoSSZDetailPageController].
class InfoSSZDetailPageControllerFamily
    extends Family<AsyncValue<List<TaskSSZModel>?>> {
  /// See also [InfoSSZDetailPageController].
  const InfoSSZDetailPageControllerFamily();

  /// See also [InfoSSZDetailPageController].
  InfoSSZDetailPageControllerProvider call(
    String dateStr,
    String mainID,
    String? idArea,
    String? idBranch,
  ) {
    return InfoSSZDetailPageControllerProvider(
      dateStr,
      mainID,
      idArea,
      idBranch,
    );
  }

  @override
  InfoSSZDetailPageControllerProvider getProviderOverride(
    covariant InfoSSZDetailPageControllerProvider provider,
  ) {
    return call(
      provider.dateStr,
      provider.mainID,
      provider.idArea,
      provider.idBranch,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'infoSSZDetailPageControllerProvider';
}

/// See also [InfoSSZDetailPageController].
class InfoSSZDetailPageControllerProvider
    extends AutoDisposeAsyncNotifierProviderImpl<InfoSSZDetailPageController,
        List<TaskSSZModel>?> {
  /// See also [InfoSSZDetailPageController].
  InfoSSZDetailPageControllerProvider(
    String dateStr,
    String mainID,
    String? idArea,
    String? idBranch,
  ) : this._internal(
          () => InfoSSZDetailPageController()
            ..dateStr = dateStr
            ..mainID = mainID
            ..idArea = idArea
            ..idBranch = idBranch,
          from: infoSSZDetailPageControllerProvider,
          name: r'infoSSZDetailPageControllerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$infoSSZDetailPageControllerHash,
          dependencies: InfoSSZDetailPageControllerFamily._dependencies,
          allTransitiveDependencies:
              InfoSSZDetailPageControllerFamily._allTransitiveDependencies,
          dateStr: dateStr,
          mainID: mainID,
          idArea: idArea,
          idBranch: idBranch,
        );

  InfoSSZDetailPageControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.dateStr,
    required this.mainID,
    required this.idArea,
    required this.idBranch,
  }) : super.internal();

  final String dateStr;
  final String mainID;
  final String? idArea;
  final String? idBranch;

  @override
  FutureOr<List<TaskSSZModel>?> runNotifierBuild(
    covariant InfoSSZDetailPageController notifier,
  ) {
    return notifier.build(
      dateStr,
      mainID,
      idArea,
      idBranch,
    );
  }

  @override
  Override overrideWith(InfoSSZDetailPageController Function() create) {
    return ProviderOverride(
      origin: this,
      override: InfoSSZDetailPageControllerProvider._internal(
        () => create()
          ..dateStr = dateStr
          ..mainID = mainID
          ..idArea = idArea
          ..idBranch = idBranch,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        dateStr: dateStr,
        mainID: mainID,
        idArea: idArea,
        idBranch: idBranch,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<InfoSSZDetailPageController,
      List<TaskSSZModel>?> createElement() {
    return _InfoSSZDetailPageControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is InfoSSZDetailPageControllerProvider &&
        other.dateStr == dateStr &&
        other.mainID == mainID &&
        other.idArea == idArea &&
        other.idBranch == idBranch;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, dateStr.hashCode);
    hash = _SystemHash.combine(hash, mainID.hashCode);
    hash = _SystemHash.combine(hash, idArea.hashCode);
    hash = _SystemHash.combine(hash, idBranch.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin InfoSSZDetailPageControllerRef
    on AutoDisposeAsyncNotifierProviderRef<List<TaskSSZModel>?> {
  /// The parameter `dateStr` of this provider.
  String get dateStr;

  /// The parameter `mainID` of this provider.
  String get mainID;

  /// The parameter `idArea` of this provider.
  String? get idArea;

  /// The parameter `idBranch` of this provider.
  String? get idBranch;
}

class _InfoSSZDetailPageControllerProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<InfoSSZDetailPageController,
        List<TaskSSZModel>?> with InfoSSZDetailPageControllerRef {
  _InfoSSZDetailPageControllerProviderElement(super.provider);

  @override
  String get dateStr => (origin as InfoSSZDetailPageControllerProvider).dateStr;
  @override
  String get mainID => (origin as InfoSSZDetailPageControllerProvider).mainID;
  @override
  String? get idArea => (origin as InfoSSZDetailPageControllerProvider).idArea;
  @override
  String? get idBranch =>
      (origin as InfoSSZDetailPageControllerProvider).idBranch;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
