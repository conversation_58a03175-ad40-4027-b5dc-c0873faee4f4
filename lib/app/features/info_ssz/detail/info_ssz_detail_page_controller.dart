import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:uer_flutter/app/core/providers/notifier_mouted.dart';
import '../../../core/models/component_history/component_history_model.dart';
import '../../../core/models/search/search_model.dart';
import '../../../core/models/task_ssz/task_ssz_model.dart';
import '../../../core/services/api/service_provider.dart';

part 'info_ssz_detail_page_controller.g.dart';

@riverpod
class InfoSSZDetailPageController extends _$InfoSSZDetailPageController
    with NotifierMounted {
  @override
  FutureOr<List<TaskSSZModel>?> build(
      String dateStr, String mainID, String? idArea, String? idBranch) {
    return _fetchSSZ(dateStr, mainID, idArea, idBranch);
  }

  Future<void> getSSZ(
      String dateStr, String mainID, String? idArea, String? idBranch) async {
    state = const AsyncValue.loading();

    var newState = await AsyncValue.guard(() async {
      return _fetchSSZ(dateStr, mainID, idArea, idBranch);
    });

    if (mounted) {
      state = newState;
    }
  }

  Future<List<TaskSSZModel>?> _fetchSSZ(
      String dateStr, String mainID, String? idArea, String? idBranch) async {
    var taskRepository = ref.read(taskRepositoryProvider);

    var items =
        await taskRepository.getTasks(mainID, idArea, idBranch, dateStr);

    return items;
  }

  Future<ComponentHistoryModel?> getHistoryModel(TaskSSZModel model) async {
    var searchModel = SearchModel(mainID: model.mainID);

    var itemRepository = ref.read(itemRepositoryProvider);
    var items = await itemRepository.getItems(searchModel);

    if ((items?.length ?? 0) > 0) {
      var item = items!.first;

      for (var component in item.components) {
        if (component.newHistory == null) {
          continue;
        }

        for (var history in component.newHistory!) {
          if (history.taskID == model.id && history.task == model.idJob) {
            return history;
          }
        }
      }
    }

    return null;
  }
}
