// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'info_ssz_page_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$infoSSZPageControllerHash() =>
    r'0a415c7ee1c8ca5076023c6bc99fcf3f1122ccef';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$InfoSSZPageController
    extends BuildlessAutoDisposeAsyncNotifier<List<TaskSSZInfoModel>?> {
  late final String dateStr;
  late final String? idArea;
  late final String? idBranch;

  FutureOr<List<TaskSSZInfoModel>?> build(
    String dateStr,
    String? idArea,
    String? idBranch,
  );
}

/// See also [InfoSSZPageController].
@ProviderFor(InfoSSZPageController)
const infoSSZPageControllerProvider = InfoSSZPageControllerFamily();

/// See also [InfoSSZPageController].
class InfoSSZPageControllerFamily
    extends Family<AsyncValue<List<TaskSSZInfoModel>?>> {
  /// See also [InfoSSZPageController].
  const InfoSSZPageControllerFamily();

  /// See also [InfoSSZPageController].
  InfoSSZPageControllerProvider call(
    String dateStr,
    String? idArea,
    String? idBranch,
  ) {
    return InfoSSZPageControllerProvider(
      dateStr,
      idArea,
      idBranch,
    );
  }

  @override
  InfoSSZPageControllerProvider getProviderOverride(
    covariant InfoSSZPageControllerProvider provider,
  ) {
    return call(
      provider.dateStr,
      provider.idArea,
      provider.idBranch,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'infoSSZPageControllerProvider';
}

/// See also [InfoSSZPageController].
class InfoSSZPageControllerProvider
    extends AutoDisposeAsyncNotifierProviderImpl<InfoSSZPageController,
        List<TaskSSZInfoModel>?> {
  /// See also [InfoSSZPageController].
  InfoSSZPageControllerProvider(
    String dateStr,
    String? idArea,
    String? idBranch,
  ) : this._internal(
          () => InfoSSZPageController()
            ..dateStr = dateStr
            ..idArea = idArea
            ..idBranch = idBranch,
          from: infoSSZPageControllerProvider,
          name: r'infoSSZPageControllerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$infoSSZPageControllerHash,
          dependencies: InfoSSZPageControllerFamily._dependencies,
          allTransitiveDependencies:
              InfoSSZPageControllerFamily._allTransitiveDependencies,
          dateStr: dateStr,
          idArea: idArea,
          idBranch: idBranch,
        );

  InfoSSZPageControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.dateStr,
    required this.idArea,
    required this.idBranch,
  }) : super.internal();

  final String dateStr;
  final String? idArea;
  final String? idBranch;

  @override
  FutureOr<List<TaskSSZInfoModel>?> runNotifierBuild(
    covariant InfoSSZPageController notifier,
  ) {
    return notifier.build(
      dateStr,
      idArea,
      idBranch,
    );
  }

  @override
  Override overrideWith(InfoSSZPageController Function() create) {
    return ProviderOverride(
      origin: this,
      override: InfoSSZPageControllerProvider._internal(
        () => create()
          ..dateStr = dateStr
          ..idArea = idArea
          ..idBranch = idBranch,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        dateStr: dateStr,
        idArea: idArea,
        idBranch: idBranch,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<InfoSSZPageController,
      List<TaskSSZInfoModel>?> createElement() {
    return _InfoSSZPageControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is InfoSSZPageControllerProvider &&
        other.dateStr == dateStr &&
        other.idArea == idArea &&
        other.idBranch == idBranch;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, dateStr.hashCode);
    hash = _SystemHash.combine(hash, idArea.hashCode);
    hash = _SystemHash.combine(hash, idBranch.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin InfoSSZPageControllerRef
    on AutoDisposeAsyncNotifierProviderRef<List<TaskSSZInfoModel>?> {
  /// The parameter `dateStr` of this provider.
  String get dateStr;

  /// The parameter `idArea` of this provider.
  String? get idArea;

  /// The parameter `idBranch` of this provider.
  String? get idBranch;
}

class _InfoSSZPageControllerProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<InfoSSZPageController,
        List<TaskSSZInfoModel>?> with InfoSSZPageControllerRef {
  _InfoSSZPageControllerProviderElement(super.provider);

  @override
  String get dateStr => (origin as InfoSSZPageControllerProvider).dateStr;
  @override
  String? get idArea => (origin as InfoSSZPageControllerProvider).idArea;
  @override
  String? get idBranch => (origin as InfoSSZPageControllerProvider).idBranch;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
