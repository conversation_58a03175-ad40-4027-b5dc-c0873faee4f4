import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../core/providers/notifier_mouted.dart';
import '../../../core/models/task_ssz_info/task_ssz_info_model.dart';
import '../../../core/services/api/service_provider.dart';

part 'info_ssz_page_controller.g.dart';

@riverpod
class InfoSSZPageController extends _$InfoSSZPageController
    with NotifierMounted {
  @override
  FutureOr<List<TaskSSZInfoModel>?> build(
      String dateStr, String? idArea, String? idBranch) {
    return _fetchInfoSSZ(dateStr, idArea, idBranch);
  }

  Future<void> getInfoSSZ(
      String dateStr, String? idArea, String? idBranch) async {
    state = const AsyncValue.loading();

    var newState = await AsyncValue.guard(() async {
      return _fetchInfoSSZ(dateStr, idArea, idBranch);
    });

    if (mounted) {
      state = newState;
    }
  }

  Future<List<TaskSSZInfoModel>?> _fetchInfoSSZ(
      String dateStr, String? idArea, String? idBranch) async {
    var taskRepository = ref.read(taskRepositoryProvider);

    var items = await taskRepository.getInfoTasksSSZ(idArea, idBranch, dateStr);

    return items;
  }

  Future<void> refresh() async {
    await getInfoSSZ(dateStr, idArea, idBranch);
  }
}
