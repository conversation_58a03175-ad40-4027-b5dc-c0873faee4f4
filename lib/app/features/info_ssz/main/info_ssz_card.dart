import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uer_flutter/app/styles/colors.dart';
import 'package:uer_flutter/app/styles/fonts.dart';
import 'package:uer_flutter/app/widgets/containers/id.dart';
import 'package:uer_flutter/app/widgets/svg_icon/index.dart';
import 'package:uer_flutter/gen/assets.gen.dart';

import '../../../core/models/task_ssz_info/task_ssz_info_model.dart';

typedef InfoSSZCardCallback = Function();

class InfoSSZCard extends ConsumerStatefulWidget {
  const InfoSSZCard({
    super.key,
    required this.model,
    required this.callback,
  });

  final TaskSSZInfoModel model;
  final InfoSSZCardCallback callback;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _InfoSSZCardState();
}

class _InfoSSZCardState extends ConsumerState<InfoSSZCard> {
  @override
  Widget build(BuildContext context) {
    final notActiveCount = widget.model.tasksCount -
        widget.model.inworkCount -
        widget.model.finishCount -
        widget.model.pauseCount;

    return Card(
      color: Colors.white,
      margin: const EdgeInsets.only(bottom: 16.0),
      child: InkWell(
        borderRadius: BorderRadius.circular(10.0),
        onTap: widget.callback,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 16.0),
          child: Column(
            children: [
              Row(
                children: [
                  IdLabel(widget.model.repairNumber),
                  const SizedBox(width: 6.0),
                  Expanded(
                    child: Text(
                      widget.model.equipment ?? "",
                      softWrap: false,
                      maxLines: 1,
                      overflow: TextOverflow.fade,
                      style: AppFonts.labelMedium.merge(
                        const TextStyle(
                          color: AppLightColors.medium,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 6.0),
                  SVGIcon(Assets.icons.checklist, width: 20.0),
                  const SizedBox(width: 10.0),
                  Text(
                    '${widget.model.tasksCount}',
                    style: AppFonts.titleSmall.merge(
                      const TextStyle(
                        fontFamily: AppFonts.monoFontFamily,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(
                height: 18.0,
              ),

              Row(children: [
                SVGIcon(
                  Assets.icons.warningAmber,
                  color: AppLightColors.warning,
                  width: 20.0,
                ),
                const SizedBox(width: 10.0),
                const Text('Неактивные: ', style: AppFonts.labelMedium),
                const Spacer(),
                Text(
                  '$notActiveCount',
                  style: AppFonts.titleSmall.merge(
                    const TextStyle(
                      fontFamily: AppFonts.monoFontFamily,
                    ),
                  ),
                ),
              ]),
              Row(children: [
                SVGIcon(
                  Assets.icons.labelImportantOutline,
                  color: AppLightColors.darkMedium,
                  width: 20.0,
                ),
                const SizedBox(width: 10.0),
                const Text('Выполняются: ', style: AppFonts.labelMedium),
                const Spacer(),
                Text(
                  '${widget.model.inworkCount}',
                  style: AppFonts.titleSmall.merge(
                    const TextStyle(
                      fontFamily: AppFonts.monoFontFamily,
                    ),
                  ),
                ),
              ]),
              Row(children: [
                SVGIcon(
                  Assets.icons.errorOutline,
                  color: AppLightColors.error,
                  width: 20.0,
                ),
                const SizedBox(width: 10.0),
                const Text('Приостановленные: ', style: AppFonts.labelMedium),
                const Spacer(),
                Text(
                  '${widget.model.pauseCount}',
                  style: AppFonts.titleSmall.merge(
                    const TextStyle(
                      fontFamily: AppFonts.monoFontFamily,
                    ),
                  ),
                ),
              ]),
              Row(children: [
                SVGIcon(
                  Assets.icons.taskAlt,
                  color: AppLightColors.success,
                  width: 20.0,
                ),
                const SizedBox(width: 10.0),
                const Text('Выполненные: ', style: AppFonts.labelMedium),
                const Spacer(),
                Text(
                  '${widget.model.finishCount}',
                  style: AppFonts.titleSmall.merge(
                    const TextStyle(
                      fontFamily: AppFonts.monoFontFamily,
                    ),
                  ),
                ),
              ]),

              // Row(
              //   children: [
              //     StatusBadgeText(
              //       icon: HistoryStatusType.inwork.statusBadge,
              //       text: inworkText,
              //     ),
              //     const Spacer(),
              //     StatusBadgeText(
              //       icon: HistoryStatusType.finish.statusBadge,
              //       text: finishText,
              //     ),
              //   ],
              // ),
              // const SizedBox(
              //   height: 8,
              // ),
              // Row(
              //   children: [
              //     StatusBadgeText(
              //       icon: HistoryStatusType.pause.statusBadge,
              //       text: pauseText,
              //     ),
              //     const Spacer(),
              //     StatusBadgeText(
              //       icon: const Icon(
              //         Icons.circle,
              //         color: Colors.grey,
              //         size: 14,
              //       ),
              //       text: notActiveText,
              //     ),
              //   ],
              // ),
            ],
          ),
        ),
      ),
    );
  }
}
