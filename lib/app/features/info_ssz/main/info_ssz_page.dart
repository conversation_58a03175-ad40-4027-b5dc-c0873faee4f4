import 'package:calendar_date_picker2/calendar_date_picker2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:uer_flutter/app/helpers/date_extension.dart';

import '../../../core/models/area/area_model.dart';
import '../../../core/models/branch/branch_model.dart';
import '../../../core/models/enums/user_role_enum.dart';
import '../../../core/models/task_ssz_info/task_ssz_info_model.dart';
import '../../../core/providers/common/common_providers.dart';
import '../../../core/providers/states/user/user_state.dart';
import '../../../routes/routes_name.dart';
import '../../../widgets/bars/app_bar.dart';
import '../../../widgets/common_button.dart';
import '../../../widgets/dialogs/actions_alert_dialog.dart';
import 'info_ssz_card.dart';
import 'info_ssz_page_controller.dart';

class InfoSSZPage extends ConsumerStatefulWidget {
  const InfoSSZPage({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _InfoSSZPageState();
}

class _InfoSSZPageState extends ConsumerState<InfoSSZPage> {
  var _currentDate = DateTime.now();

  String? idArea;
  String? idBranch;

  void openDetailInfoSSZ(TaskSSZInfoModel model) {
    context.pushNamed(
      RoutesName.infoSSZDetail.named,
      queryParameters: {
        "mainID": model.mainID,
        "repairNumber": model.repairNumber,
        "dateStr": _currentDate.dateShortStringForGetInfoSSZ(),
        "areaID": idArea,
        "branchID": idBranch,
      },
    );
  }

  void clearArea() {
    setState(() {
      idArea = null;
    });
  }

  void clearBranch() {
    setState(() {
      idBranch = null;
    });
  }

  void openDialog() async {
    List<CommonButtonModel> actionButtons = [];

    var btn1 =
        CommonButtonModel(name: "Выбрать Филиал", callback: openBranchList);
    actionButtons.add(btn1);

    var btn2 =
        CommonButtonModel(name: "Выбрать участок", callback: openAreaList);
    actionButtons.add(btn2);

    var cancelBtn = CommonButtonModel(name: "Отмена", callback: () {});

    actionButtons.add(cancelBtn);

    await showDialog(
        context: context,
        builder: (_) => ActionsAlertDialog(
            title: "Выберите тип фильтра", actionButtons: actionButtons));
  }

  void openAreaList() async {
    AreaModel? currentArea;
    BranchModel? currentBranch;

    if (idArea != null) {
      currentArea = ref.read(areaForIDAreaStrProvider(idArea));
    }

    if (idBranch != null) {
      currentBranch = ref.read(branchForIDBranchStrProvider(idBranch));
    }

    var result = await context
        .pushNamed(RoutesName.selectAreaList.named, queryParameters: {
      "branchID": currentBranch?.identifier.toString(),
      "removeAreaID": currentArea?.identifier.toString()
    });

    if (result != null && result is AreaModel) {
      setState(() {
        idArea = result.idArea;
      });
    }
  }

  void openBranchList() async {
    BranchModel? currentBranch;

    if (idBranch != null) {
      currentBranch = ref.read(branchForIDBranchStrProvider(idBranch));
    }

    var result = await context.pushNamed(RoutesName.selectBranchList.named,
        extra: currentBranch?.identifier);

    if (result != null && result is BranchModel) {
      setState(() {
        idBranch = result.idBranch;
        idArea = null;
      });
    }
  }

  void openCalendar() async {
    var results = await showCalendarDatePicker2Dialog(
      context: context,
      config: CalendarDatePicker2WithActionButtonsConfig(firstDayOfWeek: 1),
      dialogSize: const Size(325, 400),
      value: [_currentDate],
      borderRadius: BorderRadius.circular(15),
    );

    if ((results?.length ?? 0) > 0) {
      setState(() {
        _currentDate = results!.first!;
      });
    }
  }

  void refresh() async {
    var dateStr = _currentDate.dateShortStringForGetInfoSSZ();
    await ref
        .read(infoSSZPageControllerProvider(dateStr, idArea, idBranch).notifier)
        .refresh();
  }

  @override
  Widget build(BuildContext context) {
    var userState = ref.watch(currentUserProvider);
    var user = userState.maybeWhen(auth: (user) => user, orElse: () {});

    var uArea = ref.watch(currentUserAreaProvider);
    var uBranch = ref.watch(currentUserBranchProvider);

    if (user?.role == UserRole.director) {
      idBranch = uBranch?.idBranch;
    } else if (user?.role == UserRole.user) {
      if (uArea?.start == true) {
        idBranch = uBranch?.idBranch;
      } else {
        idArea = uArea?.idArea;
      }
    }

    var showChoiceArea = user?.role == UserRole.director ||
        user?.role == UserRole.admin ||
        user?.role == UserRole.manager;
    var showRemoveAreaBtn = showChoiceArea == true && idArea != null;

    var branchFilterAccessOn =
        user?.role == UserRole.admin || user?.role == UserRole.manager;
    var showRemoveBranchBtn = branchFilterAccessOn == true && idBranch != null;

    var currentArea = ref.watch(areaForIDAreaStrProvider(idArea));
    var currentBranch = ref.watch(branchForIDBranchStrProvider(idBranch));

    var dateStr = _currentDate.dateShortStringForGetInfoSSZ();

    var models =
        ref.watch(infoSSZPageControllerProvider(dateStr, idArea, idBranch));

    return Scaffold(
        appBar: CustomAppBar(
          text: "Инфо ССЗ - $dateStr",
          actions: [
            showChoiceArea == true
                ? IconButton(
                    onPressed: () {
                      if (user?.role == UserRole.admin ||
                          user?.role == UserRole.manager) {
                        openDialog();
                      } else {
                        openAreaList();
                      }
                    },
                    icon: const Icon(
                      Icons.filter_alt,
                      color: Colors.white,
                    ))
                : const SizedBox(),
            IconButton(
                onPressed: openCalendar,
                icon: const Icon(Icons.calendar_month, color: Colors.white))
          ],
        ),
        body: RefreshIndicator(
          onRefresh: () => Future.sync(
            () => refresh(),
          ),
          child: models.when(data: (data) {
            return Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                showRemoveAreaBtn == true
                    ? Padding(
                        padding: const EdgeInsets.fromLTRB(8, 8, 8, 4),
                        child: ElevatedButton.icon(
                            onPressed: clearArea,
                            icon: const Icon(
                              Icons.clear,
                            ),
                            style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.orange,
                                foregroundColor: Colors.white),
                            label: Text(
                              currentArea?.name ?? "",
                              style: const TextStyle(
                                fontWeight: FontWeight.w600,
                              ),
                            )),
                      )
                    : const SizedBox(),
                showRemoveBranchBtn == true
                    ? Padding(
                        padding: const EdgeInsets.fromLTRB(8, 8, 8, 4),
                        child: ElevatedButton.icon(
                            onPressed: clearBranch,
                            icon: const Icon(
                              Icons.clear,
                            ),
                            style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.orange,
                                foregroundColor: Colors.white),
                            label: Text(
                              currentBranch?.name ?? "",
                              style: const TextStyle(
                                fontWeight: FontWeight.w600,
                              ),
                            )),
                      )
                    : const SizedBox(),
                Expanded(
                  child: ListView.builder(
                    padding: const EdgeInsets.all(16.0),
                    itemCount: data?.length ?? 0,
                    itemBuilder: (BuildContext context, int index) {
                      final model = data![index];

                      void callback() {
                        openDetailInfoSSZ(model);
                      }

                      return InfoSSZCard(model: model, callback: callback);
                    },
                  ),
                ),
              ],
            );
          }, error: (error, stacktrace) {
            return Center(
              child: Text("Error: $error"),
            );
          }, loading: () {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }),
        ));
  }
}
