import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:flutter_layout_grid/flutter_layout_grid.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:uer_flutter/app/core/models/stats/tags_stats_model.dart';
import 'package:uer_flutter/app/widgets/bars/app_bar.dart';
import 'package:uer_flutter/app/widgets/containers/stat_row.dart';
import 'package:uer_flutter/app/widgets/interactive/chip.dart';
import 'package:uer_flutter/app/widgets/photo_preview_card.dart';
import 'package:uer_flutter/app/widgets/switch_row.dart';

import '../../helpers/photo_helpers.dart';
import '../../helpers/snack_bar.dart';
import '../../routes/routes_name.dart';
import 'receiving_page_controller.dart';

class ReceivingPage extends ConsumerStatefulWidget {
  const ReceivingPage({super.key, required this.mainID});

  final String mainID;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _ReceivingPageState();
}

class _ReceivingPageState extends ConsumerState<ReceivingPage> {
  var loadingPhoto = false;
  var loadingSave = false;

  void openQRGeneratePage() {
    context.replaceNamed(RoutesName.qrGenerator.named, queryParameters: {
      'mid': widget.mainID,
    });
  }

  void saveReceivingItem() async {
    if (loadingSave == true) {
      return;
    }

    setState(() {
      loadingSave = true;
    });

    var controller =
        ref.watch(receivingPageControllerProvider(widget.mainID).notifier);
    var success = await controller.saveNewItem(context);

    setState(() {
      loadingSave = false;
    });

    if (success == true) {
      openQRGeneratePage();
    }
  }

  void choicesPhotos(List<Uint8List> photos) {
    var value = ref.watch(receivingPageControllerProvider(widget.mainID));
    var model = value.value;

    var selectedPhotosLength = model?.selectedPhotos.length ?? 0;

    var allPhotoLength = selectedPhotosLength + photos.length;

    if (allPhotoLength > 10) {
      setState(() {
        loadingPhoto = false;
      });

      showInSnackBar("Ошибка, нельзя прикрепить больше 10 фото", context);
      return;
    }

    var controller =
        ref.watch(receivingPageControllerProvider(widget.mainID).notifier);
    controller.choicesPhotos(photos);

    setState(() {
      loadingPhoto = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    var value = ref.watch(receivingPageControllerProvider(widget.mainID));
    var controller =
        ref.watch(receivingPageControllerProvider(widget.mainID).notifier);

    void addTag(ItemTag tag) {
      final List<ItemTag> tags = [...(value.value?.tags ?? [])];

      if (!tags.contains(tag)) {
        tags.add(tag);
      }

      controller.changeTags([...tags]);
    }

    void removeTag(ItemTag tag) {
      final List<ItemTag> tags = [...(value.value?.tags ?? [])];

      if (tags.contains(tag)) {
        tags.remove(tag);
      }

      controller.changeTags([...tags]);
    }

    return Scaffold(
        appBar: CustomAppBar(
          text: "Приёмка двигателя",
          actions: [
            loadingSave == false
                ? TextButton(
                    onPressed: saveReceivingItem,
                    child: const Text(
                      "Сохранить",
                      style: TextStyle(color: Colors.white),
                    ))
                : const CircularProgressIndicator.adaptive()
          ],
        ),
        body: value.when(
          data: (data) {
            return SingleChildScrollView(
              padding: EdgeInsets.all(16.0),
              child: Column(children: [
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Column(children: [
                      StatRow(
                          "Ремонтный номер:", data?.item.repairNumber ?? "---"),
                      const SizedBox(
                        height: 6,
                      ),
                      StatRow("Контрагент:",
                          data?.item.order?.client?.name ?? "---"),
                      const SizedBox(
                        height: 6,
                      ),
                      StatRow("Дата завершения:",
                          data?.item.order?.finishDate ?? "---"),
                    ]),
                  ),
                ),
                SizedBox(height: 24.0),
                SegmentedButton(
                  segments: const [
                    ButtonSegment(value: 1, label: Text("Двигатель")),
                    ButtonSegment(value: 2, label: Text("Трансформатор"))
                  ],
                  selected: {data?.currentTypeIndex ?? 1},
                  onSelectionChanged: controller.changeType,
                ),
                SizedBox(height: 24.0),
                Wrap(spacing: 4.0, runSpacing: 4.0, children: [
                  ...ItemTag.values.map((tag) {
                    return CustomChip(
                      isEnabled: value.value?.tags.contains(tag) ?? false,
                      label: tag.getName(),
                      color: tag.getColor(),
                      onTap: () {
                        if (value.value?.tags.contains(tag) ?? false) {
                          removeTag(tag);
                        } else {
                          addTag(tag);
                        }
                      },
                    );
                  }),
                ]),
                SizedBox(height: 24.0),
                data?.currentTypeIndex == 1
                    ? Column(
                        children: [
                          SwitchRow(
                              name: data?.item.dcMachine() == true
                                  ? "Якорь"
                                  : "Ротор",
                              enable: data?.rotorEnable ?? false,
                              callback: controller.changeRotor),
                          const Divider(),
                          SwitchRow(
                              name: data?.item.dcMachine() == true
                                  ? "Индуктор"
                                  : "Статор",
                              enable: data?.statorEnable ?? false,
                              callback: controller.changeStator),
                          const Divider(),
                          const Text(
                            "Подшипники",
                            style: TextStyle(
                                fontWeight: FontWeight.w500, fontSize: 16),
                          ),
                          const SizedBox(
                            height: 6,
                          ),
                          SegmentedButton(
                            segments: const [
                              ButtonSegment(value: 1, label: Text("Нет")),
                              ButtonSegment(
                                  value: 2, label: Text("Скольжения")),
                              ButtonSegment(value: 3, label: Text("Качения"))
                            ],
                            selected: {data?.bearingTypeIndex ?? 1},
                            onSelectionChanged: controller.changeBearingType,
                          ),
                          SwitchRow(
                              name: "В сборе",
                              enable: data?.assembledValue ?? false,
                              callback: controller.changeAssembled),
                          const Divider(),
                        ],
                      )
                    : const SizedBox(),
                SizedBox(height: 24.0),
                Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SwitchRow(
                        name: "Капитальный ремонт",
                        enable: data?.majorRepair ?? false,
                        callback: controller.changeMajorRepair),
                    const Divider(),
                    const Text(
                      "Комментарий",
                      textAlign: TextAlign.left,
                    ),
                    TextField(
                      onChanged: controller.changeComment,
                    ),
                    const SizedBox(
                      height: 8,
                    ),
                    Row(
                      children: [
                        const Text("Добавить фото"),
                        PopupMenuButton<int>(
                          icon: const Icon(
                            Icons.add_a_photo_outlined,
                            color: Colors.orange,
                          ),
                          tooltip: "Добавить фото",
                          onSelected: (index) {
                            setState(() {
                              loadingPhoto = true;
                            });
                          },
                          itemBuilder: (context) {
                            return getMenuList(choicesPhotos);
                          },
                        ),
                        loadingPhoto == true
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                ),
                              )
                            : const SizedBox(),
                      ],
                    ),
                  ],
                ),
                SizedBox(height: 24.0),
                data?.selectedPhotos.isNotEmpty == true
                    ? LayoutGrid(
                        // Concise track sizing extension methods 🔥
                        columnSizes: [1.fr, 1.fr, 1.fr, 1.fr, 1.fr],
                        rowSizes: List.filled(
                            (data!.selectedPhotos.length / 5)
                                .ceilToDouble()
                                .toInt(),
                            const FixedTrackSize(100)),
                        // Column and row gaps! 🔥
                        columnGap: 6,
                        rowGap: 6,
                        // Handy grid placement extension methods on Widget 🔥
                        children:
                            List.generate(data.selectedPhotos.length, (index) {
                          final photoData = data.selectedPhotos[index];

                          callback() {
                            controller.removePhoto(index);
                          }

                          return PhotoPreviewCard(
                              photoData: photoData, removeCallback: callback);
                        }),
                      )
                    : const SizedBox(),
              ]),
            );
          },
          error: (error, stackTrace) {
            return Text(error.toString());
          },
          loading: () {
            return const CircularProgressIndicator();
          },
        ));
  }
}
