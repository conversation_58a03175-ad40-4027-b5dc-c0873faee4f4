import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:uer_flutter/app/core/models/receiving/receiving_page_model.dart';
import 'package:uer_flutter/app/core/models/stats/tags_stats_model.dart';
import 'package:uer_flutter/app/core/providers/notifier_mouted.dart';

import '../../core/models/component/component_model.dart';
import '../../core/models/component_history/component_history_model.dart';
import '../../core/models/enums/component_history_type_enum.dart';
import '../../core/models/enums/component_type_enum.dart';
import '../../core/models/enums/history_status_enum.dart';
import '../../core/models/enums/user_role_enum.dart';
import '../../core/models/item/item_model.dart';
import '../../core/models/search/search_model.dart';
import '../../core/providers/common/common_providers.dart';
import '../../core/providers/photo_upload/photo_upload_provider.dart';
import '../../core/providers/states/user/user_state.dart';
import '../../core/services/api/service_provider.dart';
import '../../helpers/snack_bar.dart';
import '../../helpers/string_helper.dart';
import '../main_user/main_user_controller.dart';

part 'receiving_page_controller.g.dart';

@riverpod
class ReceivingPageController extends _$ReceivingPageController
    with NotifierMounted {
  final String commonIdentifier = getRandomString(16);

  @override
  FutureOr<ReceivingPageModel?> build(String mainID) async {
    var item = await _fetchItem(mainID);

    if (item?.transform() == true) {
      return ReceivingPageModel(
        item: item!,
        tags: [ItemTag.itemDefault],
        currentTypeIndex: 2,
        rotorEnable: false,
        statorEnable: false,
        assembledValue: false,
        majorRepair: false,
        bearingTypeIndex: 1,
        comment: null,
        selectedPhotos: [],
      );
    } else if (item != null) {
      return ReceivingPageModel(
        item: item,
        tags: [ItemTag.itemDefault],
        currentTypeIndex: 1,
        rotorEnable: false,
        statorEnable: false,
        assembledValue: false,
        majorRepair: false,
        bearingTypeIndex: 1,
        comment: null,
        selectedPhotos: [],
      );
    }

    return null;
  }

  void changeType(Set<int> index) async {
    var newState = await AsyncValue.guard(() async {
      return state.value?.copyWith(currentTypeIndex: index.first);
    });

    if (mounted) {
      state = newState;
    }
  }

  void changeRotor(bool value) async {
    var newState = await AsyncValue.guard(() async {
      return state.value?.copyWith(rotorEnable: value);
    });

    if (mounted) {
      state = newState;
    }
  }

  void changeTags(List<ItemTag> tags) async {
    var newState = await AsyncValue.guard(() async {
      return state.value?.copyWith(tags: [...tags]);
    });

    if (mounted) {
      state = newState;
    }
  }

  void changeStator(bool value) async {
    var newState = await AsyncValue.guard(() async {
      return state.value?.copyWith(statorEnable: value);
    });

    if (mounted) {
      state = newState;
    }
  }

  void changeAssembled(bool value) async {
    var newState = await AsyncValue.guard(() async {
      return state.value?.copyWith(assembledValue: value);
    });

    if (mounted) {
      state = newState;
    }
  }

  void changeMajorRepair(bool value) async {
    var newState = await AsyncValue.guard(() async {
      return state.value?.copyWith(majorRepair: value);
    });

    if (mounted) {
      state = newState;
    }
  }

  void changeBearingType(Set<int> index) async {
    var newState = await AsyncValue.guard(() async {
      return state.value?.copyWith(bearingTypeIndex: index.first);
    });

    if (mounted) {
      state = newState;
    }
  }

  void changeComment(String comment) async {
    var newState = await AsyncValue.guard(() async {
      return state.value?.copyWith(comment: comment);
    });

    if (mounted) {
      state = newState;
    }
  }

  void choicesPhotos(List<Uint8List> photos) async {
    var currentPhotos = state.value?.selectedPhotos;

    if (currentPhotos == null) {
      return;
    }

    var selectedPhotos = [...currentPhotos, ...photos];

    var newState = await AsyncValue.guard(() async {
      return state.value?.copyWith(selectedPhotos: selectedPhotos);
    });

    if (mounted) {
      state = newState;
    }
  }

  void removePhoto(int index) async {
    var currentPhotos = state.value?.selectedPhotos;

    if (currentPhotos == null) {
      return;
    }

    currentPhotos = [...currentPhotos];

    currentPhotos = currentPhotos..removeAt(index);

    var newState = await AsyncValue.guard(() async {
      return state.value?.copyWith(selectedPhotos: currentPhotos!);
    });

    if (mounted) {
      state = newState;
    }
  }

  Future<ItemModel?> _fetchItem(String mainID) async {
    var searchModel = SearchModel(mainID: mainID, newBool: true);

    var itemRepository = ref.read(itemRepositoryProvider);

    var item = await itemRepository.getItems(searchModel);

    if ((item?.length ?? 0) > 0) {
      return item!.first;
    }

    return null;
  }

  Future<bool> saveNewItem(
    BuildContext context,
  ) async {
    var userState = ref.read(currentUserProvider);
    var user = userState.maybeWhen(auth: (user) => user, orElse: () {});

    if (user == null) {
      showInSnackBar("Ошибка. Пользователь не загружен.", context);
      return false;
    }

    var model = state.value;

    if (model == null) {
      showInSnackBar(
          "Ошибка. Данные не загружены. Повторите позднее.", context);
      return false;
    }

    if (user.role == UserRole.user) {
      var area = user.area;
      var branch = user.branch;

      if (area == null || branch == null) {
        showInSnackBar("Ошибка. Ошибка в данных пользователя.", context);
        return false;
      }

      var item = configurateModel(
        area,
        branch,
        model.transform(),
        model.rotorEnable,
        model.statorEnable,
        model.assembledValue,
        model.bearingTypeIndex,
        model.majorRepair,
        model.comment,
        model.tags,
      );

      if (item == null) {
        showInSnackBar("Ошибка. Ошибка в генерации позиции.", context);
        return false;
      }

      if (item.assembled == true) {
        if (item.components.length <= 1) {
          showInSnackBar(
              "Ошибка. В сборе должно быть больше 1 компонента", context);
          return false;
        }

        var values = item.components.where((element) =>
            element.type == ComponentType.inductor ||
            element.type == ComponentType.stator);

        if (values.isEmpty == true) {
          showInSnackBar(
              "Ошибка. В сборе должен присутствовать Статор/Индуктор", context);
          return false;
        }
      } else {
        if (item.components.isEmpty) {
          showInSnackBar("Ошибка. Выберите хотя бы 1 компонент", context);
          return false;
        }
      }

      if (model.selectedPhotos.length < 4) {
        showInSnackBar("Ошибка. Выберите минимум 4 фото", context);
        return false;
      }

      var result = await requestEditNewItem(
          item, area, user.login, model.selectedPhotos, context);

      if (result == true) {
        ref.read(mainUserControllerProvider.notifier).getItems(null);
      }

      return result;
    }

    return false;
  }

  Future<bool> requestEditNewItem(ItemModel item, int areaID, String owner,
      List<Uint8List> selectedPhotos, BuildContext context) async {
    var itemProvider = ref.read(itemRepositoryProvider);
    var successEdit = await itemProvider.editItem(item);

    if (successEdit == true) {
      if (selectedPhotos.isEmpty == true) {
        return true;
      }

      var controller = ref.read(photoUploadControllerProvider(item).notifier);
      var successPhotoUpload =
          await controller.addNewPhoto(selectedPhotos, true);

      if (successPhotoUpload == true) {
        showInSnackBar("Позиция обновлена", context);
        return true;
      } else {
        showInSnackBar("Позиция обновлена, но фото не загружены", context);
      }

      return true;
    }

    return false;
  }

  ItemModel? configurateModel(
    int areaID,
    int branchID,
    bool transform,
    bool rotor,
    bool stator,
    bool assembled,
    int bearingTypeIndex,
    bool major,
    String? comment,
    List<ItemTag> tags,
  ) {
    var item = state.value?.item;

    if (item == null) {
      return null;
    }

    List<ComponentModel> components = [];

    if (transform == true) {
      var model = configurateComponentModelWithType(
          ComponentType.transformer, areaID, comment);

      if (model != null) {
        components.add(model);
      }
    } else {
      var rotorType =
          item.dcMachine() ? ComponentType.anchor : ComponentType.rotor;
      var statorType =
          item.dcMachine() ? ComponentType.inductor : ComponentType.stator;

      if (rotor == true) {
        var model =
            configurateComponentModelWithType(rotorType, areaID, comment);

        if (model != null) {
          components.add(model);
        }
      }

      if (stator == true) {
        var model =
            configurateComponentModelWithType(statorType, areaID, comment);
        if (model != null) {
          components.add(model);
        }
      }

      if (bearingTypeIndex > 1) {
        if (bearingTypeIndex == 2) {
          var model = configurateComponentModelWithType(
              ComponentType.slidingBearing, areaID, comment);
          if (model != null) {
            components.add(model);
          }
        } else {
          var model = configurateComponentModelWithType(
              ComponentType.rollingBearing, areaID, comment);
          if (model != null) {
            components.add(model);
          }
        }
      }
    }

    var newItem = item.copyWith(
      components: components,
      major: major,
      newBool: false,
      tags: tags,
    );

    if (transform == false) {
      newItem = newItem.copyWith(assembled: assembled);
    }

    return newItem;
  }

  ComponentModel? configurateComponentModelWithType(
      ComponentType type, int areaID, String? comment) {
    var id = "";

    switch (type) {
      case ComponentType.stator:
        id = "${mainID}_s";
        break;
      case ComponentType.rotor:
        id = "${mainID}_r";
        break;
      case ComponentType.rollingBearing:
        id = "${mainID}_rb";
        break;
      case ComponentType.slidingBearing:
        id = "${mainID}_sb";
        break;
      case ComponentType.transformer:
        id = "${mainID}_tr";
        break;
      case ComponentType.anchor:
        id = "${mainID}_an";
        break;
      case ComponentType.inductor:
        id = "${mainID}_in";
        break;
    }

    var area = ref.read(areaForIDProvider(areaID));

    if (area == null) {
      return null;
    }

    var name = "${area.name} | Принято";

    var userState = ref.read(currentUserProvider);
    var user = userState.maybeWhen(auth: (user) => user, orElse: () {});

    var owner = user?.login ?? "";

    var identifier = commonIdentifier;

    var status = ComponentStatusModel(
        owner: owner, comment: comment, status: HistoryStatusType.inwork);
    var history = ComponentHistoryModel(
        name: name,
        area: areaID,
        job: 1,
        identifier: identifier,
        type: ComponentHistoryType.job,
        statuses: [status]);

    var model = ComponentModel(
        identifier: id, type: type, currentArea: areaID, newHistory: [history]);

    return model;
  }

  // Future<ItemModel> editItem() {

  // }
}
