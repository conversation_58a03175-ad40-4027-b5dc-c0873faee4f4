// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'receiving_page_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$receivingPageControllerHash() =>
    r'e0f166c14562b578f5bc23e7b576054a1f846228';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$ReceivingPageController
    extends BuildlessAutoDisposeAsyncNotifier<ReceivingPageModel?> {
  late final String mainID;

  FutureOr<ReceivingPageModel?> build(
    String mainID,
  );
}

/// See also [ReceivingPageController].
@ProviderFor(ReceivingPageController)
const receivingPageControllerProvider = ReceivingPageControllerFamily();

/// See also [ReceivingPageController].
class ReceivingPageControllerFamily
    extends Family<AsyncValue<ReceivingPageModel?>> {
  /// See also [ReceivingPageController].
  const ReceivingPageControllerFamily();

  /// See also [ReceivingPageController].
  ReceivingPageControllerProvider call(
    String mainID,
  ) {
    return ReceivingPageControllerProvider(
      mainID,
    );
  }

  @override
  ReceivingPageControllerProvider getProviderOverride(
    covariant ReceivingPageControllerProvider provider,
  ) {
    return call(
      provider.mainID,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'receivingPageControllerProvider';
}

/// See also [ReceivingPageController].
class ReceivingPageControllerProvider
    extends AutoDisposeAsyncNotifierProviderImpl<ReceivingPageController,
        ReceivingPageModel?> {
  /// See also [ReceivingPageController].
  ReceivingPageControllerProvider(
    String mainID,
  ) : this._internal(
          () => ReceivingPageController()..mainID = mainID,
          from: receivingPageControllerProvider,
          name: r'receivingPageControllerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$receivingPageControllerHash,
          dependencies: ReceivingPageControllerFamily._dependencies,
          allTransitiveDependencies:
              ReceivingPageControllerFamily._allTransitiveDependencies,
          mainID: mainID,
        );

  ReceivingPageControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.mainID,
  }) : super.internal();

  final String mainID;

  @override
  FutureOr<ReceivingPageModel?> runNotifierBuild(
    covariant ReceivingPageController notifier,
  ) {
    return notifier.build(
      mainID,
    );
  }

  @override
  Override overrideWith(ReceivingPageController Function() create) {
    return ProviderOverride(
      origin: this,
      override: ReceivingPageControllerProvider._internal(
        () => create()..mainID = mainID,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        mainID: mainID,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<ReceivingPageController,
      ReceivingPageModel?> createElement() {
    return _ReceivingPageControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ReceivingPageControllerProvider && other.mainID == mainID;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, mainID.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ReceivingPageControllerRef
    on AutoDisposeAsyncNotifierProviderRef<ReceivingPageModel?> {
  /// The parameter `mainID` of this provider.
  String get mainID;
}

class _ReceivingPageControllerProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<ReceivingPageController,
        ReceivingPageModel?> with ReceivingPageControllerRef {
  _ReceivingPageControllerProviderElement(super.provider);

  @override
  String get mainID => (origin as ReceivingPageControllerProvider).mainID;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
