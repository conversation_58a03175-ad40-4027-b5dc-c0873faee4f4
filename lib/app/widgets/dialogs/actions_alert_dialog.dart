import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../common_button.dart';

class ActionsAlertDialog extends StatelessWidget {
  final String title;
  final String? text;
  final List<CommonButtonModel> actionButtons;

  const ActionsAlertDialog(
      {super.key, required this.title, this.text, required this.actionButtons});

  @override
  Widget build(BuildContext context) {
    List<Widget> btns = [];

    for (var i = 0; i < actionButtons.length; i++) {
      var model = actionButtons[i];
      btns.add(CommonButton(
        name: model.name,
        callback: () {
          Navigator.of(context).pop();
          model.callback();
        },
      ));
      btns.add(const SizedBox(
        height: 8,
      ));
    }

    return AlertDialog(
      title: Text(title),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.0),
      ),
      content: FittedBox(
        fit: BoxFit.scaleDown,
        child: Column(
          children: text != null
              ? [
                  Text(text!),
                  const SizedBox(
                    height: 16,
                  ),
                  ...btns
                ]
              : btns,
        ),
      ),
    );
  }
}
