import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class TextFieldAlertDialog extends StatelessWidget {
  final TextEditingController _controller = TextEditingController();

  final String title;
  final String actionButtonText;

  TextFieldAlertDialog(
      {super.key, required this.title, required this.actionButtonText});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(title),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.0)),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          TextField(
            controller: _controller,
            decoration: const InputDecoration(labelText: 'Комментарий'),
          ),
          const SizedBox(
            height: 16,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                style: ElevatedButton.styleFrom(backgroundColor: Colors.grey),
                child: const Text(
                  "Отмена",
                  style: TextStyle(color: Colors.white),
                ),
              ),
              const Spacer(),
              ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop(_controller.text);
                  },
                  style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white),
                  //color: Colors.blue,
                  child: Text(
                    actionButtonText,
                  )),
            ],
          )
        ],
      ),
    );
  }
}
