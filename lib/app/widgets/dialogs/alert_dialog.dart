//import 'package:flutter/material.dart';

// showAlertDialog(BuildContext context, String continueTextBtn, String? title,
//     String? text, Function continueCallBack, Function cancelCallback) {
//   // set up the buttons
//   Widget cancelButton = TextButton(
//     child: const Text("Отмена"),
//     onPressed: () {
//       Navigator.of(context).pop();
//       cancelCallback();
//     },
//   );
//   Widget continueButton = TextButton(
//     child: Text(continueTextBtn),
//     onPressed: () {
//       Navigator.of(context).pop();
//       continueCallBack();
//     },
//   );

//   // set up the AlertDialog
//   AlertDialog alert = AlertDialog(
//     title: Text(title ?? ""),
//     content: Text(text ?? ""),
//     actions: [
//       cancelButton,
//       continueButton,
//     ],
//   );

//   // show the dialog
//   showDialog(
//     context: context,
//     builder: (BuildContext context) {
//       return alert;
//     },
//   );
// }
