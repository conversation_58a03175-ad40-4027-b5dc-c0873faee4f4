import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:numberpicker/numberpicker.dart';
import '../common_button.dart';

class TimeAlertDialog extends StatefulWidget {
  final String title;
  final bool subscribed;
  final int? currentValue;

  const TimeAlertDialog({
    super.key,
    required this.title,
    required this.subscribed,
    this.currentValue,
  });

  @override
  State<TimeAlertDialog> createState() => _TimeAlertDialogState();
}

class _TimeAlertDialogState extends State<TimeAlertDialog> {
  int _currentValue = 3;

  @override
  void initState() {
    super.initState();

    if (widget.currentValue != null) {
      _currentValue = widget.currentValue!;
    }
  }

  void plusValue() {
    setState(() {
      if (_currentValue < 99) {
        _currentValue += 1;
      } else if (_currentValue == 99) {
        _currentValue = 1;
      }
    });
  }

  void minusValue() {
    setState(() {
      if (_currentValue > 1) {
        _currentValue -= 1;
      } else if (_currentValue == 1) {
        _currentValue = 99;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    var saveBtnText = widget.subscribed == true ? "Сохранить" : "Подписаться";

    return AlertDialog(
      // title: Text(
      //   widget.title,
      // ),
      // titleTextStyle:
      //     const TextStyle(fontSize: 15, fontWeight: FontWeight.w500),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.0)),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Text(widget.title,
              style:
                  const TextStyle(fontSize: 15, fontWeight: FontWeight.w500)),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              CommonButton(name: "-", callback: minusValue),
              NumberPicker(
                value: _currentValue,
                minValue: 1,
                maxValue: 99,
                //axis: Axis.horizontal,
                haptics: true,
                infiniteLoop: true,
                onChanged: (value) => setState(() => _currentValue = value),
              ),
              CommonButton(name: "+", callback: plusValue),
            ],
          ),
          const SizedBox(
            height: 16,
          ),
          Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop(_currentValue); // return time
                  },
                  style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white),
                  //color: Colors.blue,
                  child: Text(
                    saveBtnText,
                  )),
              const SizedBox(
                height: 8,
              ),
              widget.subscribed == true
                  ? ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).pop(0);
                      },
                      style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,
                          foregroundColor: Colors.white),
                      child: const Text(
                        "Отписаться",
                      ),
                    )
                  : const SizedBox(),
              const SizedBox(
                height: 8,
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                style: ElevatedButton.styleFrom(backgroundColor: Colors.grey),
                child: const Text(
                  "Отмена",
                ),
              ),
            ],
          )
        ],
      ),
    );
  }
}
