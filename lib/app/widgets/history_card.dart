import 'package:flutter/material.dart';
import 'package:uer_flutter/app/helpers/date_extension.dart';

import '../core/models/component_history/component_history_model.dart';
import '../core/models/enums/component_history_type_enum.dart';
import '../core/models/enums/history_status_enum.dart';
import 'status_badge_text.dart';

typedef HistoryCardCallback = Function();

class HistoryCard extends StatelessWidget {
  const HistoryCard({super.key, required this.history, required this.callback});

  final ComponentHistoryModel history;
  final HistoryCardCallback callback;

  @override
  Widget build(BuildContext context) {
    var icon = history.type.icon;
    var lastStatus = history.statuses.last;
    var elementsCountStr = "${history.statuses.length} элем. истории";
    var statusBadge = lastStatus.status.statusBadge;
    var finishStatusText = lastStatus.status.desc;

    var areaName = history.getAreaName(null);
    var statusName = history.getStatusName(null);
    var owner = lastStatus.owner;
    var comment = lastStatus.comment;

    var dateAgo = DateTime.fromMillisecondsSinceEpoch(
            (history.createdAt ?? 0).toInt() * 1000)
        .timeAgo();
    var calendarTime = history.calendarTimeForAllStatuses();

    var historyType = history.type;
    var historyInfoOff = historyType != ComponentHistoryType.info;

    double width = MediaQuery.of(context).size.width;

    return Card(
      //color: Colors.wh, //: Colors.white,
      elevation: 2.0,
      margin: const EdgeInsets.all(8),
      shape: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: const BorderSide(color: Colors.white)),
      child: InkWell(
        borderRadius: BorderRadius.circular(10.0),
        onTap: callback,
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: SizedBox(
            //height: 100,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    icon,
                    const SizedBox(
                      width: 8,
                    ),
                    SizedBox(
                      width: width - 100,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            statusName,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            //softWrap: false,
                          ),
                          Text(
                            areaName,
                            maxLines: 1,
                          )
                        ],
                      ),
                    )
                  ],
                ),
                comment?.isNotEmpty == true
                    ? Column(
                        children: [
                          const SizedBox(
                            height: 8,
                          ),
                          Text(
                            comment ?? "",
                            style: const TextStyle(
                                fontStyle: FontStyle.italic,
                                fontWeight: FontWeight.w500),
                          )
                        ],
                      )
                    : const SizedBox(),
                historyInfoOff
                    ? StatusBadgeText(icon: statusBadge, text: finishStatusText)
                    : const SizedBox(),
                historyInfoOff
                    ? Row(
                        children: [
                          const Icon(Icons.access_time, size: 18),
                          const SizedBox(
                            width: 8,
                          ),
                          Text(calendarTime)
                        ],
                      )
                    : const SizedBox(),
                historyInfoOff
                    ? Row(
                        children: [
                          const Icon(Icons.list, size: 18),
                          const SizedBox(
                            width: 8,
                          ),
                          Text(elementsCountStr)
                        ],
                      )
                    : const SizedBox(),
                const SizedBox(
                  height: 8,
                ),
                Row(
                  children: [
                    Text(
                      dateAgo,
                      style: const TextStyle(
                        color: Colors.black45,
                      ),
                    ),
                    const Spacer(),
                    Text(
                      owner,
                      style: const TextStyle(
                        color: Colors.black45,
                      ),
                    )
                  ],
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
