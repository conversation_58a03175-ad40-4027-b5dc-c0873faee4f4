import 'package:flutter/material.dart';
import 'package:uer_flutter/app/styles/colors.dart';
import 'package:uer_flutter/app/styles/fonts.dart';
import 'package:uer_flutter/app/widgets/svg_icon/index.dart';

class MainCard extends StatelessWidget {
  const MainCard({
    super.key,
    required this.title,
    required this.onTap,
    this.description,
    this.icon,
    this.backgroundColor,
    this.textColor,
  });

  final String title;
  final String? description;
  final String? icon;
  final void Function() onTap;
  final Color? backgroundColor;
  final Color? textColor;

  @override
  Widget build(BuildContext context) {
    return Card(
      color: backgroundColor,
      child: InkWell(
        onTap: onTap,
        child: Ink(
          padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 18.0),
          child: Column(mainAxisAlignment: MainAxisAlignment.center, children: [
            Row(mainAxisAlignment: MainAxisAlignment.center, children: [
              if (icon != null)
                SVGIcon(
                  icon!,
                  color: textColor ?? AppLightColors.black,
                ),
              if (icon != null) const SizedBox(width: 6.0),
              Text(
                title,
                style: AppFonts.titleSmall.merge(TextStyle(
                  color: textColor,
                  fontVariations: const [FontVariation.weight(600)],
                )),
                textAlign: TextAlign.center,
              ),
            ]),
            const SizedBox(height: 6.0),
            if (description != null)
              Text(
                description!,
                style: AppFonts.titleSmall.merge(TextStyle(
                  color: textColor,
                  fontFamily: AppFonts.monoFontFamily,
                  fontVariations: const [FontVariation.weight(400)],
                )),
                textAlign: TextAlign.center,
              ),
          ]),
        ),
      ),
    );
  }
}
