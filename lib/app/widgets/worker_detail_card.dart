import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uer_flutter/app/helpers/date_extension.dart';
import '../core/models/enums/history_status_enum.dart';
import 'status_badge_text.dart';

import '../core/models/worker/worker_model.dart';

class WorkerDetailCard extends ConsumerWidget {
  const WorkerDetailCard({super.key, required this.worker});

  final WorkerModel worker;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var name = worker.name;

    WorkerStatusModel? status;
    Icon statusBadge = const Icon(
      Icons.circle,
      color: Colors.grey,
      size: 14,
    );
    String statusText = "Не приступал к работе";
    int? createdAtMiliseconds;
    String? dateAgo;

    if (worker.statuses?.isNotEmpty == true) {
      status = worker.statuses?.last;
      statusBadge = status?.status.statusBadge ??
          const Icon(
            Icons.circle,
            color: Colors.grey,
            size: 14,
          );
      statusText = status?.status.desc ?? "Не приступал к работе";
      createdAtMiliseconds = (status?.createdAt?.toInt() ?? 0) * 1000;
      dateAgo =
          DateTime.fromMillisecondsSinceEpoch(createdAtMiliseconds).timeAgo();
    }

    return Card(
      //color: Colors.wh, //: Colors.white,
      elevation: 2.0,
      margin: const EdgeInsets.all(8),
      shape: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: const BorderSide(color: Colors.white)),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: SizedBox(
          //height: 100,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                name,
                style: const TextStyle(
                    fontStyle: FontStyle.italic, fontWeight: FontWeight.w500),
              ),
              const SizedBox(
                height: 8,
              ),
              StatusBadgeText(icon: statusBadge, text: statusText),
              const SizedBox(
                height: 8,
              ),
              dateAgo != null
                  ? Text(
                      dateAgo,
                      style: const TextStyle(
                        color: Colors.black45,
                      ),
                    )
                  : const SizedBox(),
            ],
          ),
        ),
      ),
    );
  }
}
