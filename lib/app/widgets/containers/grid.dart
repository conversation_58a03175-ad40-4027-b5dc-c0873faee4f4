import 'package:flutter/material.dart';

class Grid extends StatelessWidget {
  const Grid({
    super.key,
    required this.length,
    required this.builder,
    required this.columns,
    this.gap = 10.0,
  });

  final int length;
  final Widget Function(int, int) builder;
  final int columns;
  final double gap;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        for (int rowIndex = 0; rowIndex < length; rowIndex += columns)
          Padding(
            padding: EdgeInsets.only(bottom: gap),
            child: IntrinsicHeight(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: List.generate(
                  columns,
                  (itemIndex) {
                    if (rowIndex + itemIndex < length) {
                      return Expanded(
                        child: Padding(
                          padding: itemIndex < columns - 1
                              ? EdgeInsets.only(right: gap)
                              : EdgeInsets.zero,
                          child: builder(rowIndex, itemIndex),
                        ),
                      );
                    } else {
                      // Если элементов меньше, чем колонок, создаем пустое пространство
                      return const Expanded(child: SizedBox());
                    }
                  },
                ),
              ),
            ),
          ),
      ],
    );
  }
}
