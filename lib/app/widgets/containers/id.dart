import 'package:flutter/material.dart';
import 'package:uer_flutter/app/styles/colors.dart';
import 'package:uer_flutter/app/styles/fonts.dart';

class IdLabel extends StatelessWidget {
  const IdLabel(this.label, {super.key});

  final String label;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 4.0, vertical: 2.0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4.0),
        color: AppLightColors.darkGray,
      ),
      child: Text(
        "№ $label",
        style: AppFonts.titleSmall.merge(
          const TextStyle(
            color: AppLightColors.white,
            fontFamily: AppFonts.monoFontFamily,
          ),
        ),
      ),
    );
  }
}
