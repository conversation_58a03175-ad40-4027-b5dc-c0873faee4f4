import 'package:flutter/material.dart';
import 'package:uer_flutter/app/styles/colors.dart';
import 'package:uer_flutter/app/styles/fonts.dart';

class StatRow extends StatelessWidget {
  const StatRow(
    this.label,
    this.value, {
    super.key,
  });

  final String label;
  final String value;

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.baseline,
      textBaseline: TextBaseline.alphabetic,
      children: [
        Baseline(
          baseline: 16.0,
          baselineType: TextBaseline.alphabetic,
          child: Text(
            label,
            style: AppFonts.bodyLarge.merge(
              const TextStyle(
                color: AppLightColors.medium,
                fontFamily: AppFonts.monoFontFamily,
                fontVariations: [FontVariation.weight(450)],
              ),
            ),
          ),
        ),
        const SizedBox(width: 6.0),
        const Expanded(
          child: Baseline(
            baseline: 16.0,
            baselineType: TextBaseline.alphabetic,
            child: Divider(
              height: 1.0,
              thickness: 1.0,
              indent: 6.0,
              endIndent: 6.0,
            ),
          ),
        ),
        const SizedBox(width: 6.0),
        Row(
          children: [
            Baseline(
              baseline: 16.0,
              baselineType: TextBaseline.alphabetic,
              child: Text(
                value,
                softWrap: true,
                maxLines: 3,
                textAlign: TextAlign.right,
                style: AppFonts.bodyLarge.merge(
                  const TextStyle(
                    fontFamily: AppFonts.monoFontFamily,
                    fontVariations: [FontVariation.weight(450)],
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
