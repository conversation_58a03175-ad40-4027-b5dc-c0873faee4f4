import 'package:flutter/material.dart';
import 'package:uer_flutter/app/widgets/containers/stat_row.dart';

class StatsColumn extends StatelessWidget {
  const StatsColumn({super.key, required this.children, this.gap = 4.0});

  final List<StatRow> children;
  final double gap;

  @override
  Widget build(BuildContext context) {
    return Column(
        children: children.asMap().entries.map((entry) {
      final index = entry.key;
      final row = entry.value;

      if (index != children.length - 1) {
        return Column(children: [
          StatRow(row.label, row.value),
          SizedBox(height: gap),
        ]);
      } else {
        return StatRow(row.label, row.value);
      }
    }).toList());
  }
}
