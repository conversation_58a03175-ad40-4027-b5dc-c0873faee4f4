import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uer_flutter/app/styles/colors.dart';
import 'package:uer_flutter/app/styles/fonts.dart';
import 'package:uer_flutter/app/widgets/containers/id.dart';
import 'package:uer_flutter/app/widgets/interactive/chip.dart';
import 'package:uer_flutter/app/widgets/svg_icon/index.dart';
import 'package:uer_flutter/gen/assets.gen.dart';

import '../../core/models/item/item_model.dart';

typedef ItemCardCallback = Function();

class ItemCard extends ConsumerWidget {
  const ItemCard({super.key, required this.item, required this.callback});

  final ItemModel item;
  final ItemCardCallback callback;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    print(item);

    final currentDate = DateTime.now();
    // var branch = ref.watch(branchForIDProvider(item.branch));

    final repairNumber = item.repairNumber;
    final status = item.order?.statusOrder ?? "---";
    // final branchShortName = branch?.shortName ?? "--";
    final typeName = item.order?.equipment ?? "--";
    final clientName = item.order?.client?.name ?? "--";
    final componentsStr = item.getComponentsDesc();
    final endDateStr = item.getEndContractDate();
    final durationToEndContract =
        item.getEndContactDateTime().difference(currentDate);
    final mark = item.mark ?? false;
    final newBool = item.newBool ?? false;

    // int dateOverDeadline;

    // if (item.finished == true) {
    //   dateOverDeadline =
    //       DateTime.now().getDaysOverDeadline(item.endDate, item.finishDate);
    // } else {
    //   dateOverDeadline = DateTime.now().getDaysOverDeadline(item.endDate, null);
    // }

    Color getEndContractColor() {
      if (durationToEndContract.inDays >= 14) {
        return AppLightColors.lightGray;
      } else if (durationToEndContract.inDays > 0) {
        return AppLightColors.warning;
      } else {
        return AppLightColors.error;
      }
    }

    return Card(
      color: newBool == true
          ? Colors.greenAccent.withValues(alpha: 0.2)
          : Colors.white,
      margin: const EdgeInsets.only(bottom: 16.0),
      child: InkWell(
        borderRadius: BorderRadius.circular(10.0),
        onTap: callback,
        child: Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: 12.0,
            vertical: 16.0,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Wrap(
                spacing: 6.0,
                runSpacing: 6.0,
                crossAxisAlignment: WrapCrossAlignment.center,
                children: [
                  IdLabel(repairNumber ?? ''),
                  Text(
                    status,
                    style: AppFonts.labelMedium.merge(
                      TextStyle(
                        color: mark ? Colors.red : AppLightColors.medium,
                      ),
                    ),
                  ),
                  ...(item.tags ?? []).map((tag) => CustomChip(
                        isEnabled: true,
                        label: tag.getName(),
                        color: tag.getColor(),
                      )),
                  Row(
                    children: [
                      SVGIcon(
                        Assets.icons.assignmentLate,
                        color: getEndContractColor(),
                      ),
                      const SizedBox(width: 6.0),
                      Text(
                        '${durationToEndContract.inDays.toString()} дн.',
                        style: AppFonts.bodyMedium.merge(TextStyle(
                          fontFamily: AppFonts.monoFontFamily,
                          fontVariations: const [FontVariation.weight(600)],
                          color: getEndContractColor(),
                        )),
                      )
                    ],
                  ),
                  // Text(
                  //   branchShortName,
                  //   style: const TextStyle(fontWeight: FontWeight.w500),
                  // )
                ],
              ),
              const SizedBox(height: 12.0),
              Wrap(
                  spacing: 8.0,
                  runSpacing: 8.0,
                  crossAxisAlignment: WrapCrossAlignment.center,
                  children: [
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          "Контрагент: ",
                          style: AppFonts.labelMedium.merge(
                            const TextStyle(color: AppLightColors.medium),
                          ),
                        ),
                        SizedBox(width: 4.0),
                        Flexible(
                          child: Text(
                            clientName,
                            style: AppFonts.labelMedium.merge(
                              const TextStyle(
                                fontFamily: AppFonts.monoFontFamily,
                                fontVariations: [FontVariation.weight(500)],
                              ),
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.right,
                          ),
                        ),
                      ],
                    ),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          "Оборудование: ",
                          style: AppFonts.labelMedium.merge(
                            const TextStyle(color: AppLightColors.medium),
                          ),
                        ),
                        SizedBox(width: 4.0),
                        Flexible(
                          child: Text(
                            typeName,
                            style: AppFonts.labelMedium.merge(
                              const TextStyle(
                                fontFamily: AppFonts.monoFontFamily,
                                fontVariations: [FontVariation.weight(500)],
                              ),
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.right,
                          ),
                        ),
                      ],
                    ),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          "Комплектация: ",
                          style: AppFonts.labelMedium.merge(
                            const TextStyle(color: AppLightColors.medium),
                          ),
                        ),
                        SizedBox(width: 4.0),
                        Flexible(
                          child: Text(
                            componentsStr,
                            style: AppFonts.labelMedium.merge(
                              const TextStyle(
                                fontFamily: AppFonts.monoFontFamily,
                                fontVariations: [FontVariation.weight(500)],
                              ),
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.right,
                          ),
                        ),
                      ],
                    ),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          "Дата завершения: ",
                          style: AppFonts.labelMedium.merge(
                            const TextStyle(color: AppLightColors.medium),
                          ),
                        ),
                        SizedBox(width: 4.0),
                        Flexible(
                          child: Text(
                            endDateStr,
                            style: AppFonts.labelMedium.merge(
                              const TextStyle(
                                fontFamily: AppFonts.monoFontFamily,
                                fontVariations: [FontVariation.weight(500)],
                              ),
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.right,
                          ),
                        ),
                      ],
                    ),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.photo_library_outlined,
                            color: AppLightColors.medium),
                        SizedBox(width: 4.0),
                        Flexible(
                          child: Text(
                            '${item.photos?.length ?? 0}',
                            style: AppFonts.labelMedium.merge(
                              const TextStyle(
                                fontFamily: AppFonts.monoFontFamily,
                                fontVariations: [FontVariation.weight(500)],
                              ),
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.right,
                          ),
                        ),
                      ],
                    ),
                  ]),
            ],
          ),
        ),
      ),
    );
  }
}
