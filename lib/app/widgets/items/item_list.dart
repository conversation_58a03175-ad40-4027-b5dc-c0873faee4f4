import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:uer_flutter/app/widgets/templates/not_found_view.dart';

import '../../core/models/item/item_model.dart';
import '../../core/models/search/search_model.dart';
import '../../core/services/api/service_provider.dart';
import 'item_card.dart';

typedef ItemCardCallback = Function(ItemModel);

class ItemList extends ConsumerStatefulWidget {
  const ItemList({
    super.key,
    required this.searchModel,
    required this.callback,
  });

  final SearchModel searchModel;
  final ItemCardCallback callback;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _ItemListState();
}

class _ItemListState extends ConsumerState<ItemList> {
  static const _pageSize = 20;

  final PagingController<int, ItemModel> pagingController =
      PagingController(firstPageKey: 0);

  @override
  void initState() {
    pagingController.addPageRequestListener((pageKey) {
      _fetchPage(pageKey);
    });

    pagingController.addStatusListener((status) {
      if (status == PagingStatus.subsequentPageError) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text(
              'Что-то пошло не так при загрузке новых данных',
            ),
            action: SnackBarAction(
              label: 'Повторить загрузку',
              onPressed: () => pagingController.retryLastFailedRequest(),
            ),
          ),
        );
      }
    });

    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<List<ItemModel>?> _fetchItems(SearchModel searchModel) async {
    var apiClient = ref.read(itemRepositoryProvider);
    var items = await apiClient.getItems(searchModel);
    return items;
  }

  Future<void> _fetchPage(pageKey) async {
    var search = widget.searchModel.copyWith(
      limit: _pageSize,
      createdAtSort: true,
      offset: pageKey,
      projection: '${widget.searchModel.projection ?? ''} photos',
    );

    try {
      final newItems = await _fetchItems(search);

      if (newItems == null) {
        return;
      }

      final isLastPage = newItems.length < _pageSize;
      if (isLastPage) {
        pagingController.appendLastPage(newItems);
      } else {
        final nextPageKey = pageKey + newItems.length;
        pagingController.appendPage(newItems, nextPageKey);
      }
    } catch (error) {
      // AppMetrica.reportError(
      //   message: 'Ошибка получения данных страницы (items)',
      //   errorDescription: AppMetricaErrorDescription(
      //     StackTrace.current,
      //     message: error.toString(),
      //   ),
      // );
      pagingController.error = error;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.searchModel.showSearchResult()) {
      pagingController.refresh();
    }

    return RefreshIndicator(
      onRefresh: () => Future.sync(
        () => pagingController.refresh(),
      ),
      child: CustomScrollView(
        slivers: <Widget>[
          PagedSliverList<int, ItemModel>(
            pagingController: pagingController,
            builderDelegate: PagedChildBuilderDelegate<ItemModel>(
              itemBuilder: (context, item, index) {
                mycallback() {
                  widget.callback(item);
                }

                return ItemCard(item: item, callback: mycallback);
              },
              noItemsFoundIndicatorBuilder: (context) => const NotFoundView(),
              // firstPageErrorIndicatorBuilder: (_) => const SizedBox(),
            ),
          ),
        ],
      ),
    );
  }
}
