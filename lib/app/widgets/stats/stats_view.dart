import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../core/models/enums/common_items_screens_enum.dart';
import '../../core/models/stats/stats_model.dart';
import '../../helpers/platform_check.dart';
import '../../routes/routes_name.dart';
import '../stats_card.dart';

class StatsView extends ConsumerStatefulWidget {
  const StatsView({super.key, required this.stats, this.branchID});

  final StatsModel stats;
  final int? branchID;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _StatsViewState();
}

class _StatsViewState extends ConsumerState<StatsView> {
  void openToAllItemsPage() {
    context.pushNamed(
      RoutesName.commonItems.named,
      extra: CommonItemsScreens.inWork.getData(
        branchID: widget.branchID,
      ),
    );
  }

  void openToOverDeadLineItemsPage() {
    context.pushNamed(
      RoutesName.overdeadlineItems.named,
      extra: widget.branchID,
    );
  }

  void openConcernBranchStats() {
    context.pushNamed(
      RoutesName.concernBranchStats.named,
      extra: widget.branchID,
    );
  }

  void openArchive() {
    context.pushNamed(
      RoutesName.commonItems.named,
      extra: CommonItemsScreens.archive.getData(
        branchID: widget.branchID,
      ),
    );
  }

  void openArchiveInYear() {
    context.pushNamed(
      RoutesName.commonItems.named,
      extra: CommonItemsScreens.archiveInYear.getData(
        branchID: widget.branchID,
      ),
    );
  }

  void openRecentDeadLine() {
    context.pushNamed(
      RoutesName.commonItems.named,
      extra: CommonItemsScreens.recentShipmentItems.getData(
        branchID: widget.branchID,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    var cards = [
      SizedBox(
          height: 140,
          child: StatsCard(
              values: widget.stats.getStatsInWork(),
              callback: openToAllItemsPage)),
      SizedBox(
        height: 140,
        child: StatsCard(
            values: widget.stats.getStatsArchive(), callback: openArchive),
      ),
      SizedBox(
        height: 140,
        child: StatsCard(
            values: widget.stats.getStatsArchiveYear(),
            callback: openArchiveInYear),
      ),
      SizedBox(
        height: 140,
        child: StatsCard(
            values: widget.stats.getStatsRecentDeadLine(),
            callback: openRecentDeadLine),
      ),
      SizedBox(
        height: 140,
        child: StatsCard(
            values: widget.stats.getStatsOverDeadLine(),
            callback: openToOverDeadLineItemsPage),
      ),
      SizedBox(
        height: 140,
        child: StatsCard(
            values: widget.stats.getStatsAllHoursAndOutAge(),
            callback: openConcernBranchStats),
      ),
    ];

    return isAndroidOriOS() == true
        ? SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                for (var card in cards)
                  Padding(
                    padding: const EdgeInsets.all(8),
                    child: card,
                  )
              ],
            ),
          )
        : Padding(
            padding: const EdgeInsets.all(8.0),
            child: Wrap(
              spacing: 8,
              runSpacing: 8,
              children: cards,
            ),
          );
  }
}
