import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

typedef BranchAreaCardCallback = Function();

class BranchAreaCard extends ConsumerWidget {
  const BranchAreaCard({
    super.key,
    required this.topName,
    required this.bottomName,
    required this.number,
    required this.callback,
  });

  final String topName;
  final String bottomName;
  final int number;
  final BranchAreaCardCallback callback;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Card(
      color: Colors.orange,
      elevation: 2.0,
      shape: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: const BorderSide(color: Colors.white)),
      child: InkWell(
        borderRadius: BorderRadius.circular(10.0),
        onTap: callback,
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: SizedBox(
            //height: 60,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Align(
                  alignment: Alignment.topLeft,
                  child: FittedBox(
                    child: Text(
                      topName,
                      style: const TextStyle(
                          fontWeight: FontWeight.w500,
                          color: Colors.white,
                          fontSize: 17),
                      maxLines: 1,
                    ),
                  ),
                ),
                Text("$number",
                    style: const TextStyle(
                        fontWeight: FontWeight.w700,
                        color: Colors.white,
                        fontSize: 24)),
                FittedBox(
                  child: Text(bottomName,
                      style: const TextStyle(
                          fontWeight: FontWeight.w400, color: Colors.white)),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
