import 'package:flutter/material.dart';
import 'package:uer_flutter/app/styles/colors.dart';
import 'package:uer_flutter/app/styles/fonts.dart';

class ItemHeaderText extends StatelessWidget {
  const ItemHeaderText({super.key, required this.name});

  final String name;

  @override
  Widget build(BuildContext context) {
    return Text(name,
        style: AppFonts.titleSmall.merge(const TextStyle(
          color: AppLightColors.medium,
        )));
  }
}
