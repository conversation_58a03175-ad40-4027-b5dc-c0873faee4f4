import 'package:flutter/material.dart';
import 'package:tuple/tuple.dart';

import '../core/models/component/component_model.dart';
import '../core/models/component_history/component_history_model.dart';
import '../core/models/enums/component_type_enum.dart';
import 'comment_card.dart';

typedef CommentShortInfoCardCallback = Function();

class CommentShortInfoCard extends StatelessWidget {
  const CommentShortInfoCard(
      {super.key, required this.components, required this.callback});

  final List<ComponentModel> components;
  //final List<ComponentHistoryModel> histories;
  final CommentShortInfoCardCallback callback;

  final int countShowComments = 2; // количество отображаемых комментов

  @override
  Widget build(BuildContext context) {
    List<Tuple3> comments = [];

    for (var i = 0; i < components.length; i++) {
      var component = components[i];
      var histories = component.newHistory;

      if (histories?.isEmpty == true) {
        continue;
      }

      for (int i = 0; i < component.newHistory!.length; i++) {
        var history = component.newHistory![i];
        var statuses = history.statuses
            .where((status) => ((status.comment?.length) ?? 0) > 1)
            .toList();
        var area = history.getAreaName(null);

        for (int j = 0; j < statuses.length; j++) {
          var status = statuses[j];
          var tuple = Tuple3(status, area, component.type.desc);
          comments.add(tuple);
        }
      }
    }

    comments.sort((a, b) {
      var statusA = a.item1 as ComponentStatusModel;
      var statusB = b.item1 as ComponentStatusModel;

      return (statusB.createdAt ?? 0).compareTo((statusA.createdAt ?? 0));
    });

    comments = comments.take(countShowComments).toList();

    return Card(
      //color: Colors.wh, //: Colors.white,
      elevation: 2.0,
      margin: const EdgeInsets.all(4),
      shape: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: const BorderSide(color: Colors.white)),
      child: InkWell(
        borderRadius: BorderRadius.circular(10.0),
        onTap: callback,
        child: Padding(
            padding: const EdgeInsets.all(4.0),
            child: comments.isNotEmpty
                ? Column(
                    children: [
                      for (var comment in comments)
                        CommentCard(
                            lastStatus: comment.item1,
                            componentName: comment.item3,
                            areaName: comment.item2)
                    ],
                  )
                : const Text("Нет комментариев для этого двигателя")),
      ),
    );
  }
}
