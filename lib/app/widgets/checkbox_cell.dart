import 'package:flutter/material.dart';

typedef CheckBoxCellCallback = Function(bool?);

class CheckBoxCell extends StatelessWidget {
  const CheckBoxCell(
      {super.key,
      required this.title,
      required this.checkboxValue,
      required this.callback});

  final String title;
  final bool checkboxValue;
  final CheckBoxCellCallback callback;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(8, 2, 8, 2),
      child: Column(
        children: [
          Row(
            children: [
              Text(title),
              const Spacer(),
              Checkbox(value: checkboxValue, onChanged: callback)
            ],
          ),
          const Divider()
        ],
      ),
    );
  }
}
