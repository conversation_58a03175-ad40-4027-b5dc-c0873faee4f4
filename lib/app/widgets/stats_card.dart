import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

typedef StatsCardCallback = Function();

class StatsCard extends ConsumerWidget {
  const StatsCard({super.key, required this.values, required this.callback});

  final Map<String, String> values;
  final StatsCardCallback callback;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final list = values.entries.toList();

    return Card(
      //color: Colors.white,

      elevation: 2.0,
      margin: const EdgeInsets.all(0),
      shape: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: const BorderSide(color: Colors.white)),
      child: InkWell(
        borderRadius: BorderRadius.circular(10.0),
        onTap: callback,
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: SizedBox(
            width: 120,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                for (var value in list)
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      FittedBox(
                        child: Text(
                          value.key,
                          textAlign: TextAlign.center,
                        ),
                      ),
                      const SizedBox(
                        height: 4,
                      ),
                      FittedBox(
                        child: Text(
                          value.value,
                          style: const TextStyle(
                            fontSize: 21,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      value == list.last
                          ? const SizedBox()
                          : const SizedBox(
                              height: 6,
                            )
                    ],
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
