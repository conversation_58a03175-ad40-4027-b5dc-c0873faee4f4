import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../core/models/component_history/component_history_model.dart';
import 'history_detail_card.dart';
import 'worker_detail_card.dart';

class HistoryDetailView extends ConsumerWidget {
  const HistoryDetailView({super.key, required this.history});

  final ComponentHistoryModel history;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var statuses = history.statuses.reversed.toList();
    var name = history.getStatusName(null);
    var workers = history.workers;

    var statusList = [];

    for (var status in statuses) {
      statusList.add(HistoryDetailCard(status: status));
    }

    var workerList = [];

    if (workers?.isNotEmpty == true) {
      for (var worker in workers!) {
        workerList.add(WorkerDetailCard(worker: worker));
      }
    }

    return Column(
      children: [
        Flexible(
            child: ListView(
          children: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(name,
                  style: const TextStyle(fontWeight: FontWeight.w600)),
            ),
            ...statusList,
            workers?.isNotEmpty == true
                ? const Padding(
                    padding: EdgeInsets.all(8.0),
                    child: Text("Рабочие",
                        style: TextStyle(fontWeight: FontWeight.w600)),
                  )
                : const SizedBox(),
            ...workerList,
          ],
        )),
      ],
    );
  }
}
