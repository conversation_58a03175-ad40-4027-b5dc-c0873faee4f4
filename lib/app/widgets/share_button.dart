import 'dart:typed_data';

import 'package:archive/archive.dart';
import 'package:archive/archive_io.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:share_plus/share_plus.dart';
import 'package:uer_flutter/app/core/models/photo/photo_model.dart';
import 'package:uer_flutter/app/helpers/snack_bar.dart';

import '../helpers/empty_download.dart'
    if (dart.library.html) '../helpers/download.dart';
import '../helpers/platform_check.dart';

class ShareBtn extends StatefulWidget {
  final List<PhotoModel> photos;

  const ShareBtn({super.key, required this.photos});

  @override
  State<ShareBtn> createState() => _ShareBtnState();
}

class _ShareBtnState extends State<ShareBtn> {
  var isLoading = false;

  Future<XFile> _downloadImage(String url, String name) async {
    final response = await Dio()
        .get(url, options: Options(responseType: ResponseType.bytes));
    final imgData = response.data as Uint8List;
    XFile imageFile =
        XFile.fromData(imgData, name: name, mimeType: "image/jpeg");
    return imageFile;
  }

  Future<Uint8List> _createZipArchive(List<PhotoModel> photos) async {
    final archive = Archive();

    for (var photo in photos) {
      final fileName = photo.getName();
      final file = await _downloadImage(photo.getImgUrl(), fileName);

      final fileBytes = await file.readAsBytes();
      archive.addFile(
        ArchiveFile('$fileName.jpg', fileBytes.length, fileBytes),
      );
    }

    final zippedData = ZipEncoder().encode(archive);
    return Uint8List.fromList(zippedData!);
  }

  Future<void> _shareImage(BuildContext context) async {
    if (isLoading == true) {
      return;
    }

    setState(() {
      isLoading = true;
    });

    final box = context.findRenderObject() as RenderBox?;

    if (widget.photos.isEmpty == true) {
      showInSnackBar("Изображение не выбрано", context);
      setState(() {
        isLoading = false;
      });
      return;
    }

    if (widget.photos.length == 1) {
      final fileName = widget.photos[0].getName();
      final file = await _downloadImage(widget.photos[0].getImgUrl(), fileName);
      final fileBytes = await file.readAsBytes();
      download(fileBytes, downloadName: '$fileName.jpg');
    } else {
      final zipBytes = await _createZipArchive(widget.photos);

      if (isWeb() == true) {
        download(zipBytes, downloadName: "photos.zip");
      } else {
        final zipFile = XFile.fromData(
          zipBytes,
          name: "photos.zip",
          mimeType: "application/zip",
        );
        await Share.shareXFiles(
          [zipFile],
          sharePositionOrigin: box!.localToGlobal(Offset.zero) & box.size,
        );
      }
    }

    setState(() {
      isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return IconButton(
      onPressed: () async {
        if (isLoading) return;
        _shareImage(context);
        // await FirebaseAnalytics.instance
        //     .logEvent(name: 'sharing_images', parameters: {
        //   "photos_length": widget.photos.length,
        // });

        // await AppMetrica.reportEventWithMap('Скачивание фотографий', {
        //   'Количество фотографий': widget.photos.length,
        //   'Фотографии': widget.photos,
        // });
      },
      icon: isLoading == true
          ? const CircularProgressIndicator()
          : (isWeb() ? const Icon(Icons.download) : const Icon(Icons.share)),
    );
  }
}
