import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:uer_flutter/app/styles/fonts.dart';

class CustomPieChart extends StatelessWidget {
  const CustomPieChart({super.key, required this.data});

  final CustomPieChartProps data;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 250.0,
      height: 250.0,
      child: Pie<PERSON><PERSON>(PieChartData(
        centerSpaceRadius: 50,
        sectionsSpace: 0.0,
        sections: [
          ...data.items.map((item) {
            return PieChartSectionData(
              value: item.value,
              color: item.color,
              radius: 75,
              titleStyle: AppFonts.labelSmall,
            );
          })
        ],
      )),
    );
  }
}

class CustomPieChartProp {
  final double value;
  final Color color;

  const CustomPieChartProp({
    required this.value,
    required this.color,
  });
}

class CustomPieChartProps {
  final List<CustomPieChartProp> items;

  const CustomPieChartProps({
    required this.items,
  });
}
