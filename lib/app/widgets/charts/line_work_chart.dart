// ignore_for_file: unused_local_variable

import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:uer_flutter/app/core/services/utils.dart';

import '../../core/models/stats/day_stats/day_stats_model.dart';

class LineWork<PERSON>hart extends StatefulWidget {
  const LineWorkChart({super.key, required this.dayStats});

  final List<DayStatsModel> dayStats;

  @override
  State<LineWorkChart> createState() => LineWorkChartState();
}

class LineWorkChartState extends State<LineWorkChart> {
  List<Color> gradientColors = [
    Colors.blue,
    Colors.blueAccent,
  ];

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: <Widget>[
        AspectRatio(
          aspectRatio: 1.70,
          child: Padding(
            padding: const EdgeInsets.only(
              right: 18,
              left: 12,
              top: 24,
              bottom: 12,
            ),
            child: Line<PERSON>hart(
              mainData(widget.dayStats),
            ),
          ),
        ),
      ],
    );
  }

  Widget bottomTitleWidgets(
      double value, TitleMeta meta, List<String> last30Days) {
    const style = TextStyle(
      fontWeight: FontWeight.bold,
      fontSize: 16,
    );

    var valueInt = value.toInt();

    var textStr = "";

    if (last30Days.length > valueInt) {
      var str = last30Days[valueInt];

      textStr = str;
    }

    // if (valueInt % 2 == 0) {
    //   textStr = "";
    // }

    Widget text = Text(textStr, style: style);

    return SideTitleWidget(
      meta: meta,
      child: text,
    );
  }

  // Widget leftTitleWidgets(
  //     double value, TitleMeta meta, List<FlSpot> spots) {
  //   const style = TextStyle(
  //     fontWeight: FontWeight.bold,
  //     fontSize: 15,
  //   );
  //   var valueInt = value.toInt();

  //   var textStr = "0";

  //   if (dayStats.length > valueInt) {
  //     var workHours = 0.0;

  //     for (var i = 0; i < dayStats.length; i++) {
  //       workHours += (dayStats[i].workerHours ?? 0);
  //     }

  //     textStr = "${workHours.toInt()} н/ч";
  //   }

  //   return Text(textStr, style: style, textAlign: TextAlign.left);
  // }

  List<FlSpot> configurateSpots(
      List<DayStatsModel> dayStats, List<String> last30Days) {
    List<FlSpot> spots = [];

    var lastDay = "";

    var dayStatsSort = [...dayStats];

    var format = DateFormat('dd.MM.yyyy');

    dayStatsSort.sort((a, b) {
      final date1 = format.parse(a.date);
      final date2 = format.parse(b.date);

      return date1.compareTo(date2);
    });

    int workCount = 0;
    double workHours = 0;
    double workerHours = 0;

    for (var i = 0; i < last30Days.length; i++) {
      final day = last30Days[i];

      var found = false;

      for (final stat in dayStatsSort) {
        final value = compareDate("dd.MM.yyyy", day, stat.date);

        if (value == 1 && lastDay == "" && i == 0) {
          workCount += stat.workCount ?? 0;
          workHours += stat.workHours ?? 0;
          workerHours += stat.workerHours ?? 0;
        } else if (value == 0 && lastDay == "" && i == 0) {
          workCount += stat.workCount ?? 0;
          workHours += stat.workHours ?? 0;
          workerHours += stat.workerHours ?? 0;
          lastDay = day;
          found = true;
          break;
        }

        if (found == true) {
          break;
        }

        if (lastDay != "" && day == stat.date) {
          workCount += stat.workCount ?? 0;
          workHours += stat.workHours ?? 0;
          workerHours += stat.workerHours ?? 0;
          found = true;
          break;
        }
      }

      lastDay = day;

      // if (found == false && i == 0) {
      //   workCount = 0;
      //   workHours = 0;
      //   workerHours = 0;
      //   //lastDay = day;
      // }

      var spot = FlSpot(i.toDouble(), workerHours.roundToDouble());
      spots.add(spot);
    }

    return spots;
  }

  List<double> getMinMax(List<FlSpot> spots) {
    var min = 99999999.0;
    var max = 0.0;

    for (final spot in spots) {
      if (spot.y > max) {
        max = spot.y;
      }
      if (spot.y < min) {
        min = spot.y;
      }
    }

    final delta = (max - min) * 0.1;

    min = min * 0.90;

    max = max + delta;

    return [min.roundToDouble(), max.roundToDouble()];
  }

  LineChartData mainData(List<DayStatsModel> dayStats) {
    var last30DaysVeryShort = getLastNDays("dd.MM", 30);
    var last30DaysShort = getLastNDays("dd.MM.yy", 30);
    var last30Days = getLastNDays("dd.MM.yyyy", 30);
    var spots = configurateSpots(dayStats, last30Days);
    var minMaxY = getMinMax(spots);

    return LineChartData(
      lineTouchData: LineTouchData(
        touchTooltipData: LineTouchTooltipData(
          maxContentWidth: 100,
          // tooltipBgColor: Colors.blue,
          getTooltipItems: (touchedSpots) {
            return touchedSpots.map((LineBarSpot touchedSpot) {
              const textStyle = TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 14,
              );

              var dateValue = last30DaysShort[touchedSpot.x.toInt()];

              return LineTooltipItem(
                '$dateValue \n ${touchedSpot.y.toStringAsFixed(0)}',
                textStyle,
              );
            }).toList();
          },
        ),
        handleBuiltInTouches: true,
        getTouchLineStart: (data, index) => 0,
      ),
      gridData: FlGridData(
        show: true,
        drawVerticalLine: true,
        //horizontalInterval: 1,
        //verticalInterval: 1,
        getDrawingHorizontalLine: (value) {
          return const FlLine(
            color: Colors.grey,
            strokeWidth: 1,
          );
        },
        getDrawingVerticalLine: (value) {
          return const FlLine(
            color: Colors.grey,
            strokeWidth: 1,
          );
        },
      ),
      titlesData: FlTitlesData(
        show: true,
        rightTitles: const AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        topTitles: const AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        bottomTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 24,
            interval: 5,
            getTitlesWidget: (double value, TitleMeta meta) {
              return bottomTitleWidgets(value, meta, last30DaysVeryShort);
            },
          ),
        ),
        leftTitles: const AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            //interval: ,
            // getTitlesWidget: (double value, TitleMeta meta) {
            //   return leftTitleWidgets(value, meta, dayStats);
            // },
            reservedSize: 40,
          ),
        ),
      ),
      borderData: FlBorderData(
        show: true,
        border: Border.all(color: const Color(0xff37434d)),
      ),
      minX: 0,
      maxX: spots.length - 1,
      //minY: minMaxY.first,
      maxY: minMaxY.last,
      lineBarsData: [
        LineChartBarData(
          spots: spots,
          isCurved: true,
          preventCurveOverShooting: true,
          gradient: LinearGradient(
            colors: gradientColors,
          ),
          barWidth: 3,
          isStrokeCapRound: true,
          dotData: const FlDotData(
            show: false,
          ),
          belowBarData: BarAreaData(
            show: true,
            gradient: LinearGradient(
              colors: gradientColors
                  .map((color) => color.withOpacity(0.3))
                  .toList(),
            ),
          ),
        ),
      ],
    );
  }
}
