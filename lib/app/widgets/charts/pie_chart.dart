import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';

import 'helpers/indicator.dart';

class PieItemModel {
  final String name;
  final double value;
  final Color color;

  PieItemModel({required this.name, required this.value, required this.color});
}

class PieItemStatsChart extends StatefulWidget {
  const PieItemStatsChart({super.key, required this.models});

  final List<PieItemModel> models;

  @override
  State<PieItemStatsChart> createState() => _PieItemStatsChartState();
}

class _PieItemStatsChartState extends State<PieItemStatsChart> {
  int touchedIndex = -1;

  @override
  Widget build(BuildContext context) {
    //double width = MediaQuery.of(context).size.width;

    return SizedBox(
      //width: width / 2,
      height: 200,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: <Widget>[
            AspectRatio(
              aspectRatio: 1.0,
              child: PieChart(
                PieChartData(
                  pieTouchData: PieTouchData(
                    touchCallback: (FlTouchEvent event, pieTouchResponse) {
                      setState(() {
                        if (!event.isInterestedForInteractions ||
                            pieTouchResponse == null ||
                            pieTouchResponse.touchedSection == null) {
                          touchedIndex = -1;
                          return;
                        }
                        touchedIndex = pieTouchResponse
                            .touchedSection!.touchedSectionIndex;
                      });
                    },
                  ),
                  borderData: FlBorderData(
                    show: false,
                  ),
                  sectionsSpace: 0,
                  startDegreeOffset: -90,
                  centerSpaceRadius: 0,
                  sections: showingSections(widget.models),
                ),
              ),
            ),
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  for (final model in widget.models)
                    Expanded(
                      child: Column(
                        children: [
                          Expanded(
                            child: Indicator(
                              color: model.color,
                              text:
                                  "${model.name}\n${(model.value).toStringAsFixed(0)}",
                              isSquare: false,
                            ),
                          ),
                          const SizedBox(
                            height: 4,
                          ),
                        ],
                      ),
                    ),

                  // Indicator(
                  //   color: Colors.grey,
                  //   text: 'Простои, ч',
                  //   isSquare: false,
                  // ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<PieChartSectionData> showingSections(List<PieItemModel> models) {
    var allValues = 0.0;

    for (var model in models) {
      allValues += model.value;
    }

    final onePrecent = allValues / 100;

    return List.generate(models.length, (i) {
      final isTouched = i == touchedIndex;
      final fontSize = isTouched ? 23.0 : 16.0;
      final radius = isTouched ? 80.0 : 70.0;
      const shadows = [Shadow(color: Colors.black, blurRadius: 2)];
      final model = models[i];
      switch (i) {
        case 0:
          return PieChartSectionData(
            color: Colors.orange,
            value: model.value,
            title: "${(model.value / onePrecent).toStringAsFixed(1)} %",
            titlePositionPercentageOffset: 0.55,
            radius: radius,
            titleStyle: TextStyle(
              fontSize: fontSize,
              fontWeight: FontWeight.bold,
              color: Colors.white,
              shadows: shadows,
            ),
          );
        case 1:
          return PieChartSectionData(
            color: Colors.grey,
            value: model.value,
            title: "${(model.value / onePrecent).toStringAsFixed(1)} %",
            titlePositionPercentageOffset: 0.55,
            radius: radius,
            titleStyle: TextStyle(
              fontSize: fontSize,
              fontWeight: FontWeight.bold,
              color: Colors.white,
              shadows: shadows,
            ),
          );
        default:
          throw Error();
      }
    });
  }
}
