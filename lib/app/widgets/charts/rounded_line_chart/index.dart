import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:uer_flutter/app/styles/colors.dart';
import 'package:uer_flutter/app/styles/fonts.dart';

class RoundedLineChart extends StatefulWidget {
  const RoundedLineChart({
    super.key,
    this.spots,
    this.extendedSpots,
    this.selectedColor,
  });

  final List<FlSpot>? spots;
  final Map<Color, List<FlSpot>>? extendedSpots;
  final Color? selectedColor;

  @override
  State<RoundedLineChart> createState() => _RoundedLineChartState();
}

class _RoundedLineChartState extends State<RoundedLineChart> {
  final TransformationController _chartController = TransformationController();

  @override
  Widget build(BuildContext context) {
    return AspectRatio(
      aspectRatio: 4 / 2,
      child: Padding(
        padding: const EdgeInsets.only(right: 12.0),
        child: LineChart(
          LineChartData(
            borderData: FlBorderData(
              show: false,
            ),
            titlesData: FlTitlesData(
              rightTitles: AxisTitles(
                sideTitles: SideTitles(showTitles: false),
              ),
              topTitles: AxisTitles(
                sideTitles: SideTitles(showTitles: false),
              ),
              bottomTitles: AxisTitles(
                axisNameSize: 20,
                axisNameWidget: Text(
                  'Месяц',
                  style: AppFonts.labelMedium.merge(
                    TextStyle(
                      color: AppLightColors.lightGray,
                    ),
                  ),
                ),
                sideTitles: SideTitles(
                  showTitles: true,
                  interval: 1,
                  getTitlesWidget: (value, meta) {
                    return SideTitleWidget(
                      meta: meta,
                      child: Text(
                        '${(int.tryParse(meta.formattedValue) ?? 0)}',
                        style: AppFonts.labelSmall.merge(
                          TextStyle(
                            color: AppLightColors.medium,
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
              leftTitles: AxisTitles(
                axisNameSize: 20,
                axisNameWidget: Text(
                  'Дни',
                  style: AppFonts.labelMedium.merge(
                    TextStyle(
                      color: AppLightColors.lightGray,
                    ),
                  ),
                ),
                sideTitles: SideTitles(
                  reservedSize: 40,
                  showTitles: true,
                  // interval: 50,
                  minIncluded: false,
                  maxIncluded: false,
                  getTitlesWidget: (value, meta) {
                    return SideTitleWidget(
                      meta: meta,
                      child: Text(
                        '${meta.formattedValue}д',
                        style: AppFonts.labelSmall.merge(
                          TextStyle(
                            color: AppLightColors.medium,
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
            lineTouchData: LineTouchData(
              touchTooltipData: LineTouchTooltipData(
                getTooltipColor: (touchedSpot) {
                  return AppLightColors.white;
                },
                getTooltipItems: (touchedSpots) {
                  return touchedSpots.map((spot) {
                    return LineTooltipItem(
                      '${spot.y.toStringAsFixed(2)} д',
                      AppFonts.labelMedium.merge(
                        TextStyle(
                          color: spot.bar.color,
                        ),
                      ),
                    );
                  }).toList();
                },
              ),
            ),
            gridData: FlGridData(
              getDrawingHorizontalLine: (value) {
                return FlLine(
                  color: AppLightColors.lightGray,
                  strokeWidth: 1.0,
                );
              },
              getDrawingVerticalLine: (value) {
                return FlLine(
                  color: AppLightColors.lightGray,
                  strokeWidth: 1.0,
                );
              },
            ),
            lineBarsData: [
              ...(widget.extendedSpots ?? {}).entries.map((data) {
                final color = data.key;
                final spots = data.value;

                return LineChartBarData(
                  spots: spots,
                  isCurved: true,
                  color: widget.selectedColor == color ||
                          widget.selectedColor == null
                      ? color
                      : color.withValues(alpha: 0.2),
                  barWidth: 2.5,
                  dotData: FlDotData(
                    getDotPainter: (point, value, data, index) {
                      return FlDotCirclePainter(
                        radius: 4.0,
                        color: AppLightColors.white,
                        strokeColor: widget.selectedColor == color ||
                                widget.selectedColor == null
                            ? color
                            : color.withValues(alpha: 0.2),
                        strokeWidth: 3.0,
                      );
                    },
                  ),
                  isStrokeJoinRound: true,
                  isStrokeCapRound: true,
                  // belowBarData: BarAreaData(
                  //   show: true,
                  //   color: color.withValues(alpha: 0.1),
                  // ),
                  curveSmoothness: 0.25,
                  preventCurveOverShooting: true,
                  preventCurveOvershootingThreshold: 30,
                );
              }),
              LineChartBarData(
                spots: widget.spots ?? [],
                isCurved: true,
                color: AppLightColors.blue,
                barWidth: 3.0,
                dotData: FlDotData(
                  getDotPainter: (point, value, data, index) {
                    return FlDotCirclePainter(
                      radius: 4.0,
                      color: AppLightColors.white,
                      strokeColor: data.color ?? AppLightColors.blue,
                      strokeWidth: 3.0,
                    );
                  },
                ),
                isStrokeCapRound: true,
                // belowBarData: BarAreaData(show: true, colo),
                curveSmoothness: 0.25,
                preventCurveOverShooting: true,
                preventCurveOvershootingThreshold: 30,
              ),
            ],
          ),
          transformationConfig: FlTransformationConfig(
            scaleAxis: FlScaleAxis.horizontal,
            transformationController: _chartController,
            maxScale: 100,
          ),
        ),
      ),
    );
  }
}
