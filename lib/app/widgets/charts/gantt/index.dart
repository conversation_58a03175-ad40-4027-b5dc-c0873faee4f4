import 'dart:math';

import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:uer_flutter/app/core/models/Stats_outage/stats_outage_model.dart';
import 'package:uer_flutter/app/core/services/utils.dart';
import 'package:uer_flutter/app/styles/colors.dart';
import 'package:uer_flutter/app/styles/fonts.dart';

class GanttChart extends StatelessWidget {
  const GanttChart({super.key, required this.timelines});

  final List<TimelinesStatsModel>? timelines;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: (timelines?.length ?? 1) * 10 + 40,
      child: BarChart(
        BarChartData(
          borderData: FlBorderData(
            border: Border.all(
              color: AppLightColors.lightStroke,
            ),
          ),
          alignment: BarChartAlignment.start,
          rotationQuarterTurns: 1,
          barGroups: _buildBarGroups(),
          titlesData: FlTitlesData(
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                getTitlesWidget: (value, meta) {
                  final index = value.toInt();
                  if (index >= 0 && index < (timelines?.length ?? 0)) {
                    return Transform.rotate(
                      angle: -pi / 2, // Поворот на -90 градусов
                      child: Container(
                        alignment: Alignment.center,
                        child: Text(
                          timelines?[index].name ?? 'name',
                          style: TextStyle(
                            color: Colors.black,
                            fontSize: 12,
                          ),
                          textAlign: TextAlign.right,
                        ),
                      ),
                    );
                  }
                  return Text('');
                },
                reservedSize: 200, // Зарезервируйте место для заголовков
              ),
            ),
            topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
            leftTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
            rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          ),
          barTouchData: BarTouchData(
            touchTooltipData: BarTouchTooltipData(
              maxContentWidth: 140,
              getTooltipItem: (group, groupIndex, rod, rodIndex) {
                final timeline = timelines?[group.x.toInt()];
                final entryTimeline = timeline?.timeline?[rodIndex];
                final startDate = DateTime.fromMillisecondsSinceEpoch(
                  (entryTimeline?.startDate ?? 0) * 1000,
                );
                final endDate = DateTime.fromMillisecondsSinceEpoch(
                  (entryTimeline?.endDate ?? 0) * 1000,
                );

                return BarTooltipItem(
                  '${dateToString(startDate)}\n${dateToString(endDate)}',
                  AppFonts.labelMedium.merge(
                    TextStyle(color: AppLightColors.white),
                  ),
                );
              },
            ),
          ),
          // tooltipData: BarTooltipData(
          //   getTooltipItem: (group, groupIndex, rod, rodIndex) {
          //     final timeline = timelines?[group.x.toInt()];
          //     final entryTimeline = timeline?.timeline?[rodIndex];

          //     return BarTooltipItem(
          //       'Start: ${entryTimeline?.startDate}\nEnd: ${entryTimeline?.endDate}',
          //       const TextStyle(
          //         color: Colors.white,
          //         fontWeight: FontWeight.bold,
          //       ),
          //     );
          //   },
          // ),
        ),
      ),
    );
  }

  List<BarChartGroupData> _buildBarGroups() {
    if (timelines == null || timelines!.isEmpty) {
      return [];
    }

    return timelines!.asMap().entries.map((entry) {
      final timeline = entry.value;
      final index = entry.key;
      double currentPosition = 0.0;

      return BarChartGroupData(
        x: index,
        groupVertically: true,
        barRods: _buildBarRods(timeline, currentPosition),
      );
    }).toList();
  }

  List<BarChartRodData> _buildBarRods(
    TimelinesStatsModel timeline,
    double currentPosition,
  ) {
    return (timeline.timeline ?? []).map((entryTimeline) {
      final fromY = entryTimeline.startDate?.toDouble();
      final toY = entryTimeline.endDate?.toDouble() ?? 0.0;

      return BarChartRodData(
        fromY: fromY,
        toY: toY,
        width: 10,
        borderRadius: BorderRadius.circular(2),
        color: entryTimeline.type == WorkStatus.work
            ? AppLightColors.blue
            : AppLightColors.lightGray,
      );
    }).toList();
  }
}
