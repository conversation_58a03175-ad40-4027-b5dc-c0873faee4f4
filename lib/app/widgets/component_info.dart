import 'package:flutter/material.dart';

class ComponentInfo extends StatelessWidget {
  const ComponentInfo(
      {super.key,
      required this.repairNumber,
      required this.componentType,
      required this.equipment,
      required this.currentArea});

  final String repairNumber;
  final String componentType;
  final String equipment;
  final String currentArea;

  @override
  Widget build(BuildContext context) {
    return Card(
      //color: Colors.wh, //: Colors.white,
      elevation: 2.0,
      margin: const EdgeInsets.all(8),
      shape: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: const BorderSide(color: Colors.white)),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: SizedB<PERSON>(
          //height: 60,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  const Text(
                    "Ремонтный номер:",
                    style: TextStyle(fontWeight: FontWeight.w500),
                  ),
                  const Spacer(),
                  Text(repairNumber)
                ],
              ),
              Row(
                children: [
                  const Text(
                    "Оборудование:",
                    style: TextStyle(fontWeight: FontWeight.w500),
                  ),
                  const Spacer(),
                  Text(equipment)
                ],
              ),
              Row(
                children: [
                  const Text(
                    "Тип:",
                    style: TextStyle(fontWeight: FontWeight.w500),
                  ),
                  const Spacer(),
                  Text(componentType)
                ],
              ),
              Row(
                children: [
                  const Text(
                    "Местоположение:",
                    style: TextStyle(fontWeight: FontWeight.w500),
                  ),
                  const Spacer(),
                  Text(currentArea)
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
