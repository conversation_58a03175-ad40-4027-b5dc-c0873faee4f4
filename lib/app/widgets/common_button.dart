import 'package:flutter/material.dart';

class CommonButton extends StatelessWidget {
  const CommonButton({super.key, required this.name, required this.callback});

  final String name;
  final CommonButtonCallback callback;

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: callback,
      // style: ElevatedButton.styleFrom(
      //     //minimumSize: Size.fromHeight(40),
      //     backgroundColor: Colors.orange,
      //     shape: RoundedRectangleBorder(
      //         borderRadius: BorderRadius.circular(8.0),
      //         side: const BorderSide(color: Colors.orange))),
      style: ElevatedButton.styleFrom(
          backgroundColor: Colors.orange, foregroundColor: Colors.white),
      child: Text(
        name,
        style: const TextStyle(fontSize: 17),
      ),
    );
  }
}

typedef CommonButtonCallback = Function();

class CommonButtonModel {
  const CommonButtonModel({required this.name, required this.callback});

  final String name;
  final CommonButtonCallback callback;
}
