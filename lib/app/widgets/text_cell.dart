import 'package:flutter/material.dart';

typedef TextCellCallback = Function();

class TextCell extends StatelessWidget {
  const TextCell(
      {super.key,
      required this.title,
      required this.callback,
      this.selected = false});

  final String title;
  final TextCellCallback callback;
  final bool selected;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: callback,
      child: Padding(
        padding: const EdgeInsets.fromLTRB(8, 12, 8, 12),
        child: Row(
          children: [
            Text(title),
            const Spacer(),
            if (selected)
              const Icon(
                Icons.check,
                color: Colors.green,
              )
          ],
        ),
      ),
    );
  }
}
