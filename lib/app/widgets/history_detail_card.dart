import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uer_flutter/app/helpers/date_extension.dart';
import '../core/models/component_history/component_history_model.dart';
import '../core/models/enums/history_status_enum.dart';

import 'status_badge_text.dart';

class HistoryDetailCard extends ConsumerWidget {
  const HistoryDetailCard({super.key, required this.status});

  final ComponentStatusModel status;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var comment = status.comment;

    var createdAtMiliseconds = (status.createdAt?.toInt() ?? 0) * 1000;

    var dateAgo =
        DateTime.fromMillisecondsSinceEpoch(createdAtMiliseconds).timeAgo();
    var owner = status.owner;

    var statusText = status.status.desc;
    var statusBadge = status.status.statusBadge;

    return Card(
      //color: Colors.wh, //: Colors.white,
      elevation: 2.0,
      margin: const EdgeInsets.all(8),
      shape: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: const BorderSide(color: Colors.white)),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: SizedBox(
          //height: 100,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              comment?.isNotEmpty == true
                  ? Text(
                      status.comment ?? "",
                      style: const TextStyle(
                          fontStyle: FontStyle.italic,
                          fontWeight: FontWeight.w500),
                    )
                  : const SizedBox(),
              const SizedBox(
                height: 8,
              ),
              StatusBadgeText(icon: statusBadge, text: statusText),
              const SizedBox(
                height: 8,
              ),
              Row(
                children: [
                  Text(
                    dateAgo,
                    style: const TextStyle(
                      color: Colors.black45,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    owner,
                    style: const TextStyle(
                      color: Colors.black45,
                    ),
                  )
                ],
              )
            ],
          ),
        ),
      ),
    );
  }
}
