import 'package:flutter/material.dart';
import 'package:uer_flutter/app/helpers/date_extension.dart';
import '../core/models/component_history/component_history_model.dart';

//typedef CommentCardCallback = Function();

class CommentCard extends StatelessWidget {
  const CommentCard(
      {super.key,
      required this.lastStatus,
      required this.componentName,
      this.areaName});

  final ComponentStatusModel lastStatus;
  final String componentName;
  final String? areaName;
  //final HistoryCardCallback callback;

  @override
  Widget build(BuildContext context) {
    var owner = lastStatus.owner;
    var comment = lastStatus.comment;

    var dateAgo = DateTime.fromMillisecondsSinceEpoch(
            (lastStatus.createdAt ?? 0).toInt() * 1000)
        .timeAgo();

    //double width = MediaQuery.of(context).size.width;

    return Card(
      //color: Colors.wh, //: Colors.white,
      elevation: 2.0,
      margin: const EdgeInsets.all(8),
      shape: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: const BorderSide(color: Colors.white)),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: SizedBox(
          //height: 100,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              areaName != null
                  ? Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Text(areaName!,
                            maxLines: 1,
                            style:
                                const TextStyle(fontWeight: FontWeight.w500)),
                        const Spacer(),
                        Text(
                          componentName,
                          style: const TextStyle(fontWeight: FontWeight.w500),
                        ),
                      ],
                    )
                  : const SizedBox(),
              const SizedBox(
                height: 4,
              ),
              Text(
                comment ?? "",
                style: const TextStyle(
                    fontStyle: FontStyle.italic, fontWeight: FontWeight.w600),
              ),
              const SizedBox(
                height: 4,
              ),
              Row(
                children: [
                  Text(
                    dateAgo,
                    style: const TextStyle(
                      color: Colors.black45,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    owner,
                    style: const TextStyle(
                      color: Colors.black45,
                    ),
                  )
                ],
              )
            ],
          ),
        ),
      ),
    );
  }
}
