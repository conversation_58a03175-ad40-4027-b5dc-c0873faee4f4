import 'package:flutter/material.dart';
import 'package:uer_flutter/app/styles/colors.dart';

class CustomCard extends StatelessWidget {
  const CustomCard({
    super.key,
    this.padding,
    this.onTap,
    this.child,
    this.onSecondaryTapDown,
    this.border,
    this.color,
  });

  final EdgeInsets? padding;
  final void Function()? onTap;
  final void Function(TapDownDetails)? onSecondaryTapDown;
  final Widget? child;
  final BoxBorder? border;
  final Color? color;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: color ?? AppLightColors.white,
        borderRadius: BorderRadius.circular(20.0),
        border: border,
        // boxShadow: [
        //   BoxShadow(
        //     offset: const Offset(0, 4),
        //     blurRadius: 12.0,
        //     color: AppColors.lightPrimary
        //         .withOpacity(isLoading != true ? 0.06 : 0),
        //   ),
        // ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20.0),
          onTap: onTap,
          onSecondaryTapDown: onSecondaryTapDown,
          splashFactory: InkSparkle.splashFactory,
          child: Ink(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20.0),
            ),
            padding: padding ??
                const EdgeInsets.symmetric(horizontal: 12.0, vertical: 16.0),
            child: child,
          ),
        ),
      ),
    );
  }
}
