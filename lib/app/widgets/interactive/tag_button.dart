import 'package:flutter/material.dart';
import 'package:uer_flutter/app/styles/colors.dart';
import 'package:uer_flutter/app/styles/fonts.dart';

enum TagButtonTypes { initial, active }

class TagButton extends StatelessWidget {
  const TagButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.type = TagButtonTypes.initial,
    this.visualDensity = VisualDensity.compact,
  });

  final String text;
  final TagButtonTypes type;
  final void Function() onPressed;
  final VisualDensity visualDensity;

  Color getBackgroundColor(TagButtonTypes type) {
    switch (type) {
      case TagButtonTypes.initial:
        return AppLightColors.white;
      case TagButtonTypes.active:
        return AppLightColors.darkGray;
    }
  }

  Color getTextColor(TagButtonTypes type) {
    switch (type) {
      case TagButtonTypes.initial:
        return AppLightColors.black;
      case TagButtonTypes.active:
        return AppLightColors.white;
    }
  }

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ButtonStyle(
        visualDensity: visualDensity,
        backgroundColor: WidgetStatePropertyAll(getBackgroundColor(type)),
      ),
      child: Text(
        text,
        style: AppFonts.labelLarge.merge(
          TextStyle(
            color: getTextColor(type),
          ),
        ),
      ),
    );
  }
}
