import 'package:flutter/material.dart';
import 'package:uer_flutter/app/styles/colors.dart';
import 'package:uer_flutter/app/styles/fonts.dart';

class CustomChip extends StatelessWidget {
  const CustomChip({
    super.key,
    required this.isEnabled,
    this.color = AppLightColors.accent,
    required this.label,
    this.onTap,
  });

  final bool isEnabled;
  final Color color;
  final String label;
  final void Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(1000.0),
      hoverColor: color.withValues(alpha: 0.1),
      splashColor: color.withValues(alpha: 0.1),
      child: Ink(
        padding: EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
        decoration: BoxDecoration(
          color: isEnabled ? color : AppLightColors.white,
          border: Border.all(
            color: isEnabled ? Colors.transparent : color,
            width: 2.0,
          ),
          borderRadius: BorderRadius.circular(1000.0),
        ),
        child: Text(
          label,
          style: AppFonts.labelMedium.merge(TextStyle(
            color: isEnabled ? AppLightColors.white : color,
          )),
        ),
      ),
    );
  }
}
