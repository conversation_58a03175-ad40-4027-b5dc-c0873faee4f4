import 'package:flutter/material.dart';
import 'package:uer_flutter/app/styles/colors.dart';
import 'package:uer_flutter/app/styles/fonts.dart';

enum ElevatedButtonTypes { initial, inverse, accent }

class CustomElevatedButton extends StatelessWidget {
  const CustomElevatedButton({
    super.key,
    required this.onPressed,
    required this.text,
    this.type = ElevatedButtonTypes.initial,
  });

  final void Function() onPressed;
  final String text;
  final ElevatedButtonTypes type;

  Color _getBakcgroundColor(ElevatedButtonTypes type) {
    switch (type) {
      case ElevatedButtonTypes.initial:
        return AppLightColors.white;
      case ElevatedButtonTypes.inverse:
        return AppLightColors.darkGray;
      case ElevatedButtonTypes.accent:
        return AppLightColors.accent;
    }
  }

  Color _getOverlayColor(ElevatedButtonTypes type) {
    switch (type) {
      case ElevatedButtonTypes.initial:
        return AppLightColors.black.withAlpha(32);
      case ElevatedButtonTypes.inverse:
        return AppLightColors.white.withAlpha(32);
      case ElevatedButtonTypes.accent:
        return AppLightColors.white.withAlpha(32);
    }
  }

  Color _getTextColor(ElevatedButtonTypes type) {
    switch (type) {
      case ElevatedButtonTypes.initial:
        return AppLightColors.black;
      case ElevatedButtonTypes.inverse:
        return AppLightColors.white;
      case ElevatedButtonTypes.accent:
        return AppLightColors.white;
    }
  }

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        visualDensity: VisualDensity.standard,
        overlayColor: _getOverlayColor(type),
        backgroundColor: _getBakcgroundColor(type),
        shape: const StadiumBorder(),
      ),
      child: Text(
        text,
        style: AppFonts.titleMedium.merge(TextStyle(
          color: _getTextColor(type),
          fontVariations: const [FontVariation.weight(550)],
        )),
      ),
    );
  }
}
