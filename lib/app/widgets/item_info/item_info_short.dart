import 'package:flutter/material.dart';
import 'package:uer_flutter/app/widgets/containers/stat_row.dart';
import 'package:uer_flutter/app/widgets/interactive/elevated_button.dart';

import '../../helpers/KeyValueObject.dart';

typedef ItemInfoShortCallback = Function();

class ItemInfoShort extends StatelessWidget {
  const ItemInfoShort({
    super.key,
    required this.infoArr,
    required this.showDetailBtn,
    required this.callback,
  });

  final List<KeyValueObject>? infoArr;
  final bool showDetailBtn;
  final ItemInfoShortCallback callback;

  @override
  Widget build(BuildContext context) {
    if (infoArr == null) {
      return const Center(
        child: CircularProgressIndicator.adaptive(),
      );
    }

    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        for (var info in infoArr!)
          Column(
            children: [
              StatRow(info.key, info.value),
              const SizedBox(
                height: 4,
              ),
            ],
          ),
        if (showDetailBtn) const SizedBox(height: 32.0),
        if (showDetailBtn)
          Row(
            children: [
              Expanded(
                child: CustomElevatedButton(
                  onPressed: () {
                    callback();
                  },
                  text: 'Подробнее',
                ),
              ),
            ],
          ),
      ],
    );
  }
}
