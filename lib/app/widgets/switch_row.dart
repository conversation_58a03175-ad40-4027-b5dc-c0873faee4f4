import 'package:flutter/material.dart';

typedef SwitchRowCallback = Function(bool);

class SwitchRow extends StatelessWidget {
  const SwitchRow(
      {super.key,
      required this.name,
      required this.enable,
      required this.callback});

  final String name;
  final bool enable;
  final SwitchRowCallback callback;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Text(name),
        const Spacer(),
        Switch.adaptive(
            value: enable,
            onChanged: (value) {
              callback(value);
            })
      ],
    );
  }
}
