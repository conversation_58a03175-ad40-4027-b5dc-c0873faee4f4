import 'dart:typed_data';

import 'package:flutter/material.dart';

typedef PhotoPreviewCardRemoveCallback = Function();

class PhotoPreviewCard extends StatelessWidget {
  const PhotoPreviewCard({
    super.key,
    required this.photoData,
    required this.removeCallback,
  });

  final Uint8List photoData;
  final PhotoPreviewCardRemoveCallback removeCallback;

  @override
  Widget build(BuildContext context) {
    return Stack(
      textDirection: TextDirection.rtl,
      children: [
        Padding(
          padding: const EdgeInsets.all(24.0),
          child: Image.memory(
            photoData,
            //width: 100,
            //height: 100,
          ),
        ),
        IconButton(
            alignment: Alignment.topRight,
            splashRadius: 12,
            iconSize: 17,
            onPressed: removeCallback,
            icon: const Icon(
              Icons.close,
            )),
      ],
    );
  }
}
