import 'package:flutter/material.dart';
import 'package:uer_flutter/app/styles/colors.dart';
import 'package:uer_flutter/app/styles/fonts.dart';
import 'package:uer_flutter/app/widgets/svg_icon/index.dart';
import 'package:uer_flutter/gen/assets.gen.dart';

typedef SearchFieldCallback = Function(String);

class SearchField extends StatefulWidget {
  const SearchField({
    super.key,
    required this.hint,
    required this.controller,
    required this.callback,
  });

  final String hint;
  final TextEditingController controller;
  final SearchFieldCallback callback;

  @override
  State<SearchField> createState() => _SearchFieldState();
}

class _SearchFieldState extends State<SearchField> {
  //var controller = TextEditingController();

  bool showClearBtn = false;

  @override
  void initState() {
    super.initState();

    if (widget.controller.text.isNotEmpty) {
      showClearBtn = true;
    } else {
      showClearBtn = false;
    }
  }

  void clearText() {
    widget.controller.clear();
    widget.callback("");
    setState(() {
      showClearBtn = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (widget.controller.text.isNotEmpty) {
      showClearBtn = true;
    } else {
      showClearBtn = false;
    }

    return Expanded(
      child: TextField(
        style: AppFonts.bodyLarge,
        controller: widget.controller,
        decoration: InputDecoration(
          hintText: widget.hint,
          prefixIcon: Padding(
            padding: const EdgeInsets.only(left: 16.0, right: 10.0),
            child: Align(
              widthFactor: 1.0,
              heightFactor: 1.0,
              child: SVGIcon(
                Assets.icons.search,
                color: AppLightColors.medium,
              ),
            ),
          ),
          suffixIcon: showClearBtn
              ? Padding(
                  padding: const EdgeInsets.only(right: 2.0),
                  child: IconButton(
                    onPressed: clearText,
                    icon: SVGIcon(Assets.icons.close),
                  ),
                )
              : const SizedBox(),
        ),
        onChanged: (str) {
          //currentText = str;
          widget.callback(str);

          if (str.isNotEmpty) {
            setState(() {
              showClearBtn = true;
            });
          } else {
            setState(() {
              showClearBtn = false;
            });
          }
        },
      ),
    );
  }
}
