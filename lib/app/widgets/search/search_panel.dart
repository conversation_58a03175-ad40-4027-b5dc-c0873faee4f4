import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:uer_flutter/app/widgets/interactive/tag_button.dart';
import 'package:uer_flutter/app/widgets/svg_icon/index.dart';
import 'package:uer_flutter/gen/assets.gen.dart';

import '../../core/models/enums/search_enum.dart';
import '../../core/models/search/search_model.dart';
import '../../helpers/constants.dart';
import '../../routes/routes_name.dart';
import 'search_field.dart';

typedef SearchPanelCallback = Function(SearchModel?);

class SearchPanel extends StatefulWidget {
  const SearchPanel({super.key, required this.callback});

  final SearchPanelCallback callback;

  @override
  State<SearchPanel> createState() => _SearchPanelState();
}

class _SearchPanelState extends State<SearchPanel> {
  String hintText = SearchType.repairNumber.hint;
  SearchType currentType = SearchType.repairNumber;
  String lastSearch = "";
  //String? searchQRText;

  final TextEditingController _controller = TextEditingController();

  void changeSearchType(SearchType type) {
    setState(() {
      currentType = type;
      hintText = type.hint;
    });

    newSearch(lastSearch);
  }

  void newSearch(String str) {
    var searchModel = const SearchModel(projection: PROJECTION_SEARCH_ITEM);

    if (str.isEmpty) {
      widget.callback(null);
      lastSearch = "";
      return;
    }

    switch (currentType) {
      case SearchType.repairNumber:
        searchModel = searchModel.copyWith(searchField: str);
        break;
      case SearchType.client:
        searchModel = searchModel.copyWith(searchClient: str);
        break;
      case SearchType.mainID:
        searchModel = searchModel.copyWith(mainID: str);
        break;
      case SearchType.equipment:
        searchModel = searchModel.copyWith(searchEquipment: str);
        break;
    }

    lastSearch = str;

    widget.callback(searchModel);
  }

  void openQRCodeScanPage() async {
    var value = await context.pushNamed(RoutesName.qrScan.named);

    if (value != null) {
      if (value is String) {
        parseQRCode(value);
      }
    }
  }

  void parseQRCode(String qrCode) {
    //print(qrCode);

    var values = qrCode.split("//");

    if (values.length == 3) {
      var mainID = values[1];

      changeTextFieldValue(mainID);

      //lastSearch = mainID;

      changeSearchType(SearchType.mainID);
    } else if (values.length == 4) {
      var repairNumber = values[1];

      changeTextFieldValue(repairNumber);

      //lastSearch = repairNumber;

      changeSearchType(SearchType.repairNumber);
    }
  }

  void changeTextFieldValue(String value) {
    setState(() {
      _controller.value = TextEditingValue(
          text: value,
          selection: TextSelection.fromPosition(
            TextPosition(offset: value.length),
          ));
    });

    lastSearch = value;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16.0),
        Row(
          children: [
            SearchField(
              hint: hintText,
              controller: _controller,
              callback: newSearch,
            ),
            const SizedBox(width: 10.0),
            IconButton(
              onPressed: openQRCodeScanPage,
              icon: SVGIcon(Assets.icons.qrCode),
            ),
          ],
        ),
        const SizedBox(height: 8.0),
        SizedBox(
          height: 32.0,
          child: ListView(
            clipBehavior: Clip.none,
            scrollDirection: Axis.horizontal,
            children: SearchType.values.expand((type) {
              return [
                TagButton(
                  type: currentType == type
                      ? TagButtonTypes.active
                      : TagButtonTypes.initial,
                  onPressed: () => changeSearchType(type),
                  text: type.name,
                ),
                if (type.index != SearchType.values.length - 1)
                  const SizedBox(width: 8.0),
              ];
            }).toList(),
          ),
        )
      ],
    );
  }
}
