import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

typedef CommonCardRowCallback = Function();

class CommonCardRow extends ConsumerWidget {
  const CommonCardRow(
      {super.key,
      required this.name,
      required this.icon,
      required this.callback});

  final String name;
  final IconData icon;
  final CommonCardRowCallback callback;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Card(
      color: Colors.white,
      elevation: 2.0,
      margin: const EdgeInsets.all(8),
      shape: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: const BorderSide(color: Colors.white)),
      child: InkWell(
        borderRadius: BorderRadius.circular(10.0),
        onTap: callback,
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: SizedBox(
            //height: 60,
            child: Row(
              children: [
                Icon(
                  icon,
                  color: Colors.orange,
                ),
                const SizedBox(
                  width: 8,
                ),
                Text(name, style: const TextStyle(fontWeight: FontWeight.w500)),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
