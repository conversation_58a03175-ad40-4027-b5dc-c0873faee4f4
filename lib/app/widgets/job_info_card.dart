import 'package:flutter/material.dart';
import 'package:uer_flutter/app/helpers/date_extension.dart';

import '../core/models/component_history/component_history_model.dart';
import '../core/models/enums/component_history_type_enum.dart';
import '../core/models/enums/history_status_enum.dart';
import 'status_badge_text.dart';

typedef JobInfoCardCallback = Function();

class JobInfoCard extends StatelessWidget {
  const JobInfoCard({super.key, required this.history, required this.callback});

  final ComponentHistoryModel history;
  final JobInfoCardCallback callback;

  @override
  Widget build(BuildContext context) {
    var icon = history.type.icon;
    var lastStatus = history.statuses.last;

    var statusBadge = lastStatus.status.statusBadge;
    var finishStatusText = lastStatus.status.desc;

    var areaName = history.getAreaName(null);
    var statusName = history.getStatusName(null);
    var owner = lastStatus.owner;
    //var comment = lastStatus.comment;

    var dateAgo = DateTime.fromMillisecondsSinceEpoch(
            (lastStatus.createdAt ?? 0).toInt() * 1000)
        .timeAgo();

    var historyType = history.type;
    var historyInfoOff = historyType != ComponentHistoryType.info;

    double width = MediaQuery.of(context).size.width;

    return Card(
      elevation: 2.0,
      margin: const EdgeInsets.all(8),
      shape: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: const BorderSide(color: Colors.white)),
      child: InkWell(
        borderRadius: BorderRadius.circular(10.0),
        onTap: callback,
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: SizedBox(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    icon,
                    const SizedBox(
                      width: 8,
                    ),
                    SizedBox(
                      width: width - 100,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            areaName,
                            maxLines: 1,
                          ),
                          Text(
                            statusName,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            //softWrap: false,
                          ),
                        ],
                      ),
                    )
                  ],
                ),
                historyInfoOff
                    ? StatusBadgeText(icon: statusBadge, text: finishStatusText)
                    : const SizedBox(),
                const SizedBox(
                  height: 8,
                ),
                Row(
                  children: [
                    Text(
                      dateAgo,
                      style: const TextStyle(
                        color: Colors.black45,
                      ),
                    ),
                    const Spacer(),
                    Text(
                      owner,
                      style: const TextStyle(
                        color: Colors.black45,
                      ),
                    )
                  ],
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
