import 'package:flutter/material.dart';
import 'package:uer_flutter/app/styles/colors.dart';
import 'package:uer_flutter/app/styles/fonts.dart';
import 'package:uer_flutter/app/widgets/svg_icon/index.dart';
import 'package:uer_flutter/gen/assets.gen.dart';

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  const CustomAppBar({
    super.key,
    required this.text,
    this.actions,
    this.height = 60.0,
    this.withLogo = true,
  });

  final String text;
  final List<Widget>? actions;
  final double height;
  final bool withLogo;

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Row(children: [
        if (withLogo)
          SVGIcon(
            Assets.icons.uerLogo,
            width: 36.0,
            height: 27.0,
            color: AppLightColors.white,
          ),
        if (withLogo) const SizedBox(width: 12.0),
        Text(
          text,
          style: AppFonts.titleLarge.merge(
            const TextStyle(
              fontVariations: [FontVariation.weight(700)],
            ),
          ),
        ),
      ]),
      actions: [...?actions, const SizedBox(width: 16.0)],
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(height);
}
