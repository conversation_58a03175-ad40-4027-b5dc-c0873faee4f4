import 'package:flutter/material.dart';
import 'package:uer_flutter/app/styles/colors.dart';
import 'package:uer_flutter/app/styles/fonts.dart';
import 'package:uer_flutter/app/widgets/svg_icon/index.dart';
import 'package:uer_flutter/gen/assets.gen.dart';

class CustomNavigationRail extends StatelessWidget {
  const CustomNavigationRail({
    super.key,
    required this.currentIndex,
    required this.onSelected,
  });

  final int currentIndex;
  final void Function(int) onSelected;

  @override
  Widget build(BuildContext context) {
    return NavigationRail(
      extended: true,
      indicatorColor: AppLightColors.accent,
      onDestinationSelected: onSelected,
      backgroundColor: AppLightColors.white,
      minExtendedWidth: 200.0,
      destinations: [
        NavigationRailDestination(
          icon: SVGIcon(
            Assets.icons.home,
            color:
                currentIndex == 0 ? AppLightColors.white : AppLightColors.black,
          ),
          label: const Text('Главная', style: AppFonts.labelLarge),
        ),
        NavigationRailDestination(
          indicatorColor: AppLightColors.accent,
          icon: SVGIcon(
            Assets.icons.photoLibrary,
            color:
                currentIndex == 1 ? AppLightColors.white : AppLightColors.black,
          ),
          label: const Text('Фото', style: AppFonts.labelLarge),
        ),
        NavigationRailDestination(
          indicatorColor: AppLightColors.accent,
          icon: SVGIcon(
            Assets.icons.work,
            color:
                currentIndex == 2 ? AppLightColors.white : AppLightColors.black,
          ),
          label: const Text('ССЗ', style: AppFonts.labelLarge),
        ),
        NavigationRailDestination(
          indicatorColor: AppLightColors.accent,
          icon: SVGIcon(
            Assets.icons.settings,
            color:
                currentIndex == 3 ? AppLightColors.white : AppLightColors.black,
          ),
          label: const Text('Настройки', style: AppFonts.labelLarge),
        ),
      ],
      selectedIndex: currentIndex,
      // trailing: Container(
      //   width: 200.0,
      //   height: 20.0,
      //   color: Colors.amber,
      //   child: const Text('test'),
      // ),
    );
  }
}
