import 'package:flutter/material.dart';
import 'package:uer_flutter/app/styles/colors.dart';
import 'package:uer_flutter/app/styles/fonts.dart';
import 'package:uer_flutter/app/widgets/svg_icon/index.dart';
import 'package:uer_flutter/gen/assets.gen.dart';

class CustomBottomNavigationBar extends StatelessWidget {
  const CustomBottomNavigationBar({
    super.key,
    required this.onTap,
    required this.currentIndex,
  });

  final void Function(int) onTap;
  final int currentIndex;

  @override
  Widget build(BuildContext context) {
    return BottomNavigationBar(
      selectedLabelStyle: AppFonts.bodySmall,
      unselectedLabelStyle: AppFonts.bodySmall,
      items: [
        BottomNavigationBarItem(
          icon: SVGIcon(
            Assets.icons.home,
            color: currentIndex == 0
                ? AppLightColors.accent
                : AppLightColors.black,
          ),
          label: 'Главная',
        ),
        BottomNavigationBarItem(
          icon: SVGIcon(
            Assets.icons.photoLibrary,
            color: currentIndex == 1
                ? AppLightColors.accent
                : AppLightColors.black,
          ),
          label: 'Фото',
        ),
        BottomNavigationBarItem(
          icon: SVGIcon(
            Assets.icons.work,
            color: currentIndex == 2
                ? AppLightColors.accent
                : AppLightColors.black,
          ),
          label: 'ССЗ',
        ),
        BottomNavigationBarItem(
          icon: SVGIcon(
            Assets.icons.settings,
            color: currentIndex == 3
                ? AppLightColors.accent
                : AppLightColors.black,
          ),
          label: 'Настройки',
        ),
      ],
      currentIndex: currentIndex,
      onTap: onTap,
    );
  }
}
