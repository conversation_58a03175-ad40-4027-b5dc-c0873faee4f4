import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uer_flutter/app/styles/colors.dart';
import 'package:uer_flutter/app/styles/fonts.dart';
import 'package:uer_flutter/app/widgets/containers/id.dart';
import 'package:uer_flutter/app/widgets/interactive/chip.dart';
import 'package:uer_flutter/app/widgets/svg_icon/index.dart';
import 'package:uer_flutter/gen/assets.gen.dart';

import '../core/models/component/component_model.dart';
import '../core/models/enums/component_type_enum.dart';
import '../core/models/item/item_model.dart';
import '../core/providers/common/common_providers.dart';

typedef ComponentCellCallback = Function();

class ComponentCell extends ConsumerWidget {
  const ComponentCell({
    super.key,
    required this.item,
    required this.component,
    required this.currentAreaID,
    required this.callback,
  });

  final ItemModel item;
  final ComponentModel component;
  final int currentAreaID;
  final ComponentCellCallback callback;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    String? statusText;
    String? areaName;

    if (component.newHistory?.isNotEmpty == true) {
      var areaID = component.newHistory!.last.area;
      var statusID = component.newHistory!.last.job;

      var area = ref.watch(areaForIDProvider(areaID));
      var status = ref.watch(statusForIDProvider(statusID));

      areaName = component.newHistory?.last.getAreaName(area);
      statusText = component.newHistory?.last.getStatusName(status);
    }

    // bool colorBrown = false;

    // if (currentAreaID != component.currentArea) {
    //   colorBrown = true;
    // }

    bool assembled = false;

    if (item.assembled == true) {
      if (component.type == ComponentType.stator ||
          component.type == ComponentType.inductor) {
        assembled = true;
      }
    }

    var dateAgo = component.lastUpdate();
    // var mark = item.mark ?? false;

    return Card(
      // color: colorBrown == true
      //     ? const Color.fromARGB(255, 198, 156, 141)
      //     : Colors.white,
      margin: const EdgeInsets.only(bottom: 8.0),
      child: InkWell(
        // borderRadius: BorderRadius.circular(10.0),
        onTap: callback,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  IdLabel(item.repairNumber ?? ''),
                  const SizedBox(width: 6.0),
                  Text(
                    component.type.desc,
                    style: AppFonts.labelMedium.merge(const TextStyle(
                      color: AppLightColors.medium,
                      fontVariations: [FontVariation.weight(500)],
                    )),
                  ),
                  if (assembled) const SizedBox(width: 6.0),
                  if (assembled)
                    SVGIcon(Assets.icons.viewInAr,
                        color: AppLightColors.success),
                  const Spacer(),
                  Text(dateAgo,
                      textAlign: TextAlign.end,
                      style: AppFonts.labelMedium.merge(const TextStyle(
                        color: AppLightColors.medium,
                        fontVariations: [FontVariation.weight(500)],
                      ))),
                ],
              ),
              const SizedBox(height: 12.0),
              Wrap(spacing: 4.0, runSpacing: 4.0, children: [
                ...(item.tags ?? []).map((tag) => CustomChip(
                      isEnabled: true,
                      label: tag.getName(),
                      color: tag.getColor(),
                    )),
              ]),
              const SizedBox(height: 10.0),
              Text(item.order?.equipment ?? "",
                  style: AppFonts.bodyLarge.merge(const TextStyle(
                    fontFamily: AppFonts.monoFontFamily,
                    color: AppLightColors.medium,
                  ))),
              const SizedBox(height: 14.0),
              Text(areaName ?? "",
                  style: AppFonts.bodyLarge.merge(const TextStyle(
                    fontVariations: [FontVariation.weight(550)],
                  ))),
              const SizedBox(height: 4.0),
              Text(
                statusText ?? "",
                style: AppFonts.bodyLarge,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
