/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/widgets.dart';

class $AssetsIconGen {
  const $AssetsIconGen();

  /// File path: assets/icon/icon.png
  AssetGenImage get icon => const AssetGenImage('assets/icon/icon.png');

  /// List of all assets
  List<AssetGenImage> get values => [icon];
}

class $AssetsIconsGen {
  const $AssetsIconsGen();

  /// File path: assets/icons/Account circle.svg
  String get accountCircle => 'assets/icons/Account circle.svg';

  /// File path: assets/icons/Add photo alternate.svg
  String get addPhotoAlternate => 'assets/icons/Add photo alternate.svg';

  /// File path: assets/icons/Add shopping cart.svg
  String get addShoppingCart => 'assets/icons/Add shopping cart.svg';

  /// File path: assets/icons/Arrow back.svg
  String get arrowBack => 'assets/icons/Arrow back.svg';

  /// File path: assets/icons/Assignment late.svg
  String get assignmentLate => 'assets/icons/Assignment late.svg';

  /// File path: assets/icons/Balance.svg
  String get balance => 'assets/icons/Balance.svg';

  /// File path: assets/icons/Calendar month.svg
  String get calendarMonth => 'assets/icons/Calendar month.svg';

  /// File path: assets/icons/Chat info.svg
  String get chatInfo => 'assets/icons/Chat info.svg';

  /// File path: assets/icons/Checklist.svg
  String get checklist => 'assets/icons/Checklist.svg';

  /// File path: assets/icons/Close.svg
  String get close => 'assets/icons/Close.svg';

  /// File path: assets/icons/Download.svg
  String get download => 'assets/icons/Download.svg';

  /// File path: assets/icons/Error outline.svg
  String get errorOutline => 'assets/icons/Error outline.svg';

  /// File path: assets/icons/Filter alt.svg
  String get filterAlt => 'assets/icons/Filter alt.svg';

  /// File path: assets/icons/Finance mode.svg
  String get financeMode => 'assets/icons/Finance mode.svg';

  /// File path: assets/icons/History.svg
  String get history => 'assets/icons/History.svg';

  /// File path: assets/icons/Home.svg
  String get home => 'assets/icons/Home.svg';

  /// File path: assets/icons/Info.svg
  String get info => 'assets/icons/Info.svg';

  /// File path: assets/icons/Inventory.svg
  String get inventory => 'assets/icons/Inventory.svg';

  /// File path: assets/icons/Label important outline.svg
  String get labelImportantOutline =>
      'assets/icons/Label important outline.svg';

  /// File path: assets/icons/Logout.svg
  String get logout => 'assets/icons/Logout.svg';

  /// File path: assets/icons/More vert.svg
  String get moreVert => 'assets/icons/More vert.svg';

  /// File path: assets/icons/Notifications active.svg
  String get notificationsActive => 'assets/icons/Notifications active.svg';

  /// File path: assets/icons/Notifications.svg
  String get notifications => 'assets/icons/Notifications.svg';

  /// File path: assets/icons/Person add.svg
  String get personAdd => 'assets/icons/Person add.svg';

  /// File path: assets/icons/Photo library.svg
  String get photoLibrary => 'assets/icons/Photo library.svg';

  /// File path: assets/icons/Qr code.svg
  String get qrCode => 'assets/icons/Qr code.svg';

  /// File path: assets/icons/Search.svg
  String get search => 'assets/icons/Search.svg';

  /// File path: assets/icons/Settings.svg
  String get settings => 'assets/icons/Settings.svg';

  /// File path: assets/icons/Share.svg
  String get share => 'assets/icons/Share.svg';

  /// File path: assets/icons/Task alt.svg
  String get taskAlt => 'assets/icons/Task alt.svg';

  /// File path: assets/icons/Video call.svg
  String get videoCall => 'assets/icons/Video call.svg';

  /// File path: assets/icons/Videocam.svg
  String get videocam => 'assets/icons/Videocam.svg';

  /// File path: assets/icons/View in ar off.svg
  String get viewInArOff => 'assets/icons/View in ar off.svg';

  /// File path: assets/icons/View in ar.svg
  String get viewInAr => 'assets/icons/View in ar.svg';

  /// File path: assets/icons/Warning amber.svg
  String get warningAmber => 'assets/icons/Warning amber.svg';

  /// File path: assets/icons/Work.svg
  String get work => 'assets/icons/Work.svg';

  /// File path: assets/icons/clear_icon.png
  AssetGenImage get clearIcon =>
      const AssetGenImage('assets/icons/clear_icon.png');

  /// File path: assets/icons/icon.png
  AssetGenImage get icon => const AssetGenImage('assets/icons/icon.png');

  /// File path: assets/icons/math.svg
  String get math => 'assets/icons/math.svg';

  /// File path: assets/icons/uer_logo.svg
  String get uerLogo => 'assets/icons/uer_logo.svg';

  /// List of all assets
  List<dynamic> get values => [
        accountCircle,
        addPhotoAlternate,
        addShoppingCart,
        arrowBack,
        assignmentLate,
        balance,
        calendarMonth,
        chatInfo,
        checklist,
        close,
        download,
        errorOutline,
        filterAlt,
        financeMode,
        history,
        home,
        info,
        inventory,
        labelImportantOutline,
        logout,
        moreVert,
        notificationsActive,
        notifications,
        personAdd,
        photoLibrary,
        qrCode,
        search,
        settings,
        share,
        taskAlt,
        videoCall,
        videocam,
        viewInArOff,
        viewInAr,
        warningAmber,
        work,
        clearIcon,
        icon,
        math,
        uerLogo
      ];
}

class Assets {
  const Assets._();

  static const String aEnv = '.env';
  static const $AssetsIconGen icon = $AssetsIconGen();
  static const $AssetsIconsGen icons = $AssetsIconsGen();

  /// List of all assets
  static List<String> get values => [aEnv];
}

class AssetGenImage {
  const AssetGenImage(
    this._assetName, {
    this.size,
    this.flavors = const {},
  });

  final String _assetName;

  final Size? size;
  final Set<String> flavors;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.medium,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({
    AssetBundle? bundle,
    String? package,
  }) {
    return AssetImage(
      _assetName,
      bundle: bundle,
      package: package,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
