// import 'dart:io';

import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:media_kit/media_kit.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:uer_flutter/app.dart';
import 'package:uer_flutter/app/helpers/platform_check.dart';
import 'package:uer_flutter/app/helpers/platform_exception.dart';

import 'app/helpers/ssl_helper.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  HttpOverrides.global = MyHttpOverrides();

  try {
    SecurityContext.defaultContext.setTrustedCertificatesBytes(
      ascii.encode(ISRG_X1),
    );
  } catch (e) {
    // ignore errors here, maybe it's already trusted
  }

  await dotenv.load(fileName: ".env");

  try {
    MediaKit.ensureInitialized();
  } catch (e) {
    print("MediaKit initialization error: $e");
  }

  // try {
  //   if (!kIsWeb) {
  //     // await Firebase.initializeApp(
  //     //   options: DefaultFirebaseOptions.currentPlatform,
  //     // );
  //     await AppMetrica.activate(
  //       const AppMetricaConfig("413f62e9-2cfe-4a9c-8bb3-c52266d23290"),
  //     );
  //     // await FirebaseAnalytics.instance.logAppOpen();
  //   }
  // } catch (e) {
  //   print("AppMetrica initialization failed: $e");
  // }

  if (isAndroidOriOS() == true) {
    // Кофигурация OneSignal для мобильных платформ
    configurateOneSignal();
  }

  runApp(
    const ProviderScope(
      child: MyApp(),
    ),
  );
}

/// Configure OneSignal (notifications)
void configurateOneSignal() {
  if (kDebugMode) {
    OneSignal.Debug.setLogLevel(OSLogLevel.verbose);
  }

  OneSignal.initialize(dotenv.env["ONE_SIGNAL_API"]!);

  // The promptForPushNotificationsWithUserResponse function will show the iOS or Android push notification prompt. We recommend removing the following code and instead using an In-App Message to prompt for notification permission
  OneSignal.Notifications.requestPermission(true);
}
