<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="21701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" colorMatched="YES" initialViewController="01J-lp-oVM">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21679"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="EHf-IW-A2E">
            <objects>
                <viewController id="01J-lp-oVM" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="Ydg-fD-yQy"/>
                        <viewControllerLayoutGuide type="bottom" id="xbc-2k-c8Z"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="Ze5-6b-2t3">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" fixedFrame="YES" image="LaunchImage" translatesAutoresizingMaskIntoConstraints="NO" id="YRO-k0-Ey4">
                                <rect key="frame" x="196.33333333333334" y="0.0" width="0.33333333333334281" height="0.33333333333331439"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                            </imageView>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="uer-icon4" translatesAutoresizingMaskIntoConstraints="NO" id="Cjj-FM-8cG">
                                <rect key="frame" x="40" y="214.66666666666663" width="313" height="313"/>
                                <constraints>
                                    <constraint firstAttribute="width" secondItem="Cjj-FM-8cG" secondAttribute="height" multiplier="1:1" id="gdT-cQ-3tj"/>
                                </constraints>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Уралэлектроремонт" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumScaleFactor="0.5" translatesAutoresizingMaskIntoConstraints="NO" id="SFE-rr-6xI">
                                <rect key="frame" x="40" y="551.66666666666663" width="313" height="40"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="40" id="smz-IM-CvT"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="boldSystem" pointSize="32"/>
                                <color key="textColor" red="0.83529411760000005" green="0.36078431370000003" blue="0.14901960780000001" alpha="1" colorSpace="calibratedRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstAttribute="trailingMargin" secondItem="Cjj-FM-8cG" secondAttribute="trailing" constant="24" id="4Tu-w2-NYI"/>
                            <constraint firstItem="Cjj-FM-8cG" firstAttribute="centerY" secondItem="Ze5-6b-2t3" secondAttribute="centerY" constant="-55" id="5Dx-if-2qN"/>
                            <constraint firstItem="Cjj-FM-8cG" firstAttribute="centerX" secondItem="Ze5-6b-2t3" secondAttribute="centerX" id="CFE-JB-qaL"/>
                            <constraint firstAttribute="trailingMargin" secondItem="SFE-rr-6xI" secondAttribute="trailing" constant="24" id="JOz-dW-ev5"/>
                            <constraint firstItem="Cjj-FM-8cG" firstAttribute="leading" secondItem="Ze5-6b-2t3" secondAttribute="leadingMargin" constant="24" id="M0f-Xq-lKL"/>
                            <constraint firstItem="SFE-rr-6xI" firstAttribute="leading" secondItem="Ze5-6b-2t3" secondAttribute="leadingMargin" constant="24" id="aNa-hr-cJv"/>
                            <constraint firstItem="SFE-rr-6xI" firstAttribute="top" secondItem="Cjj-FM-8cG" secondAttribute="bottom" constant="24" id="iEk-B6-vKT"/>
                        </constraints>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="iYj-Kq-Ea1" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="80.916030534351137" y="264.08450704225356"/>
        </scene>
    </scenes>
    <resources>
        <image name="LaunchImage" width="0.3333333432674408" height="0.3333333432674408"/>
        <image name="uer-icon4" width="1000" height="1000"/>
    </resources>
</document>
