#!/bin/sh

# Fail this script if any subcommand fails.
set -e

echo "Script started..."

# The default execution directory of this script is the ci_scripts directory.
cd $CI_PRIMARY_REPOSITORY_PATH # change working directory to the root of your cloned repo.
echo "Changed directory to $CI_PRIMARY_REPOSITORY_PATH"

# Install Flutter with proper error handling
echo "Checking Flutter installation..."
if [ -d "$HOME/flutter" ]; then
    echo "Flutter directory already exists, updating..."
    cd $HOME/flutter
    git pull origin stable
else
    echo "Cloning new Flutter repository..."
    git clone https://github.com/flutter/flutter.git -b stable $HOME/flutter
fi

# Verify Flutter installation
if [ $? -eq 0 ]; then
    echo "Flutter cloned/updated successfully."
else
    echo "Flutter clone/update failed"
    exit 1
fi

# Setup PATH
export PATH="$PATH:$HOME/flutter/bin"
echo "Flutter path added to PATH."

# Verify Flutter installation
flutter --version
if [ $? -ne 0 ]; then
    echo "Flutter installation verification failed"
    exit 1
fi

# Install Flutter artifacts for iOS (--ios), or macOS (--macos) platforms.
echo "Running flutter precache --ios..."
flutter precache --ios
echo "Flutter precache completed."

# Install Flutter dependencies.
echo "Running flutter pub get..."
flutter pub get
echo "Flutter dependencies installed."

# Check if CocoaPods is installed and install if not.
if ! command -v pod &> /dev/null; then
    echo "CocoaPods not installed. Installing..."
    HOMEBREW_NO_AUTO_UPDATE=1 brew install cocoapods
else
    echo "CocoaPods already installed."
fi

# Update CocoaPods repository and install CocoaPods dependencies.
cd ios
echo "Changed directory to ios folder."

echo "Installing CocoaPods dependencies..."
export COCOAPODS_DISABLE_STATS=1
pod install --verbose # Установка зависимостей CocoaPods.
echo "CocoaPods dependencies installed."

echo "Script completed successfully."
exit 0