// Загрузка переменных окружения из .env файла
require('dotenv').config();

const bodyParser = require('body-parser');
const path = require('path')
const express = require('express');
const helmet = require("helmet");
const morgan = require('morgan');
const fs = require('fs');
let cors = require('cors');

// Инициализация Redis
const redisClient = require('./src/utils/redis');

require('./src/db')

const addRoutes = require('./routes')
// const ssz = require('./src/ssz/ssz')
// const fix = require('./src/parse/fix_auto_close')
//const statsParse = require('./src/parse/outage_stats_parse.js')

const dir = '/z_ftp/uer-data/mobil/foto/zpr' //path.join(__dirname, '/../uploads'); // __dirname, '/../uploads'

//const script = require('./src/parse/updateItems')

//console.log(dir)

const port = process.env.PORT || 3000

let app = express();

app.use(cors());

switch (app.get('env')) {
    case 'development':
        app.use(require('morgan')('dev'));
        break;
    case 'production':
        const stream = fs.createWriteStream(__dirname + '/access.log', {flags:'a'})
        app.use(morgan('combined', {stream}));
        break;
}

app.use(express.static(dir));
app.use(helmet());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({extended:true}));

addRoutes(app)

app.listen(port, function(){
    console.log( `Express started on http://localhost:${port}` +
        '; press Ctrl-C to terminate.' )

    //script.updateItems();

    //fix.fixIncorrectDates(); // Исправление дат для всего периода
    //fix.fixIncorrectDatesByDate('2024-11-20'); // Исправление дат для конкретной даты
    //ssz.updateExportSZZ();
    //ssz.exportSSZForAllDay()
    //ssz.autoCloseSSZ()
    //ssz.closeMissingSSZForDay(); // Закрытие незакрытых ССЗ за вчерашний день
    //fix.fixIncorrectDatesByDate('2025-03-06'); // Исправление дат для конкретной даты YYYY-MM-DD
    //ssz.closeMissingSSZForDay('2024-11-20'); // Закрытие незакрытых ССЗ за конкретную дату

    // let date = new Date();
    // date.setDate(date.getDate() - 1);
    // exportSSZForDay(new Date(date));
    //statsParse.outAgeStatsParse();
});

//Обработка завершения работы приложения
process.on('SIGINT', () => {
    console.log('Closing Redis connection...');
    redisClient.close();
    process.exit();
});

