services:
  # mongo:
  #   image: mongo
  #   container_name: mongodb
  #   restart: always
  #   ports:
  #     - '27017:27017'
  #   volumes:
  #     - mongodb:/data/db
  #   env_file:
  #     - ./.env
  #   environment:
  #     MONGO_INITDB_ROOT_USERNAME: ${MONGO_INITDB_ROOT_USERNAME}
  #     MONGO_INITDB_ROOT_PASSWORD: ${MONGO_INITDB_ROOT_PASSWORD}
  #     MONGO_INITDB_DATABASE: ${MONGODB_DB_NAME}
  redis_uer:
    image: redis:latest
    container_name: redis_uer
    restart: always
    ports:
      - '6383:6383'
    volumes:
      - redis_uer:/data
    command:
      ['redis-server', '--port', '6383', '--requirepass', '${REDIS_PASSWORD}']
    environment:
      REDIS_PASSWORD: ${REDIS_PASSWORD}
volumes:
  redis_uer:
  #mongodb:
