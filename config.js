const credentials = {
    mongo: {
        connectionString: process.env.MONGO_URI || 'mongodb://localhost:27017/main',
        dbName: process.env.DB_NAME || 'main',
    },
    redis: {
        url: process.env.REDIS_URL || 'redis://localhost:6379',
        enabled: process.env.REDIS_ENABLED !== 'false', // По умолчанию включено
        caching: {
            enabled: process.env.CACHE_ENABLED !== 'false', // По умолчанию включено
            // Конфигурация TTL для различных ресурсов (в секундах)
            ssz_time_stats: 3600, // 1 час
            out_age_stats: 1800, // 30 минут
            average_repair_time: 86400, // 1 день
            tags_stats: 3600, // 1 час
        }
    },
    // Другие настройки приложения...
};

module.exports = { credentials };