function setExternalUserId(uid) {

    console.log("setExternalUserId start: " + uid);

    OneSignalDeferred.push(function (OneSignal) {

        OneSignal.login(uid);

        console.log("setExternalUserId end: " + uid)
    });
}

function disablePush() {

    console.log("disablePush start: ");

    OneSignalDeferred.push(function (OneSignal) {

        OneSignal.logout();
    });
}

function notificationPromptWeb() {

    console.log("notificationPromptWeb");

    OneSignalDeferred.push(function (OneSignal) {

        OneSignal.Notifications.requestPermission();
    });
}